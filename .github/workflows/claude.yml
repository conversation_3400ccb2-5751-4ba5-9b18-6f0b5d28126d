name: <PERSON> Assistant

on:
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]
  issues:
    types: [opened, assigned]
  pull_request_review:
    types: [submitted]

jobs:
  claude-code-action:
    if: |
      (github.event_name == 'issue_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review_comment' && contains(github.event.comment.body, '@claude')) ||
      (github.event_name == 'pull_request_review' && contains(github.event.review.body, '@claude')) ||
      (github.event_name == 'issues' && contains(github.event.issue.body, '@claude'))
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
      issues: write
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SERVICE_ACCOUNT }}

      - name: Run Claude PR Action
        uses: anthropics/claude-code-action@beta
        with:
          trigger_phrase: "@msu-reevo"
          assignee_trigger: "msu-reevo"
          # Pass the comment text as the prompt
          custom_instructions: |
            Let's commit the changes. Run tests, typechecks, and format checks. Then commit, push, and create a pull request.
          timeout_minutes: "60"
          use_vertex: "true"
          allowed_tools: "Bash(uv run ruff check .),Bash(uv run mypy .),Bash(uv run pytest --verbosity=2 --record-mode=none),Bash(make docker-start-dep),Bash(uv run python -m salestech_be),Bash(git:*),Bash(gh pr create),Bash(gh pr list),Bash(gh pr view),Bash(gh issue list),Bash(gh issue view),Task,Glob,Grep,LS,Read,Edit,MultiEdit,Write,TodoRead,TodoWrite,WebSearch,mcp__github__create_pull_request,mcp__github__create_branch"
        env:
          ANTHROPIC_VERTEX_PROJECT_ID: ${{ steps.auth.outputs.project_id }}
          CLOUD_ML_REGION: us-east5
