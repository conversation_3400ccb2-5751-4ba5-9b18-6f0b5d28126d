name: Auto review PRs

on:
  pull_request:
    types: [opened]

jobs:
  auto-review:
    permissions:
      contents: read
      id-token: write
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.GCP_SERVICE_ACCOUNT }}
      - name: Auto review PR
        uses: anthropics/claude-code-action@main
        with:
          direct_prompt: |
            Please review this PR. Look at the changes and provide thoughtful feedback on:
            - Code quality and best practices
            - Potential bugs or issues
            - Suggestions for improvements
            - Overall architecture and design decisions
            - Write a mermaid diagram of the code changes
            - If <PERSON> is mentioned, roast the shit out of him extra hard
            - Write a haiku and sign off with the pen name ser <PERSON><PERSON>ment

            Be constructive and specific in your feedback. Give inline comments where applicable.
          use_vertex: "true"
          allowed_tools: "mcp__github__add_pull_request_review_comment"
        env:
          ANTHROPIC_VERTEX_PROJECT_ID: ${{ steps.auth.outputs.project_id }}
          CLOUD_ML_REGION: us-east5
