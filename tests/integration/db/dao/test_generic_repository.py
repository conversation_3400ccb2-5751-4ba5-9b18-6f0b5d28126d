import datetime
import re
import uuid
from unittest.mock import patch

import pytest

from salestech_be.db.dao.array import ColumnIsArray
from salestech_be.db.dao.generic_repository import (
    GenericRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.account import (
    Account,
    AccountStatus,
    AccountUpdate,
)
from salestech_be.db.models.attachment import Attachment
from salestech_be.db.models.insight import (
    Insight,
    InsightAuthorType,
    InsightReferenceIdType,
    InsightSourceType,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none


class SomeAccountRelatedRepo(GenericRepository):
    async def insert_but_rolled_back(self, model_instance: Account) -> Account:
        try:
            async with self.engine.begin():
                inserted = await self.insert(model_instance)
                raise ValueError("error will cause rollback")
        except ValueError:
            # NOTE: this is purely to demonstrate the rollback behavior!
            return not_none(inserted)


class SomeRepoWhyBeSomeAccountRepoJustBeSomeRepo(GenericRepository):
    pass


async def test_crud(_engine: DatabaseEngine) -> None:
    repo = SomeAccountRelatedRepo(engine=_engine)

    account_org_uuid = uuid.uuid4()
    another_org_uuid = uuid.uuid4()
    account = Account(
        id=uuid.uuid4(),
        display_name="the name",
        status=AccountStatus.TARGET,
        x_url="https://abc.com",
        created_at=datetime.datetime.now(tz=datetime.UTC),
        created_by_user_id=uuid.uuid4(),
        organization_id=account_org_uuid,
        owner_user_id=uuid.uuid4(),
    )

    inserted = await repo.insert(account)
    assert inserted.model_dump(exclude={"sys_updated_at"}) == account.model_dump(
        exclude={"sys_updated_at"}
    )

    found_by_tenant_pk = not_none(
        await repo.find_by_tenanted_primary_key(
            Account,
            organization_id=account_org_uuid,
            id=account.id,
        )
    )
    assert found_by_tenant_pk.model_dump(
        exclude={"sys_updated_at"}
    ) == account.model_dump(exclude={"sys_updated_at"})

    not_found = await repo.find_by_tenanted_primary_key(
        Account,
        organization_id=another_org_uuid,
        id=account.id,
    )
    assert not not_found

    found = not_none(await repo.find_by_primary_key(Account, id=account.id))
    assert found.model_dump(exclude={"sys_updated_at"}) == account.model_dump(
        exclude={"sys_updated_at"}
    )

    found_by_column = await repo._find_by_column_values(
        Account,
        status=account.status,
        x_url=account.x_url,
    )
    assert len(found_by_column)
    sorted_result = sorted(
        found_by_column,
        key=lambda account: account.created_at,
        reverse=True,
    )
    assert sorted_result[0].model_dump(
        exclude={"sys_updated_at"}
    ) == account.model_dump(exclude={"sys_updated_at"})

    found_by_column_no_match = await repo._find_by_column_values(
        Account,
        status=account.status,
        x_url="www.boba.com",
    )
    assert not len(found_by_column_no_match)

    with pytest.raises(ValueError):
        await repo._find_by_column_values(Account, boba=account.status)

    update_technologies = "very cool technologies"
    updated = await repo.update_by_primary_key(
        Account,
        primary_key_to_value={"id": account.id},
        column_to_update=AccountUpdate(
            technology_list=[update_technologies],
            updated_by_user_id=uuid.uuid4(),
        ),
    )
    assert isinstance(updated, Account)
    assert updated.model_dump(
        exclude={
            "technology_list",
            "sys_updated_at",
            "updated_at",
            "updated_by_user_id",
        }
    ) == account.model_dump(
        exclude={
            "technology_list",
            "sys_updated_at",
            "updated_at",
            "updated_by_user_id",
        },
    )

    revert_updated = not_none(
        await repo.update_by_tenanted_primary_key(
            table_model=Account,
            primary_key_to_value={"id": account.id},
            column_to_update=AccountUpdate(
                technology_list=None,
                updated_by_user_id=uuid.uuid4(),
            ),
            organization_id=account_org_uuid,
        )
    )
    assert revert_updated.model_dump(
        exclude={"sys_updated_at", "updated_at", "updated_by_user_id"}
    ) == account.model_dump(
        exclude={"sys_updated_at", "updated_at", "updated_by_user_id"}
    )


async def test_transaction(_engine: DatabaseEngine) -> None:
    repo = SomeAccountRelatedRepo(engine=_engine)

    account = Account(
        id=uuid.uuid4(),
        display_name="the name",
        status=AccountStatus.TARGET,
        x_url="https://abc.com",
        created_at=datetime.datetime.now(tz=datetime.UTC),
        created_by_user_id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        owner_user_id=uuid.uuid4(),
    )

    account_rolled_back = await repo.insert_but_rolled_back(account)
    assert not (await repo.find_by_primary_key(Account, id=account_rolled_back.id))


async def test_get_table_model_upsert_unique_columns_stmt() -> None:
    attachment = Attachment(
        id=uuid.uuid4(),
        file_name="test_file_name",
        content_type="image/jpeg",
        size=100,
        is_inline=False,
        s3_key="s3-key",
        user_id=uuid.uuid4(),
        provider="NYLAS",
        provider_id=str(uuid.uuid4()),
        email_account_id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
        media_vtt_url=None,
        media_sprite_url=None,
        md5_checksum=None,
        media_preview_thumbnail_url=None,
    )
    # Test 1: set exclude_columns_from_update
    stmt = GenericRepository._get_table_model_upsert_unique_columns_stmt(
        instance=attachment,
        on_conflict_target_columns=[
            "provider",
            "provider_id",
            "email_account_id",
        ],
        exclude_columns_from_update=[
            "id",
            "user_id",
            "s3_key",
            "organization_id",
            "created_at",
        ],
    )
    str_stmt = re.sub("\n +", " ", str(stmt).strip())
    expected_stmt = (
        'insert into public.attachment ("content_type", "created_at", "deleted_at", '
        '"email_account_id", "file_name", "id", "is_inline", '
        '"md5_checksum", "media_preview_thumbnail_url", "media_sprite_url", "media_vtt_url", '
        '"organization_id", "provider", "provider_content_id", "provider_id", '
        '"s3_key", "size", "updated_at", "user_id") values (:content_type, :created_at, :deleted_at, '
        ":email_account_id, :file_name, :id, :is_inline, :md5_checksum, "
        ":media_preview_thumbnail_url, :media_sprite_url, :media_vtt_url, :organization_id, :provider, "
        ":provider_content_id, :provider_id, :s3_key, :size, :updated_at, :user_id) "
        "on conflict (provider, provider_id, email_account_id) "
        "where provider is not null and provider_id is not null "
        "and email_account_id is not null  "
        "do update set content_type = :content_type, deleted_at = :deleted_at, "
        "file_name = :file_name, is_inline = :is_inline, "
        "md5_checksum = :md5_checksum, media_preview_thumbnail_url = :media_preview_thumbnail_url, "
        "media_sprite_url = :media_sprite_url, media_vtt_url = :media_vtt_url, "
        "provider_content_id = :provider_content_id, size = :size, updated_at = :updated_at returning *"
    )
    assert str_stmt == expected_stmt

    # Test 2: set columns_to_update
    stmt = GenericRepository._get_table_model_upsert_unique_columns_stmt(
        instance=attachment,
        on_conflict_target_columns=[
            "provider",
            "provider_id",
            "email_account_id",
        ],
        columns_to_update=["content_type", "deleted_at"],
    )
    str_stmt = re.sub("\n +", " ", str(stmt).strip())
    expected_stmt = (
        'insert into public.attachment ("content_type", "created_at", "deleted_at", '
        '"email_account_id", "file_name", "id", "is_inline", '
        '"md5_checksum", "media_preview_thumbnail_url", "media_sprite_url", "media_vtt_url", '
        '"organization_id", "provider", "provider_content_id", "provider_id", '
        '"s3_key", "size", "updated_at", "user_id") values (:content_type, :created_at, :deleted_at, '
        ":email_account_id, :file_name, :id, :is_inline, :md5_checksum, "
        ":media_preview_thumbnail_url, :media_sprite_url, :media_vtt_url, :organization_id, :provider, "
        ":provider_content_id, :provider_id, :s3_key, :size, :updated_at, :user_id) "
        "on conflict (provider, provider_id, email_account_id) "
        "where provider is not null and provider_id is not null "
        "and email_account_id is not null  "
        "do update set content_type = :content_type, deleted_at = :deleted_at returning *"
    )
    assert str_stmt == expected_stmt

    # Test 3: set same field to both columns_to_update and exclude_columns_from_update
    with pytest.raises(ValueError) as e:
        GenericRepository._get_table_model_upsert_unique_columns_stmt(
            instance=attachment,
            on_conflict_target_columns=[
                "provider",
                "provider_id",
                "email_account_id",
            ],
            columns_to_update=["user_id"],
            exclude_columns_from_update=[
                "id",
                "user_id",
                "s3_key",
                "organization_id",
                "created_at",
            ],
        )
    assert (
        str(e.value)
        == "columns_to_update or exclude_columns_from_update should provide at most 1."
    )

    # Test 4: No Update
    stmt = GenericRepository._get_table_model_upsert_unique_columns_stmt(
        instance=attachment,
        on_conflict_target_columns=[
            "provider",
            "provider_id",
            "email_account_id",
        ],
        exclude_columns_from_update=list(attachment.column_fields),
    )
    str_stmt = re.sub("\n +", " ", str(stmt).strip())
    expected_stmt = (
        'insert into public.attachment ("content_type", "created_at", "deleted_at", '
        '"email_account_id", "file_name", "id", "is_inline", '
        '"md5_checksum", "media_preview_thumbnail_url", "media_sprite_url", "media_vtt_url", '
        '"organization_id", "provider", "provider_content_id", "provider_id", '
        '"s3_key", "size", "updated_at", "user_id") values (:content_type, :created_at, :deleted_at, '
        ":email_account_id, :file_name, :id, :is_inline, :md5_checksum, "
        ":media_preview_thumbnail_url, :media_sprite_url, :media_vtt_url, :organization_id, :provider, "
        ":provider_content_id, :provider_id, :s3_key, :size, :updated_at, :user_id) "
        "on conflict (provider, provider_id, email_account_id) "
        "where provider is not null and provider_id is not null "
        "and email_account_id is not null  "
        "do update set provider = EXCLUDED.provider, provider_id = EXCLUDED.provider_id, "
        "email_account_id = EXCLUDED.email_account_id returning *"
    )
    assert str_stmt == expected_stmt


async def test_update_array_field(_engine: DatabaseEngine) -> None:
    user_id = uuid.uuid4()
    timenow = zoned_utc_now()
    fake_insight = Insight(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        reference_type=InsightReferenceIdType.MEETING,
        reference_id=uuid.uuid4(),
        contact_id=uuid.uuid4(),
        insight_section_id=uuid.uuid4(),
        created_by_user_id=user_id,
        updated_by_user_id=user_id,
        deleted_by_user_id=None,
        source_type=InsightSourceType.TRANSCRIPTION,
        tags=["tag_one", "tag_two"],
        brief_values=["this is about tag_one", "this is about tag two"],
        detailed_explanation="I chose tag_one and tag_two",
        transcript_locations=[10, 20, 23],
        source_locations=None,
        created_at=timenow,
        updated_at=timenow,
        deleted_at=None,
        author_type=InsightAuthorType.USER,
        insight_name="insight name",
        insight_description="insight description",
        user_feedback=None,
        approved_at=None,
        approved_by_user_id=None,
        metadata={},
        rank=0,
        version=0,
    )
    repo = SomeRepoWhyBeSomeAccountRepoJustBeSomeRepo(engine=_engine)
    inserted_insight = await repo.insert(fake_insight)
    retrieved_insight = await repo.find_by_primary_key(Insight, id=fake_insight.id)

    assert inserted_insight == retrieved_insight
    assert fake_insight == retrieved_insight


async def test_bulk_insert(_engine: DatabaseEngine) -> None:
    """Test that bulk_insert can insert multiple records at once."""
    repo = SomeRepoWhyBeSomeAccountRepoJustBeSomeRepo(engine=_engine)
    org_id = uuid.uuid4()
    now = zoned_utc_now()
    user_id = uuid.uuid4()

    # Create multiple account instances
    num_accounts = 10
    accounts_to_insert = []

    for i in range(num_accounts):
        account = Account(
            id=uuid.uuid4(),
            display_name=f"Bulk Account {i}",
            status=AccountStatus.TARGET,
            x_url=f"https://example{i}.com",
            created_at=now,
            created_by_user_id=user_id,
            organization_id=org_id,
            owner_user_id=user_id,
        )
        accounts_to_insert.append(account)

    # Patch the calculate_max_batch_size function to force smaller batches (5 accounts per batch)
    with patch(
        "salestech_be.db.dao.generic_repository.calculate_max_batch_size",
        return_value=2,
    ):
        # Perform bulk insert which should now use multiple batches
        inserted_accounts = await repo.bulk_insert(
            table_model=Account, instances=accounts_to_insert
        )

    # Verify all accounts were inserted
    assert len(inserted_accounts) == num_accounts

    # Verify account details were preserved
    for i, original_account in enumerate(accounts_to_insert):
        inserted_account = inserted_accounts[i]

        # Verify primary key and essential fields
        assert inserted_account.id == original_account.id
        assert inserted_account.display_name == original_account.display_name
        assert inserted_account.organization_id == org_id

        # Verify we can retrieve it from the database
        retrieved_account = await repo.find_by_primary_key(
            Account, id=original_account.id
        )
        assert retrieved_account is not None
        assert retrieved_account.display_name == f"Bulk Account {i}"

    # Test with JSON fields
    insights_to_insert = []
    for i in range(3):
        insight = Insight(
            id=uuid.uuid4(),
            organization_id=org_id,
            reference_type=InsightReferenceIdType.MEETING,
            reference_id=uuid.uuid4(),
            contact_id=uuid.uuid4(),
            insight_section_id=uuid.uuid4(),
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
            deleted_by_user_id=None,
            source_type=InsightSourceType.TRANSCRIPTION,
            tags=[f"tag_{i}_1", f"tag_{i}_2"],
            brief_values=[f"brief value {i}"],
            detailed_explanation=f"Detailed explanation {i}",
            transcript_locations=[i * 10, i * 20, i * 30],
            source_locations=None,
            created_at=now,
            updated_at=now,
            deleted_at=None,
            author_type=InsightAuthorType.USER,
            insight_name=f"Insight {i}",
            insight_description=f"Description {i}",
            user_feedback=None,
            approved_at=None,
            approved_by_user_id=None,
            metadata={"test_key": f"test_value_{i}"},
            rank=i,
            version=0,
        )
        insights_to_insert.append(insight)

    # Perform bulk insert with JSON fields (1 batch)
    inserted_insights = await repo.bulk_insert(
        table_model=Insight, instances=insights_to_insert
    )

    # Verify all insights were inserted with correct JSON fields
    assert len(inserted_insights) == len(insights_to_insert)

    for i, original_insight in enumerate(insights_to_insert):
        inserted_insight = inserted_insights[i]

        # Check JSON fields were preserved
        assert inserted_insight.tags == original_insight.tags
        assert inserted_insight.metadata == original_insight.metadata
        assert (
            inserted_insight.transcript_locations
            == original_insight.transcript_locations
        )

        # Verify we can retrieve it from the database
        retrieved_insight = await repo.find_by_primary_key(
            Insight, id=original_insight.id
        )
        assert retrieved_insight is not None
        assert retrieved_insight.metadata == {"test_key": f"test_value_{i}"}


async def test_find_by_column_values_with_none_in_lists(
    _engine: DatabaseEngine,
) -> None:
    """Test _find_by_column_values with None values in lists and ColumnIsArray objects."""
    repo = SomeAccountRelatedRepo(engine=_engine)

    org_id = uuid.uuid4()
    user_id = uuid.uuid4()
    now = datetime.datetime.now(tz=datetime.UTC)

    # Create test accounts with different statuses and x_urls
    accounts = [
        Account(
            id=uuid.uuid4(),
            display_name="Account 1",
            status=AccountStatus.TARGET,
            x_url="https://example1.com",
            created_at=now,
            created_by_user_id=user_id,
            organization_id=org_id,
            owner_user_id=user_id,
        ),
        Account(
            id=uuid.uuid4(),
            display_name="Account 2",
            status=AccountStatus.SELLING,
            x_url="https://example2.com",
            created_at=now,
            created_by_user_id=user_id,
            organization_id=org_id,
            owner_user_id=user_id,
        ),
        Account(
            id=uuid.uuid4(),
            display_name="Account 3",
            status=AccountStatus.TARGET,
            x_url=None,  # This account has null x_url
            created_at=now,
            created_by_user_id=user_id,
            organization_id=org_id,
            owner_user_id=user_id,
        ),
        Account(
            id=uuid.uuid4(),
            display_name="Account 4",
            status=AccountStatus.CUSTOMER,
            x_url=None,  # This account also has null x_url
            created_at=now,
            created_by_user_id=user_id,
            organization_id=org_id,
            owner_user_id=user_id,
        ),
    ]

    # Insert all accounts
    for account in accounts:
        await repo.insert(account)

    # Test 1: Find by list containing None - should match both specific values and null
    found_accounts = await repo._find_by_column_values(
        Account,
        x_url=["https://example1.com", "https://example2.com", None],
        organization_id=org_id,
    )
    # Should find accounts 1, 2, 3, and 4 (all accounts)
    assert len(found_accounts) == 4
    found_ids = {acc.id for acc in found_accounts}
    expected_ids = {acc.id for acc in accounts}
    assert found_ids == expected_ids

    # Test 2: Find by list containing only None - should match only null values
    found_accounts = await repo._find_by_column_values(
        Account,
        x_url=[None],
        organization_id=org_id,
    )
    # Should find accounts 3 and 4 (null x_url)
    assert len(found_accounts) == 2
    found_ids = {acc.id for acc in found_accounts}
    expected_ids = {accounts[2].id, accounts[3].id}
    assert found_ids == expected_ids

    # Test 3: Find by list without None - should match only specific values
    found_accounts = await repo._find_by_column_values(
        Account,
        x_url=["https://example1.com", "https://example2.com"],
        organization_id=org_id,
    )
    # Should find accounts 1 and 2
    assert len(found_accounts) == 2
    found_ids = {acc.id for acc in found_accounts}
    expected_ids = {accounts[0].id, accounts[1].id}
    assert found_ids == expected_ids

    # Test 4: Find by status list containing None
    found_accounts = await repo._find_by_column_values(
        Account,
        status=[AccountStatus.TARGET, None],
        organization_id=org_id,
    )
    # Should find accounts 1 and 3 (TARGET status)
    assert len(found_accounts) == 2
    found_ids = {acc.id for acc in found_accounts}
    expected_ids = {accounts[0].id, accounts[2].id}
    assert found_ids == expected_ids

    # Test 5: Test with ColumnIsArray containing None
    found_accounts = await repo._find_by_column_values(
        Account,
        status=ColumnIsArray(value=[AccountStatus.TARGET, AccountStatus.SELLING, None]),
        organization_id=org_id,
    )
    # Should find accounts 1, 2, and 3 (TARGET and SELLING statuses)
    assert len(found_accounts) == 3
    found_ids = {acc.id for acc in found_accounts}
    expected_ids = {accounts[0].id, accounts[1].id, accounts[2].id}
    assert found_ids == expected_ids

    # Test 6: Test with ColumnIsArray containing only None
    found_accounts = await repo._find_by_column_values(
        Account,
        x_url=ColumnIsArray(value=[None]),
        organization_id=org_id,
    )
    # Should find accounts 3 and 4 (null x_url)
    assert len(found_accounts) == 2
    found_ids = {acc.id for acc in found_accounts}
    expected_ids = {accounts[2].id, accounts[3].id}
    assert found_ids == expected_ids

    # Test 7: Test with empty list - should match nothing
    found_accounts = await repo._find_by_column_values(
        Account,
        x_url=[],
        organization_id=org_id,
    )
    # Should find no accounts
    assert len(found_accounts) == 0

    # Test 8: Test with ColumnIsArray containing empty list - should match nothing
    found_accounts = await repo._find_by_column_values(
        Account,
        status=ColumnIsArray(value=[]),
        organization_id=org_id,
    )
    # Should find no accounts
    assert len(found_accounts) == 0
