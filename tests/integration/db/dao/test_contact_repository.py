import random
import uuid
from collections.abc import Awaitable, Callable
from typing import cast
from uuid import UUID, uuid4

import pytest
from faker import Faker
from frozendict import frozendict

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import ConflictResourceError
from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    ContactField,
    StdObjectIdentifiers,
    StdSelectListIdentifier,
)
from salestech_be.common.type.contact import ContactChannelLabel, ContactChannelType
from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_query_service import ContactQueryService
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
)
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.models.account import (
    Account,
    AccountStatus,
)
from salestech_be.db.models.address import Address, AddressCreateRequest
from salestech_be.db.models.contact import (
    Contact,
    ContactUpdate,
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailAccountAssociationRequest,
    CreateDbContactEmailRequest,
    CreateDbContactPhoneNumberAccountAssociationRequest,
    CreateDbContactPhoneNumberRequest,
    CreateDbContactRequest,
    UpdateContactEmailAccountAssociationRequest,
    UpdateContactEmailRequest,
    UpdateContactRequest,
)
from salestech_be.db.models.contact_account_association import (
    ContactAccountAssociation,
    ContactAccountAssociationUpdate,
)
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumber,
    ContactPhoneNumberAccountAssociation,
)
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
)
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.pydantic_types.str import (
    PhoneNumberWithExtension,
    parse_phone_number,
    validate_e164,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from tests.util.factories import AddressFactory, ContactFactory, PipelineFactory


@pytest.fixture
async def pre_engagement_account(account_repo: AccountRepository) -> Account:
    return await account_repo.insert(
        Account(
            id=uuid4(),
            display_name=f"pre-engagement account - {uuid4()}",
            status=AccountStatus.TARGET,
            owner_user_id=uuid4(),
            created_at=zoned_utc_now(),
            created_by_user_id=uuid4(),
            organization_id=uuid4(),
        )
    )


@pytest.fixture
async def lead_account(
    pre_engagement_account: Account, account_repo: AccountRepository
) -> Account:
    return await account_repo.insert(
        Account(
            id=uuid4(),
            display_name=f"lead account - {uuid4()}",
            status=AccountStatus.TARGET,
            owner_user_id=uuid4(),
            created_at=zoned_utc_now(),
            created_by_user_id=uuid4(),
            organization_id=pre_engagement_account.organization_id,
        )
    )


@pytest.fixture
async def deal_account(
    pre_engagement_account: Account, account_repo: AccountRepository
) -> Account:
    return await account_repo.insert(
        Account(
            id=uuid4(),
            display_name=f"deal account - {uuid4()}",
            status=AccountStatus.TARGET,
            owner_user_id=uuid4(),
            created_at=zoned_utc_now(),
            created_by_user_id=uuid4(),
            organization_id=pre_engagement_account.organization_id,
        )
    )


@pytest.fixture
async def archived_deal_account(
    pre_engagement_account: Account, account_repo: AccountRepository
) -> Account:
    return await account_repo.insert(
        Account(
            id=uuid4(),
            display_name=f"archived deal account - {uuid4()}",
            status=AccountStatus.TARGET,
            owner_user_id=uuid4(),
            created_at=zoned_utc_now(),
            created_by_user_id=uuid4(),
            organization_id=pre_engagement_account.organization_id,
            archived_at=zoned_utc_now(),
            archived_by_user_id=uuid4(),
        )
    )


@pytest.fixture
def create_contact_on_pre_engagement_account_request(
    pre_engagement_account: Account,
) -> CreateContactRequest:
    random_extension = random.randint(999, 9999)  # noqa: S311
    phone_number = f"******-253-0000 ext. {random_extension}"
    return CreateContactRequest(
        contact=CreateDbContactRequest(
            display_name="Test Me",
            created_by_user_id=uuid4(),
            owner_user_id=uuid4(),
            first_name="Test",
            last_name="Me",
            middle_name="Not",
            linkedin_url="https://www.linkedin.com/in/testme",
            zoominfo_url="https://www.zoominfo.com/c/testme",
            facebook_url="https://www.facebook.com/testme",
            x_url="https://www.x.com/testme",
            address_id=uuid4(),
            title="Cleaner",
            department="Back Office",
            stage_id=uuid4(),
        ),
        contact_emails=[
            CreateDbContactEmailRequest(
                email=f"{uuid4()}@test.com",
                is_contact_primary=True,
                email_account_associations=[
                    CreateDbContactEmailAccountAssociationRequest(
                        account_id=pre_engagement_account.id,
                        is_contact_account_primary=True,
                    )
                ],
            )
        ],
        contact_phone_numbers=[
            CreateDbContactPhoneNumberRequest(
                phone_number=phone_number,
                is_contact_primary=True,
                phone_number_account_associations=[
                    CreateDbContactPhoneNumberAccountAssociationRequest(
                        account_id=pre_engagement_account.id,
                        is_contact_account_primary=True,
                    )
                ],
            )
        ],
        contact_account_roles=[
            CreateContactAccountRoleRequest(
                account_id=pre_engagement_account.id,
                is_primary_account=True,
            )
        ],
    )


@pytest.fixture
def create_contact_request_without_account_assoc() -> CreateContactRequest:
    return CreateContactRequest(
        contact=CreateDbContactRequest(
            display_name="Test Me",
            created_by_user_id=uuid4(),
            owner_user_id=uuid4(),
            first_name="Test",
            last_name="Me",
            middle_name="Not",
            primary_phone_number="+***********",
            linkedin_url="https://www.linkedin.com/in/testme",
            zoominfo_url="https://www.zoominfo.com/c/testme",
            facebook_url="https://www.facebook.com/testme",
            x_url="https://www.x.com/testme",
            address_id=uuid4(),
            title="Cleaner",
            department="Back Office",
            stage_id=uuid4(),
        ),
        contact_emails=[
            CreateDbContactEmailRequest(
                email=f"{uuid4()}@test.com",
                is_contact_primary=True,
                email_account_associations=[],
            )
        ],
        contact_account_roles=[],
    )


async def helper_test_create_or_unarchive_contact_with_stable_models(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    create_request: CreateContactRequest,
    organization_id: UUID,
) -> Contact:
    """Helper function to create a contact using stable DB models."""
    (
        db_contact,
        db_contact_address,
        db_contact_emails,
        db_contact_phone_numbers,
        db_contact_account_associations,
    ) = contact_service._generate_stable_db_models(
        create_contact_request=create_request,
        organization_id=organization_id,
    )
    return await contact_repo.create_or_unarchive_contact(
        organization_id=organization_id,
        db_contact=db_contact,
        db_contact_address=db_contact_address,
        db_contact_emails=db_contact_emails,
        db_contact_phone_numbers=db_contact_phone_numbers,
        db_contact_account_associations=db_contact_account_associations,
    )


async def test_create_contact_v2(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    account_repo: AccountRepository,
    pre_engagement_account: Account,
    create_contact_on_pre_engagement_account_request: CreateContactRequest,
    lead_account: Account,
) -> None:
    create_request = create_contact_on_pre_engagement_account_request
    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    assert contact.stage_id == create_request.contact.stage_id
    assert contact.display_name == create_request.contact.display_name
    assert contact.first_name == create_request.contact.first_name
    assert contact.last_name == create_request.contact.last_name
    assert contact.middle_name == create_request.contact.middle_name
    assert create_request.contact_phone_numbers
    country_code, number_e164, extension = parse_phone_number(
        phone_number_raw=create_request.contact_phone_numbers[0].phone_number
    )
    assert contact.primary_phone_number == number_e164
    assert contact.linkedin_url == create_request.contact.linkedin_url
    assert contact.zoominfo_url == create_request.contact.zoominfo_url
    assert contact.facebook_url == create_request.contact.facebook_url
    assert contact.x_url == create_request.contact.x_url
    assert contact.title == create_request.contact.title
    assert contact.department == create_request.contact.department
    assert contact.address_id == create_request.contact.address_id
    assert contact.address_id == create_request.contact.address_id
    assert contact.person_id is None

    # ensure there is a contact_account_association inserted as well.
    contact_account_association = await contact_repo._find_unique_by_column_values(
        ContactAccountAssociation,
        organization_id=pre_engagement_account.organization_id,
        contact_id=contact.id,
        account_id=not_none(create_request.primary_account_role).account_id,
    )
    assert contact_account_association is not None
    assert contact_account_association.created_by_user_id == contact.created_by_user_id
    assert contact_account_association.updated_by_user_id == contact.updated_by_user_id
    assert contact_account_association.archived_at is None
    assert contact_account_association.archived_by_user_id is None
    assert contact_account_association.organization_id == contact.organization_id
    assert contact_account_association.contact_id == contact.id

    # ensure contact_email and contact_email_account_association are inserted as well
    contact_email = await contact_repo._find_unique_by_column_values(
        ContactEmail,
        organization_id=contact.organization_id,
        contact_id=contact.id,
    )
    assert contact_email
    assert contact_email.email == create_request.contact_emails[0].email
    assert contact_email.is_contact_primary
    assert contact_email.organization_id == contact.organization_id
    assert contact_email.contact_id == contact.id
    assert contact_email.deleted_at is None
    assert contact_email.archived_at is None
    assert contact_email.deleted_by_user_id is None
    assert contact_email.archived_by_user_id is None
    primary_account_id = await contact_repo.get_primary_account_id_by_contact_id(
        contact_id=contact.id,
        organization_id=contact.organization_id,
    )
    contact_email_account_association = (
        await contact_repo._find_unique_by_column_values(
            ContactEmailAccountAssociation,
            organization_id=contact.organization_id,
            contact_email_id=contact_email.id,
            contact_id=contact.id,
            account_id=primary_account_id,
        )
    )
    assert contact_email_account_association
    assert contact_email_account_association.organization_id == contact.organization_id
    assert contact_email_account_association.contact_email_id == contact_email.id
    assert contact_email_account_association.contact_id == contact.id
    assert contact_email_account_association.account_id == primary_account_id
    assert contact_email_account_association.is_contact_account_primary
    assert contact_email_account_association.deleted_at is None
    assert contact_email_account_association.archived_at is None
    assert contact_email_account_association.deleted_by_user_id is None
    assert contact_email_account_association.archived_by_user_id is None

    # ensure contact_phone_number and contact_phone_number_account_association are inserted as well
    contact_phone_number = await contact_repo._find_unique_by_column_values(
        ContactPhoneNumber,
        organization_id=contact.organization_id,
        contact_id=contact.id,
    )
    assert contact_phone_number
    assert (
        contact_phone_number.phone_number
        == create_request.contact_phone_numbers[0].phone_number
    )
    assert contact_phone_number.is_contact_primary
    assert contact_phone_number.organization_id == contact.organization_id
    assert contact_phone_number.contact_id == contact.id
    assert contact_phone_number.deleted_at is None
    assert contact_phone_number.archived_at is None
    assert contact_phone_number.deleted_by_user_id is None
    assert contact_phone_number.archived_by_user_id is None
    contact_phone_number_account_association = (
        await contact_repo._find_unique_by_column_values(
            ContactPhoneNumberAccountAssociation,
            organization_id=contact.organization_id,
            contact_phone_number_id=contact_phone_number.id,
            contact_id=contact.id,
            account_id=primary_account_id,
        )
    )
    assert contact_phone_number_account_association
    assert (
        contact_phone_number_account_association.organization_id
        == contact.organization_id
    )
    assert (
        contact_phone_number_account_association.contact_phone_number_id
        == contact_phone_number.id
    )
    assert contact_phone_number_account_association.contact_id == contact.id
    assert contact_phone_number_account_association.account_id == primary_account_id
    assert contact_phone_number_account_association.is_contact_account_primary
    assert contact_phone_number_account_association.deleted_at is None
    assert contact_phone_number_account_association.archived_at is None
    assert contact_phone_number_account_association.deleted_by_user_id is None
    assert contact_phone_number_account_association.archived_by_user_id is None

    # after create the first contact on a pre-engagement account,
    # I should see the account.stage staying at ContactStage.PRE_ENGAGEMENT
    updated_pre_engagement_account = not_none(
        await account_repo.find_by_tenanted_primary_key(
            Account,
            organization_id=pre_engagement_account.organization_id,
            id=not_none(create_request.primary_account_role).account_id,
        )
    )
    assert updated_pre_engagement_account.id == pre_engagement_account.id

    # now try to create a contact with the same primary email, but DIFFERENT account id
    recreate_request = create_request.model_copy(
        update={
            "contact": create_request.contact.model_copy(
                update={
                    "display_name": contact.display_name + "_2",
                    "title": "boss",
                }
            ),
            "contact_emails": [
                create_request.contact_emails[0].model_copy(
                    update={
                        "email_account_associations": [
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=lead_account.id,
                                is_contact_account_primary=True,
                            )
                        ]
                    }
                )
            ],
            "contact_phone_numbers": [
                create_request.contact_phone_numbers[0].model_copy(
                    update={
                        "phone_number_account_associations": [
                            CreateDbContactPhoneNumberAccountAssociationRequest(
                                account_id=lead_account.id,
                                is_contact_account_primary=True,
                            )
                        ]
                    }
                )
            ],
            "contact_account_roles": [
                CreateContactAccountRoleRequest(
                    account_id=lead_account.id,
                    is_primary_account=True,
                )
            ],
        }
    )
    with pytest.raises(ConflictResourceError):
        await helper_test_create_or_unarchive_contact_with_stable_models(
            contact_repo=contact_repo,
            contact_service=contact_service,
            create_request=recreate_request,
            organization_id=lead_account.organization_id,
        )
    # nothing happened above, so when I list contact and associations,
    # I only see the first ones
    retrieved_contacts = await contact_repo._find_by_column_values(
        Contact, organization_id=contact.organization_id
    )
    retrieved_contact_account_assocs = await contact_repo._find_by_column_values(
        ContactAccountAssociation, organization_id=contact.organization_id
    )
    retrieved_contact_emails = await contact_repo._find_by_column_values(
        ContactEmail, organization_id=contact.organization_id
    )
    retrieved_contact_email_account_assocs = await contact_repo._find_by_column_values(
        ContactEmailAccountAssociation, organization_id=contact.organization_id
    )
    assert len(retrieved_contacts) == 1
    assert retrieved_contacts[0].id == contact.id
    assert len(retrieved_contact_account_assocs) == 1
    assert retrieved_contact_account_assocs[0].id == contact_account_association.id
    assert len(retrieved_contact_emails) == 1
    assert retrieved_contact_emails[0].id == contact_email.id
    assert len(retrieved_contact_email_account_assocs) == 1
    assert (
        retrieved_contact_email_account_assocs[0].id
        == contact_email_account_association.id
    )

    # now when I archive the contact, contact_email and contact_email_account_association,
    # it should allow me to recreate one, but different contact
    await contact_repo.archive_contact(
        organization_id=contact.organization_id,
        contact_id=contact.id,
        archived_by_user_id=uuid4(),
    )
    new_contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=recreate_request,
        organization_id=lead_account.organization_id,
    )
    assert new_contact.stage_id == recreate_request.contact.stage_id
    assert new_contact.display_name == recreate_request.contact.display_name
    assert new_contact.first_name == recreate_request.contact.first_name
    assert new_contact.last_name == recreate_request.contact.last_name
    assert new_contact.middle_name == recreate_request.contact.middle_name
    assert recreate_request.contact_phone_numbers
    country_code, number_e164, extension = parse_phone_number(
        phone_number_raw=recreate_request.contact_phone_numbers[0].phone_number
    )
    assert new_contact.primary_phone_number == number_e164
    assert new_contact.linkedin_url == recreate_request.contact.linkedin_url
    assert new_contact.zoominfo_url == recreate_request.contact.zoominfo_url
    assert new_contact.facebook_url == recreate_request.contact.facebook_url
    assert new_contact.x_url == recreate_request.contact.x_url
    assert new_contact.title == recreate_request.contact.title
    assert new_contact.department == recreate_request.contact.department
    assert new_contact.address_id == recreate_request.contact.address_id
    assert new_contact.id != contact.id
    assert not new_contact.archived_at
    assert not new_contact.archived_by_user_id

    new_primary_account_id = await contact_repo.get_primary_account_id_by_contact_id(
        contact_id=new_contact.id,
        organization_id=new_contact.organization_id,
    )
    new_contact_account_association = await contact_repo._find_unique_by_column_values(
        ContactAccountAssociation,
        organization_id=new_contact.organization_id,
        contact_id=new_contact.id,
        account_id=new_primary_account_id,
    )
    assert new_contact_account_association
    assert (
        new_contact_account_association.contact_id
        != contact_account_association.contact_id
    )
    new_contact_email = await contact_repo._find_unique_by_column_values(
        ContactEmail,
        organization_id=contact.organization_id,
        contact_id=new_contact.id,
    )
    assert new_contact_email
    assert new_contact_email.email == create_request.contact_emails[0].email
    assert new_contact_email.is_contact_primary

    new_contact_email_account_association = (
        await contact_repo._find_unique_by_column_values(
            ContactEmailAccountAssociation,
            organization_id=contact.organization_id,
            contact_email_id=new_contact_email.id,
            contact_id=new_contact.id,
            account_id=new_primary_account_id,
        )
    )
    assert new_contact_email_account_association
    assert new_contact_email_account_association.is_contact_account_primary
    assert new_contact_email_account_association.contact_id == new_contact.id
    assert new_contact_email_account_association.account_id == new_primary_account_id

    # also since the second account is a lead account, I shouldn't see this account
    # stage changes after attaching the contact , and therefore no lead should be
    # created for it
    updated_lead_account = not_none(
        await account_repo.find_by_tenanted_primary_key(
            Account,
            organization_id=lead_account.organization_id,
            id=lead_account.id,
        )
    )
    assert updated_lead_account.id == lead_account.id

    # now try to create a contact with the same primary_account_id but different primary_email
    # as well as create an address for it
    address_create_request = AddressCreateRequest(
        street_one=str(uuid4()),
        city="San Francisco",
        state="CA",
        country="US",
        zip_code="94105",
        created_by_user_id=uuid4(),
        organization_id=new_contact.organization_id,
    )

    new_primary_email = f"{uuid4()}@test.com"
    another_create_request = create_request.model_copy(
        update={
            "contact": create_request.contact.model_copy(
                update={
                    "display_name": contact.display_name + "_5",
                    "title": "big boss",
                    "primary_email": new_primary_email,
                    "address_id": None,
                    "address": address_create_request,
                }
            ),
            "contact_emails": [
                create_request.contact_emails[0].model_copy(
                    update={
                        "email": new_primary_email,
                    }
                )
            ],
        }
    )

    another_contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=another_create_request,
        organization_id=new_contact.organization_id,
    )
    assert another_contact.primary_account_id == contact.primary_account_id
    another_primary_account_id = (
        await contact_repo.get_primary_account_id_by_contact_id(
            contact_id=another_contact.id,
            organization_id=another_contact.organization_id,
        )
    )
    assert another_primary_account_id == primary_account_id
    assert another_contact.id != contact.id

    another_contact_address = await contact_repo.find_by_tenanted_primary_key(
        Address,
        organization_id=another_contact.organization_id,
        id=not_none(another_contact.address_id),
    )
    assert another_contact_address
    assert another_contact_address.street_one == address_create_request.street_one
    assert another_contact_address.city == address_create_request.city
    assert another_contact_address.state == address_create_request.state
    assert another_contact_address.country == address_create_request.country
    assert another_contact_address.zip_code == address_create_request.zip_code

    # now I can only retrieve the second one by the initial account_id,
    # since the first contact is already associated with a different primary account
    all_contacts = await contact_repo.map_contact_by_primary_account_ids(
        organization_id=contact.organization_id,
        primary_account_ids={
            not_none(primary_account_id),
        },
    )
    assert len(all_contacts) == 1
    retrieved_accounts = all_contacts.get(not_none(primary_account_id))
    assert retrieved_accounts
    assert len(retrieved_accounts) == 1
    assert Contact.model_validate(new_contact.model_dump()) not in retrieved_accounts
    assert Contact.model_validate(another_contact.model_dump()) in retrieved_accounts


async def test_patch_contact_address(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    pre_engagement_account: Account,
    create_contact_on_pre_engagement_account_request: CreateContactRequest,
) -> None:
    # Create a copy of the contact object first with the updated fields
    updated_contact_request = (
        create_contact_on_pre_engagement_account_request.contact.model_copy(
            update={"address_id": None, "address": None}
        )
    )

    # Then create a copy of the request with the updated contact
    create_request = create_contact_on_pre_engagement_account_request.model_copy(
        update={"contact": updated_contact_request}
    )

    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    assert not contact.address_id

    updated_by_user_id = uuid4()
    patch_request = UpdateContactRequest(
        contact_update=ContactUpdate(updated_by_user_id=updated_by_user_id),
        contact_id=contact.id,
        organization_id=contact.organization_id,
        address_request=AddressCreateRequest(
            street_one="123 Main St",
            city="San Francisco",
            state="CA",
            country="US",
            zip_code="94105",
            created_by_user_id=updated_by_user_id,
            organization_id=contact.organization_id,
        ),
    )
    updated_contact = await contact_repo.patch_contact(request=patch_request)
    assert updated_contact.display_name == contact.display_name
    assert updated_contact.id == contact.id
    assert updated_contact.address_id
    assert (
        updated_contact.updated_by_user_id
        == patch_request.contact_update.updated_by_user_id
    )
    patched_address = await contact_repo.find_by_tenanted_primary_key(
        Address,
        organization_id=contact.organization_id,
        id=not_none(updated_contact.address_id),
    )
    assert patched_address
    assert patch_request.address_request
    assert patched_address.street_one == patch_request.address_request.street_one
    assert patched_address.city == patch_request.address_request.city
    assert patched_address.state == patch_request.address_request.state
    assert patched_address.country == patch_request.address_request.country
    assert patched_address.zip_code == patch_request.address_request.zip_code
    assert (
        patched_address.created_by_user_id
        == patch_request.address_request.created_by_user_id
    )
    assert (
        patched_address.organization_id == patch_request.address_request.organization_id
    )
    assert patched_address.updated_by_user_id == updated_contact.updated_by_user_id
    assert patched_address.created_at == patched_address.updated_at

    updated_by_user_id_2 = uuid4()
    update_address_patch_request = UpdateContactRequest(
        contact_update=ContactUpdate(updated_by_user_id=updated_by_user_id_2),
        contact_id=contact.id,
        organization_id=contact.organization_id,
        address_request=AddressCreateRequest(
            street_one="1000123 Main St",
            city="San Francisco",
            state="CA",
            country="US",
            zip_code="94105",
            created_by_user_id=updated_by_user_id_2,
            organization_id=contact.organization_id,
        ),
    )
    address_updated_contact = await contact_repo.patch_contact(
        request=update_address_patch_request
    )
    assert address_updated_contact.id == contact.id
    assert address_updated_contact.address_id == updated_contact.address_id
    assert address_updated_contact.updated_by_user_id == updated_by_user_id_2
    updated_address = await contact_repo.find_by_tenanted_primary_key(
        Address,
        organization_id=contact.organization_id,
        id=not_none(address_updated_contact.address_id),
    )
    assert updated_address
    assert update_address_patch_request.address_request
    assert (
        updated_address.street_one
        == update_address_patch_request.address_request.street_one
    )
    assert updated_address.city == update_address_patch_request.address_request.city
    assert updated_address.state == update_address_patch_request.address_request.state
    assert (
        updated_address.country == update_address_patch_request.address_request.country
    )
    assert (
        updated_address.zip_code
        == update_address_patch_request.address_request.zip_code
    )
    assert (
        updated_address.updated_by_user_id
        == update_address_patch_request.address_request.created_by_user_id
    )
    assert updated_address.created_by_user_id == patched_address.created_by_user_id
    assert updated_address.updated_by_user_id == updated_by_user_id_2
    assert updated_address.id == patched_address.id
    assert updated_address.street_one != patched_address.street_one

    updated_by_user_id_3 = uuid4()
    remove_address_patch_request = UpdateContactRequest(
        contact_update=ContactUpdate(
            updated_by_user_id=updated_by_user_id_3, address_id=None
        ),
        contact_id=contact.id,
        organization_id=contact.organization_id,
        address_request=None,
    )
    removed_address_contact = await contact_repo.patch_contact(
        request=remove_address_patch_request
    )
    assert removed_address_contact.display_name == address_updated_contact.display_name
    assert removed_address_contact.id == address_updated_contact.id
    assert not removed_address_contact.address_id
    assert removed_address_contact.updated_by_user_id == updated_by_user_id_3


async def test_patch_contact_email_with_original_primary_email_not_null(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    create_contact_on_pre_engagement_account_request: CreateContactRequest,
    pre_engagement_account: Account,
) -> None:
    create_request = create_contact_on_pre_engagement_account_request.model_copy()
    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    original_primary_contact_email = cast(
        ContactEmail,
        await contact_repo.get_primary_contact_channel_info_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )

    assert original_primary_contact_email
    assert (
        original_primary_contact_email.email == create_request.contact_emails[0].email
    )
    assert original_primary_contact_email.is_contact_primary

    updated_by_user_id = uuid4()
    new_primary_email = f"{uuid4()}@test.com"
    patch_request = UpdateContactRequest(
        contact_update=ContactUpdate(
            updated_by_user_id=updated_by_user_id,
        ),
        contact_id=contact.id,
        primary_email=new_primary_email,
        organization_id=contact.organization_id,
    )
    await contact_repo.patch_contact(request=patch_request)

    patched_contact = await contact_repo.find_by_tenanted_primary_key(
        Contact,
        organization_id=contact.organization_id,
        id=contact.id,
    )
    assert patched_contact
    contact_email = cast(
        EmailStrLower,
        await contact_repo.get_primary_channel_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=patched_contact.organization_id,
            contact_id=patched_contact.id,
        ),
    )
    assert contact_email == new_primary_email

    updated_primary_contact_email = cast(
        ContactEmail,
        await contact_repo.get_primary_contact_channel_info_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )

    assert updated_primary_contact_email
    assert updated_primary_contact_email.email == contact_email
    assert updated_primary_contact_email.is_contact_primary

    original_primary_contact_email = cast(
        ContactEmail,
        await contact_repo.get_contact_channel_info_by_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_channel_info_id=original_primary_contact_email.id,
        ),
    )
    assert original_primary_contact_email
    assert original_primary_contact_email.id == updated_primary_contact_email.id


async def test_patch_contact_email_with_original_primary_email_null(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    pre_engagement_account: Account,
    create_contact_on_pre_engagement_account_request: CreateContactRequest,
) -> None:
    create_request = create_contact_on_pre_engagement_account_request.model_copy(
        update={"contact_emails": []}
    )
    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    original_primary_contact_email = cast(
        ContactEmail,
        await contact_repo.get_primary_contact_channel_info_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )
    original_primary_contact_email_account_association = cast(
        list[ContactEmailAccountAssociation],
        await contact_repo.list_contact_channel_info_account_associations_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )

    # expect no records in contact_email tables since primary_email is null
    contact_emails = await contact_repo.list_contact_channel_infos_by_contact_id(
        contact_channel_type=ContactChannelType.EMAIL,
        organization_id=contact.organization_id,
        contact_id=contact.id,
    )
    assert not contact_emails
    assert create_request.contact_emails == []
    assert original_primary_contact_email is None
    assert not original_primary_contact_email_account_association

    updated_by_user_id = uuid4()
    new_primary_email = f"{uuid4()}@test.com"
    patch_request = UpdateContactRequest(
        contact_update=ContactUpdate(
            updated_by_user_id=updated_by_user_id,
        ),
        contact_id=contact.id,
        primary_email=new_primary_email,
        organization_id=contact.organization_id,
    )
    await contact_repo.patch_contact(request=patch_request)

    patched_contact = await contact_repo.find_by_tenanted_primary_key(
        Contact,
        organization_id=contact.organization_id,
        id=contact.id,
    )

    # expect primary_email is updated and records in contact_email tables are created properly
    assert patched_contact
    contact_email = cast(
        EmailStrLower,
        await contact_repo.get_primary_channel_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=patched_contact.organization_id,
            contact_id=patched_contact.id,
        ),
    )
    assert contact_email == new_primary_email

    updated_primary_contact_email = cast(
        ContactEmail,
        await contact_repo.get_primary_contact_channel_info_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )

    assert updated_primary_contact_email
    assert updated_primary_contact_email.email == contact_email
    assert updated_primary_contact_email.is_contact_primary


async def test_patch_contact_with_phone_number(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    pre_engagement_account: Account,
    create_contact_on_pre_engagement_account_request: CreateContactRequest,
) -> None:
    create_request = create_contact_on_pre_engagement_account_request.model_copy(
        update={"contact_emails": [], "contact_phone_numbers": []}
    )
    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    original_primary_contact_email_account_association = cast(
        list[ContactEmailAccountAssociation],
        await contact_repo.list_contact_channel_info_account_associations_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )

    # expect no records in contact_email tables since primary_email is null
    contact_phone_numbers = await contact_repo.list_contact_channel_infos_by_contact_id(
        contact_channel_type=ContactChannelType.PHONE_NUMBER,
        organization_id=contact.organization_id,
        contact_id=contact.id,
    )
    assert not contact_phone_numbers
    assert not original_primary_contact_email_account_association

    updated_by_user_id = uuid4()
    new_primary_phone_number = f"******-253-0000 ext. {random.randint(999, 9999)}"  # noqa S311
    patch_request = UpdateContactRequest(
        contact_update=ContactUpdate(
            updated_by_user_id=updated_by_user_id,
            primary_phone_number=new_primary_phone_number,
        ),
        contact_id=contact.id,
        organization_id=contact.organization_id,
    )
    await contact_repo.patch_contact(request=patch_request)

    contact_phone_number = cast(
        PhoneNumberWithExtension,
        await contact_repo.get_primary_channel_by_contact_id(
            contact_channel_type=ContactChannelType.PHONE_NUMBER,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )
    country_code, number_e164, extension = parse_phone_number(
        phone_number_raw=new_primary_phone_number
    )
    assert (
        contact_phone_number == f"{number_e164},{extension}"
        if extension
        else number_e164
    )


async def test_patch_contact_email_to_null_with_original_primary_email_not_null(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    pre_engagement_account: Account,
    create_contact_on_pre_engagement_account_request: CreateContactRequest,
) -> None:
    create_request = create_contact_on_pre_engagement_account_request.model_copy()
    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    original_primary_contact_email = cast(
        ContactEmail,
        await contact_repo.get_primary_contact_channel_info_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )

    assert original_primary_contact_email
    assert (
        original_primary_contact_email.email == create_request.contact_emails[0].email
    )
    assert original_primary_contact_email.is_contact_primary

    updated_by_user_id = uuid4()
    patch_request = UpdateContactRequest(
        contact_update=ContactUpdate(
            updated_by_user_id=updated_by_user_id,
        ),
        primary_email=None,
        remove_current_primary_email=True,
        contact_id=contact.id,
        organization_id=contact.organization_id,
    )
    await contact_repo.patch_contact(request=patch_request)

    patched_contact = await contact_repo.find_by_tenanted_primary_key(
        Contact,
        organization_id=contact.organization_id,
        id=contact.id,
    )
    assert patched_contact

    updated_primary_contact_email = cast(
        ContactEmail,
        await contact_repo.get_primary_contact_channel_info_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )
    assert updated_primary_contact_email is None

    original_primary_contact_email = await contact_repo.find_by_tenanted_primary_key(
        ContactEmail,
        exclude_deleted_or_archived=False,
        organization_id=contact.organization_id,
        id=original_primary_contact_email.id,
    )
    assert original_primary_contact_email
    assert original_primary_contact_email.deleted_at


async def test_patch_primary_account(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    create_contact_request_without_account_assoc: CreateContactRequest,
    deal_account: Account,
    lead_account: Account,
) -> None:
    create_request = create_contact_request_without_account_assoc.model_copy()
    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=deal_account.organization_id,
    )
    primary_account_id = await contact_repo.get_primary_account_id_by_contact_id(
        organization_id=contact.organization_id,
        contact_id=contact.id,
    )
    assert not primary_account_id
    assert not contact.primary_account_id

    # patch request to attach account
    updated_user_1 = uuid4()
    account_id = deal_account.id
    association_result = await contact_repo.upsert_contact_account_association(
        association=ContactAccountAssociation(
            contact_id=contact.id,
            account_id=account_id,
            created_by_user_id=updated_user_1,
            organization_id=contact.organization_id,
            is_primary=True,
            department=contact.department,
            title=contact.title,
            updated_by_user_id=updated_user_1,
        )
    )
    with_account_contact = association_result.updated_contact
    assert not with_account_contact
    acct_asso = (
        await contact_repo.map_active_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_ids={account_id},
        )
    ).get(account_id)
    assert acct_asso
    assert not acct_asso.archived_at
    assert not acct_asso.archived_by_user_id
    assert acct_asso.is_primary

    # patch request to unset primary account, but the primary account not change,
    # since there is only one account associated with the contact
    updated_user_2 = uuid4()
    patch_association_result = await contact_repo.patch_contact_account_association(
        organization_id=contact.organization_id,
        contact_id=contact.id,
        account_id=account_id,
        update=ContactAccountAssociationUpdate(
            is_primary=False,
            updated_by_user_id=updated_user_2,
        ),
    )
    unset_primary_escaped_acct_account_contact = (
        patch_association_result.updated_contact
    )
    assert not patch_association_result.promoted_sibling
    assert not patch_association_result.demoted_sibling
    assert not unset_primary_escaped_acct_account_contact
    demote_escaped_association = patch_association_result.updated
    assert demote_escaped_association
    assert not demote_escaped_association.archived_at, (
        "it's just removed as primary asso, but shouldn't be archived."
    )
    assert demote_escaped_association.is_primary
    assert demote_escaped_association.updated_by_user_id == updated_user_2

    # patch request to attach the same account again with some updates on contact
    updated_user_3 = uuid4()
    patch_contact_request = UpdateContactRequest(
        contact_update=ContactUpdate(
            updated_by_user_id=updated_user_3,
            display_name="Recovered Name",
            title="Recovered Title",
            department="Recovered Department",
        ),
        contact_id=contact.id,
        organization_id=contact.organization_id,
    )
    patched_contact = await contact_repo.patch_contact(request=patch_contact_request)
    patch_primary_account_id = await contact_repo.get_primary_account_id_by_contact_id(
        organization_id=patched_contact.organization_id,
        contact_id=patched_contact.id,
    )
    assert patched_contact.id == contact.id
    assert patch_primary_account_id

    # promote the association back to primary, there won't be a promoted sibling
    # since the primary account is the same
    promoted_association_result = await contact_repo.patch_contact_account_association(
        organization_id=contact.organization_id,
        contact_id=contact.id,
        account_id=account_id,
        update=ContactAccountAssociationUpdate(
            is_primary=True,
            updated_by_user_id=updated_user_3,
        ),
    )
    recovered_contact_account_association = promoted_association_result.updated
    assert not promoted_association_result.promoted_sibling
    assert not promoted_association_result.demoted_sibling
    promoted_contact = promoted_association_result.updated_contact
    assert not promoted_contact
    assert recovered_contact_account_association
    assert recovered_contact_account_association.id == acct_asso.id
    assert not recovered_contact_account_association.archived_at
    assert not recovered_contact_account_association.archived_by_user_id
    assert recovered_contact_account_association.updated_by_user_id == updated_user_3
    assert recovered_contact_account_association.is_primary

    # patch request to attach a different account
    updated_user_4 = uuid4()
    new_account_id = lead_account.id
    new_association_result = await contact_repo.upsert_contact_account_association(
        association=ContactAccountAssociation(
            contact_id=contact.id,
            account_id=new_account_id,
            created_by_user_id=updated_user_4,
            organization_id=contact.organization_id,
            is_primary=True,
            department=contact.department,
            title=contact.title,
            updated_by_user_id=updated_user_4,
        )
    )
    with_new_account_contact = new_association_result.updated_contact
    assert new_association_result.demoted_sibling
    assert not new_association_result.promoted_sibling
    assert not with_new_account_contact
    new_contact_account_association = new_association_result.upserted
    assert new_contact_account_association
    assert new_contact_account_association.account_id == new_account_id
    demoted_by_new_account_contact_account_association = (
        new_association_result.demoted_sibling
    )
    assert demoted_by_new_account_contact_account_association
    assert not demoted_by_new_account_contact_account_association.archived_at
    assert (
        demoted_by_new_account_contact_account_association.updated_by_user_id
        == updated_user_4
    )
    assert not demoted_by_new_account_contact_account_association.is_primary


async def test_contact_primary_email_unique_index(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    create_contact_on_pre_engagement_account_request: CreateContactRequest,
    pre_engagement_account: Account,
) -> None:
    create_request = create_contact_on_pre_engagement_account_request.model_copy()
    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    assert contact is not None
    contact_email = cast(
        EmailStrLower,
        await contact_repo.get_primary_channel_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact.id,
        ),
    )

    # Use the same email to create should raise error from creating contact email step
    with pytest.raises(ConflictResourceError) as e:
        await helper_test_create_or_unarchive_contact_with_stable_models(
            contact_repo=contact_repo,
            contact_service=contact_service,
            create_request=create_request,
            organization_id=pre_engagement_account.organization_id,
        )
    assert (
        not_none(e.value.additional_error_details).code
        == "CONTACT_EMAIL_ALREADY_EXISTS"
    )
    assert not_none(e.value.additional_error_details).error_code == ErrorCode.CONFLICT
    assert (
        not_none(e.value.additional_error_details).details
        == "contact email with organization_id, contact_id, and email already exists."
    )
    assert not_none(e.value.additional_error_details).reference_id == str(contact.id)
    assert (
        not_none(e.value.additional_error_details).conflicted_existing_object
        == StdObjectIdentifiers.contact.identifier
    )
    assert not_none(
        e.value.additional_error_details
    ).conflicted_existing_object_attrs == {
        ContactField.organization_id: contact.organization_id,
        ContactField.id: contact.id,
        ContactField.display_name: contact.display_name,
        ContactField.archived_at: None,
        "conflict_email": contact_email,
        "existing_email_archive_at": None,
    }
    # archive the contact and primary email
    assert await contact_repo.archive_contact(
        organization_id=contact.organization_id,
        contact_id=contact.id,
        archived_by_user_id=uuid4(),
    )

    # create another contact with same email should succeed
    contact_2 = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    assert contact_2 is not None

    contact_email_2 = cast(
        EmailStrLower,
        await contact_repo.get_primary_channel_by_contact_id(
            contact_channel_type=ContactChannelType.EMAIL,
            organization_id=contact.organization_id,
            contact_id=contact_2.id,
        ),
    )
    assert contact_2.id != contact.id
    assert contact_email_2 == contact_email


async def test_find_by_linkedin_urls(
    contact_repo: ContactRepository,
    contact_service: ContactService,
    create_contact_on_pre_engagement_account_request: CreateContactRequest,
    pre_engagement_account: Account,
) -> None:
    linkedin_url = f"https://www.linkedin.com/in/{uuid4()}"
    create_request = create_contact_on_pre_engagement_account_request.model_copy(
        update={
            "contact": create_contact_on_pre_engagement_account_request.contact.model_copy(
                update={"linkedin_url": linkedin_url}
            )
        }
    )
    contact = await helper_test_create_or_unarchive_contact_with_stable_models(
        contact_repo=contact_repo,
        contact_service=contact_service,
        create_request=create_request,
        organization_id=pre_engagement_account.organization_id,
    )
    assert contact is not None

    assert (
        await contact_repo.list_by_linkedin_urls(
            organization_id=contact.organization_id, linkedin_urls=[]
        )
        == []
    )

    assert (
        await contact_repo.list_by_linkedin_urls(
            organization_id=contact.organization_id, linkedin_urls=[str(uuid4())]
        )
        == []
    )

    db_contact_list = await contact_repo.list_by_linkedin_urls(
        organization_id=contact.organization_id, linkedin_urls=[linkedin_url]
    )

    assert len(db_contact_list) == 1
    assert db_contact_list[0].id == contact.id


class TestContactAccountAssociationLifecycle:
    async def test_create_contact_without_associations(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        pre_engagement_account: Account,
    ) -> None:
        create_request = create_contact_request_without_account_assoc.model_copy()
        contact = await helper_test_create_or_unarchive_contact_with_stable_models(
            contact_repo=contact_repo,
            contact_service=contact_service,
            create_request=create_request,
            organization_id=pre_engagement_account.organization_id,
        )

        assert not contact.primary_account_id
        associations = await contact_repo.map_latest_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
        )
        assert not associations

    async def test_add_first_association_becomes_primary(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        deal_account: Account,
    ) -> None:
        # Setup contact
        create_request = create_contact_request_without_account_assoc.model_copy()
        contact = await helper_test_create_or_unarchive_contact_with_stable_models(
            contact_repo=contact_repo,
            contact_service=contact_service,
            create_request=create_request,
            organization_id=deal_account.organization_id,
        )

        # Add first association
        first_upsert_result = await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=deal_account.id,
                is_primary=False,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )
        deal_asso = first_upsert_result.upserted
        updated_contact = first_upsert_result.updated_contact

        # Verify it became primary
        assert deal_asso
        assert not updated_contact
        assert deal_asso.is_primary
        assert deal_asso.account_id == deal_account.id
        assert deal_asso.contact_id == contact.id
        associations = await contact_repo.map_latest_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
        )
        assert [*associations.values()] == [deal_asso]

    async def test_add_first_association_two_times_succeeds_as_noop(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        deal_account: Account,
    ) -> None:
        # Setup contact
        create_request = create_contact_request_without_account_assoc.model_copy()
        contact = await helper_test_create_or_unarchive_contact_with_stable_models(
            contact_repo=contact_repo,
            contact_service=contact_service,
            create_request=create_request,
            organization_id=deal_account.organization_id,
        )

        # Add first association
        first_upsert_result = await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=deal_account.id,
                is_primary=False,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )
        deal_asso = first_upsert_result.upserted
        updated_contact = first_upsert_result.updated_contact

        # Verify first association
        assert deal_asso
        assert not updated_contact
        assert deal_asso.is_primary
        assert deal_asso.account_id == deal_account.id
        assert deal_asso.contact_id == contact.id

        # Add same association again
        second_upsert_result = await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=deal_account.id,
                is_primary=False,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )

        # Verify nothing changed
        assert second_upsert_result.upserted
        assert not second_upsert_result.updated_contact
        assert second_upsert_result.upserted.is_primary
        assert second_upsert_result.upserted.account_id == deal_account.id
        assert second_upsert_result.upserted.contact_id == contact.id

        # Verify associations
        associations = await contact_repo.map_latest_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
        )
        assert len(associations) == 1
        assert [*associations.values()] == [second_upsert_result.upserted]

    async def test_add_second_nonprimary_association(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        deal_account: Account,
        lead_account: Account,
    ) -> None:
        account1_id = deal_account.id
        account2_id = lead_account.id
        # Setup contact with first association
        create_request = create_contact_request_without_account_assoc.model_copy()
        contact = await helper_test_create_or_unarchive_contact_with_stable_models(
            contact_repo=contact_repo,
            contact_service=contact_service,
            create_request=create_request,
            organization_id=deal_account.organization_id,
        )
        first_upsert_result = await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=account1_id,
                is_primary=False,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )
        assert not first_upsert_result.promoted_sibling
        assert not first_upsert_result.demoted_sibling
        assert not first_upsert_result.updated_contact
        assert first_upsert_result.upserted
        assert first_upsert_result.upserted.is_primary

        # archive the first association but then add it back
        await contact_repo.archive_contact_account_association(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_id=account1_id,
            user_id=contact.created_by_user_id,
        )
        first_reasso_result = await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=account1_id,
                is_primary=False,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )

        # Add second association
        second_upsert_result = await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=account2_id,
                is_primary=False,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )
        assert not second_upsert_result.promoted_sibling
        assert not second_upsert_result.demoted_sibling
        assert not second_upsert_result.updated_contact
        assert second_upsert_result.upserted
        assert not second_upsert_result.upserted.is_primary

        # Verify associations
        association_journeys = await contact_repo.map_contact_account_association_journeys_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_ids={account1_id, account2_id},
        )
        assert len(association_journeys) == 2
        assert (journey1 := association_journeys[account1_id])
        assert (journey2 := association_journeys[account2_id])
        assert journey1.active_association
        assert journey2.active_association
        assert journey1.active_association.account_id == account1_id
        assert journey2.active_association.account_id == account2_id
        assert journey1.active_association.is_primary
        assert len(journey1.associations_ordered_by_created_at_desc) == 2
        assert (
            journey1.associations_ordered_by_created_at_desc[0].id
            == first_reasso_result.upserted.id
        )
        assert (
            journey1.associations_ordered_by_created_at_desc[1].id
            == first_upsert_result.upserted.id
        )
        assert not journey2.active_association.is_primary

        current_contact = await contact_repo.find_by_tenanted_primary_key(
            Contact,
            organization_id=contact.organization_id,
            id=contact.id,
        )
        assert current_contact
        primary_account_id = await contact_repo.get_primary_account_id_by_contact_id(
            organization_id=contact.organization_id,
            contact_id=contact.id,
        )
        assert primary_account_id == account1_id

    async def test_unset_primary_promotes_other_association(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        deal_account: Account,
        lead_account: Account,
    ) -> None:
        account1_id = deal_account.id
        account2_id = lead_account.id
        # Setup contact with two associations
        contact = await self._setup_contact_with_two_associations(
            contact_repo,
            contact_service,
            create_contact_request_without_account_assoc,
            account1_id,
            account2_id,
            organization_id=deal_account.organization_id,
            user_id=deal_account.created_by_user_id,
        )

        # Unset primary account
        updated_user = uuid4()
        unset_result = await contact_repo.patch_contact_account_association(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_id=account1_id,
            update=ContactAccountAssociationUpdate(
                updated_by_user_id=updated_user, is_primary=False
            ),
        )
        updated_contact = unset_result.updated_contact
        assert not updated_contact
        assert unset_result.promoted_sibling
        assert not unset_result.demoted_sibling
        assert unset_result.promoted_sibling.account_id == lead_account.id

        # Verify lead account was promoted to primary
        active_associations = await contact_repo.map_active_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_ids={account1_id, account2_id},
        )
        assert len(active_associations) == 2
        assert active_associations[account2_id].is_primary
        assert not active_associations[account1_id].is_primary

    async def test_archive_primary_promotes_other_association(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        deal_account: Account,
        lead_account: Account,
    ) -> None:
        account1_id = deal_account.id
        account2_id = lead_account.id
        # Setup contact with two associations
        contact = await self._setup_contact_with_two_associations(
            contact_repo,
            contact_service,
            create_contact_request_without_account_assoc,
            account1_id,
            account2_id,
            organization_id=deal_account.organization_id,
            user_id=deal_account.created_by_user_id,
        )

        # Archive primary association
        updated_user = uuid4()
        archive_result = await contact_repo.archive_contact_account_association(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_id=account1_id,
            user_id=updated_user,
        )
        archived_asso = archive_result.archived
        updated_contact = archive_result.updated_contact
        assert not updated_contact
        assert archive_result.promoted_sibling
        assert archive_result.promoted_sibling.account_id == account2_id

        # Verify lead account was promoted
        assert archived_asso
        assert archived_asso.archived_at
        assert archived_asso.archived_by_user_id == updated_user
        assert not archived_asso.is_primary

        active_associations = await contact_repo.map_active_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_ids={account1_id, account2_id},
        )
        assert len(active_associations) == 1
        assert active_associations[account2_id].is_primary

    async def test_archive_all_associations(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        deal_account: Account,
        lead_account: Account,
    ) -> None:
        account1_id = deal_account.id
        account2_id = lead_account.id
        # Setup contact with two associations
        contact = await self._setup_contact_with_two_associations(
            contact_repo,
            contact_service,
            create_contact_request_without_account_assoc,
            account1_id,
            account2_id,
            organization_id=deal_account.organization_id,
            user_id=deal_account.created_by_user_id,
        )

        account1_associations = (
            await contact_repo.list_active_contact_associations_for_account(
                organization_id=contact.organization_id,
                account_id=account1_id,
            )
        )
        assert any(
            association.account_id == account1_id
            for association in account1_associations
        )
        account2_associations = (
            await contact_repo.list_active_contact_associations_for_account(
                organization_id=contact.organization_id,
                account_id=account2_id,
            )
        )
        assert any(
            association.account_id == account2_id
            for association in account2_associations
        )

        # Archive both associations
        updated_user = uuid4()
        for account_id in [account1_id, account2_id]:
            await contact_repo.archive_contact_account_association(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=account_id,
                user_id=updated_user,
            )

        # Verify no active associations remain
        active_associations = await contact_repo.map_active_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_ids={account1_id, account2_id},
        )
        assert not active_associations

        # Verify no active associations remain for accounts
        assert not await contact_repo.list_active_contact_associations_for_account(
            organization_id=contact.organization_id,
            account_id=account1_id,
        )
        assert not await contact_repo.list_active_contact_associations_for_account(
            organization_id=contact.organization_id,
            account_id=account2_id,
        )

        # Verify contact primary account is unset
        contact = await contact_repo.find_by_tenanted_primary_key_or_fail(
            Contact,
            organization_id=contact.organization_id,
            id=contact.id,
        )
        assert not contact.primary_account_id
        primary_account_id = await contact_repo.get_primary_account_id_by_contact_id(
            organization_id=contact.organization_id,
            contact_id=contact.id,
        )
        assert not primary_account_id

    async def test_add_association_to_archived_account(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        archived_deal_account: Account,
    ) -> None:
        # Setup contact
        create_request = create_contact_request_without_account_assoc.model_copy()
        contact = await helper_test_create_or_unarchive_contact_with_stable_models(
            contact_repo=contact_repo,
            contact_service=contact_service,
            create_request=create_request,
            organization_id=archived_deal_account.organization_id,
        )

        # Add first association
        first_upsert_result = await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=archived_deal_account.id,
                is_primary=False,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )
        archived_deal_asso = first_upsert_result.upserted
        updated_contact = first_upsert_result.updated_contact

        # Verify it became primary
        assert archived_deal_asso
        assert not updated_contact
        assert archived_deal_asso.is_primary
        assert archived_deal_asso.account_id == archived_deal_account.id
        assert archived_deal_asso.contact_id == contact.id
        associations = await contact_repo.map_latest_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
        )
        assert [*associations.values()] == [archived_deal_asso]

    async def test_update_contact_account_association(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_on_pre_engagement_account_request: CreateContactRequest,
        deal_account: Account,
        lead_account: Account,
    ) -> None:
        account1_id = deal_account.id
        account2_id = lead_account.id
        # Setup contact with two associations
        contact = await self._setup_contact_with_two_associations(
            contact_repo,
            contact_service,
            create_contact_on_pre_engagement_account_request,
            account1_id,
            account2_id,
            organization_id=deal_account.organization_id,
            user_id=deal_account.created_by_user_id,
        )

        # Update the non-primary association with new title and department
        updated_user = uuid4()
        new_title = "New Title"
        new_department = "New Department"
        patch_result = await contact_repo.patch_contact_account_association(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_id=account2_id,
            update=ContactAccountAssociationUpdate(
                title=new_title,
                department=new_department,
                updated_by_user_id=updated_user,
            ),
        )
        updated_asso = patch_result.updated
        updated_contact = patch_result.updated_contact
        assert not updated_contact, (
            "contact should not be updated when not involving primary association change"
        )
        assert updated_asso

        # Verify the update
        assert updated_asso.title == new_title
        assert updated_asso.department == new_department
        assert updated_asso.updated_by_user_id == updated_user
        assert not updated_asso.is_primary
        assert updated_asso.account_id == account2_id

        # Verify other association remains unchanged
        active_associations = await contact_repo.map_active_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_ids={account1_id, account2_id},
        )
        assert len(active_associations) == 2
        assert active_associations[account1_id].is_primary
        assert active_associations[account1_id].title != new_title
        assert active_associations[account1_id].department != new_department

    async def test_update_association_primary_flag(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        deal_account: Account,
        lead_account: Account,
    ) -> None:
        account1_id = deal_account.id
        account2_id = lead_account.id
        # Setup contact with two associations
        contact = await self._setup_contact_with_two_associations(
            contact_repo,
            contact_service,
            create_contact_request_without_account_assoc,
            account1_id,
            account2_id,
            organization_id=deal_account.organization_id,
            user_id=deal_account.created_by_user_id,
        )
        # Verify deal_account is primary before update
        active_associations = await contact_repo.map_active_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_ids={account1_id, account2_id},
        )
        assert len(active_associations) == 2
        primary_asso = next(a for a in active_associations.values() if a.is_primary)
        assert primary_asso.account_id == account1_id
        assert primary_asso.is_primary

        # Update the non-primary association to be primary
        updated_user = uuid4()
        patch_result = await contact_repo.patch_contact_account_association(
            organization_id=contact.organization_id,
            contact_id=contact.id,
            account_id=account2_id,
            update=ContactAccountAssociationUpdate(
                is_primary=True,
                updated_by_user_id=updated_user,
            ),
        )
        updated_asso = patch_result.updated
        updated_contact = patch_result.updated_contact
        assert not updated_contact
        assert updated_asso

        # Verify the update made this association primary
        assert updated_asso.is_primary
        assert updated_asso.account_id == lead_account.id
        assert updated_asso.updated_by_user_id == updated_user

        # Verify the previously primary association is now non-primary
        associations = await contact_repo.map_latest_contact_account_association_by_account_id_for_contact(
            organization_id=contact.organization_id,
            contact_id=contact.id,
        )
        assert len(associations) == 2
        old_primary_asso = next(
            a for a in associations.values() if a.account_id == deal_account.id
        )
        assert not old_primary_asso.is_primary
        assert old_primary_asso.updated_by_user_id == updated_user

    # Helper method
    async def _setup_contact_with_two_associations(
        self,
        contact_repo: ContactRepository,
        contact_service: ContactService,
        create_contact_request_without_account_assoc: CreateContactRequest,
        account1_id: UUID,
        account2_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> Contact:
        create_request = create_contact_request_without_account_assoc
        contact = await helper_test_create_or_unarchive_contact_with_stable_models(
            contact_repo=contact_repo,
            contact_service=contact_service,
            create_request=create_request,
            organization_id=organization_id,
        )

        # Add deal account as primary
        await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=account1_id,
                is_primary=True,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )

        # Add lead account as non-primary
        await contact_repo.upsert_contact_account_association(
            association=ContactAccountAssociation(
                organization_id=contact.organization_id,
                contact_id=contact.id,
                account_id=account2_id,
                is_primary=False,
                created_by_user_id=contact.created_by_user_id,
                updated_by_user_id=contact.created_by_user_id,
            )
        )
        return await contact_repo.find_by_tenanted_primary_key_or_fail(
            Contact,
            organization_id=contact.organization_id,
            id=contact.id,
        )


class TestContactEmail:
    async def test_delete_already_deleted_email(
        self,
        contact_repo: ContactRepository,
        faker: Faker,
    ) -> None:
        # Setup
        organization_id = uuid4()
        contact_id = uuid4()
        user_id = uuid4()
        email = faker.email().lower()
        account_id = uuid4()
        deleted_at = zoned_utc_now()
        deleted_by = uuid4()

        # Create a pre-deleted contact email
        contact_email = ContactEmail(
            id=uuid4(),
            organization_id=organization_id,
            contact_id=contact_id,
            email=email,
            is_contact_primary=False,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            deleted_at=deleted_at,
            deleted_by_user_id=deleted_by,
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_id,
        )
        await contact_repo.insert(contact_email)

        contact_email_account_association = ContactEmailAccountAssociation(
            id=uuid4(),
            organization_id=organization_id,
            contact_id=contact_id,
            contact_email_id=contact_email.id,
            account_id=account_id,
            is_contact_account_primary=True,
            created_at=zoned_utc_now(),
            created_by_user_id=user_id,
            deleted_at=deleted_at,
            deleted_by_user_id=deleted_by,
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_id,
        )
        await contact_repo.insert(contact_email_account_association)

        with pytest.raises(ResourceNotFoundError):
            await contact_repo.delete_contact_email(
                organization_id=organization_id,
                user_id=user_id,
                contact_id=contact_id,
                email=email,
            )


class TestFindContactAccountAttributes:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(scope="function")
    async def account_1(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name=faker.company(),
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def account_2(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name=faker.company(),
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def contact_req(
        self,
        faker: Faker,
        select_list_service: InternalSelectListService,
        account_1: AccountV2,
    ) -> CreateDbContactRequest:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        return CreateDbContactRequest(
            display_name="contact_1",
            stage_id=default_stage_list.default_or_initial_active_value.id,
            created_by_user_id=self.user_id,
            owner_user_id=self.user_id,
            first_name=faker.first_name(),
            last_name=faker.last_name(),
        )

    @pytest.fixture(scope="function")
    async def contact_email_req(
        self,
        faker: Faker,
        account_1: AccountV2,
    ) -> CreateDbContactEmailRequest:
        return CreateDbContactEmailRequest(
            email=faker.email(),
            is_contact_primary=True,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                )
            ],
        )

    @pytest.fixture(scope="function")
    async def contact_account_association_req(
        self,
        account_1: AccountV2,
    ) -> CreateContactAccountRoleRequest:
        return CreateContactAccountRoleRequest(
            account_id=account_1.id,
            is_primary_account=True,
        )

    @pytest.fixture(scope="function")
    async def contact_phone_number_req(
        self,
        account_1: AccountV2,
    ) -> CreateDbContactPhoneNumberRequest:
        return CreateDbContactPhoneNumberRequest(
            phone_number="************ ext. 103",
            is_contact_primary=True,
            phone_number_account_associations=[
                CreateDbContactPhoneNumberAccountAssociationRequest(
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                )
            ],
        )

    async def test_find_primary_account_company_name_for_contact_ids(
        self,
        contact_req: CreateDbContactRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
        account_repo: AccountRepository,
    ) -> None:
        # Setup
        account_1_id = uuid4()
        account_2_id = uuid4()

        # Create test accounts
        account_1 = Account(
            id=account_1_id,
            organization_id=self.organization_id,
            display_name="Test Account 1",
            status=AccountStatus.TARGET,
            owner_user_id=self.user_id,
            created_at=zoned_utc_now(),
            created_by_user_id=self.user_id,
        )
        account_2 = Account(
            id=account_2_id,
            organization_id=self.organization_id,
            display_name="Test Account 2",
            status=AccountStatus.TARGET,
            owner_user_id=self.user_id,
            created_at=zoned_utc_now(),
            created_by_user_id=self.user_id,
        )
        await account_repo.insert(account_1)
        await account_repo.insert(account_2)

        # Create test contacts
        contact_req_1 = strict_model_copy(
            contact_req,
            display_name="Test Contact 1",
            owner_user_id=self.user_id,
            created_at=zoned_utc_now(),
            created_by_user_id=self.user_id,
        )
        contact_account_association_req_1 = strict_model_copy(
            contact_account_association_req, account_id=account_1_id
        )
        req_1 = CreateContactRequest(
            contact=contact_req_1,
            contact_emails=[],
            contact_account_roles=[contact_account_association_req_1],
        )
        contact_req_2 = strict_model_copy(
            contact_req,
            display_name="Test Contact 2",
            owner_user_id=self.user_id,
            created_at=zoned_utc_now(),
            created_by_user_id=self.user_id,
        )
        contact_account_association_req_2 = strict_model_copy(
            contact_account_association_req, account_id=account_2_id
        )
        req_2 = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[],
            contact_account_roles=[contact_account_association_req_2],
        )
        contact_dto_1 = await contact_repo.create_contact(
            req=req_1, organization_id=self.organization_id, user_id=self.user_id
        )
        contact_dto_2 = await contact_repo.create_contact(
            req=req_2, organization_id=self.organization_id, user_id=self.user_id
        )
        assert contact_dto_1
        assert contact_dto_2
        contact_1_id = contact_dto_1.contact.id
        contact_2_id = contact_dto_2.contact.id

        # Test finding company names
        result = await contact_repo.find_primary_account_company_name_for_contact_ids(
            contact_ids={contact_1_id, contact_2_id},
            organization_id=self.organization_id,
        )

        # Verify results
        assert len(result) == 2
        assert result[contact_1_id] == "Test Account 1"
        assert result[contact_2_id] == "Test Account 2"

        # Test with empty contact ids
        empty_result = (
            await contact_repo.find_primary_account_company_name_for_contact_ids(
                contact_ids=set(),
                organization_id=self.organization_id,
            )
        )
        assert len(empty_result) == 0

        # Test with non-existent contact id
        non_existent_result = (
            await contact_repo.find_primary_account_company_name_for_contact_ids(
                contact_ids={uuid4()},
                organization_id=self.organization_id,
            )
        )
        assert len(non_existent_result) == 0

        # Test contact without primary account
        contact_req_3 = strict_model_copy(
            contact_req,
            display_name="Test Contact 3",
            owner_user_id=self.user_id,
            created_at=zoned_utc_now(),
            created_by_user_id=self.user_id,
        )
        req_3 = CreateContactRequest(
            contact=contact_req_3,
            contact_emails=[],
            contact_account_roles=[],
        )
        contact_dto_3 = await contact_repo.create_contact(
            req=req_3, organization_id=self.organization_id, user_id=self.user_id
        )
        assert contact_dto_3
        contact_id_without_primary_account = contact_dto_3.contact.id

        result = await contact_repo.find_primary_account_company_name_for_contact_ids(
            contact_ids={contact_id_without_primary_account},
            organization_id=self.organization_id,
        )
        assert len(result) == 0

    async def test_create_contact_simple(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
        account_1: AccountV2,
    ) -> None:
        contact_email_req = strict_model_copy(
            contact_email_req, labels=[ContactChannelLabel.PERSONAL]
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_account_associations_by_contact_id = (
            await contact_repo.map_active_contact_account_association_by_contact_ids(
                organization_id=self.organization_id,
                contact_ids={
                    contact_dto.contact.id,
                },
            )
        )
        contact_account_association = contact_account_associations_by_contact_id.get(
            contact_dto.contact.id
        )
        assert contact_account_association is not None
        assert len(contact_account_association) == 1
        assert (
            contact_account_association[0].account_id
            == contact_dto.contact.primary_account_id
        )

        primary_contact_email = cast(
            ContactEmail,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact, organization_id=self.organization_id, id=contact_dto.contact.id
        )
        assert primary_contact_email and contact
        assert primary_contact_email.email == contact_email_req.email
        assert (
            primary_contact_email.labels
            and primary_contact_email.labels[0] == ContactChannelLabel.WORK
        )
        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert len(contact_emails) == 1

        contact_email_account_associations = cast(
            list[ContactEmailAccountAssociation],
            await contact_repo.list_contact_channel_info_account_associations_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert len(contact_email_account_associations) == 1
        assert contact_email_account_associations[0].account_id == account_1.id
        assert contact_email_account_associations[0].is_contact_account_primary
        assert (
            contact_email_account_associations[0].contact_email_id
            == contact_emails[0].id
        )
        assert (
            contact_email_account_associations[0].contact_id == contact_dto.contact.id
        )

    async def test_create_contact_no_email_no_association(
        self,
        contact_req: CreateDbContactRequest,
        contact_repo: ContactRepository,
    ) -> None:
        req = CreateContactRequest(
            contact=contact_req,
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_account_associations_by_contact_id = (
            await contact_repo.map_active_contact_account_association_by_contact_ids(
                organization_id=self.organization_id,
                contact_ids={
                    contact_dto.contact.id,
                },
            )
        )
        contact_account_association = contact_account_associations_by_contact_id.get(
            contact_dto.contact.id
        )
        assert not contact_account_association

        primary_contact_email = cast(
            ContactEmail,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact,
            organization_id=self.organization_id,
            id=contact_dto.contact.id,
        )
        assert primary_contact_email is None
        assert contact
        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert len(contact_emails) == 0

        contact_email_account_associations = cast(
            list[ContactEmailAccountAssociation],
            await contact_repo.list_contact_channel_info_account_associations_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert len(contact_email_account_associations) == 0

    async def test_create_contacts_conflict_email(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        req_1 = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req_1, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        req_2 = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        with pytest.raises(ConflictResourceError) as e:
            await contact_repo.create_contact(
                req=req_2, organization_id=self.organization_id, user_id=self.user_id
            )
        assert e.value.additional_error_details == ConflictErrorDetails(
            code="CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ACTIVE_EMAIL",
            details="contact email conflict, try to update the existing contact",
            conflicted_existing_object=StdObjectIdentifiers.contact.identifier,
            reference_id=str(contact_dto.contact.id),
            conflicted_existing_object_attrs={
                ContactField.organization_id: contact_dto.contact.organization_id,
                ContactField.id: contact_dto.contact.id,
                ContactField.display_name: contact_dto.contact.display_name,
                ContactField.archived_at: None,
                "existing_email_archive_at": None,
                "conflict_email": str(contact_email_req.email),
            },
        )

    async def test_create_contact_not_exist_account(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_account_association_req = strict_model_copy(
            contact_account_association_req, account_id=uuid4()
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_account_roles=[contact_account_association_req],
        )
        with pytest.raises(ResourceNotFoundError):
            await contact_repo.create_contact(
                req=req, organization_id=self.organization_id, user_id=self.user_id
            )

    async def test_create_contact_no_primary_one_email(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req = strict_model_copy(
            contact_email_req, is_contact_primary=False
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_account_associations_by_contact_id = (
            await contact_repo.map_active_contact_account_association_by_contact_ids(
                organization_id=self.organization_id,
                contact_ids={
                    contact_dto.contact.id,
                },
            )
        )
        contact_account_association = contact_account_associations_by_contact_id.get(
            contact_dto.contact.id
        )
        assert contact_account_association is not None
        assert (
            contact_account_association[0].account_id
            == contact_dto.contact.primary_account_id
        )

        primary_contact_email = cast(
            ContactEmail,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact, organization_id=self.organization_id, id=contact_dto.contact.id
        )
        assert primary_contact_email and contact
        assert primary_contact_email.email == contact_email_req.email
        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert len(contact_emails) == 1

    async def test_create_contact_no_primary_multi_email(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req = strict_model_copy(
            contact_email_req, is_contact_primary=False
        )
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_email_req.email_account_associations[
                        0
                    ].account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        with pytest.raises(InvalidArgumentError):
            await contact_repo.create_contact(
                req=req, organization_id=self.organization_id, user_id=self.user_id
            )

    async def test_create_contact_conflict_archived_email(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req = strict_model_copy(
            contact_email_req, is_contact_primary=False
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        await contact_repo.archive_contact(
            organization_id=self.organization_id,
            contact_id=contact_dto.contact.id,
            archived_by_user_id=self.user_id,
        )

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        req_2 = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )

        with pytest.raises(ConflictResourceError):
            await contact_repo.create_contact(
                req=req_2, organization_id=self.organization_id, user_id=self.user_id
            )

    async def test_upsert_contact_email_update_primary(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req = strict_model_copy(
            contact_email_req, is_contact_primary=True
        )
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_email_req.email_account_associations[
                        0
                    ].account_id,
                    is_contact_account_primary=False,
                )
            ],
        )

        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )

        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        primary_contact_email = cast(
            ContactEmail,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact, organization_id=self.organization_id, id=contact_dto.contact.id
        )
        assert primary_contact_email and contact
        assert primary_contact_email.email == contact_email_req.email

        contact_email_req_3 = strict_model_copy(
            contact_email_req_2,
            is_contact_primary=True,
            email_account_associations=[],
        )

        contact_email_to_add, contact_email_account_association_to_add = (
            contact_email_req_3.to_db_inserts(
                organization_id=self.organization_id,
                user_id=self.user_id,
                contact_id=contact_dto.contact.id,
            )
        )
        await contact_repo.upsert_contact_email(
            contact_email=contact_email_to_add,
            contact_email_account_associations=contact_email_account_association_to_add,
        )

        primary_contact_email = cast(
            ContactEmail,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact, organization_id=self.organization_id, id=contact_dto.contact.id
        )

        contact_emails = await contact_repo.list_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email},
        )
        assert contact_emails
        contact_email = contact_emails[0]
        assert primary_contact_email and contact and contact_email
        assert primary_contact_email.email == contact_email_req_2.email
        assert contact_email.is_contact_primary is False

    async def test_upsert_contact_phone_number_update_primary(
        self,
        contact_req: CreateDbContactRequest,
        contact_phone_number_req: CreateDbContactPhoneNumberRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_phone_number_req_2 = strict_model_copy(
            contact_phone_number_req,
            phone_number="******-253-0000 ext. 1234",
            is_contact_primary=False,
        )

        req = CreateContactRequest(
            contact=contact_req,
            contact_phone_numbers=[
                contact_phone_number_req,
                contact_phone_number_req_2,
            ],
            contact_account_roles=[contact_account_association_req],
        )

        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        primary_contact_phone_number = cast(
            ContactPhoneNumber,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact, organization_id=self.organization_id, id=contact_dto.contact.id
        )
        assert primary_contact_phone_number and contact
        assert (
            primary_contact_phone_number.phone_number
            == contact_phone_number_req.phone_number
        )
        assert (
            validate_e164(primary_contact_phone_number.phone_number)
            == contact.primary_phone_number
        )

        contact_phone_number_req_3 = strict_model_copy(
            contact_phone_number_req_2,
            is_contact_primary=True,
            phone_number_account_associations=[],
        )

        contact_phone_number_to_add, contact_phone_number_account_associations = (
            contact_phone_number_req_3.to_db_inserts(
                organization_id=self.organization_id,
                user_id=self.user_id,
                contact_id=contact_dto.contact.id,
            )
        )
        await contact_repo.upsert_contact_phone_number(
            contact_phone_number=contact_phone_number_to_add,
            contact_phone_number_account_associations=contact_phone_number_account_associations,
        )

        primary_contact_phone_number = cast(
            ContactPhoneNumber,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact, organization_id=self.organization_id, id=contact_dto.contact.id
        )

        db_contact_phone_numbers = cast(
            list[ContactPhoneNumber],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.PHONE_NUMBER,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )

        assert db_contact_phone_numbers and len(db_contact_phone_numbers) == 2
        assert primary_contact_phone_number and contact
        assert (
            primary_contact_phone_number.phone_number
            == contact_phone_number_req_2.phone_number
        )
        assert (
            validate_e164(primary_contact_phone_number.phone_number)
            == contact.primary_phone_number
        )

    async def test_map_contact_email_by_contact_id(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        contact_email_req_2 = strict_model_copy(contact_email_req, email=faker.email())
        contact_email_req_3 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_email_req_2.email_account_associations[
                        0
                    ].account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req_2 = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req_2, contact_email_req_3],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto_2 = await contact_repo.create_contact(
            req=req_2, organization_id=self.organization_id, user_id=self.user_id
        )

        map_contact_email = cast(
            frozendict[UUID, list[ContactEmail]],
            await contact_repo.map_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_ids={contact_dto.contact.id, contact_dto_2.contact.id},
            ),
        )
        assert len(not_none(map_contact_email.get(contact_dto.contact.id))) == 1
        assert len(not_none(map_contact_email.get(contact_dto_2.contact.id))) == 2

    async def test_get_contact_email_by_id(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails = await contact_repo.list_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email},
        )
        assert contact_emails
        contact_email = contact_emails[0]
        assert contact_email
        assert contact_email.email == contact_email_req.email

    async def test_get_preferred_contact_email_of_account(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        account_id = contact_account_association_req.account_id
        faked_email1 = faker.email()
        contact_email_req_1 = strict_model_copy(
            contact_email_req,
            email=faked_email1,
            is_contact_primary=True,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_id,
                    is_contact_account_primary=True,
                )
            ],
        )
        faked_email2 = faker.email()
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faked_email2,
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_id,
                    is_contact_account_primary=False,
                )
            ],
        )

        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req_1, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )
        contact_id = contact_dto.contact.id
        contact_email = await contact_repo.get_preferred_contact_email_of_account(
            organization_id=self.organization_id,
            contact_id=contact_id,
            account_id=account_id,
        )
        assert contact_email is not None
        assert contact_email.email == faked_email1
        assert contact_email.email != faked_email2
        assert contact_email.contact_id == contact_id

        # Verify account association properties
        email_account_assoc = await contact_repo.get_contact_email_account_association(
            organization_id=self.organization_id,
            contact_id=contact_id,
            contact_email_id=contact_email.id,
            account_id=account_id,
        )
        assert email_account_assoc is not None
        assert email_account_assoc.is_contact_account_primary is True

    async def test_list_contact_emails_by_contact_id(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert len(contact_emails) == 2

    async def test_get_primary_email_by_contact_id(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        primary_email = cast(
            EmailStrLower,
            await contact_repo.get_primary_channel_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert primary_email == contact_email_req.email

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        contact_email_req_3 = strict_model_copy(contact_email_req, email=faker.email())
        req = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req_3],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto_2 = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_email_map = cast(
            dict[UUID, EmailStrLower],
            await contact_repo.get_primary_channels_by_contact_ids(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_ids={contact_dto.contact.id, contact_dto_2.contact.id},
            ),
        )
        assert contact_email_map.get(contact_dto.contact.id) == contact_email_req.email
        assert (
            contact_email_map.get(contact_dto_2.contact.id) == contact_email_req_3.email
        )

    async def test_list_contact_emails_by_emails(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails = await contact_repo.list_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )
        assert len(contact_emails) == 2

    async def test_map_contact_emails_by_emails(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_email_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )
        assert (
            not_none(contact_email_map.get(contact_email_req.email)).email
            == contact_email_req.email
        )
        assert (
            not_none(contact_email_map.get(contact_email_req_2.email)).email
            == contact_email_req_2.email
        )

    async def test_delete_contact_email_deleted_or_archived(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        result = await contact_repo.delete_contact_email(
            organization_id=self.organization_id,
            user_id=self.user_id,
            contact_id=contact_dto.contact.id,
            email=contact_email_req_2.email,
        )
        assert result.deleted.email == contact_email_req_2.email
        with pytest.raises(ResourceNotFoundError):
            await contact_repo.delete_contact_email(
                organization_id=self.organization_id,
                user_id=self.user_id,
                contact_id=contact_dto.contact.id,
                email=contact_email_req_2.email,
            )

        await contact_repo.archive_contact(
            organization_id=self.organization_id,
            contact_id=contact_dto.contact.id,
            archived_by_user_id=self.user_id,
        )
        with pytest.raises(InvalidArgumentError):
            await contact_repo.delete_contact_email(
                organization_id=self.organization_id,
                user_id=self.user_id,
                contact_id=contact_dto.contact.id,
                email=contact_email_req.email,
            )

    async def test_patch_contact_email_primary_to_non_primary(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_req = strict_model_copy(contact_req, first_name=None, last_name=None)
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )
        assert contact_dto.contact.display_name == "contact_1"

        contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        primary_email = contact_emails_map.get(contact_email_req.email)
        no_primary_email = contact_emails_map.get(contact_email_req_2.email)
        assert primary_email and no_primary_email

        with pytest.raises(ResourceNotFoundError):
            await contact_repo.patch_contact_email_by_id(
                organization_id=self.organization_id,
                user_id=self.user_id,
                req=UpdateContactEmailRequest(id=uuid4(), is_contact_primary=False),
            )

        # can't unset primary without alternative_email
        with pytest.raises(InvalidArgumentError):
            await contact_repo.patch_contact_email_by_id(
                organization_id=self.organization_id,
                user_id=self.user_id,
                req=UpdateContactEmailRequest(
                    id=primary_email.id, is_contact_primary=False
                ),
            )

        # can't set alternative email belongs to another contact
        with pytest.raises(InvalidArgumentError):
            contact_email_3 = await contact_repo.insert(
                ContactEmail(
                    organization_id=self.organization_id,
                    contact_id=uuid4(),
                    email=faker.email(),
                    is_contact_primary=True,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                ),
            )
            await contact_repo.patch_contact_email_by_id(
                organization_id=self.organization_id,
                user_id=self.user_id,
                req=UpdateContactEmailRequest(
                    id=primary_email.id,
                    is_contact_primary=False,
                    alternative_email_id=contact_email_3.id,
                ),
            )

        # can't set same email as alternative email
        with pytest.raises(InvalidArgumentError):
            await contact_repo.patch_contact_email_by_id(
                organization_id=self.organization_id,
                user_id=self.user_id,
                req=UpdateContactEmailRequest(
                    id=primary_email.id,
                    is_contact_primary=False,
                    alternative_email_id=primary_email.id,
                ),
            )

        # can't set non-existing alternative email
        with pytest.raises(InvalidArgumentError):
            await contact_repo.patch_contact_email_by_id(
                organization_id=self.organization_id,
                user_id=self.user_id,
                req=UpdateContactEmailRequest(
                    id=primary_email.id,
                    is_contact_primary=False,
                    alternative_email_id=uuid4(),
                ),
            )

        result = await contact_repo.patch_contact_email_by_id(
            organization_id=self.organization_id,
            user_id=self.user_id,
            req=UpdateContactEmailRequest(
                id=primary_email.id,
                is_contact_primary=False,
                alternative_email_id=no_primary_email.id,
            ),
        )
        assert result.updated_contact_email.email == contact_email_req.email
        assert not result.updated_contact_email.is_contact_primary
        assert (
            result.updated_alter_contact_email
            and result.updated_alter_contact_email.is_contact_primary
        )
        contacts = await contact_repo.find_contacts_by_ids(
            organization_id=self.organization_id, contact_ids=[primary_email.contact_id]
        )
        assert len(contacts) == 1
        assert contacts[0].display_name == no_primary_email.email
        assert contacts[0].primary_email == no_primary_email.email

        updated_contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        updated_primary_email = updated_contact_emails_map.get(contact_email_req.email)
        updated_no_primary_email = updated_contact_emails_map.get(
            contact_email_req_2.email
        )
        assert updated_primary_email and updated_no_primary_email
        assert not updated_primary_email.is_contact_primary
        assert updated_no_primary_email.is_contact_primary

    async def test_patch_contact_email_non_primary_to_primary(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        no_primary_email = contact_emails_map.get(contact_email_req_2.email)
        assert no_primary_email
        result = await contact_repo.patch_contact_email_by_id(
            organization_id=self.organization_id,
            user_id=self.user_id,
            req=UpdateContactEmailRequest(
                id=no_primary_email.id, is_contact_primary=True
            ),
        )
        assert result.updated_contact_email.email == no_primary_email.email
        assert result.updated_contact_email.is_contact_primary
        assert result.updated_alter_contact_email
        assert result.updated_alter_contact_email.email == contact_email_req.email
        assert not result.updated_alter_contact_email.is_contact_primary

        updated_contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        updated_primary_email = updated_contact_emails_map.get(contact_email_req.email)
        updated_no_primary_email = updated_contact_emails_map.get(
            contact_email_req_2.email
        )
        assert updated_primary_email and updated_no_primary_email
        assert not updated_primary_email.is_contact_primary
        assert updated_no_primary_email.is_contact_primary

    async def test_patch_contact_email_not_changed(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        primary_email = contact_emails_map.get(contact_email_req.email)
        no_primary_email = contact_emails_map.get(contact_email_req_2.email)
        assert primary_email and no_primary_email

        result = await contact_repo.patch_contact_email_by_id(
            organization_id=self.organization_id,
            user_id=self.user_id,
            req=UpdateContactEmailRequest(id=primary_email.id, is_contact_primary=True),
        )
        assert result.updated_contact_email.email == primary_email.email
        assert result.updated_contact_email.is_contact_primary
        assert not result.updated_alter_contact_email

        result = await contact_repo.patch_contact_email_by_id(
            organization_id=self.organization_id,
            user_id=self.user_id,
            req=UpdateContactEmailRequest(
                id=no_primary_email.id, is_contact_primary=False
            ),
        )
        assert result.updated_contact_email.email == no_primary_email.email
        assert not result.updated_contact_email.is_contact_primary
        assert not result.updated_alter_contact_email

    async def test_patch_contact_email_account_association_primary_to_non_primary(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )

        created_contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        primary_email = contact_emails_map.get(contact_email_req.email)
        no_primary_email = contact_emails_map.get(contact_email_req_2.email)
        assert primary_email and no_primary_email
        assert primary_email.contact_id == created_contact_dto.contact.id
        assert no_primary_email.contact_id == created_contact_dto.contact.id

        primary_asso = await contact_repo.get_contact_email_account_association(
            organization_id=self.organization_id,
            contact_id=created_contact_dto.contact.id,
            contact_email_id=primary_email.id,
            account_id=contact_account_association_req.account_id,
        )
        assert primary_asso
        assert primary_asso.account_id == contact_account_association_req.account_id
        assert primary_asso.is_contact_account_primary

        no_primary_asso = await contact_repo.get_contact_email_account_association(
            organization_id=self.organization_id,
            contact_id=created_contact_dto.contact.id,
            contact_email_id=no_primary_email.id,
            account_id=contact_account_association_req.account_id,
        )
        assert no_primary_asso
        assert no_primary_asso.account_id == contact_account_association_req.account_id
        assert not no_primary_asso.is_contact_account_primary

        result = await contact_repo.patch_contact_email_account_association(
            organization_id=self.organization_id,
            user_id=self.user_id,
            req=UpdateContactEmailAccountAssociationRequest(
                id=primary_asso.id,
                is_contact_account_primary=False,
                alternative_email_account_association_id=no_primary_asso.id,
            ),
        )

        assert result.updated_contact_email_account_association.id == primary_asso.id
        assert not result.updated_contact_email_account_association.is_contact_account_primary
        assert result.updated_alter_contact_email_account_association
        assert (
            result.updated_alter_contact_email_account_association.id
            == no_primary_asso.id
        )
        assert result.updated_alter_contact_email_account_association.is_contact_account_primary

        updated_primary_asso = cast(
            ContactEmailAccountAssociation,
            await contact_repo.get_contact_channel_info_account_association_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_channel_info_account_association_id=primary_asso.id,
            ),
        )
        assert updated_primary_asso
        assert not updated_primary_asso.is_contact_account_primary

        updated_no_primary_asso = cast(
            ContactEmailAccountAssociation,
            await contact_repo.get_contact_channel_info_account_association_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_channel_info_account_association_id=no_primary_asso.id,
            ),
        )
        assert updated_no_primary_asso
        assert updated_no_primary_asso.is_contact_account_primary

    async def test_patch_contact_email_account_association_non_primary_to_primary(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )

        created_contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        primary_email = contact_emails_map.get(contact_email_req.email)
        no_primary_email = contact_emails_map.get(contact_email_req_2.email)
        assert primary_email and no_primary_email
        assert primary_email.contact_id == created_contact_dto.contact.id
        assert no_primary_email.contact_id == created_contact_dto.contact.id

        primary_asso = await contact_repo.get_contact_email_account_association(
            organization_id=self.organization_id,
            contact_id=created_contact_dto.contact.id,
            contact_email_id=primary_email.id,
            account_id=contact_account_association_req.account_id,
        )
        assert primary_asso
        assert primary_asso.account_id == contact_account_association_req.account_id
        assert primary_asso.is_contact_account_primary

        no_primary_asso = await contact_repo.get_contact_email_account_association(
            organization_id=self.organization_id,
            contact_id=created_contact_dto.contact.id,
            contact_email_id=no_primary_email.id,
            account_id=contact_account_association_req.account_id,
        )
        assert no_primary_asso
        assert no_primary_asso.account_id == contact_account_association_req.account_id
        assert not no_primary_asso.is_contact_account_primary

        result = await contact_repo.patch_contact_email_account_association(
            organization_id=self.organization_id,
            user_id=self.user_id,
            req=UpdateContactEmailAccountAssociationRequest(
                id=no_primary_asso.id,
                is_contact_account_primary=True,
                alternative_email_account_association_id=primary_asso.id,
            ),
        )

        assert result.updated_contact_email_account_association.id == no_primary_asso.id
        assert (
            result.updated_contact_email_account_association.is_contact_account_primary
        )
        assert result.updated_alter_contact_email_account_association
        assert (
            result.updated_alter_contact_email_account_association.id == primary_asso.id
        )
        assert not result.updated_alter_contact_email_account_association.is_contact_account_primary

        updated_primary_asso = cast(
            ContactEmailAccountAssociation,
            await contact_repo.get_contact_channel_info_account_association_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_channel_info_account_association_id=primary_asso.id,
            ),
        )
        assert updated_primary_asso
        assert not updated_primary_asso.is_contact_account_primary

        updated_no_primary_asso = cast(
            ContactEmailAccountAssociation,
            await contact_repo.get_contact_channel_info_account_association_by_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_channel_info_account_association_id=no_primary_asso.id,
            ),
        )
        assert updated_no_primary_asso
        assert updated_no_primary_asso.is_contact_account_primary

    async def test_delete_contact_email_only_one_email(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        assert len(contact_dto.emails) == 1
        assert contact_dto.emails[0].email == contact_email_req.email
        assert contact_dto.emails[0].is_contact_primary
        assert len(contact_dto.email_account_associations) == 1
        assert (
            contact_dto.email_account_associations[0].account_id
            == contact_account_association_req.account_id
        )
        assert contact_dto.email_account_associations[0].is_contact_account_primary

        result = await contact_repo.delete_contact_email(
            organization_id=self.organization_id,
            user_id=self.user_id,
            contact_id=contact_dto.contact.id,
            email=contact_email_req.email,
        )

        assert result.deleted.id == contact_dto.emails[0].id
        assert not result.promoted

        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact,
            exclude_deleted_or_archived=False,
            organization_id=self.organization_id,
            id=contact_dto.contact.id,
        )
        assert contact
        contact_email = cast(
            EmailStrLower,
            await contact_repo.get_primary_channel_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact.id,
            ),
        )
        assert contact_email is None

    async def test_delete_primary_contact_email_with_multiple_emails(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )

        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )

        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        assert len(contact_dto.emails) == 2
        assert len(contact_dto.email_account_associations) == 2

        contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        primary_email = contact_emails_map.get(contact_email_req.email)
        no_primary_email = contact_emails_map.get(contact_email_req_2.email)
        assert primary_email and no_primary_email

        result = await contact_repo.delete_contact_email(
            organization_id=self.organization_id,
            user_id=self.user_id,
            contact_id=contact_dto.contact.id,
            email=primary_email.email,
        )

        assert result.deleted.id == primary_email.id
        assert result.promoted
        assert result.promoted.id == no_primary_email.id

        new_primary_email = cast(
            ContactEmail,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert new_primary_email
        assert new_primary_email.id == no_primary_email.id
        assert new_primary_email.email == no_primary_email.email
        assert new_primary_email.is_contact_primary

        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact,
            exclude_deleted_or_archived=False,
            organization_id=self.organization_id,
            id=contact_dto.contact.id,
        )
        assert contact

    async def test_delete_non_primary_contact_email_with_multiple_emails(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )

        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )

        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        assert len(contact_dto.emails) == 2
        assert len(contact_dto.email_account_associations) == 2

        contact_emails_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req.email, contact_email_req_2.email},
        )

        primary_email = contact_emails_map.get(contact_email_req.email)
        no_primary_email = contact_emails_map.get(contact_email_req_2.email)
        assert primary_email and no_primary_email

        result = await contact_repo.delete_contact_email(
            organization_id=self.organization_id,
            user_id=self.user_id,
            contact_id=contact_dto.contact.id,
            email=no_primary_email.email,
        )

        assert result.deleted.id == no_primary_email.id
        assert result.promoted is None

        new_primary_email = cast(
            ContactEmail,
            await contact_repo.get_primary_contact_channel_info_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert new_primary_email
        assert new_primary_email.id == primary_email.id
        assert new_primary_email.email == primary_email.email
        assert new_primary_email.is_contact_primary

        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact,
            exclude_deleted_or_archived=False,
            organization_id=self.organization_id,
            id=contact_dto.contact.id,
        )
        assert contact

    async def test_archive_contact(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert len(contact_emails) == 1
        contact_email_id = contact_emails[0].id

        contact_email_account_associations = cast(
            list[ContactEmailAccountAssociation],
            await contact_repo.list_contact_channel_info_account_associations_by_contact_channel_info_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_channel_info_id=contact_email_id,
            ),
        )
        assert len(contact_email_account_associations) == 1
        contact_email_account_association_id = contact_email_account_associations[0].id

        await contact_repo.archive_contact(
            organization_id=self.organization_id,
            contact_id=contact_dto.contact.id,
            archived_by_user_id=self.user_id,
        )

        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact,
            exclude_deleted_or_archived=False,
            organization_id=self.organization_id,
            id=contact_dto.contact.id,
        )
        assert contact
        assert contact.archived_at is not None
        assert contact.archived_by_user_id == self.user_id

        contact_email = await contact_repo.find_by_tenanted_primary_key(
            ContactEmail,
            exclude_deleted_or_archived=False,
            organization_id=self.organization_id,
            id=contact_email_id,
        )
        assert contact_email
        assert contact_email.archived_at is not None
        assert contact_email.archived_by_user_id == self.user_id

        contact_email_account_association = (
            await contact_repo.find_by_tenanted_primary_key(
                ContactEmailAccountAssociation,
                exclude_deleted_or_archived=False,
                organization_id=self.organization_id,
                id=contact_email_account_association_id,
            )
        )
        assert contact_email_account_association
        assert contact_email_account_association.archived_at is not None
        assert contact_email_account_association.archived_by_user_id == self.user_id

    async def test_unarchive_contact(
        self,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        await contact_repo.archive_contact(
            organization_id=self.organization_id,
            contact_id=contact_dto.contact.id,
            archived_by_user_id=self.user_id,
        )
        await contact_repo.unarchive_contact(
            organization_id=self.organization_id,
            contact_id=contact_dto.contact.id,
            unarchived_by_user_id=self.user_id,
        )

        contact = await contact_repo.find_by_tenanted_primary_key(
            Contact,
            exclude_deleted_or_archived=False,
            organization_id=self.organization_id,
            id=contact_dto.contact.id,
        )
        assert contact
        assert contact.archived_at is None
        assert contact.archived_by_user_id is None

        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert contact_emails
        assert contact_emails[0].archived_at is None
        assert contact_emails[0].archived_by_user_id is None

        contact_email_account_associations = cast(
            list[ContactEmailAccountAssociation],
            await contact_repo.list_contact_channel_info_account_associations_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_dto.contact.id,
            ),
        )
        assert contact_email_account_associations
        assert contact_email_account_associations[0].archived_at is None
        assert contact_email_account_associations[0].archived_by_user_id is None

    async def test_find_contacts_by_contact_emails(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=contact_account_association_req.account_id,
                    is_contact_account_primary=False,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )
        contact_id_1 = contact_dto.contact.id

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        contact_email_req_3 = strict_model_copy(
            contact_email_req, email=faker.email(), is_contact_primary=True
        )
        req = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req_3],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )
        contact_id_2 = contact_dto.contact.id

        # find by multi emails
        email_contact_map = await contact_repo.find_contacts_by_contact_emails(
            organization_id=self.organization_id,
            emails=[contact_email_req.email, contact_email_req_3.email],
        )
        assert email_contact_map and len(email_contact_map) == 2
        contact = email_contact_map.get(contact_email_req.email)
        assert contact and contact.id == contact_id_1
        contact = email_contact_map.get(contact_email_req_3.email)
        assert contact and contact.id == contact_id_2

        # find by not primary email
        email_contact_map = await contact_repo.find_contacts_by_contact_emails(
            organization_id=self.organization_id,
            emails=[contact_email_req_2.email],
        )
        assert email_contact_map and len(email_contact_map) == 1
        contact = email_contact_map.get(contact_email_req_2.email)
        assert contact and contact.id == contact_id_1

        contact = await contact_repo.find_contact_by_primary_email(
            organization_id=self.organization_id, primary_email=contact_email_req.email
        )
        assert contact and contact.id == contact_id_1

    async def test_upsert_contact_email_conflict(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
        contact_service: ContactService,
    ) -> None:
        contact_email_req_no_primary = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_no_primary],
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req,
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_1 = contact_dto.contact

        contact_email_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req_no_primary.email},
        )
        contact_email_1 = contact_email_map.get(contact_email_req_no_primary.email)
        assert contact_email_1
        contact_email_id_1 = contact_email_1.id

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        contact_email_req_2 = strict_model_copy(
            contact_email_req, email=faker.email(), is_contact_primary=True
        )
        contact_dto = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact_req_2,
                contact_emails=[contact_email_req_2],
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_2 = contact_dto.contact

        # 1. create new contact with active contact and active email conflict
        # 2. upsert new contact with active contact and active email conflict
        contact_req_3 = strict_model_copy(contact_req, display_name="contact_3")
        with pytest.raises(ConflictResourceError) as e:
            await contact_repo.create_contact(
                req=CreateContactRequest(
                    contact=contact_req_3,
                    contact_emails=[contact_email_req_no_primary],
                    contact_account_roles=[contact_account_association_req],
                ),
                organization_id=self.organization_id,
                user_id=self.user_id,
            )
        await self._assert_conflict_error_details(
            contact_repo=contact_repo,
            conflict_error_details=e.value.additional_error_details,
            conflict_email_id=contact_email_id_1,
            conflict_contact=contact_1,
            code="CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ACTIVE_EMAIL",
            details="contact email conflict, try to update the existing contact",
        )

        with pytest.raises(ConflictResourceError) as e:
            await contact_service.batch_upsert_contact_email(
                organization_id=self.organization_id,
                user_id=self.user_id,
                contact_id=contact_2.id,
                create_contact_email_requests=[contact_email_req_no_primary],
            )
        await self._assert_conflict_error_details(
            contact_repo=contact_repo,
            conflict_error_details=e.value.additional_error_details,
            conflict_email_id=contact_email_id_1,
            conflict_contact=contact_1,
            code="CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ACTIVE_EMAIL",
            details="contact email conflict, try to merge the existing contact",
        )

        await contact_repo._archive_contact_email(
            organization_id=self.organization_id,
            user_id=self.user_id,
            contact_id=contact_1.id,
        )
        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_1.id,
            ),
        )
        assert not contact_emails
        # 3. create new contact with active contact and archived email conflict
        # 4. upsert new contact with active contact and archived email conflict
        contact_req_3 = strict_model_copy(contact_req, display_name="contact_3")
        with pytest.raises(ConflictResourceError) as e:
            await contact_repo.create_contact(
                req=CreateContactRequest(
                    contact=contact_req_3,
                    contact_emails=[contact_email_req_no_primary],
                    contact_account_roles=[contact_account_association_req],
                ),
                organization_id=self.organization_id,
                user_id=self.user_id,
            )
        await self._assert_conflict_error_details(
            contact_repo=contact_repo,
            conflict_error_details=e.value.additional_error_details,
            conflict_email_id=contact_email_id_1,
            conflict_contact=contact_1,
            code="CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ARCHIVED_EMAIL",
            details="contact email conflict, try to update the existing contact or overwrite contact email",
        )

        with pytest.raises(ConflictResourceError) as e:
            await contact_service.batch_upsert_contact_email(
                organization_id=self.organization_id,
                user_id=self.user_id,
                contact_id=contact_2.id,
                create_contact_email_requests=[contact_email_req_no_primary],
            )
        await self._assert_conflict_error_details(
            contact_repo=contact_repo,
            conflict_error_details=e.value.additional_error_details,
            conflict_email_id=contact_email_id_1,
            conflict_contact=contact_1,
            code="CONTACT_EMAIL_ALREADY_EXISTS_ACTIVE_CONTACT_ARCHIVED_EMAIL",
            details="contact email conflict, try to merge or overwrite contact email",
        )

        await contact_repo.archive_contact(
            organization_id=self.organization_id,
            contact_id=contact_1.id,
            archived_by_user_id=self.user_id,
        )
        current_contact_1 = await contact_repo.find_by_tenanted_primary_key(
            Contact,
            exclude_deleted_or_archived=False,
            organization_id=self.organization_id,
            id=contact_1.id,
        )
        assert current_contact_1 and current_contact_1.archived_at is not None
        # 5. create new contact with archived contact and archived email conflict
        # 6. upsert new contact with archived contact and archived email conflict
        contact_req_3 = strict_model_copy(contact_req, display_name="contact_3")
        with pytest.raises(ConflictResourceError) as e:
            await contact_repo.create_contact(
                req=CreateContactRequest(
                    contact=contact_req_3,
                    contact_emails=[contact_email_req_no_primary],
                    contact_account_roles=[contact_account_association_req],
                ),
                organization_id=self.organization_id,
                user_id=self.user_id,
            )
        await self._assert_conflict_error_details(
            contact_repo=contact_repo,
            conflict_error_details=e.value.additional_error_details,
            conflict_email_id=contact_email_id_1,
            conflict_contact=current_contact_1,
            code="CONTACT_EMAIL_ALREADY_EXISTS_ARCHIVED_CONTACT",
            details="try to unarchive the existing contact or overwrite contact email",
        )

        with pytest.raises(ConflictResourceError) as e:
            await contact_service.batch_upsert_contact_email(
                organization_id=self.organization_id,
                user_id=self.user_id,
                contact_id=contact_2.id,
                create_contact_email_requests=[contact_email_req_no_primary],
            )
        await self._assert_conflict_error_details(
            contact_repo=contact_repo,
            conflict_error_details=e.value.additional_error_details,
            conflict_email_id=contact_email_id_1,
            conflict_contact=current_contact_1,
            code="CONTACT_EMAIL_ALREADY_EXISTS_ARCHIVED_CONTACT",
            details="try to unarchive the existing contact or overwrite contact email",
        )

        # overwrite: create new contact and overwrite archived email conflict
        contact_req_3 = strict_model_copy(contact_req, display_name="contact_3")
        contact_dto_3 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact_req_3,
                contact_emails=[contact_email_req_no_primary],
                contact_account_roles=[contact_account_association_req],
            ),
            overwrite_archived_emails=True,
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_email_map = await contact_repo.map_contact_emails_by_emails(
            organization_id=self.organization_id,
            emails={contact_email_req_no_primary.email},
        )
        assert contact_email_map
        contact_email_3 = contact_email_map.get(contact_email_req_no_primary.email)
        assert contact_email_3 and contact_email_3.id == contact_email_id_1
        assert contact_email_3.contact_id == contact_dto_3.contact.id

    async def test_list_primary_contact_channel_infos_by_contact_ids(
        self,
        faker: Faker,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[contact_account_association_req],
        )
        contact_1_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        contact_email_req_3 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[],
        )
        contact_email_req_4 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=True,
            email_account_associations=[],
        )
        req = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req_3, contact_email_req_4],
            contact_account_roles=[],
        )
        contact_2_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_primary_contact_channel_infos_by_contact_ids(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_ids=[contact_1_dto.contact.id, contact_2_dto.contact.id],
            ),
        )
        assert len(contact_emails) == 2
        for contact_email in contact_emails:
            if contact_email.contact_id == contact_1_dto.contact.id:
                assert contact_email.email == contact_email_req.email
            else:
                assert contact_email.contact_id == contact_2_dto.contact.id
                assert contact_email.email == contact_email_req_4.email

    async def test_list_primary_account_prefer_contact_email_by_contact_id(
        self,
        faker: Faker,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_account_association_req_2 = strict_model_copy(
            contact_account_association_req,
            account_id=account_2.id,
            is_primary_account=False,
        )
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_2.id,
                    is_contact_account_primary=True,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[
                contact_account_association_req,
                contact_account_association_req_2,
            ],
        )
        contact_1_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        contact_email_req_3 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[],
        )
        contact_email_req_4 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=True,
            email_account_associations=[],
        )
        req = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req_3, contact_email_req_4],
            contact_account_roles=[],
        )
        contact_2_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_dto_map = (
            await contact_repo.map_primary_account_prefer_contact_email_by_contact_id(
                organization_id=self.organization_id,
                contact_ids=[contact_1_dto.contact.id, contact_2_dto.contact.id],
            )
        )
        assert len(contact_dto_map) == 1
        contact_email_dto = contact_dto_map[contact_1_dto.contact.id]
        contact_email, contact_email_account_association = contact_email_dto
        assert contact_email.email == contact_email_req.email
        assert (
            contact_email_account_association.account_id
            == contact_account_association_req.account_id
        )

        assert contact_2_dto.contact.id not in contact_email_dto

    async def test_list_prefer_contact_email_by_contact_id_and_account_id(
        self,
        faker: Faker,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
    ) -> None:
        contact_account_association_req_2 = strict_model_copy(
            contact_account_association_req,
            account_id=account_2.id,
            is_primary_account=False,
        )
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_2.id,
                    is_contact_account_primary=True,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[
                contact_account_association_req,
                contact_account_association_req_2,
            ],
        )
        contact_1_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        contact_account_association_req_3 = strict_model_copy(
            contact_account_association_req,
            account_id=account_1.id,
            is_primary_account=False,
        )
        contact_email_req_3 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                )
            ],
        )
        contact_email_req_4 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=True,
            email_account_associations=[],
        )
        req = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req_3, contact_email_req_4],
            contact_account_roles=[contact_account_association_req_3],
        )
        contact_2_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_dto_map = (
            await contact_repo.map_prefer_contact_email_by_contact_id_and_account_id(
                organization_id=self.organization_id,
                contact_account_pairs=[
                    (contact_1_dto.contact.id, account_2.id),
                    (contact_2_dto.contact.id, account_1.id),
                ],
            )
        )
        assert len(contact_dto_map) == 2
        contact_email_dto = contact_dto_map[contact_1_dto.contact.id]
        contact_email, contact_email_account_association = contact_email_dto
        assert contact_email.email == contact_email_req_2.email
        assert (
            contact_email_account_association.account_id
            == contact_account_association_req_2.account_id
        )
        contact_email_dto = contact_dto_map[contact_2_dto.contact.id]
        contact_email, contact_email_account_association = contact_email_dto
        assert contact_email.email == contact_email_req_3.email
        assert (
            contact_email_account_association.account_id
            == contact_account_association_req_3.account_id
        )

        contact_dto_map = (
            await contact_repo.map_prefer_contact_email_by_contact_id_and_account_id(
                organization_id=self.organization_id,
                contact_account_pairs=[
                    (contact_1_dto.contact.id, None),
                    (contact_2_dto.contact.id, account_2.id),
                ],
            )
        )
        assert len(contact_dto_map) == 0

    async def test_map_the_most_relevant_contact_emails(
        self,
        faker: Faker,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_req: CreateDbContactRequest,
        contact_email_req: CreateDbContactEmailRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        contact_repo: ContactRepository,
        contact_query_service: ContactQueryService,
        contact_resolve_service: ContactResolveService,
    ) -> None:
        contact_account_association_req_2 = strict_model_copy(
            contact_account_association_req,
            account_id=account_2.id,
            is_primary_account=False,
        )
        contact_email_req_2 = strict_model_copy(
            contact_email_req,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_2.id,
                    is_contact_account_primary=True,
                )
            ],
        )
        req = CreateContactRequest(
            contact=contact_req,
            contact_emails=[contact_email_req, contact_email_req_2],
            contact_account_roles=[
                contact_account_association_req,
                contact_account_association_req_2,
            ],
        )
        contact_1_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_req_2 = strict_model_copy(contact_req, display_name="contact_2")
        contact_account_association_req_3 = strict_model_copy(
            contact_account_association_req,
            account_id=account_1.id,
            is_primary_account=True,
        )
        contact_email_req_3 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=False,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                )
            ],
        )
        contact_email_req_4 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=True,
            email_account_associations=[],
        )
        req = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req_3, contact_email_req_4],
            contact_account_roles=[contact_account_association_req_3],
        )
        contact_2_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_req_3 = strict_model_copy(contact_req, display_name="contact_3")
        contact_email_req_5 = strict_model_copy(
            contact_email_req_2,
            email=faker.email(),
            is_contact_primary=True,
            email_account_associations=[],
        )
        req = CreateContactRequest(
            contact=contact_req_3,
            contact_emails=[contact_email_req_5],
            contact_account_roles=[],
        )
        contact_3_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        contact_4_id = uuid4()
        contact_dto_map = (
            await contact_resolve_service.map_the_most_relevant_contact_emails(
                organization_id=self.organization_id,
                contact_account_pairs=[
                    (contact_1_dto.contact.id, account_2.id),
                    (contact_2_dto.contact.id, account_2.id),
                    (contact_3_dto.contact.id, account_1.id),
                    (contact_4_id, None),
                ],
            )
        )

        contact_dto_res = contact_dto_map.get(contact_1_dto.contact.id)
        assert contact_dto_res
        email, account_id = contact_dto_res
        assert email == contact_email_req_2.email
        assert account_id
        assert account_id == account_2.id

        contact_dto_res = contact_dto_map.get(contact_2_dto.contact.id)
        assert contact_dto_res
        email, account_id = contact_dto_res
        assert email == contact_email_req_3.email
        assert account_id
        assert account_id == account_1.id

        contact_dto_res = contact_dto_map.get(contact_3_dto.contact.id)
        assert contact_dto_res
        email, account_id = contact_dto_res
        assert email == contact_email_req_5.email
        assert not account_id

        assert contact_4_id not in contact_dto_map

    async def test_resolve_relevant_contact_and_account_by_emails(
        self,
        account_1: AccountV2,
        account_2: AccountV2,
        contact_req: CreateDbContactRequest,
        contact_repo: ContactRepository,
        contact_query_service: ContactQueryService,
        contact_resolve_service: ContactResolveService,
        account_service: AccountService,
        faker: Faker,
    ) -> None:
        # Create contact 1 with email 1 and 2
        contact_req_1 = strict_model_copy(contact_req, display_name=faker.name())
        contact_email_req_1 = CreateDbContactEmailRequest(
            email=faker.email(),
            is_contact_primary=True,
            email_account_associations=[
                CreateDbContactEmailAccountAssociationRequest(
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                )
            ],
        )
        contact_email_req_2 = CreateDbContactEmailRequest(
            email=faker.email(),
            is_contact_primary=False,
        )
        req = CreateContactRequest(
            contact=contact_req_1,
            contact_emails=[contact_email_req_1, contact_email_req_2],
            contact_account_roles=[
                CreateContactAccountRoleRequest(
                    account_id=account_1.id,
                    is_primary_account=True,
                )
            ],
        )
        contact_1_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        # Create contact 2 with email 3
        contact_req_2 = strict_model_copy(contact_req, display_name=faker.name())
        contact_email_req_3 = CreateDbContactEmailRequest(
            email=faker.email(),
            is_contact_primary=True,
        )
        req = CreateContactRequest(
            contact=contact_req_2,
            contact_emails=[contact_email_req_3],
            contact_account_roles=[],
        )
        contact_2_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )

        # Test resolving contacts and accounts by emails
        result = (
            await contact_resolve_service.batch_resolve_relevant_contact_info_by_email(
                organization_id=self.organization_id,
                emails=[
                    contact_email_req_1.email,
                    contact_email_req_2.email,
                    contact_email_req_3.email,
                    "<EMAIL>",
                ],
            )
        )

        # Check contact 1 emails
        contact_account = result.get(contact_email_req_1.email)
        assert contact_account
        assert contact_account.contact_id == contact_1_dto.contact.id
        assert contact_account.account_id == account_1.id

        contact_account = result.get(contact_email_req_2.email)
        assert contact_account
        assert contact_account.contact_id == contact_1_dto.contact.id
        assert contact_account.account_id == account_1.id

        # Check contact 2 email
        contact_account = result.get(contact_email_req_3.email)
        assert contact_account
        assert contact_account.contact_id == contact_2_dto.contact.id
        assert contact_account.account_id is None

        # Check nonexistent email
        contact_account = result.get("<EMAIL>")
        assert contact_account
        assert not contact_account.contact_id
        assert not contact_account.account_id

    async def _assert_conflict_error_details(
        self,
        contact_repo: ContactRepository,
        conflict_error_details: ConflictErrorDetails | None,
        conflict_email_id: UUID,
        conflict_contact: Contact,
        code: str,
        details: str,
    ) -> None:
        contact_email = await contact_repo.find_by_tenanted_primary_key(
            ContactEmail,
            exclude_deleted_or_archived=False,
            organization_id=self.organization_id,
            id=conflict_email_id,
        )
        assert contact_email

        assert conflict_error_details == ConflictErrorDetails(
            code=code,
            details=details,
            reference_id=str(conflict_contact.id),
            conflicted_existing_object=StdObjectIdentifiers.contact.identifier,
            conflicted_existing_object_attrs={
                ContactField.organization_id: conflict_contact.organization_id,
                ContactField.id: conflict_contact.id,
                ContactField.display_name: conflict_contact.display_name,
                ContactField.archived_at: conflict_contact.archived_at,
                "existing_email_archive_at": contact_email.archived_at,
                "conflict_email": contact_email.email,
            },
        )


class TestUpsertContactEmailAndContactEmailAccountAssociations:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(scope="function")
    async def account_1(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name=faker.company(),
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def contact_req(
        self,
        faker: Faker,
        select_list_service: InternalSelectListService,
    ) -> CreateDbContactRequest:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        return CreateDbContactRequest(
            display_name="contact_1",
            stage_id=default_stage_list.default_or_initial_active_value.id,
            created_by_user_id=self.user_id,
            owner_user_id=self.user_id,
            first_name=faker.first_name(),
            last_name=faker.last_name(),
        )

    @pytest.fixture(scope="function")
    async def contact_account_association_req(
        self,
        account_1: AccountV2,
    ) -> CreateContactAccountRoleRequest:
        return CreateContactAccountRoleRequest(
            account_id=account_1.id,
            is_primary_account=True,
        )

    async def get_contact_email_map(
        self,
        contact_id: UUID,
        contact_repo: ContactRepository,
    ) -> dict[str, ContactEmail]:
        contact_emails = cast(
            list[ContactEmail],
            await contact_repo.list_contact_channel_infos_by_contact_id(
                contact_channel_type=ContactChannelType.EMAIL,
                organization_id=self.organization_id,
                contact_id=contact_id,
            ),
        )
        return {ce.email: ce for ce in contact_emails}

    async def test_upsert_contact_email(
        self,
        contact_req: CreateDbContactRequest,
        contact_repo: ContactRepository,
    ) -> None:
        # upsert first email as non-primary -> primary(first)
        contact_dto = await contact_repo.create_contact(
            req=CreateContactRequest(contact=contact_req),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id = contact_dto.contact.id

        email1 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email1,
                labels=[ContactChannelLabel.WORK],
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        contact_email = contact_emails.get(email1)
        assert contact_email
        assert contact_email.is_contact_primary is True
        label = (
            contact_email.labels[0] if contact_email and contact_email.labels else None
        )
        assert label == ContactChannelLabel.WORK.value

        # remove first email, upsert first email as primary -> primary(first)
        await contact_repo.update_by_primary_key(
            ContactEmail,
            primary_key_to_value={"id": contact_emails[email1].id},
            column_to_update={"deleted_at": zoned_utc_now()},
        )

        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email1,
                is_contact_primary=True,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        assert contact_emails.get(email1)
        assert contact_emails[email1].is_contact_primary is True

        # upsert first email as non-primary -> still primary(first)
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email1,
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        assert contact_emails.get(email1)
        assert contact_emails[email1].is_contact_primary is True

        # upsert first email as primary -> primary(first)
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email1,
                is_contact_primary=True,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        assert contact_emails.get(email1)
        assert contact_emails[email1].is_contact_primary is True

        # upsert second email as non-primary -> primary(first), non-primary(second)
        email2 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email2,
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        assert contact_emails.get(email1)
        assert contact_emails.get(email2)
        assert contact_emails[email1].is_contact_primary is True
        assert contact_emails[email2].is_contact_primary is False

        # upsert second email as non-primary, again -> do nothing, primary(first), non-primary(second)
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email2,
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        assert contact_emails.get(email1)
        assert contact_emails.get(email2)
        assert contact_emails[email1].is_contact_primary is True
        assert contact_emails[email2].is_contact_primary is False

        # upsert second email as primary -> primary(second), non-primary(first)
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email2,
                is_contact_primary=True,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        assert contact_emails.get(email1)
        assert contact_emails.get(email2)
        assert contact_emails[email1].is_contact_primary is False
        assert contact_emails[email2].is_contact_primary is True

        # upsert second email as primary, again -> do nothing, primary(second), non-primary(first)
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email2,
                is_contact_primary=True,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        assert contact_emails.get(email1)
        assert contact_emails.get(email2)
        assert contact_emails[email1].is_contact_primary is False
        assert contact_emails[email2].is_contact_primary is True

        # upsert second email as non-primary -> primary(first), non-primary(second)
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id,
                email=email2,
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        assert contact_emails.get(email1)
        assert contact_emails.get(email2)
        assert contact_emails[email1].is_contact_primary is True
        assert contact_emails[email2].is_contact_primary is False

    async def get_contact_email_account_association_map(
        self,
        contact_id: UUID,
        contact_repo: ContactRepository,
    ) -> dict[str, ContactEmailAccountAssociation]:
        # only for this test case, can assume one email will only have one association
        contact_emails = await self.get_contact_email_map(contact_id, contact_repo)
        contact_email_account_association_map: dict[
            str, ContactEmailAccountAssociation
        ] = {}
        for email, contact_email in contact_emails.items():
            associations = cast(
                list[ContactEmailAccountAssociation],
                await contact_repo.list_contact_channel_info_account_associations_by_contact_channel_info_id(
                    contact_channel_type=ContactChannelType.EMAIL,
                    organization_id=self.organization_id,
                    contact_channel_info_id=contact_email.id,
                ),
            )
            assert associations and len(associations) == 1
            contact_email_account_association_map[email] = associations[0]
        return contact_email_account_association_map

    async def test_upsert_contact_email_account_association(
        self,
        contact_req: CreateDbContactRequest,
        contact_repo: ContactRepository,
        contact_account_association_req: CreateContactAccountRoleRequest,
        account_1: AccountV2,
    ) -> None:
        # upsert first association as non-primary -> primary(first)
        req = CreateContactRequest(
            contact=contact_req,
            contact_account_roles=[contact_account_association_req],
        )
        contact_dto = await contact_repo.create_contact(
            req=req, organization_id=self.organization_id, user_id=self.user_id
        )
        contact_id = contact_dto.contact.id
        email1 = "<EMAIL>"

        contact_email_req = ContactEmail(
            organization_id=self.organization_id,
            contact_id=contact_id,
            email=email1,
            is_contact_primary=True,
            created_by_user_id=self.user_id,
            updated_by_user_id=self.user_id,
        )

        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=False,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )

        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map[email1].is_contact_account_primary is True

        # remove first association, upsert first association as primary -> primary(first)
        await contact_repo.update_by_primary_key(
            ContactEmailAccountAssociation,
            primary_key_to_value={"id": association_map[email1].id},
            column_to_update={"deleted_at": zoned_utc_now()},
        )

        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )
        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map[email1].is_contact_account_primary is True

        # upsert first association as non-primary -> still primary(first)
        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=False,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )
        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map[email1].is_contact_account_primary is True

        # upsert first association as primary -> primary(first)
        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )
        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map[email1].is_contact_account_primary is True

        # upsert second association as non-primary -> primary(first), non-primary(second)
        email2 = "<EMAIL>"
        contact_email_req2 = ContactEmail(
            organization_id=self.organization_id,
            contact_id=contact_id,
            email=email2,
            is_contact_primary=False,
            created_by_user_id=self.user_id,
            updated_by_user_id=self.user_id,
        )
        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req2,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=False,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )
        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map.get(email2)
        assert association_map[email1].is_contact_account_primary is True
        assert association_map[email2].is_contact_account_primary is False

        # upsert second association as non-primary, again -> do nothing, primary(first), non-primary(second)
        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req2,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=False,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )
        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map.get(email2)
        assert association_map[email1].is_contact_account_primary is True
        assert association_map[email2].is_contact_account_primary is False

        # upsert second association as primary -> primary(second), non-primary(first)
        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req2,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )
        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map.get(email2)
        assert association_map[email1].is_contact_account_primary is False
        assert association_map[email2].is_contact_account_primary is True

        # upsert second association as primary, again -> do nothing, primary(second), non-primary(first)
        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req2,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=True,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )
        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map.get(email2)
        assert association_map[email1].is_contact_account_primary is False
        assert association_map[email2].is_contact_account_primary is True

        # upsert second association as non-primary -> primary(first), non-primary(second)
        await contact_repo.upsert_contact_email(
            contact_email=contact_email_req2,
            contact_email_account_associations=[
                ContactEmailAccountAssociation(
                    organization_id=self.organization_id,
                    contact_id=contact_id,
                    account_id=account_1.id,
                    is_contact_account_primary=False,
                    created_by_user_id=self.user_id,
                    updated_by_user_id=self.user_id,
                )
            ],
        )
        association_map = await self.get_contact_email_account_association_map(
            contact_id, contact_repo
        )
        assert association_map.get(email1)
        assert association_map.get(email2)
        assert association_map[email1].is_contact_account_primary is True
        assert association_map[email2].is_contact_account_primary is False


class TestContactRepositorySearch:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(scope="function")
    async def account_1(
        self, faker: Faker, account_service: AccountService
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name=faker.company(),
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def contact_req(
        self,
        faker: Faker,
        select_list_service: InternalSelectListService,
    ) -> CreateDbContactRequest:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        return CreateDbContactRequest(
            display_name="contact_1",
            stage_id=default_stage_list.default_or_initial_active_value.id,
            created_by_user_id=self.user_id,
            owner_user_id=self.user_id,
            first_name=faker.first_name(),
            last_name=faker.last_name(),
        )

    @pytest.fixture(scope="function")
    async def contact_account_association_req(
        self,
        account_1: AccountV2,
    ) -> CreateContactAccountRoleRequest:
        return CreateContactAccountRoleRequest(
            account_id=account_1.id,
            is_primary_account=True,
        )

    async def test_search_contact_by_email(
        self,
        contact_repo: ContactRepository,
        contact_req: CreateDbContactRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        account_1: AccountV2,
    ) -> None:
        # Setup - Create multiple contacts with different emails
        contact_1_req = contact_req.model_copy(
            update={
                "id": uuid4(),
            }
        )
        contact_1 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact_1_req,
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id_1 = contact_1.contact.id

        email1 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id_1,
                email=email1,
                labels=[ContactChannelLabel.WORK],
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact2_req = contact_req.model_copy(
            update={
                "id": uuid4(),
            }
        )
        contact_dto = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact2_req,
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id_2 = contact_dto.contact.id
        email2 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id_2,
                email=email2,
                labels=[ContactChannelLabel.WORK],
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact3_req = contact_req.model_copy(
            update={
                "id": uuid4(),
            }
        )
        contact3 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact3_req,
            ),
            user_id=self.user_id,
            organization_id=self.organization_id,
        )
        contact_id_3 = contact3.contact.id
        email3 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id_3,
                email=email3,
                labels=[ContactChannelLabel.WORK],
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        # Test exact match
        results = await contact_repo.search_contact_by_email(
            organization_id=self.organization_id,
            keyword=email1,
        )
        assert len(results) == 1
        assert contact_id_1 in results

        # Test partial match
        results = await contact_repo.search_contact_by_email(
            organization_id=self.organization_id,
            keyword="special",
        )
        assert len(results) == 1
        assert contact_id_3 in results

        # Test with account filter
        results = await contact_repo.search_contact_by_email(
            organization_id=self.organization_id,
            keyword="test",
            account_id=account_1.id,
        )
        assert len(results) == 2
        assert contact_id_1 in results
        assert contact_id_2 in results

        # Test no matches
        results = await contact_repo.search_contact_by_email(
            organization_id=self.organization_id,
            keyword="<EMAIL>",
        )
        assert len(results) == 0

    async def test_search_contact_by_display_name(
        self,
        contact_repo: ContactRepository,
        contact_req: CreateDbContactRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        account_1: AccountV2,
    ) -> None:
        # Setup - Create multiple contacts with different display names
        contact1_req = contact_req.model_copy(
            update={"id": uuid4(), "display_name": "John Smith"}
        )
        contact1 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact1_req,
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id_1 = contact1.contact.id

        contact2_req = contact_req.model_copy(
            update={"id": uuid4(), "display_name": "Jane Doe"}
        )
        await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact2_req,
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

        contact3_req = contact_req.model_copy(
            update={"id": uuid4(), "display_name": "John John"}
        )
        contact3 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact3_req,
            ),
            user_id=self.user_id,
            organization_id=self.organization_id,
        )
        contact_id_3 = contact3.contact.id
        # Test exact match
        results = await contact_repo.search_contact_by_display_name(
            organization_id=self.organization_id,
            keyword="John Smith",
        )
        assert len(results) == 1
        assert contact_id_1 in results

        # Test partial match
        results = await contact_repo.search_contact_by_display_name(
            organization_id=self.organization_id,
            keyword="John",
        )
        assert len(results) == 2
        assert contact_id_1 in results
        assert contact_id_3 in results

        # Test with account filter
        results = await contact_repo.search_contact_by_display_name(
            organization_id=self.organization_id,
            keyword="John",
            account_id=account_1.id,
        )
        assert len(results) == 1
        assert contact_id_1 in results

        # Test no matches
        results = await contact_repo.search_contact_by_display_name(
            organization_id=self.organization_id,
            keyword="Nonexistent Name",
        )
        assert len(results) == 0

    async def test_search_contact_by_email_and_filter(
        self,
        contact_repo: ContactRepository,
        contact_query_service: ContactQueryService,
        contact_req: CreateDbContactRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        account_1: AccountV2,
    ) -> None:
        # Setup - Create multiple contacts with different emails
        contact_1_req = contact_req.model_copy(
            update={
                "id": uuid4(),
            }
        )
        contact_1 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact_1_req,
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id_1 = contact_1.contact.id

        email1 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id_1,
                email=email1,
                labels=[ContactChannelLabel.WORK],
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact2_req = contact_req.model_copy(
            update={
                "display_name": "contact_2",
                "id": uuid4(),
            }
        )
        contact_dto = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact2_req,
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id_2 = contact_dto.contact.id
        email2 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id_2,
                email=email2,
                labels=[ContactChannelLabel.WORK],
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact3_req = contact_req.model_copy(
            update={
                "display_name": "contact_3",
                "id": uuid4(),
            }
        )
        contact3 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact3_req,
            ),
            user_id=self.user_id,
            organization_id=self.organization_id,
        )
        contact_id_3 = contact3.contact.id
        email3 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id_3,
                email=email3,
                labels=[ContactChannelLabel.WORK],
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )

        contact4_req = contact_req.model_copy(
            update={"display_name": "contact_4", "id": uuid4()}
        )
        contact4 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact4_req,
            ),
            user_id=self.user_id,
            organization_id=self.organization_id,
        )
        contact_id_4 = contact4.contact.id
        email4 = "<EMAIL>"
        await contact_repo.upsert_contact_email(
            contact_email=ContactEmail(
                organization_id=self.organization_id,
                contact_id=contact_id_4,
                email=email4,
                labels=[ContactChannelLabel.WORK],
                is_contact_primary=False,
                created_by_user_id=self.user_id,
                updated_by_user_id=self.user_id,
            ),
            contact_email_account_associations=[],
        )
        await contact_repo.archive_contact(
            organization_id=self.organization_id,
            contact_id=contact_id_4,
            archived_by_user_id=self.user_id,
        )

        # Test with keyword filter
        (
            additional_contacts,
            search_res_contacts,
            cursor,
        ) = await contact_query_service.search_paginated_contact_by_display_name_or_email(
            organization_id=self.organization_id, keyword="test"
        )
        assert len(search_res_contacts) == 3
        assert search_res_contacts[0].contact_id == contact_id_1
        assert search_res_contacts[1].contact_id == contact_id_2
        assert search_res_contacts[2].contact_id == contact_id_3

        # Test with account association required
        (
            additional_contacts,
            search_res_contacts,
            cursor,
        ) = await contact_query_service.search_paginated_contact_by_display_name_or_email(
            organization_id=self.organization_id,
            keyword="test",
            need_to_have_account_association=True,
        )
        assert len(search_res_contacts) == 2
        assert search_res_contacts[0].contact_id == contact_id_1
        assert search_res_contacts[1].contact_id == contact_id_2

        # Test with exclude contact id
        (
            additional_contacts,
            search_res_contacts,
            cursor,
        ) = await contact_query_service.search_paginated_contact_by_display_name_or_email(
            organization_id=self.organization_id,
            keyword="test",
            exclude_contact_ids=[contact_id_1],
            need_to_have_account_association=True,
        )
        assert len(search_res_contacts) == 1
        assert search_res_contacts[0].contact_id == contact_id_2

        # Test include archived contacts
        (
            additional_contacts,
            search_res_contacts,
            cursor,
        ) = await contact_query_service.search_paginated_contact_by_display_name_or_email(
            organization_id=self.organization_id,
            keyword="test",
            exclude_contact_ids=[contact_id_1],
            need_to_have_account_association=True,
            additional_contact_ids=[contact_id_4],
        )
        assert len(additional_contacts) == 1
        assert additional_contacts[0].contact_id == contact_id_4
        assert len(search_res_contacts) == 1
        assert search_res_contacts[0].contact_id == contact_id_2

    async def test_get_contact_display_name_and_emails_by_contact_ids(
        self,
        contact_repo: ContactRepository,
        contact_req: CreateDbContactRequest,
        contact_account_association_req: CreateContactAccountRoleRequest,
        account_1: AccountV2,
    ) -> None:
        # Setup - Create multiple contacts with different details
        contact1_req = contact_req.model_copy(
            update={
                "id": uuid4(),
                "first_name": "John",
                "last_name": "Smith",
                "display_name": "John Smith",
            }
        )
        contact_email_req = CreateDbContactEmailRequest(
            email="<EMAIL>",
            is_contact_primary=True,
            labels=[ContactChannelLabel.WORK],
        )
        contact_email_req2 = CreateDbContactEmailRequest(
            email="<EMAIL>",
            is_contact_primary=False,
            labels=[ContactChannelLabel.WORK],
        )
        contact1 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact1_req,
                contact_emails=[contact_email_req, contact_email_req2],
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id_1 = contact1.contact.id
        contact2_req = contact_req.model_copy(
            update={
                "id": uuid4(),
                "first_name": "Jane",
                "last_name": "Doe",
                "display_name": "Jane Doe",
            }
        )
        contact_2_email_req = CreateDbContactEmailRequest(
            email="<EMAIL>",
            is_contact_primary=True,
            labels=[ContactChannelLabel.WORK],
        )
        contact2 = await contact_repo.create_contact(
            req=CreateContactRequest(
                contact=contact2_req,
                contact_emails=[contact_2_email_req],
                contact_account_roles=[contact_account_association_req],
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id_2 = contact2.contact.id

        # edge case: contact has no emails
        contact3_req = contact_req.model_copy(
            update={"id": uuid4(), "display_name": "Bob Wong"}
        )
        contact3 = await contact_repo.create_contact(
            req=CreateContactRequest(contact=contact3_req),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )
        contact_id_3 = contact3.contact.id

        # Test getting multiple contacts
        results = await contact_repo.list_contacts_with_display_name_and_emails(
            organization_id=self.organization_id,
            contact_ids=[contact_id_1, contact_id_2, contact_id_3],
        )

        assert len(results) == 3

        # Check first contact (Bob Wong)
        assert results[0].contact_id == contact_id_3
        assert results[0].display_name == "Bob Wong"
        assert results[0].emails == []
        assert results[0].account_associations == []

        # Check second contact (Jane Doe)
        assert results[1].contact_id == contact_id_2
        assert results[1].display_name == "Jane Doe"
        assert results[1].emails[0].email == "<EMAIL>"
        assert results[1].account_associations[0].account_id == account_1.id
        assert results[1].account_associations[0].display_name == account_1.display_name
        assert results[1].account_associations[0].is_primary

        # Check third contact (John Smith)
        assert results[2].contact_id == contact_id_1
        assert results[2].display_name == "John Smith"
        assert results[2].emails[0].email == "<EMAIL>"
        assert results[2].emails[1].email == "<EMAIL>"
        assert results[2].account_associations[0].account_id == account_1.id
        assert results[2].account_associations[0].display_name == account_1.display_name
        assert results[2].account_associations[0].is_primary

        # Test with non-existent contact ID
        results = await contact_repo.list_contacts_with_display_name_and_emails(
            organization_id=self.organization_id,
            contact_ids=[uuid4()],
        )
        assert len(results) == 0


async def test_list_by_pipeline_id(
    contact_repo: ContactRepository,
    contact_factory: ContactFactory,
    pipeline_factory: PipelineFactory,
) -> None:
    # Setup
    org_id = UUID("********-1111-1111-1111-********1111")

    # Create contacts first
    contact1 = await contact_repo.insert(contact_factory.build(organization_id=org_id))
    contact2 = await contact_repo.insert(contact_factory.build(organization_id=org_id))
    contact3 = await contact_repo.insert(contact_factory.build(organization_id=org_id))

    # Create pipeline
    pipeline = await contact_repo.insert(pipeline_factory.build(organization_id=org_id))

    # Create contact pipeline associations
    await contact_repo.insert(
        ContactPipelineAssociation(
            id=uuid.uuid4(),
            organization_id=org_id,
            pipeline_id=pipeline.id,
            contact_id=contact1.id,
            is_primary=True,
            created_at=zoned_utc_now(),
            created_by_user_id=uuid.uuid4(),
            updated_at=zoned_utc_now(),
            updated_by_user_id=uuid.uuid4(),
        )
    )

    contact_pipeline_association_id = uuid.uuid4()
    await contact_repo.insert(
        ContactPipelineAssociation(
            id=contact_pipeline_association_id,
            organization_id=org_id,
            pipeline_id=pipeline.id,
            contact_id=contact2.id,
            is_primary=False,
            created_at=zoned_utc_now(),
            created_by_user_id=uuid.uuid4(),
            updated_at=zoned_utc_now(),
            updated_by_user_id=uuid.uuid4(),
        )
    )

    # Test listing contacts
    contacts = await contact_repo.list_by_pipeline_id(
        organization_id=org_id,
        pipeline_id=pipeline.id,
    )

    # Assert
    assert len(contacts) == 2
    contact_ids = {contact.id for contact in contacts}
    assert contact1.id in contact_ids
    assert contact2.id in contact_ids
    assert contact3.id not in contact_ids

    # Test archiving association
    association = await contact_repo.update_by_tenanted_primary_key(
        ContactPipelineAssociation,
        organization_id=org_id,
        primary_key_to_value={
            "id": contact_pipeline_association_id,
        },
        column_to_update={
            "archived_at": zoned_utc_now(),
            "archived_by_user_id": uuid.uuid4(),
            "updated_at": zoned_utc_now(),
            "updated_by_user_id": uuid.uuid4(),
        },
    )
    assert association is not None

    # Test after archiving
    contacts_after_archive = await contact_repo.list_by_pipeline_id(
        organization_id=org_id,
        pipeline_id=pipeline.id,
    )

    # Assert after archiving
    assert len(contacts_after_archive) == 1
    assert contacts_after_archive[0].id == contact1.id


async def test_find_contact_id_display_name_by_emails(
    contact_repo: ContactRepository,
    contact_factory: ContactFactory,
    faker: Faker,
) -> None:
    # Setup
    org_id = UUID("********-1111-1111-1111-********1111")
    user_id = UUID("*************-2222-2222-************")

    # Create contacts first
    contact1 = await contact_repo.insert(contact_factory.build(organization_id=org_id))
    contact2 = await contact_repo.insert(contact_factory.build(organization_id=org_id))

    email1 = faker.email()
    email2 = faker.email()
    email3 = faker.email()

    await contact_repo.upsert_contact_email(
        contact_email=ContactEmail(
            organization_id=org_id,
            contact_id=contact1.id,
            email=email1,
            is_contact_primary=True,
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
        ),
        contact_email_account_associations=[],
    )
    await contact_repo.upsert_contact_email(
        contact_email=ContactEmail(
            organization_id=org_id,
            contact_id=contact2.id,
            email=email2,
            is_contact_primary=True,
            created_by_user_id=user_id,
            updated_by_user_id=user_id,
        ),
        contact_email_account_associations=[],
    )

    # case 1
    result = await contact_repo.find_contact_id_display_name_by_emails(
        organization_id=org_id,
        emails={email1, email2, email3},
    )

    assert len(result) == 2
    result_emails = [r.from_email for r in result]
    assert email1 in result_emails
    assert email2 in result_emails
    for r in result:
        if r.from_email == email1:
            assert r.id == contact1.id
            assert r.display_name == contact1.display_name
            assert r.organization_id == org_id
        elif r.from_email == email2:
            assert r.id == contact2.id
            assert r.display_name == contact2.display_name
            assert r.organization_id == org_id

    # case 2
    empty_result = await contact_repo.find_contact_id_display_name_by_emails(
        organization_id=org_id,
        emails=set(),
    )
    assert empty_result == []

    # case 3
    await contact_repo.archive_contact(
        organization_id=org_id,
        contact_id=contact1.id,
        archived_by_user_id=user_id,
    )
    archived_result = await contact_repo.find_contact_id_display_name_by_emails(
        organization_id=org_id,
        emails={email1, email2},
    )
    assert len(archived_result) == 1
    assert archived_result[0].from_email == email2
    assert archived_result[0].id == contact2.id
    assert archived_result[0].display_name == contact2.display_name
    assert archived_result[0].organization_id == org_id


async def test_find_contacts_by_address(
    contact_repo: ContactRepository,
    contact_factory: ContactFactory,
    address_factory: AddressFactory,
    faker: Faker,
) -> None:
    # Setup - Create organizations
    org_id_1 = uuid4()
    org_id_2 = uuid4()

    # Create addresses that will be shared between contacts
    shared_address = address_factory.build(organization_id=org_id_1)
    unique_address = address_factory.build(organization_id=org_id_2)

    # Create contacts in org_id_1 with shared and unique addresses
    contact1_org1 = await contact_repo.insert(
        contact_factory.build(organization_id=org_id_1, address_id=shared_address.id)
    )
    contact2_org1 = await contact_repo.insert(
        contact_factory.build(organization_id=org_id_1, address_id=shared_address.id)
    )
    contact3_org1 = await contact_repo.insert(
        contact_factory.build(organization_id=org_id_1, address_id=unique_address.id)
    )

    # Create contact in org_id_2 with shared address
    contact1_org2 = await contact_repo.insert(
        contact_factory.build(organization_id=org_id_2, address_id=shared_address.id)
    )

    print("contact1_org1", contact1_org1)  # noqa: T201
    print("contact2_org1", contact2_org1)  # noqa: T201
    print("contact3_org1", contact3_org1)  # noqa: T201

    # Test finding contacts by address in org_id_1
    contacts_org1 = await contact_repo.find_contacts_by_address(
        organization_id=org_id_1, address_ids=[shared_address.id]
    )
    assert len(contacts_org1) == 2
    assert {c.id for c in contacts_org1} == {contact1_org1.id, contact2_org1.id}

    # Test finding contacts by unique address in org_id_1
    contacts_unique = await contact_repo.find_contacts_by_address(
        organization_id=org_id_1, address_ids=[unique_address.id]
    )
    assert len(contacts_unique) == 1
    assert contacts_unique[0].id == contact3_org1.id

    # Test finding contacts by address in org_id_2
    contacts_org2 = await contact_repo.find_contacts_by_address(
        organization_id=org_id_2, address_ids=[shared_address.id]
    )
    assert len(contacts_org2) == 1
    assert contacts_org2[0].id == contact1_org2.id

    # Test finding contacts with non-existent address
    contacts_non_existent = await contact_repo.find_contacts_by_address(
        organization_id=org_id_1, address_ids=[uuid4()]
    )
    assert len(contacts_non_existent) == 0

    # Test finding contacts with address in non-existent organization
    non_existent_org_id = uuid4()
    contacts_non_existent_org = await contact_repo.find_contacts_by_address(
        organization_id=non_existent_org_id, address_ids=[shared_address.id]
    )
    assert len(contacts_non_existent_org) == 0
