import uuid

from faker import Faker

from salestech_be.db.dao.email_account import EmailAccountRepository
from salestech_be.db.models.email_account import EmailAccount, EmailAccountType
from salestech_be.util.time import zoned_utc_now

faker = Faker()
outbound_domain_id = uuid.uuid4()
user_id_1 = uuid.uuid4()
user_id_2 = uuid.uuid4()
organization_id = uuid.uuid4()


async def create_test_email_account(
    email_account_repository: EmailAccountRepository,
    owner_user_id: uuid.UUID = user_id_1,
    archived: bool = False,
) -> EmailAccount:
    email = faker.email()
    now = zoned_utc_now()
    return await email_account_repository.insert(
        EmailAccount(
            id=uuid.uuid4(),
            owner_user_id=owner_user_id,
            email=email,
            type=EmailAccountType.REGULAR,
            outbound_domain_id=outbound_domain_id,
            first_name=faker.first_name(),
            last_name=faker.last_name(),
            is_default=True,
            imap_host=email,
            imap_port=993,
            imap_username=email,
            imap_password=faker.password(),
            smtp_host=email,
            smtp_port=465,
            smtp_username=email,
            smtp_password=faker.password(),
            seconds_delay_between_emails=60,
            organization_id=organization_id,
            created_by_user_id=uuid.uuid4(),
            created_at=now,
            archived_at=now if archived else None,
            active=True,
        )
    )


class TestEmailAccountRepository:
    async def test_find_email_accounts_with_permission_filter_all_accounts_no_filters(
        self,
        email_account_repository: EmailAccountRepository,
    ) -> None:
        """Test finding all accounts without any filters."""
        # Create test accounts for different users
        account1 = await create_test_email_account(email_account_repository, user_id_1)
        account2 = await create_test_email_account(email_account_repository, user_id_2)
        account3 = await create_test_email_account(email_account_repository, user_id_1)

        # Find all accounts without filters
        accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
            )
        )

        # Should return all active accounts
        account_ids = [account.id for account in accounts]
        assert account1.id in account_ids
        assert account2.id in account_ids
        assert account3.id in account_ids
        assert len(accounts) >= 3

    async def test_find_email_accounts_with_permission_filter_by_owner_user_id(
        self,
        email_account_repository: EmailAccountRepository,
    ) -> None:
        """Test filtering accounts by owner user ID."""
        # Create test accounts for different users
        account1 = await create_test_email_account(email_account_repository, user_id_1)
        account2 = await create_test_email_account(email_account_repository, user_id_2)
        account3 = await create_test_email_account(email_account_repository, user_id_1)

        # Find accounts for user_id_1 only
        accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                owner_user_id=user_id_1,
            )
        )

        # Should only return accounts owned by user_id_1
        account_ids = [account.id for account in accounts]
        assert account1.id in account_ids
        assert account3.id in account_ids
        assert account2.id not in account_ids

        # Verify all returned accounts belong to user_id_1
        for account in accounts:
            if account.id in [account1.id, account3.id]:
                assert account.owner_user_id == user_id_1

    async def test_find_email_accounts_with_permission_filter_by_specific_ids(
        self,
        email_account_repository: EmailAccountRepository,
    ) -> None:
        """Test filtering accounts by specific email account IDs."""
        # Create test accounts
        account1 = await create_test_email_account(email_account_repository, user_id_1)
        account2 = await create_test_email_account(email_account_repository, user_id_2)
        account3 = await create_test_email_account(email_account_repository, user_id_1)

        # Find specific accounts by IDs
        target_ids = [account1.id, account3.id]
        accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                email_account_ids=target_ids,
            )
        )

        # Should only return the specified accounts
        account_ids = [account.id for account in accounts]
        assert len(accounts) == 2
        assert account1.id in account_ids
        assert account3.id in account_ids
        assert account2.id not in account_ids

    async def test_find_email_accounts_with_permission_filter_by_ids_and_owner(
        self,
        email_account_repository: EmailAccountRepository,
    ) -> None:
        """Test filtering accounts by both specific IDs and owner user ID."""
        # Create test accounts
        account1 = await create_test_email_account(email_account_repository, user_id_1)
        account2 = await create_test_email_account(email_account_repository, user_id_2)
        account3 = await create_test_email_account(email_account_repository, user_id_1)

        # Find specific accounts by IDs but filter by owner
        target_ids = [account1.id, account2.id, account3.id]
        accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                email_account_ids=target_ids,
                owner_user_id=user_id_1,
            )
        )

        # Should only return accounts owned by user_id_1 from the specified IDs
        account_ids = [account.id for account in accounts]
        assert len(accounts) == 2
        assert account1.id in account_ids
        assert account3.id in account_ids
        assert account2.id not in account_ids  # Different owner

    async def test_find_email_accounts_with_permission_filter_include_archived(
        self,
        email_account_repository: EmailAccountRepository,
    ) -> None:
        """Test including archived accounts in results."""
        # Create active and archived accounts
        active_account = await create_test_email_account(
            email_account_repository, user_id_1, archived=False
        )
        archived_account = await create_test_email_account(
            email_account_repository, user_id_1, archived=True
        )

        # Find accounts without including archived
        active_accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                owner_user_id=user_id_1,
                include_archived=False,
            )
        )

        # Should only return active account
        active_account_ids = [account.id for account in active_accounts]
        assert active_account.id in active_account_ids
        assert archived_account.id not in active_account_ids

        # Find accounts including archived
        all_accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                owner_user_id=user_id_1,
                include_archived=True,
            )
        )

        # Should return both active and archived accounts
        all_account_ids = [account.id for account in all_accounts]
        assert active_account.id in all_account_ids
        assert archived_account.id in all_account_ids

    async def test_find_email_accounts_with_permission_filter_archived_with_specific_ids(
        self,
        email_account_repository: EmailAccountRepository,
    ) -> None:
        """Test finding archived accounts by specific IDs."""
        # Create active and archived accounts
        await create_test_email_account(
            email_account_repository, user_id_1, archived=False
        )
        archived_account = await create_test_email_account(
            email_account_repository, user_id_1, archived=True
        )

        # Find specific archived account by ID without including archived
        accounts_without_archived = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                email_account_ids=[archived_account.id],
                include_archived=False,
            )
        )

        # Should not return the archived account
        assert len(accounts_without_archived) == 0

        # Find specific archived account by ID including archived
        accounts_with_archived = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                email_account_ids=[archived_account.id],
                include_archived=True,
            )
        )

        # Should return the archived account
        assert len(accounts_with_archived) == 1
        assert accounts_with_archived[0].id == archived_account.id

    async def test_find_email_accounts_with_permission_filter_empty_results(
        self,
        email_account_repository: EmailAccountRepository,
    ) -> None:
        """Test cases that should return empty results."""
        # Create test account
        account = await create_test_email_account(email_account_repository, user_id_1)

        # Search for non-existent account IDs
        accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                email_account_ids=[uuid.uuid4()],  # Random UUID that doesn't exist
            )
        )
        assert len(accounts) == 0

        # Search for accounts with wrong owner
        accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                email_account_ids=[account.id],
                owner_user_id=user_id_2,  # Different owner
            )
        )
        assert len(accounts) == 0

        # Search in wrong organization
        accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=uuid.uuid4(),  # Different organization
                email_account_ids=[account.id],
            )
        )
        assert len(accounts) == 0

    async def test_find_email_accounts_with_permission_filter_complex_scenario(
        self,
        email_account_repository: EmailAccountRepository,
    ) -> None:
        """Test a complex scenario with multiple filters and conditions."""
        # Create various test accounts
        user1_active = await create_test_email_account(
            email_account_repository, user_id_1, archived=False
        )
        user1_archived = await create_test_email_account(
            email_account_repository, user_id_1, archived=True
        )
        user2_active = await create_test_email_account(
            email_account_repository, user_id_2, archived=False
        )
        await create_test_email_account(
            email_account_repository, user_id_2, archived=True
        )

        # Test: Find specific accounts for user1, including archived
        target_ids = [user1_active.id, user1_archived.id, user2_active.id]
        accounts = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                email_account_ids=target_ids,
                owner_user_id=user_id_1,
                include_archived=True,
            )
        )

        # Should return both user1 accounts (active and archived) but not user2's account
        account_ids = [account.id for account in accounts]
        assert len(accounts) == 2
        assert user1_active.id in account_ids
        assert user1_archived.id in account_ids
        assert user2_active.id not in account_ids

        # Test: Same query but without including archived
        accounts_no_archived = (
            await email_account_repository.find_email_accounts_with_permission_filter(
                organization_id=organization_id,
                email_account_ids=target_ids,
                owner_user_id=user_id_1,
                include_archived=False,
            )
        )

        # Should only return user1's active account
        account_ids_no_archived = [account.id for account in accounts_no_archived]
        assert len(accounts_no_archived) == 1
        assert user1_active.id in account_ids_no_archived
        assert user1_archived.id not in account_ids_no_archived
