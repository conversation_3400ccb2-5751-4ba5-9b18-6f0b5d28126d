from datetime import timedelta
from decimal import Decimal
from uuid import UUID, uuid4

import pytest
from sqlalchemy.exc import IntegrityError

from salestech_be.db.dao.pipeline_repository import PipelineRepository
from salestech_be.db.models.pipeline import Pipeline, PipelineStatus, PipelineUpdate
from salestech_be.db.models.pipeline_tracking import PipelineTracking
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from tests.test_util import assert_models


def _pipeline_tracking_to_insert(
    field_last_updated_at: ZoneRequiredDateTime | None = None,
    time_in_last_value: timedelta | None = None,
) -> PipelineTracking:
    return PipelineTracking(
        organization_id=uuid4(),
        pipeline_id=uuid4(),
        created_at=zoned_utc_now(),
        created_by_user_id=uuid4(),
        field_name="test",
        is_current=True,
        field_last_updated_at=field_last_updated_at,
        time_in_last_value=time_in_last_value,
    )


async def test_pipeline_tracking(
    pipeline_repository: PipelineRepository,
) -> None:
    # test no field_last_updated_at nor time_in_last_value
    pipeline_tracking_to_insert = _pipeline_tracking_to_insert()
    inserted = await pipeline_repository.insert(pipeline_tracking_to_insert)
    assert inserted == pipeline_tracking_to_insert

    # test field_last_updated_at specified and time_in_last_value auto-populated
    pipeline_tracking_to_insert = _pipeline_tracking_to_insert(
        field_last_updated_at=zoned_utc_now() - timedelta(minutes=1),
        time_in_last_value=None,
    )
    inserted = await pipeline_repository.insert(pipeline_tracking_to_insert)
    assert_models(
        act=inserted,
        exp=pipeline_tracking_to_insert,
        ignored_fields={"time_in_last_value"},
    )
    assert inserted.time_in_last_value
    assert inserted.field_last_updated_at
    assert inserted.time_in_last_value == (
        inserted.created_at - inserted.field_last_updated_at
    )

    # test field_last_updated_at is null and time_in_last_value is not null, that should raise an error due to DB check constraint
    pipeline_tracking_to_insert = _pipeline_tracking_to_insert(
        field_last_updated_at=None,
        time_in_last_value=timedelta(minutes=1),
    )
    with pytest.raises(
        IntegrityError, match="chk_field_last_updated_at_time_in_last_value_null"
    ):
        await pipeline_repository.insert(pipeline_tracking_to_insert)

    # test there can only be one current tracking record per pipeline per field
    pipeline_tracking_to_insert = _pipeline_tracking_to_insert()
    inserted = await pipeline_repository.insert(pipeline_tracking_to_insert)
    assert inserted == pipeline_tracking_to_insert
    assert inserted.is_current
    duplicate = strict_model_copy(inserted, id=uuid4())
    assert duplicate.is_current
    with pytest.raises(
        IntegrityError, match="pipeline_tracking_uniq_current_field_idx"
    ):
        await pipeline_repository.insert(duplicate)


async def test_automatic_pipeline_tracking_triggers(
    pipeline_repository: PipelineRepository,
) -> None:
    # Test pipeline insert triggers tracking records
    pipeline = await pipeline_repository.insert(
        Pipeline(
            id=uuid4(),
            organization_id=uuid4(),
            account_id=uuid4(),
            display_name="test",
            amount=Decimal("1000.00"),
            stage_id=uuid4(),
            state=PipelineStatus.DEAL,
            owner_user_id=uuid4(),
            created_at=zoned_utc_now(),
            sys_updated_at=zoned_utc_now(),
            updated_by_user_id=uuid4(),
            created_by_user_id=uuid4(),
            status=PipelineStatus.DEAL,
        )
    )

    # Verify amount tracking record was created
    amount_tracking_records = await pipeline_repository.list_pipeline_tracking_records(
        organization_id=pipeline.organization_id,
        pipeline_id=pipeline.id,
        field_name="amount",
        is_current=True,
    )
    assert len(amount_tracking_records) == 1
    amount_tracking = amount_tracking_records[0]
    assert amount_tracking.from_value_numeric is None
    assert amount_tracking.to_value_numeric == Decimal("1000.00")
    assert amount_tracking.field_last_updated_at is None
    assert amount_tracking.time_in_last_value is None
    assert amount_tracking.created_at == pipeline.updated_at
    assert amount_tracking.created_by_user_id == pipeline.updated_by_user_id

    # Verify stage tracking record was created
    stage_tracking_records = await pipeline_repository.list_pipeline_tracking_records(
        organization_id=pipeline.organization_id,
        pipeline_id=pipeline.id,
        field_name="stage_id",
        is_current=True,
    )
    assert len(stage_tracking_records) == 1
    stage_tracking = stage_tracking_records[0]
    assert stage_tracking.from_value_uuid is None
    assert stage_tracking.to_value_uuid == pipeline.stage_id
    assert stage_tracking.field_last_updated_at is None
    assert stage_tracking.time_in_last_value is None
    assert stage_tracking.created_at == pipeline.updated_at
    assert stage_tracking.created_by_user_id == pipeline.updated_by_user_id

    # Test pipeline update triggers new tracking records
    updated_pipeline = await pipeline_repository.patch_pipeline(
        organization_id=pipeline.organization_id,
        pipeline_id=pipeline.id,
        pipeline_update=PipelineUpdate(
            amount=Decimal("2000.00"), stage_id=uuid4(), updated_by_user_id=uuid4()
        ),
    )

    # Verify new amount tracking record was created
    new_amount_tracking_records = (
        await pipeline_repository.list_pipeline_tracking_records(
            organization_id=updated_pipeline.organization_id,
            pipeline_id=updated_pipeline.id,
            field_name="amount",
            is_current=True,
        )
    )
    assert len(new_amount_tracking_records) == 1
    new_amount_tracking = new_amount_tracking_records[0]
    assert new_amount_tracking.from_value_numeric == Decimal("1000.00")
    assert new_amount_tracking.to_value_numeric == Decimal("2000.00")
    assert new_amount_tracking.field_last_updated_at == amount_tracking.created_at
    assert new_amount_tracking.time_in_last_value
    assert new_amount_tracking.field_last_updated_at
    assert new_amount_tracking.time_in_last_value == (
        new_amount_tracking.created_at - new_amount_tracking.field_last_updated_at
    )
    assert new_amount_tracking.created_at == updated_pipeline.updated_at
    assert new_amount_tracking.created_by_user_id == updated_pipeline.updated_by_user_id

    # Verify old amount tracking record is no longer current
    old_amount_tracking_records = (
        await pipeline_repository.list_pipeline_tracking_records(
            organization_id=pipeline.organization_id,
            pipeline_id=pipeline.id,
            field_name="amount",
            is_current=False,
        )
    )
    assert len(old_amount_tracking_records) == 1
    assert old_amount_tracking_records[0].id == amount_tracking.id

    # Verify new stage tracking record was created
    new_stage_tracking_records = (
        await pipeline_repository.list_pipeline_tracking_records(
            organization_id=updated_pipeline.organization_id,
            pipeline_id=updated_pipeline.id,
            field_name="stage_id",
            is_current=True,
        )
    )
    assert len(new_stage_tracking_records) == 1
    new_stage_tracking = new_stage_tracking_records[0]
    assert new_stage_tracking.from_value_uuid == pipeline.stage_id
    assert new_stage_tracking.to_value_uuid == updated_pipeline.stage_id
    assert new_stage_tracking.field_last_updated_at == stage_tracking.created_at
    assert new_stage_tracking.time_in_last_value
    assert new_stage_tracking.field_last_updated_at
    assert new_stage_tracking.time_in_last_value == (
        new_stage_tracking.created_at - new_stage_tracking.field_last_updated_at
    )
    assert new_stage_tracking.created_at == updated_pipeline.updated_at
    assert new_stage_tracking.created_by_user_id == updated_pipeline.updated_by_user_id

    # Verify old stage tracking record is no longer current
    old_stage_tracking_records = (
        await pipeline_repository.list_pipeline_tracking_records(
            organization_id=pipeline.organization_id,
            pipeline_id=pipeline.id,
            field_name="stage_id",
            is_current=False,
        )
    )
    assert len(old_stage_tracking_records) == 1
    assert old_stage_tracking_records[0].id == stage_tracking.id

    # Test no tracking generated when no changes
    pipeline_update_no_changes = PipelineUpdate(
        updated_by_user_id=uuid4(), display_name="Updated display name"
    )
    updated_pipeline_no_changes = await pipeline_repository.patch_pipeline(
        organization_id=pipeline.organization_id,
        pipeline_id=pipeline.id,
        pipeline_update=pipeline_update_no_changes,
    )
    assert (
        updated_pipeline_no_changes.display_name
        == pipeline_update_no_changes.display_name
    )

    # Verify no new amount tracking records
    amount_tracking_records = await pipeline_repository.list_pipeline_tracking_records(
        organization_id=pipeline.organization_id,
        pipeline_id=pipeline.id,
        field_name="amount",
        is_current=True,
    )
    assert len(amount_tracking_records) == 1
    assert amount_tracking_records[0].id == new_amount_tracking.id

    # Verify no new stage tracking records
    stage_tracking_records = await pipeline_repository.list_pipeline_tracking_records(
        organization_id=pipeline.organization_id,
        pipeline_id=pipeline.id,
        field_name="stage_id",
        is_current=True,
    )
    assert len(stage_tracking_records) == 1
    assert stage_tracking_records[0].id == new_stage_tracking.id

    # Test tracking when amount updated to null
    pipeline_update_null_amount = PipelineUpdate(
        updated_by_user_id=uuid4(), amount=None
    )
    updated_pipeline_null_amount = await pipeline_repository.patch_pipeline(
        organization_id=pipeline.organization_id,
        pipeline_id=pipeline.id,
        pipeline_update=pipeline_update_null_amount,
    )
    assert updated_pipeline_null_amount.amount is None

    # Verify new amount tracking record with null value
    null_amount_tracking_records = (
        await pipeline_repository.list_pipeline_tracking_records(
            organization_id=pipeline.organization_id,
            pipeline_id=pipeline.id,
            field_name="amount",
            is_current=True,
        )
    )
    assert len(null_amount_tracking_records) == 1
    null_amount_tracking = null_amount_tracking_records[0]
    assert null_amount_tracking.from_value_numeric == updated_pipeline.amount
    assert null_amount_tracking.to_value_numeric is None
    assert null_amount_tracking.field_last_updated_at == new_amount_tracking.created_at
    assert null_amount_tracking.time_in_last_value
    assert null_amount_tracking.field_last_updated_at
    assert null_amount_tracking.time_in_last_value == (
        null_amount_tracking.created_at - null_amount_tracking.field_last_updated_at
    )
    assert null_amount_tracking.created_at == updated_pipeline_null_amount.updated_at
    assert (
        null_amount_tracking.created_by_user_id
        == updated_pipeline_null_amount.updated_by_user_id
    )

    # Verify previous amount tracking is no longer current
    old_amount_tracking_records = (
        await pipeline_repository.list_pipeline_tracking_records(
            organization_id=pipeline.organization_id,
            pipeline_id=pipeline.id,
            field_name="amount",
            is_current=False,
        )
    )
    assert len(old_amount_tracking_records) == 2
    assert new_amount_tracking.id in {r.id for r in old_amount_tracking_records}
    assert amount_tracking.id in {r.id for r in old_amount_tracking_records}


async def test_find_pipelines_by_display_name_account(
    pipeline_repository: PipelineRepository,
) -> None:
    # Create test data
    organization_id = uuid4()
    account_id = uuid4()
    user_id = uuid4()

    # Create a pipeline with a specific display name and account ID
    pipeline1 = Pipeline(
        id=uuid4(),
        organization_id=organization_id,
        display_name="Test Pipeline 1",
        account_id=account_id,
        owner_user_id=user_id,
        created_by_user_id=user_id,
        updated_by_user_id=user_id,
        stage_id=uuid4(),
        status=PipelineStatus.PROSPECT,
        state=PipelineStatus.PROSPECT,
    )

    # Create another pipeline with the same account but different display name
    pipeline2 = Pipeline(
        id=uuid4(),
        organization_id=organization_id,
        display_name="Test Pipeline 2",
        account_id=account_id,
        owner_user_id=user_id,
        created_by_user_id=user_id,
        updated_by_user_id=user_id,
        stage_id=uuid4(),
        status=PipelineStatus.PROSPECT,
        state=PipelineStatus.PROSPECT,
    )

    # Insert both pipelines
    await pipeline_repository.insert(pipeline1)
    await pipeline_repository.insert(pipeline2)

    # Find pipeline by display name and account ID
    result = await pipeline_repository.find_pipelines_by_display_name_account(
        organization_id=organization_id,
        display_name="Test Pipeline 1",
        account_id=account_id,
    )

    # Verify that only the matching pipeline is returned
    assert len(result) == 1
    assert result[0].id == pipeline1.id
    assert result[0].display_name == "Test Pipeline 1"
    assert result[0].account_id == account_id

    # Create an archived pipeline with the same display name and account ID
    pipeline3 = Pipeline(
        id=uuid4(),
        organization_id=organization_id,
        display_name="Test Pipeline 1",
        account_id=account_id,
        owner_user_id=user_id,
        created_by_user_id=user_id,
        updated_by_user_id=user_id,
        stage_id=uuid4(),
        status=PipelineStatus.PROSPECT,
        state=PipelineStatus.PROSPECT,
        archived_at=zoned_utc_now(),
        archived_by_user_id=user_id,
    )

    # Insert the archived pipeline
    await pipeline_repository.insert(pipeline3)

    # Find pipeline by display name and account ID, excluding archived
    result = await pipeline_repository.find_pipelines_by_display_name_account(
        organization_id=organization_id,
        display_name="Test Pipeline 1",
        account_id=account_id,
        exclude_archived=True,
    )

    # Verify that only the non-archived matching pipeline is returned
    assert len(result) == 1
    assert result[0].id == pipeline1.id

    # Find pipeline by display name and account ID, including archived
    result = await pipeline_repository.find_pipelines_by_display_name_account(
        organization_id=organization_id,
        display_name="Test Pipeline 1",
        account_id=account_id,
        exclude_archived=False,
    )

    # Verify that both matching pipelines are returned
    assert len(result) == 2
    assert {p.id for p in result} == {pipeline1.id, pipeline3.id}


async def test_map_contact_associations_for_org_pipelines_sql_grouped_for_empty_sets(
    pipeline_repository: PipelineRepository,
) -> None:
    # Create test data
    organization_id = uuid4()

    # Use set[UUID] instead of Mapping
    empty_set: set[UUID] = set()
    result = await pipeline_repository.map_contact_associations_for_org_pipelines_sql_grouped(
        organization_id=organization_id, db_pipeline_ids=empty_set
    )

    # Check that the frozendict returned is empty
    assert len(result) == 0
