import uuid
from datetime import timed<PERSON><PERSON>
from http import HTTPStatus
from uuid import uuid4

import pytest
from faker import Faker

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.query_util.legacy.sort_schema import OrderEnum
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.query_util.sort_schema import Sorter, SortingSpec
from salestech_be.common.schema_manager.std_object_field_identifier import (
    MeetingField,
    StdSelectListIdentifier,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    MeetingRelationship,
)
from salestech_be.common.type.metadata.schema import QualifiedField
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.common.service_api_patch_spec import (
    AcceptCurrentAIRecPatchSpec,
    PatchStandardObjectBySourceRequest,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.meeting.meeting_ai_rec_service import (
    PatchMeetingSalesActionsRequest,
)
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    CreatePipelineRequest,
    FullContactPipelineAssociationRequests,
)
from salestech_be.db.dao.account_repository import AccountRepository
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.models.crm_ai_rec import CrmAIRecType
from salestech_be.db.models.meeting import (
    Meeting,
    MeetingAttendee,
    MeetingCancelReason,
    MeetingInvitee,
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingStatus,
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.common.container import ListCustomizableEntityRequest
from salestech_be.web.api.contact.schema import CreateContactRequest
from salestech_be.web.api.meeting.schema import PatchMeetingRequest
from tests.integration.web.api.common.test_utils import create_meeting
from tests.integration.web.api.util.api_client import APITestError
from tests.integration.web.api.util.common_api_client import (
    CommonAPIClient,
)


# test meeting crud with picklist custom field data
async def test_meeting_crud_with_picklist_field_data(
    faker: Faker,
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    # Setup custom fields for account
    (
        single_select_list,
        multi_select_list,
        single_single_select_field,
        multi_single_select_field,
    ) = await common_api_client.setup_custom_fields_for_standard_object(
        standard_object=ExtendableStandardObject.meeting
    )
    assert single_select_list
    assert len(single_select_list.select_list_values) == 2
    single_select_list_value_0_id = single_select_list.select_list_values[0].id
    # single_select_list_value_1_id = single_select_list.select_list_values[1].id

    assert multi_select_list
    assert len(multi_select_list.select_list_values) == 2
    multi_select_list_value_0_id = multi_select_list.select_list_values[0].id
    # multi_select_list_value_1_id = multi_select_list.select_list_values[1].id

    #################################Create/Patch/List/Filter account with single select field##################################
    # create meeting
    meeting_1 = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        user_id=uuid4(),  # make sure the reference_id is unique
        organization_id=common_api_client.api_test_client.default_organization_id,
    )

    assert meeting_1
    assert meeting_1.id

    # patch meeting
    patch_meeting_request = await common_api_client.patch_meeting(
        meeting_id=meeting_1.id,
        patch_request=PatchMeetingRequest(
            custom_field_data={
                single_single_select_field.id: single_select_list_value_0_id,
            },
        ),
    )
    assert patch_meeting_request

    # negative test for invalid single select field id
    with pytest.raises(APITestError) as e:
        await common_api_client.patch_meeting(
            meeting_id=meeting_1.id,
            patch_request=PatchMeetingRequest(
                custom_field_data={single_single_select_field.id: uuid4()},
            ),
        )
    assert e.value.status == HTTPStatus.NOT_FOUND
    assert e.value.extra.get("error") == ResourceNotFoundError.__name__

    # TODO: retrieve meeting and check if the single select field is updated

    #################################Create/Patch/List/Filter meeting with multi select field##################################
    meeting_2 = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),  # make sure the reference_id is unique
    )
    assert meeting_2
    assert meeting_2.id

    # patch meeting
    patch_meeting_request = await common_api_client.patch_meeting(
        meeting_id=meeting_2.id,
        patch_request=PatchMeetingRequest(
            custom_field_data={
                multi_single_select_field.id: {multi_select_list_value_0_id},
            },
        ),
    )
    assert patch_meeting_request

    # negative test for invalid multi select field id
    with pytest.raises(APITestError) as e:
        await common_api_client.patch_meeting(
            meeting_id=meeting_2.id,
            patch_request=PatchMeetingRequest(
                custom_field_data={multi_single_select_field.id: {uuid4()}}
            ),
        )
    assert e.value.status == HTTPStatus.NOT_FOUND
    assert e.value.extra.get("error") == ResourceNotFoundError.__name__

    # TODO: retrieve meeting and check if the multi select field is updated


async def test_meeting_list_v2(
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
) -> None:
    # data preparation
    now = zoned_utc_now()
    user_id = common_api_client.api_test_client.default_user_id

    attendee_contact_id_1 = uuid4()
    attendee_contact_id_2 = uuid4()
    attendee_user_id_1 = uuid4()
    attendee_user_id_2 = uuid4()
    invitee_contact_id_1 = uuid4()
    invitee_contact_id_2 = uuid4()
    invitee_user_id_1 = uuid4()
    invitee_user_id_2 = uuid4()
    rescheduled_from_id = uuid4()
    event_schedule_id = uuid4()

    # prepare meeting
    await meeting_repository.insert(
        Meeting(
            id=uuid4(),
            organization_id=common_api_client.api_test_client.default_organization_id,
            reference_id="refer_id",
            reference_id_type=MeetingReferenceIdType.VOICE,
            meeting_url="www.google.com",
            meeting_platform=MeetingProvider.ZOOM,
            started_at=now,
            ended_at=now,
            metadata=None,
            created_at=now,
            updated_at=now,
            conferencing_details={"a": "b"},
            starts_at=now,
            ends_at=now,
            title="title",
            description="description",
            location="LA",
            organizer_user_id=user_id,
            consent_id=uuid4(),
            verbal_consent_at=now,
            status=MeetingStatus.SCHEDULED,
            event_schedule_id=event_schedule_id,
            created_by_user_id=user_id,
            cancel_reason=MeetingCancelReason.NO_ANSWER,
            is_rescheduled=False,
            rescheduled_from_id=rescheduled_from_id,
            invitees=[
                MeetingInvitee(
                    contact_id=invitee_contact_id_1,
                    user_id=invitee_user_id_1,
                    is_organizer=True,
                ),
                MeetingInvitee(
                    contact_id=invitee_contact_id_2,
                    user_id=invitee_user_id_2,
                    is_organizer=False,
                ),
            ],
            attendees=[
                MeetingAttendee(
                    contact_id=attendee_contact_id_1,
                    user_id=attendee_user_id_1,
                    is_organizer=True,
                ),
                MeetingAttendee(
                    contact_id=attendee_contact_id_2,
                    user_id=attendee_user_id_2,
                    is_organizer=False,
                ),
            ],
        )
    )

    list_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    assert len(list_meetings.list_data) == 1

    list_meeting = list_meetings.list_data[0].data
    assert list_meeting.meeting_url == "www.google.com"
    assert list_meeting.meeting_platform == MeetingProvider.ZOOM
    assert list_meeting.invitee_contact_id_list == [
        invitee_contact_id_1,
        invitee_contact_id_2,
    ]
    assert list_meeting.invitee_user_id_list == [invitee_user_id_1, invitee_user_id_2]
    assert list_meeting.attendee_contact_id_list == [
        attendee_contact_id_1,
        attendee_contact_id_2,
    ]
    assert list_meeting.attendee_user_id_list == [
        attendee_user_id_1,
        attendee_user_id_2,
    ]
    assert list_meeting.starts_at == now
    assert list_meeting.ends_at == now
    assert list_meeting.started_at == now
    assert list_meeting.ended_at == now
    assert list_meeting.title == "title"
    assert list_meeting.description == "<div>description</div>"
    assert list_meeting.organizer_user_id == user_id
    assert list_meeting.event_schedule_id == event_schedule_id
    assert list_meeting.created_by_user_id == user_id
    assert not list_meeting.is_sales_meeting
    assert list_meeting.is_recorded  # reference id type == MeetingReferenceIdType.VOICE

    # test query by record id
    get_meeting = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            record_id=list_meeting.id,
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    assert len(get_meeting.list_data) == 1

    # fiter spec
    meeting_filter_spec = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.organizer_user_id,),
                                fetch_relationship_ids=(
                                    MeetingRelationship.meeting__to__organizer_user,
                                ),
                            ),
                            operator=MatchOperator.EQ,
                            value=user_id,
                        ),
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.created_by_user_id,),
                                fetch_relationship_ids=(
                                    MeetingRelationship.meeting__to__created_by_user,
                                ),
                            ),
                            operator=MatchOperator.EQ,
                            value=user_id,
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )
    assert len(meeting_filter_spec.list_data) == 1


async def test_list_meetings_with_baseline_filter(
    faker: Faker,
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    """Test that the baseline filter extraction works correctly for list meetings endpoint."""
    # Create meetings with different start times
    now = zoned_utc_now()
    yesterday = now - timedelta(days=1)
    tomorrow = now + timedelta(days=1)
    next_week = now + timedelta(days=7)

    # Create 3 meetings first
    meeting_yesterday = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid.uuid4(),
    )

    meeting_tomorrow = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid.uuid4(),
    )

    meeting_next_week = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid.uuid4(),
    )

    # Now patch each meeting to set the starts_at time
    await meeting_repository.update_by_tenanted_primary_key(
        Meeting,
        organization_id=common_api_client.api_test_client.default_organization_id,
        primary_key_to_value={"id": meeting_yesterday.id},
        column_to_update={"starts_at": yesterday},
    )

    await meeting_repository.update_by_tenanted_primary_key(
        Meeting,
        organization_id=common_api_client.api_test_client.default_organization_id,
        primary_key_to_value={"id": meeting_tomorrow.id},
        column_to_update={"starts_at": tomorrow},
    )

    await meeting_repository.update_by_tenanted_primary_key(
        Meeting,
        organization_id=common_api_client.api_test_client.default_organization_id,
        primary_key_to_value={"id": meeting_next_week.id},
        column_to_update={"starts_at": next_week},
    )

    # Test 1: List all meetings without filters
    all_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.ASC,
                    ),
                ),
            ),
        ),
    )

    # Should return all 3 meetings
    assert len(all_meetings.list_data) >= 3
    meeting_ids = {meeting.data.id for meeting in all_meetings.list_data}
    assert meeting_yesterday.id in meeting_ids
    assert meeting_tomorrow.id in meeting_ids
    assert meeting_next_week.id in meeting_ids

    # Test 2: Filter meetings with starts_at > now (should return tomorrow and next week meetings)
    future_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.starts_at,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.GT,
                            value=now,
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.ASC,
                    ),
                ),
            ),
        ),
    )

    # Should return 2 meetings (tomorrow and next week)
    assert len(future_meetings.list_data) == 2
    future_meeting_ids = {meeting.data.id for meeting in future_meetings.list_data}
    assert meeting_yesterday.id not in future_meeting_ids
    assert meeting_tomorrow.id in future_meeting_ids
    assert meeting_next_week.id in future_meeting_ids

    # Test 3: Filter meetings with starts_at between now and next week
    between_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.starts_at,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.GT,
                            value=now,
                        ),
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.starts_at,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.LT,
                            value=next_week,
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.ASC,
                    ),
                ),
            ),
        ),
    )

    # Should return 1 meeting (tomorrow)
    assert len(between_meetings.list_data) == 1
    assert between_meetings.list_data[0].data.id == meeting_tomorrow.id

    # Test 4: Filter by specific meeting ID
    specific_meeting = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            record_id=meeting_yesterday.id,
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.ASC,
                    ),
                ),
            ),
        ),
    )

    # Should return 1 meeting (yesterday)
    assert len(specific_meeting.list_data) == 1
    assert specific_meeting.list_data[0].data.id == meeting_yesterday.id

    # Test 5: Filter by recorded value (False)
    unrecorded_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.is_recorded,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.EQ,
                            value=False,
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.ASC,
                    ),
                ),
            ),
        ),
    )

    # Should return all meetings - none are recorded
    assert len(unrecorded_meetings.list_data) == 3

    # Test 6: Filter by recorded value (True)
    recorded_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.is_recorded,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.EQ,
                            value=True,
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.ASC,
                    ),
                ),
            ),
        ),
    )

    # Should no meetings - none are recorded
    assert len(recorded_meetings.list_data) == 0


async def test_list_meetings_with_complex_filters(
    faker: Faker,
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    """Test more complex filter scenarios for the list meetings endpoint."""
    # Create meetings with different properties
    now = zoned_utc_now()
    user_id = common_api_client.api_test_client.default_user_id

    # Create a meeting with specific properties
    meeting = await meeting_repository.insert(
        Meeting(
            id=uuid.uuid4(),
            organization_id=common_api_client.api_test_client.default_organization_id,
            reference_id="test_reference_id",
            reference_id_type=MeetingReferenceIdType.VOICE,
            meeting_url="https://example.com/meeting",
            meeting_platform=MeetingProvider.GMEET,
            started_at=now,
            ended_at=now + timedelta(hours=1),
            metadata=None,
            created_at=now,
            updated_at=now,
            conferencing_details={"provider": "zoom"},
            starts_at=now,
            ends_at=now + timedelta(hours=1),
            title="Test Meeting with Complex Filters",
            description="This is a test meeting for complex filters",
            location="Virtual",
            organizer_user_id=user_id,
            consent_id=uuid.uuid4(),
            verbal_consent_at=now,
            status=MeetingStatus.SCHEDULED,
            created_by_user_id=user_id,
        )
    )

    # Create a second meeting with different properties
    second_meeting = await meeting_repository.insert(
        Meeting(
            id=uuid.uuid4(),
            organization_id=common_api_client.api_test_client.default_organization_id,
            reference_id="another_reference_id",
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            meeting_url="https://example.com/another-meeting",
            meeting_platform=MeetingProvider.GMEET,
            started_at=now - timedelta(days=1),
            ended_at=now - timedelta(days=1) + timedelta(hours=2),
            metadata={"tags": ["important", "follow-up"]},
            created_at=now - timedelta(days=2),
            updated_at=now - timedelta(days=2),
            conferencing_details={"provider": "teams"},
            starts_at=now - timedelta(days=1),
            ends_at=now - timedelta(days=1) + timedelta(hours=2),
            title="Another Meeting with Different Properties",
            description="This is another test meeting with different properties",
            location="Conference Room A",
            organizer_user_id=uuid.uuid4(),  # Different organizer
            consent_id=uuid.uuid4(),
            verbal_consent_at=now - timedelta(days=1),
            status=MeetingStatus.COMPLETED,
            created_by_user_id=user_id,
        )
    )

    # Test 1: Filter by organizer user ID
    organizer_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.organizer_user_id,),
                                fetch_relationship_ids=(
                                    MeetingRelationship.meeting__to__organizer_user,
                                ),
                            ),
                            operator=MatchOperator.EQ,
                            value=user_id,
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    # Should include our test meeting
    assert len(organizer_meetings.list_data) == 1
    assert organizer_meetings.list_data[0].data.id == meeting.id

    # Test 2: Filter by meeting title
    title_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.title_,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.CONTAINS,
                            value="Complex Filters",
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    # Should return our test meeting
    assert len(title_meetings.list_data) == 1
    assert title_meetings.list_data[0].data.id == meeting.id

    # Test 3: Combine multiple filters (title and organizer)
    combined_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.title_,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.CONTAINS,
                            value="Complex Filters",
                        ),
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.organizer_user_id,),
                                fetch_relationship_ids=(
                                    MeetingRelationship.meeting__to__organizer_user,
                                ),
                            ),
                            operator=MatchOperator.EQ,
                            value=user_id,
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    # Should return our test meeting
    assert len(combined_meetings.list_data) == 1
    assert combined_meetings.list_data[0].data.id == meeting.id

    # Test 4: Filter that should return no results
    no_result_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.title_,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.EQ,
                            value="This title does not exist",
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    # Test 5: Filter by starts_at
    starts_at_filter_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.starts_at,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.GTE,
                            value=meeting.starts_at,
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    # Should return our test meeting
    assert len(starts_at_filter_meetings.list_data) == 1
    assert starts_at_filter_meetings.list_data[0].data.id == meeting.id

    # Test with a date range
    date_range_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.starts_at,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.GTE,
                            value=meeting.starts_at - timedelta(days=1),
                        ),
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.starts_at,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.LTE,
                            value=meeting.starts_at + timedelta(days=1),
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    # Should return both meetings if they're within the date range
    expected_count = sum(
        1
        for m in [meeting, second_meeting]
        if meeting.starts_at - timedelta(days=1)
        <= m.starts_at
        <= meeting.starts_at + timedelta(days=1)
    )
    assert len(date_range_meetings.list_data) == expected_count
    # Should return no meetings
    assert len(no_result_meetings.list_data) == 0


async def test_list_meetings_with_only_include_object_ids(
    faker: Faker,
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    # Create multiple meetings first
    meeting_1 = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),  # make sure the reference_id is unique
    )

    _meeting_2 = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),  # make sure the reference_id is unique
    )

    # Test fetching only specific meetings
    list_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            record_id=meeting_1.id,
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.DESC,
                    ),
                ),
            ),
        ),
    )

    # Should only return meeting_1
    assert len(list_meetings.list_data) == 1
    assert list_meetings.list_data[0].data.id == meeting_1.id


async def test_list_meetings_with_live_meeting_filter(
    faker: Faker,
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    """Test that active meetings are shown regardless of their start time when applying filters."""
    # Create a meeting that started in the past but is still active
    now = zoned_utc_now()
    one_hour_ago = now - timedelta(hours=1)
    future_time = now + timedelta(days=365)  # Set future time far ahead

    invitee_user_1_id = uuid4()
    invitee_user_2_id = uuid4()

    # Create the base meeting
    live_meeting = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),
        invitees=[
            MeetingInvitee(
                user_id=invitee_user_1_id,
                is_organizer=True,
            ),
            MeetingInvitee(
                user_id=invitee_user_2_id,
                is_organizer=False,
            ),
        ],
    )

    # Update the meeting to be active and have started in the past
    await meeting_repository.update_by_tenanted_primary_key(
        Meeting,
        organization_id=common_api_client.api_test_client.default_organization_id,
        primary_key_to_value={"id": live_meeting.id},
        column_to_update={
            "starts_at": one_hour_ago,
            "started_at": one_hour_ago,
            "status": MeetingStatus.ACTIVE,
            "meeting_platform": MeetingProvider.GMEET,  # Not VOICE to avoid the none_of filter
        },
    )

    # Create another meeting that's scheduled for the future
    future_meeting = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),
    )

    await meeting_repository.update_by_tenanted_primary_key(
        Meeting,
        organization_id=common_api_client.api_test_client.default_organization_id,
        primary_key_to_value={"id": future_meeting.id},
        column_to_update={
            "starts_at": now + timedelta(hours=1),
            "status": MeetingStatus.SCHEDULED,
            "meeting_platform": MeetingProvider.GMEET,
        },
    )

    # Create a canceled meeting
    canceled_meeting = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),
    )

    await meeting_repository.update_by_tenanted_primary_key(
        Meeting,
        organization_id=common_api_client.api_test_client.default_organization_id,
        primary_key_to_value={"id": canceled_meeting.id},
        column_to_update={
            "starts_at": now + timedelta(hours=2),
            "status": MeetingStatus.CANCELED,
            "meeting_platform": MeetingProvider.GMEET,
        },
    )

    # Test the combined filter scenario
    filtered_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        # Status != canceled
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.meeting_status,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.NE,
                            value=MeetingStatus.CANCELED,
                        ),
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.invitee_user_id_list,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.CONTAINS,
                            value=invitee_user_1_id,
                        ),
                        # (starts_at > future_time OR status = active)
                        CompositeFilter(
                            any_of=[
                                ValueFilter(
                                    field=QualifiedField(
                                        path=(MeetingField.starts_at,),
                                        fetch_relationship_ids=(),
                                    ),
                                    operator=MatchOperator.GT,
                                    value=future_time,
                                ),
                                ValueFilter(
                                    field=QualifiedField(
                                        path=(MeetingField.meeting_status,),
                                        fetch_relationship_ids=(),
                                    ),
                                    operator=MatchOperator.EQ,
                                    value="active",
                                ),
                            ]
                        ),
                        # NOT voice platform OR is_recorded must be true
                        CompositeFilter(
                            any_of=[
                                ValueFilter(
                                    field=QualifiedField(
                                        path=(MeetingField.meeting_platform,),
                                        fetch_relationship_ids=(),
                                    ),
                                    operator=MatchOperator.NE,
                                    value=MeetingProvider.VOICE,
                                ),
                                ValueFilter(
                                    field=QualifiedField(
                                        path=(MeetingField.is_recorded,),
                                        fetch_relationship_ids=(),
                                    ),
                                    operator=MatchOperator.EQ,
                                    value=True,
                                ),
                            ]
                        ),
                    ]
                ),
            ),
            sorting_spec=SortingSpec(
                primary_object_identifier=MeetingV2.object_id,
                ordered_sorters=(
                    Sorter(
                        field=QualifiedField(path=(MeetingField.starts_at,)),
                        order=OrderEnum.ASC,
                    ),
                ),
            ),
        ),
    )

    # Should return the live meeting (because it's active) but not the canceled meeting
    # Future meeting is filtered out because its start time is before future_time
    assert len(filtered_meetings.list_data) == 1
    assert filtered_meetings.list_data[0].data.id == live_meeting.id
    assert filtered_meetings.list_data[0].data.meeting_status == MeetingStatus.ACTIVE
    assert filtered_meetings.list_data[0].data.started_at == one_hour_ago

    # test filter on non-existing invitee user id returns nothing
    filtered_meetings = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=True,
            filter_spec=FilterSpec(
                primary_object_identifier=MeetingV2.object_id,
                filter=CompositeFilter(
                    all_of=[
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.invitee_user_id_list,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.CONTAINS,
                            value=uuid4(),
                        ),
                        ValueFilter(
                            field=QualifiedField(
                                path=(MeetingField.id,),
                                fetch_relationship_ids=(),
                            ),
                            operator=MatchOperator.EQ,
                            value=live_meeting.id,
                        ),
                    ]
                ),
            ),
        ),
    )
    assert len(filtered_meetings.list_data) == 0


async def test_patch_meeting_sales_action_types(
    faker: Faker,
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    # Create a meeting
    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),
    )

    # Patch the meeting
    patched_meeting = await common_api_client.patch_meeting(
        meeting_id=meeting.id,
        patch_request=PatchMeetingRequest(
            sales_action_types=[StandardSalesActionType.INTRO]
        ),
    )

    # Verify the sales action types are patched
    assert patched_meeting.sales_action_types == [StandardSalesActionType.INTRO]


async def test_patch_meeting_sales_action_types_with_ai_rec(
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    # Create a meeting
    db_meeting = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),
    )

    assert not db_meeting.sales_action_types

    patch_request = PatchMeetingRequest(
        sales_action_types=[StandardSalesActionType.INTRO]
    )

    # Note: this is usually done by AI async workflow, hence no API exposure
    await common_api_client.temp_create_ai_recs_from_patch_request(
        meeting_id=db_meeting.id,
        ai_rec_type=CrmAIRecType.correction,
        patch_request=PatchMeetingSalesActionsRequest(
            sales_action_types=patch_request.sales_action_types,
        ),
    )

    # Now when we get meeting, we should see the pending AI rec
    meeting_result = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=False,
            record_id=db_meeting.id,
        ),
    )
    assert len(meeting_result.list_data) == 1
    meeting = meeting_result.list_data[0].data
    assert meeting.property_metadata
    assert len(meeting.property_metadata) == 1
    assert meeting.property_metadata[0].field_path == ("sales_action_types",)
    assert meeting.property_metadata[0].ai_rec
    assert meeting.property_metadata[0].ai_rec.rec_type == CrmAIRecType.correction
    assert meeting.property_metadata[0].ai_rec.rec_value == [
        StandardSalesActionType.INTRO
    ]

    # Note: this need to be a proper API call, will be added separeately
    patched_meeting = await common_api_client.patch_meeting_by_source(
        meeting_id=meeting.id,
        patch_request=PatchStandardObjectBySourceRequest(
            specs=[
                AcceptCurrentAIRecPatchSpec(
                    field_path=("sales_action_types",),
                    ai_rec_id=meeting.property_metadata[0].ai_rec.id,
                )
            ],
        ),
    )

    assert patched_meeting.sales_action_types == [StandardSalesActionType.INTRO]

    # post accepting, the ai rec should be gone
    post_patched_meeting = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=False,
            record_id=meeting.id,
        ),
    )
    assert len(post_patched_meeting.list_data) == 1
    property_metadata = post_patched_meeting.list_data[0].data.property_metadata
    assert property_metadata
    assert len(property_metadata) == 1
    assert not property_metadata[0].ai_rec


async def test_patch_meeting_pipeline_id(
    common_api_client: CommonAPIClient,
    meeting_repository: MeetingRepository,
    account_repo: AccountRepository,
) -> None:
    """Test patching a meeting's pipeline_id."""
    # Create a meeting
    meeting = await create_meeting(
        meeting_repository=meeting_repository,
        account_repository=account_repo,
        organization_id=common_api_client.api_test_client.default_organization_id,
        user_id=uuid4(),
    )

    # Create an account for the pipeline
    account = await common_api_client.create_account(
        create_request=CreateAccountRequest(
            display_name="Test Account for Pipeline",
            owner_user_id=common_api_client.api_test_client.default_user_id,
        )
    )

    # Create a contact for the pipeline
    contact = await common_api_client.create_contact(
        create_request=CreateContactRequest(
            first_name="Test",
            last_name="Contact",
            primary_email="<EMAIL>",
            owner_user_id=common_api_client.api_test_client.default_user_id,
            primary_account_id=account.id,
        )
    )

    # Get a pipeline stage select list to use for the pipeline
    pipeline_stage_lists = await common_api_client.list_select_lists()
    pipeline_stage_list = next(
        detail
        for detail in pipeline_stage_lists.list_data
        if detail.application_code_name == StdSelectListIdentifier.pipeline_stage
    )
    stage_id = pipeline_stage_list.select_list_values[0].id

    # Create a pipeline to associate with the meeting
    pipeline = await common_api_client.create_pipeline(
        create_request=CreatePipelineRequest(
            display_name="Test Pipeline for Meeting",
            owner_user_id=common_api_client.api_test_client.default_user_id,
            account_id=account.id,
            stage_id=stage_id,
            contact_pipeline_associations=FullContactPipelineAssociationRequests(
                primary=ContactPipelineAssociationRequest(
                    contact_id=contact.id,
                ),
            ),
        )
    )

    # Patch the meeting with the pipeline_id
    patched_meeting = await common_api_client.patch_meeting(
        meeting_id=meeting.id,
        patch_request=PatchMeetingRequest(pipeline_id=pipeline.id),
    )

    # Verify the pipeline_id was patched
    assert patched_meeting.pipeline_id == pipeline.id

    # Retrieve the meeting to confirm the change was persisted
    retrieved_meeting = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=False,
            record_id=meeting.id,
        ),
    )

    # Verify the retrieved meeting has the correct pipeline_id
    assert retrieved_meeting.list_data[0].data.pipeline_id == pipeline.id

    # Test setting pipeline_id to None
    patched_meeting = await common_api_client.patch_meeting(
        meeting_id=meeting.id,
        patch_request=PatchMeetingRequest(pipeline_id=None),
    )

    # Verify the pipeline_id was set to None
    assert patched_meeting.pipeline_id is None

    # Retrieve the meeting again to confirm the change was persisted
    retrieved_meeting = await common_api_client.list_meetings_v2(
        list_meeting_request=ListCustomizableEntityRequest(
            include_custom_field_data=False,
            record_id=meeting.id,
        ),
    )

    # Verify the retrieved meeting has pipeline_id set to None
    assert retrieved_meeting.list_data[0].data.pipeline_id is None
