from collections.abc import Awaitable, Callable
from datetime import datetime
from typing import Literal
from unittest.mock import AsyncMock, patch
from uuid import UUID, uuid4

import pytest
from faker import Faker
from pytest_mock import MockerFixture

from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.core.account.service.account_service import AccountService
from salestech_be.core.account.types import CreateAccountRequest
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.service.contact_query_service import ContactQueryService
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
)
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.service_api_schema import PatchContactRequest
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
)
from salestech_be.core.research_agent.models.person_info import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    PersonResearchInfo,
)
from salestech_be.db.models.contact import (
    Contact,
    CreateContactAccountRoleRequest,
    CreateContactRequest,
    CreateDbContactEmailAccountAssociationRequest,
    CreateDbContactEmailRequest,
    CreateDbContactRequest,
)
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.contact.schema import (
    CreateContactRequest as CreateContactApiRequest,
)

logger = get_logger(__name__)


class TestContactService:
    async def test_get_contact_research_resilient_to_missing_fields(
        self,
        contact_service: ContactService,
    ) -> None:
        """
        Test that get_contact_research is resilient to:
        1. Missing optional fields in the source model
        2. New metadata fields being added to the model

        This ensures that when we add new fields to PersonResearch in the future,
        the conversion to ContactResearchDto won't break as long as the new fields
        are optional or have default values.
        """
        # Arrange
        contact_id = uuid4()
        organization_id = uuid4()

        # Create a PersonResearch object with:
        # 1. Only the minimal required fields
        # 2. A metadata field (latest_provider_status) that isn't directly mapped to ContactResearchDto
        # This simulates what would happen if new fields are added to PersonResearch in the future
        person_research = PersonResearch(
            intel_person_id=uuid4(),
            info=PersonResearchInfo(
                name="John Doe",
                title="CEO",
                last_updated=datetime.now(),
            ),
            # Empty social activities list
            social_activities=[],
        )

        contact = Contact(
            id=contact_id,
            organization_id=organization_id,
            linkedin_url="https://linkedin.com/in/johndoe",
            display_name="John Doe",
            created_by_user_id=uuid4(),
            owner_user_id=uuid4(),
            stage_id=uuid4(),
            created_at=zoned_utc_now(),
        )

        with (
            patch.object(
                contact_service.research_agent_service,
                "get_research_for_contact_or_none",
                AsyncMock(return_value=person_research),
            ),
            patch.object(
                contact_service.contact_repository,
                "get_by_id",
                AsyncMock(return_value=contact),
            ),
        ):
            # Act
            result = await contact_service.get_contact_research(
                contact_id=contact_id,
                organization_id=organization_id,
                referer=None,
            )

            # Assert
            assert result is not None
            assert result.intel_person_id is not None
            assert result.snippet.full_name == "John Doe"
            assert result.snippet.title == "CEO"
            assert result.socials == []

            # The test passes if no exceptions are thrown during the conversion
            # This confirms that new fields added to PersonResearch won't break
            # the conversion to ContactResearchDto as long as they're optional or have defaults

    async def test_get_contact_research_different_scenarios(
        self,
        contact_service: ContactService,
        mocker: MockerFixture,
    ) -> None:
        """
        Test get_contact_research with different contact scenarios:
        1. Contact doesn't exist - should return None
        2. Contact exists but has no LinkedIn URL and setting is disabled - should return None
        3. Contact exists with LinkedIn URL - should return research data
        """
        organization_id = uuid4()
        contact_id = uuid4()
        non_existent_contact_id = uuid4()

        # Mock settings
        settings_mock = mocker.patch(
            "salestech_be.core.contact.service.contact_service.settings"
        )
        settings_mock.research_agent_enable_find_linkedin_url_by_email = False

        # Mock contact repository get_by_id
        contact_with_linkedin = mocker.Mock()
        contact_with_linkedin.linkedin_url = "https://linkedin.com/in/johndoe"

        contact_without_linkedin = mocker.Mock()
        contact_without_linkedin.linkedin_url = None

        async def mock_get_by_id(
            contact_id: UUID, organization_id: UUID
        ) -> Contact | None:
            if contact_id == non_existent_contact_id:
                return None
            elif contact_without_linkedin.linkedin_url is None:
                return contact_without_linkedin  # type: ignore
            else:
                return contact_with_linkedin  # type: ignore

        with patch.object(
            contact_service.contact_repository,
            "get_by_id",
            AsyncMock(side_effect=mock_get_by_id),
        ):
            # Mock research agent service
            person_research = PersonResearch(
                intel_person_id=uuid4(),
                info=PersonResearchInfo(
                    name="John Doe",
                    title="CEO",
                    last_updated=datetime.now(),
                ),
                social_activities=[],
                latest_provider_status=None,
            )
            contact_service.research_agent_service.get_research_for_contact_or_none = (  # type: ignore
                AsyncMock(return_value=person_research)
            )

            # Mock research metric tracking
            mocker.patch(
                "salestech_be.core.contact.service.contact_service.ResearchMetric.track_person_research_availability"
            )

            # Scenario 1: Contact doesn't exist
            result = await contact_service.get_contact_research(
                contact_id=non_existent_contact_id,
                organization_id=organization_id,
                referer=None,
            )
            assert result is None

            # Scenario 2: Contact exists but has no LinkedIn URL and setting is disabled
            result = await contact_service.get_contact_research(
                contact_id=contact_id,
                organization_id=organization_id,
                referer=None,
            )
            assert result is None

            # Scenario 3: Contact exists with LinkedIn URL
            contact_without_linkedin.linkedin_url = "https://linkedin.com/in/johndoe"
            result = await contact_service.get_contact_research(
                contact_id=contact_id,
                organization_id=organization_id,
                referer=None,
            )
            assert result is not None
            assert result.intel_person_id is not None
            assert result.snippet.full_name == "John Doe"
            assert result.snippet.title == "CEO"

    async def test_create_contact_with_custom_field_find_by_emails(
        self,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
    ) -> None:
        # create a contact
        user_id = uuid4()
        organization_id = uuid4()

        await select_list_service.bootstrap_all_direct_std_select_lists(
            organization_id=organization_id,
            user_id=user_id,
        )
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value

        create_contact_api_request = CreateContactApiRequest(
            owner_user_id=user_id,
            primary_email="<EMAIL>",
        )

        contact = await contact_service.create_contact_with_custom_field(
            create_contact_api_request=create_contact_api_request,
            organization_id=organization_id,
            user_id=user_id,
        )

        assert contact is not None

        found_contact = await contact_service.list_by_emails(
            organization_id=organization_id, emails=["<EMAIL>"]
        )

        assert len(found_contact) == 1
        assert found_contact[0].display_name == "<EMAIL>"
        logger.info(found_contact[0])


class TestContactFindRelevantEmail:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(scope="function")
    def primary_email_for_non_account(self) -> str:
        return "<EMAIL>"

    @pytest.fixture(scope="function")
    def primary_email(self) -> str:
        return "<EMAIL>"

    @pytest.fixture(scope="function")
    def primary_email_for_primary_account(self) -> str:
        return "<EMAIL>"

    @pytest.fixture(scope="function")
    def primary_email_for_non_primary_account(self) -> str:
        return "<EMAIL>"

    @pytest.fixture(scope="function")
    async def account_primary(
        self,
        faker: Faker,
        account_service: AccountService,
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name=faker.company(),
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def account_non_primary(
        self,
        faker: Faker,
        account_service: AccountService,
    ) -> AccountV2:
        return await account_service.create_account_v2(
            create_account_request=CreateAccountRequest(
                display_name=faker.company(),
                owner_user_id=self.user_id,
            ),
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

    @pytest.fixture(scope="function")
    async def contact(
        self,
        faker: Faker,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
        account_primary: AccountV2,
        account_non_primary: AccountV2,
        primary_email: str,
        primary_email_for_primary_account: str,
        primary_email_for_non_primary_account: str,
    ) -> ContactV2:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        return await contact_service.create_contact_with_contact_channels(
            organization_id=self.organization_id,
            user_id=self.user_id,
            create_contact_with_contact_channel_request=CreateContactRequest(
                contact=CreateDbContactRequest(
                    first_name=faker.first_name(),
                    last_name=faker.last_name(),
                    display_name="contact",
                    owner_user_id=self.user_id,
                    created_by_user_id=self.user_id,
                    stage_id=default_stage_list.default_or_initial_active_value.id,
                ),
                contact_account_roles=[
                    CreateContactAccountRoleRequest(
                        account_id=account_primary.id,
                        is_primary_account=True,
                    ),
                    CreateContactAccountRoleRequest(
                        account_id=account_non_primary.id,
                        is_primary_account=False,
                    ),
                ],
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email=primary_email,
                        is_contact_primary=True,
                    ),
                    CreateDbContactEmailRequest(
                        email=primary_email_for_primary_account,
                        is_contact_primary=False,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_primary.id,
                                is_contact_account_primary=True,
                            )
                        ],
                    ),
                    CreateDbContactEmailRequest(
                        email=primary_email_for_non_primary_account,
                        is_contact_primary=False,
                        email_account_associations=[
                            CreateDbContactEmailAccountAssociationRequest(
                                account_id=account_non_primary.id,
                                is_contact_account_primary=True,
                            )
                        ],
                    ),
                ],
            ),
        )

    @pytest.fixture(scope="function")
    async def contact_without_primary_account(
        self,
        faker: Faker,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
        primary_email_for_non_account: str,
    ) -> ContactV2:
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value
        return await contact_service.create_contact_with_contact_channels(
            organization_id=self.organization_id,
            user_id=self.user_id,
            create_contact_with_contact_channel_request=CreateContactRequest(
                contact=CreateDbContactRequest(
                    first_name=faker.first_name(),
                    last_name=faker.last_name(),
                    display_name="contact",
                    owner_user_id=self.user_id,
                    created_by_user_id=self.user_id,
                    stage_id=default_stage_list.default_or_initial_active_value.id,
                ),
                contact_emails=[
                    CreateDbContactEmailRequest(
                        email=primary_email_for_non_account,
                        is_contact_primary=True,
                    )
                ],
            ),
        )

    async def test_find_most_relevant_contact_email_with_account_primary_email(
        self,
        contact: ContactV2,
        contact_query_service: ContactQueryService,
        account_primary: AccountV2,
        account_non_primary: AccountV2,
        primary_email: str,
        primary_email_for_primary_account: str,
        primary_email_for_non_primary_account: str,
    ) -> None:
        result = await contact_query_service.find_the_most_relevant_contact_email(
            organization_id=self.organization_id,
            contact_id=contact.id,
            account_id=account_non_primary.id,
        )

        assert result == primary_email_for_non_primary_account

        result = await contact_query_service.find_the_most_relevant_contact_email(
            organization_id=self.organization_id,
            contact_id=contact.id,
            account_id=account_primary.id,
        )

        assert result == primary_email_for_primary_account

    async def test_find_most_relevant_contact_email_fallback_to_primary_account_email(
        self,
        contact: ContactV2,
        contact_query_service: ContactQueryService,
        account_primary: AccountV2,
        account_non_primary: AccountV2,
        primary_email: str,
        primary_email_for_primary_account: str,
        primary_email_for_non_primary_account: str,
    ) -> None:
        result = await contact_query_service.find_the_most_relevant_contact_email(
            organization_id=self.organization_id,
            contact_id=contact.id,
        )

        assert result == primary_email_for_primary_account

    async def test_find_most_relevant_contact_email_fallback_to_primary_email(
        self,
        contact_without_primary_account: ContactV2,
        contact_query_service: ContactQueryService,
        primary_email_for_non_account: str,
    ) -> None:
        result = await contact_query_service.find_the_most_relevant_contact_email(
            organization_id=self.organization_id,
            contact_id=contact_without_primary_account.id,
        )

        assert result == primary_email_for_non_account


class TestContactListContactEmails:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    @pytest.fixture(scope="function")
    def contact_id(self) -> UUID:
        return uuid4()

    @pytest.fixture(scope="function")
    def account_id(self) -> UUID:
        return uuid4()

    def create_associtaion(
        self,
        contact_id: UUID,
        account_id: UUID,
        contact_email_id: UUID,
        association_type: Literal["active", "archived", "deleted"],
        is_contact_account_primary: bool,
    ) -> ContactEmailAccountAssociation:
        active_association = ContactEmailAccountAssociation(
            id=uuid4(),
            organization_id=self.organization_id,
            contact_id=contact_id,
            contact_email_id=contact_email_id,
            account_id=account_id,
            is_contact_account_primary=is_contact_account_primary,
            archived_at=None,
            archived_by_user_id=None,
            deleted_at=None,
            deleted_by_user_id=None,
            created_at=zoned_utc_now(),
            created_by_user_id=self.user_id,
            updated_at=zoned_utc_now(),
            updated_by_user_id=self.user_id,
        )

        if association_type == "archived":
            return strict_model_copy(
                active_association,
                archived_at=zoned_utc_now(),
                archived_by_user_id=self.user_id,
            )
        elif association_type == "deleted":
            return strict_model_copy(
                active_association,
                deleted_at=zoned_utc_now(),
                deleted_by_user_id=self.user_id,
            )

        return active_association

    def create_contact_email(
        self,
        contact_email_id: UUID,
        email: str,
        contact_id: UUID,
        is_contact_primary: bool,
    ) -> ContactEmail:
        return ContactEmail(
            id=contact_email_id,
            organization_id=self.organization_id,
            contact_id=contact_id,
            email=email,
            is_contact_primary=is_contact_primary,
            created_at=zoned_utc_now(),
            created_by_user_id=self.user_id,
            updated_at=zoned_utc_now(),
            updated_by_user_id=self.user_id,
        )

    async def test_list_contact_emails(
        self,
        contact_id: UUID,
        account_id: UUID,
        contact_service: ContactService,
        mocker: MockerFixture,
    ) -> None:
        contact_email_id_1 = uuid4()
        contact_email_id_2 = uuid4()
        contact_email_id_3 = uuid4()
        contact_email_id_4 = uuid4()
        contact_email_id_5 = uuid4()
        contact_email_id_6 = uuid4()
        contact_email_id_7 = uuid4()

        test_data: dict[ContactEmail, list[ContactEmailAccountAssociation]] = {
            self.create_contact_email(
                contact_email_id=contact_email_id_1,
                email="<EMAIL>",
                contact_id=contact_id,
                is_contact_primary=True,
            ): [
                self.create_associtaion(
                    contact_id=contact_id,
                    account_id=account_id,
                    contact_email_id=contact_email_id_1,
                    association_type="active",
                    is_contact_account_primary=True,
                )
            ],  # one active association
            self.create_contact_email(
                contact_email_id=contact_email_id_2,
                email="<EMAIL>",
                contact_id=contact_id,
                is_contact_primary=False,
            ): [],  # no active association
            self.create_contact_email(
                contact_email_id=contact_email_id_3,
                email="<EMAIL>",
                contact_id=contact_id,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id,
                    account_id=account_id,
                    contact_email_id=contact_email_id_3,
                    association_type="archived",
                    is_contact_account_primary=False,
                )
            ],  # one archived association
            self.create_contact_email(
                contact_email_id=contact_email_id_4,
                email="<EMAIL>",
                contact_id=contact_id,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id,
                    account_id=account_id,
                    contact_email_id=contact_email_id_4,
                    association_type="deleted",
                    is_contact_account_primary=False,
                )
            ],  # one deleted association
            self.create_contact_email(
                contact_email_id=contact_email_id_5,
                email="<EMAIL>",
                contact_id=contact_id,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id,
                    account_id=account_id,
                    contact_email_id=contact_email_id_5,
                    association_type="active",
                    is_contact_account_primary=False,
                ),
                self.create_associtaion(
                    contact_id=contact_id,
                    account_id=account_id,
                    contact_email_id=contact_email_id_5,
                    association_type="deleted",
                    is_contact_account_primary=False,
                ),
            ],  # one active + archived + deleted association
            strict_model_copy(
                self.create_contact_email(
                    contact_email_id=contact_email_id_6,
                    email="<EMAIL>",
                    contact_id=contact_id,
                    is_contact_primary=False,
                ),
                deleted_at=zoned_utc_now(),
                deleted_by_user_id=self.user_id,
            ): [],  # deleted contact email
            strict_model_copy(
                self.create_contact_email(
                    contact_email_id=contact_email_id_7,
                    email="<EMAIL>",
                    contact_id=contact_id,
                    is_contact_primary=False,
                ),
                archived_at=zoned_utc_now(),
                archived_by_user_id=self.user_id,
            ): [],  # archived contact email
        }

        for contact_email, associations in test_data.items():
            await contact_service.contact_repository.insert(contact_email)

            for association in associations:
                await contact_service.contact_repository.insert(association)

        mocker.patch(
            "salestech_be.db.dao.generic_repository.GenericRepository.find_by_tenanted_primary_key_or_fail",
            return_value=None,
        )

        contact_email_dtos = await contact_service.contact_query_service.list_contact_email_dtos_by_contact_id(
            organization_id=self.organization_id,
            contact_id=contact_id,
        )

        assert len(contact_email_dtos) == 5
        assert {
            contact_email_dto.contact_email.id
            for contact_email_dto in contact_email_dtos
        } == {
            contact_email_id_1,
            contact_email_id_2,
            contact_email_id_3,
            contact_email_id_4,
            contact_email_id_5,
        }

        for contact_email_dto in contact_email_dtos:
            contact_email = contact_email_dto.contact_email
            associations = contact_email_dto.contact_email_account_associations
            if contact_email.id in [contact_email_id_1, contact_email_id_5]:
                assert len(associations) == 1
            elif contact_email.id in [
                contact_email_id_2,
                contact_email_id_3,
                contact_email_id_4,
            ]:
                assert len(associations) == 0
            else:
                raise AssertionError

    async def test_relevant_contact_emails(
        self,
        contact_service: ContactService,
        contact_query_service: ContactQueryService,
    ) -> None:
        organization_id = uuid4()
        contact_email_id_1 = uuid4()
        contact_email_id_2 = uuid4()
        contact_email_id_3 = uuid4()
        contact_email_id_4 = uuid4()
        contact_email_id_5 = uuid4()
        contact_email_id_6 = uuid4()
        contact_email_id_7 = uuid4()

        contact_id_1 = uuid4()
        contact_id_2 = uuid4()

        account_id_1 = uuid4()
        account_id_2 = uuid4()

        test_data: dict[ContactEmail, list[ContactEmailAccountAssociation]] = {
            self.create_contact_email(
                contact_email_id=contact_email_id_1,
                email="<EMAIL>",
                contact_id=contact_id_1,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_1,
                    account_id=account_id_1,
                    contact_email_id=contact_email_id_1,
                    association_type="active",
                    is_contact_account_primary=True,
                ),
                self.create_associtaion(
                    contact_id=contact_id_1,
                    account_id=account_id_2,
                    contact_email_id=contact_email_id_1,
                    association_type="active",
                    is_contact_account_primary=False,
                ),
            ],  # two active association
            self.create_contact_email(
                contact_email_id=contact_email_id_2,
                email="<EMAIL>",
                contact_id=contact_id_1,
                is_contact_primary=True,
            ): [],  # no active association
            self.create_contact_email(
                contact_email_id=contact_email_id_3,
                email="<EMAIL>",
                contact_id=contact_id_1,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_1,
                    account_id=account_id_2,
                    contact_email_id=contact_email_id_3,
                    association_type="active",
                    is_contact_account_primary=False,
                )
            ],  # one active association
            self.create_contact_email(
                contact_email_id=contact_email_id_4,
                email="<EMAIL>",
                contact_id=contact_id_1,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_1,
                    account_id=account_id_2,
                    contact_email_id=contact_email_id_4,
                    association_type="active",
                    is_contact_account_primary=True,
                )
            ],  # one deleted association
            self.create_contact_email(
                contact_email_id=contact_email_id_5,
                email="<EMAIL>",
                contact_id=contact_id_1,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_1,
                    account_id=account_id_1,
                    contact_email_id=contact_email_id_5,
                    association_type="active",
                    is_contact_account_primary=False,
                ),
                self.create_associtaion(
                    contact_id=contact_id_1,
                    account_id=account_id_2,
                    contact_email_id=contact_email_id_5,
                    association_type="active",
                    is_contact_account_primary=False,
                ),
            ],
        }

        all_associations: list[ContactEmailAccountAssociation] = []
        for contact_email, associations in test_data.items():
            await contact_service.contact_repository.insert(contact_email)

            for association in associations:
                await contact_service.contact_repository.insert(association)
                all_associations.append(association)

        """
        contact_1
        primary_account: account_id_2
        test1: account_1-primary, account_2
        test2(primary):
        test3: account_2
        test4: account_2-primary
        test5: account_1, account_2
        """
        contact_emails_by_account_1 = (
            await contact_query_service._sort_contact_emails_by_relevant_order(
                contact_account_associations=[
                    ContactAccountAssociation(
                        id=uuid4(),
                        organization_id=organization_id,
                        contact_id=contact_id_1,
                        account_id=account_id_2,
                        is_primary=True,
                        created_by_user_id=uuid4(),
                        updated_by_user_id=uuid4(),
                    ),
                    ContactAccountAssociation(
                        id=uuid4(),
                        organization_id=organization_id,
                        contact_id=contact_id_1,
                        account_id=account_id_1,
                        is_primary=False,
                        created_by_user_id=uuid4(),
                        updated_by_user_id=uuid4(),
                    ),
                ],
                contact_emails=list(test_data.keys()),
                contact_email_account_associations=all_associations,
                account_id=account_id_1,
            )
        )
        assert len(contact_emails_by_account_1) == 5
        assert contact_emails_by_account_1[0].email == "<EMAIL>"
        assert contact_emails_by_account_1[1].email == "<EMAIL>"
        assert contact_emails_by_account_1[2].email == "<EMAIL>"
        assert contact_emails_by_account_1[3].email == "<EMAIL>"
        assert contact_emails_by_account_1[4].email == "<EMAIL>"
        contact_emails_by_account_2 = (
            await contact_query_service._sort_contact_emails_by_relevant_order(
                contact_account_associations=[
                    ContactAccountAssociation(
                        id=uuid4(),
                        organization_id=organization_id,
                        contact_id=contact_id_1,
                        account_id=account_id_2,
                        is_primary=True,
                        created_by_user_id=uuid4(),
                        updated_by_user_id=uuid4(),
                    ),
                    ContactAccountAssociation(
                        id=uuid4(),
                        organization_id=organization_id,
                        contact_id=contact_id_1,
                        account_id=account_id_1,
                        is_primary=False,
                        created_by_user_id=uuid4(),
                        updated_by_user_id=uuid4(),
                    ),
                ],
                contact_emails=list(test_data.keys()),
                contact_email_account_associations=all_associations,
                account_id=account_id_2,
            )
        )
        assert len(contact_emails_by_account_2) == 5
        assert contact_emails_by_account_2[0].email == "<EMAIL>"
        assert contact_emails_by_account_2[1].email == "<EMAIL>"
        assert contact_emails_by_account_2[2].email == "<EMAIL>"
        assert contact_emails_by_account_2[3].email == "<EMAIL>"
        assert contact_emails_by_account_2[4].email == "<EMAIL>"
        contact_emails_by_no_account = (
            await contact_query_service._sort_contact_emails_by_relevant_order(
                contact_account_associations=[
                    ContactAccountAssociation(
                        id=uuid4(),
                        organization_id=organization_id,
                        contact_id=contact_id_1,
                        account_id=account_id_2,
                        is_primary=True,
                        created_by_user_id=uuid4(),
                        updated_by_user_id=uuid4(),
                    ),
                    ContactAccountAssociation(
                        id=uuid4(),
                        organization_id=organization_id,
                        contact_id=contact_id_1,
                        account_id=account_id_1,
                        is_primary=False,
                        created_by_user_id=uuid4(),
                        updated_by_user_id=uuid4(),
                    ),
                ],
                contact_emails=list(test_data.keys()),
                contact_email_account_associations=all_associations,
            )
        )
        assert len(contact_emails_by_no_account) == 5
        assert contact_emails_by_no_account[0].email == "<EMAIL>"
        assert contact_emails_by_no_account[1].email == "<EMAIL>"
        assert contact_emails_by_no_account[2].email == "<EMAIL>"
        assert contact_emails_by_no_account[3].email == "<EMAIL>"
        assert contact_emails_by_no_account[4].email == "<EMAIL>"

        test_data2: dict[ContactEmail, list[ContactEmailAccountAssociation]] = {
            self.create_contact_email(
                contact_email_id=contact_email_id_6,
                email="<EMAIL>",
                contact_id=contact_id_2,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_2,
                    account_id=account_id_1,
                    contact_email_id=contact_email_id_6,
                    association_type="active",
                    is_contact_account_primary=False,
                )
            ],
            self.create_contact_email(
                contact_email_id=contact_email_id_7,
                email="<EMAIL>",
                contact_id=contact_id_2,
                is_contact_primary=True,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_2,
                    account_id=account_id_2,
                    contact_email_id=contact_email_id_7,
                    association_type="active",
                    is_contact_account_primary=False,
                )
            ],
        }
        for contact_email, associations in test_data2.items():
            await contact_service.contact_repository.insert(contact_email)

            for association in associations:
                await contact_service.contact_repository.insert(association)

        """
        contact_1
        primary_account: account_id_2
        test1: account_1-primary, account_2
        test2(primary):
        test3: account_2
        test4: account_2-primary
        test5: account_1, account_2

        contact_2
        <EMAIL>(primary): account_1
        <EMAIL>: account_2
        """
        account_id = (
            await contact_query_service.get_single_association_account_id_by_emails(
                organization_id=self.organization_id,
                emails=[
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                ],
            )
        )
        assert not account_id
        account_id = (
            await contact_query_service.get_single_association_account_id_by_emails(
                organization_id=self.organization_id,
                emails=["<EMAIL>", "<EMAIL>"],
            )
        )
        assert not account_id
        account_id = (
            await contact_query_service.get_single_association_account_id_by_emails(
                organization_id=self.organization_id,
                emails=["<EMAIL>", "<EMAIL>"],
            )
        )
        assert not account_id
        account_id = (
            await contact_query_service.get_single_association_account_id_by_emails(
                organization_id=self.organization_id,
                emails=["<EMAIL>", "<EMAIL>"],
            )
        )
        assert account_id == account_id_2

    async def test_resolve_account_id(
        self,
        contact_service: ContactService,
        contact_query_service: ContactQueryService,
        contact_resolve_service: ContactResolveService,
    ) -> None:
        contact_email_id_1 = uuid4()
        contact_email_id_2 = uuid4()
        contact_email_id_3 = uuid4()
        contact_email_id_4 = uuid4()

        contact_id_1 = uuid4()
        contact_id_2 = uuid4()
        contact_id_3 = uuid4()

        account_id_1 = uuid4()
        account_id_2 = uuid4()

        test_data: dict[ContactEmail, list[ContactEmailAccountAssociation]] = {
            self.create_contact_email(
                contact_email_id=contact_email_id_1,
                email="<EMAIL>",
                contact_id=contact_id_1,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_1,
                    account_id=account_id_1,
                    contact_email_id=contact_email_id_1,
                    association_type="active",
                    is_contact_account_primary=True,
                ),
                self.create_associtaion(
                    contact_id=contact_id_1,
                    account_id=account_id_2,
                    contact_email_id=contact_email_id_1,
                    association_type="active",
                    is_contact_account_primary=False,
                ),
            ],  # two active association
            self.create_contact_email(
                contact_email_id=contact_email_id_2,
                email="<EMAIL>",
                contact_id=contact_id_2,
                is_contact_primary=True,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_2,
                    account_id=account_id_1,
                    contact_email_id=contact_email_id_2,
                    association_type="active",
                    is_contact_account_primary=True,
                )
            ],  # no active association
            self.create_contact_email(
                contact_email_id=contact_email_id_3,
                email="<EMAIL>",
                contact_id=contact_id_3,
                is_contact_primary=False,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_3,
                    account_id=account_id_2,
                    contact_email_id=contact_email_id_3,
                    association_type="active",
                    is_contact_account_primary=False,
                ),
            ],
            self.create_contact_email(
                contact_email_id=contact_email_id_4,
                email="<EMAIL>",
                contact_id=contact_id_3,
                is_contact_primary=True,
            ): [
                self.create_associtaion(
                    contact_id=contact_id_3,
                    account_id=account_id_1,
                    contact_email_id=contact_email_id_4,
                    association_type="active",
                    is_contact_account_primary=True,
                ),
            ],
        }

        all_associations: list[ContactEmailAccountAssociation] = []
        for contact_email, associations in test_data.items():
            await contact_service.contact_repository.insert(contact_email)

            for association in associations:
                await contact_service.contact_repository.insert(association)
                all_associations.append(association)

        """
        contact_1
        <EMAIL>: account_1-primary, account_2
        contact_2
        <EMAIL>: account_1-primary
        contact_3
        <EMAIL>: account_2
        <EMAIL>: account_1-primary
        """
        resolve_account_id = (
            await contact_resolve_service.resolve_relevant_account_by_contact_and_email(
                organization_id=self.organization_id,
                contact_id=contact_id_1,
                email="<EMAIL>",
            )
        )
        assert resolve_account_id == account_id_1

        resolve_account_id = (
            await contact_resolve_service.resolve_relevant_account_by_contact_and_email(
                organization_id=self.organization_id,
                contact_id=contact_id_3,
                email="<EMAIL>",
            )
        )
        assert not resolve_account_id

        resolve_account_ids = await contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
            organization_id=self.organization_id,
            contact_email_pairs=[
                (contact_id_1, "<EMAIL>"),
                (contact_id_2, "<EMAIL>"),
                (contact_id_3, "<EMAIL>"),
            ],
        )
        assert len(resolve_account_ids) == 3
        for resolve_account_id in resolve_account_ids.values():
            assert resolve_account_id == account_id_1

        resolve_account_ids = await contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
            organization_id=self.organization_id,
            contact_email_pairs=[
                (contact_id_1, "<EMAIL>"),
                (contact_id_2, "<EMAIL>"),
                (contact_id_3, "<EMAIL>"),
            ],
        )
        assert len(resolve_account_ids) == 3
        for resolve_contact_id, resolve_account_id in resolve_account_ids.items():
            if resolve_contact_id == contact_id_3:
                assert resolve_account_id == account_id_2
            else:
                assert resolve_account_id == account_id_1


class TestContactLinkedInUpdate:
    @pytest.fixture(autouse=True, scope="function")
    async def setup(
        self, make_user_org: Callable[[], Awaitable[tuple[UUID, UUID]]]
    ) -> None:
        self.user_id, self.organization_id = await make_user_org()

    async def test_patch_contact_linkedin_url(
        self,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
        mocker: MockerFixture,
    ) -> None:
        # Arrange
        # Mock the research_agent_service.delete_intel_person_association_by_contact_id method
        mock_delete_intel_association = mocker.patch.object(
            contact_service.research_agent_service,
            "delete_intel_person_association_by_contact_id",
            new_callable=AsyncMock,
        )

        # Create a contact with a LinkedIn URL
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value

        # Create a contact
        create_contact_api_request = CreateContactApiRequest(
            owner_user_id=self.user_id,
            primary_email="<EMAIL>",
            linkedin_url="https://linkedin.com/in/oldprofile",
        )

        contact = await contact_service.create_contact_with_custom_field(
            create_contact_api_request=create_contact_api_request,
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

        # Act
        # Update the LinkedIn URL
        patch_request = PatchContactRequest(
            linkedin_url="https://linkedin.com/in/newprofile",
        )

        updated_contact = await contact_service.patch_by_id(
            organization_id=self.organization_id,
            user_id=self.user_id,
            contact_id=contact.id,
            request=patch_request,
        )

        # Assert
        # Verify the LinkedIn URL was updated
        assert updated_contact.linkedin_url == "https://linkedin.com/in/newprofile"

        # Verify the delete_intel_person_association_by_contact_id method was called
        mock_delete_intel_association.assert_called_once_with(
            contact_id=contact.id,
            user_id=self.user_id,
        )

    async def test_patch_contact_linkedin_url_to_none(
        self,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
        mocker: MockerFixture,
    ) -> None:
        # Arrange
        # Mock the research_agent_service.delete_intel_person_association_by_contact_id method
        mock_delete_intel_association = mocker.patch.object(
            contact_service.research_agent_service,
            "delete_intel_person_association_by_contact_id",
            new_callable=AsyncMock,
        )

        # Create a contact with a LinkedIn URL
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value

        # Create a contact
        create_contact_api_request = CreateContactApiRequest(
            owner_user_id=self.user_id,
            primary_email="<EMAIL>",
            linkedin_url="https://linkedin.com/in/oldprofile",
        )

        contact = await contact_service.create_contact_with_custom_field(
            create_contact_api_request=create_contact_api_request,
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

        # Act
        # Explicitly set LinkedIn URL to None
        patch_request = PatchContactRequest(
            linkedin_url=None,
        )

        updated_contact = await contact_service.patch_by_id(
            organization_id=self.organization_id,
            user_id=self.user_id,
            contact_id=contact.id,
            request=patch_request,
        )

        # Assert
        # Verify the LinkedIn URL was set to None
        assert updated_contact.linkedin_url is None

        # Verify the delete_intel_person_association_by_contact_id method was called
        # This is important because specified(request.linkedin_url) will be True even if the value is None
        mock_delete_intel_association.assert_called_once_with(
            contact_id=contact.id,
            user_id=self.user_id,
        )

    async def test_patch_contact_without_linkedin_url(
        self,
        contact_service: ContactService,
        select_list_service: InternalSelectListService,
        mocker: MockerFixture,
    ) -> None:
        # Arrange
        # Mock the research_agent_service.delete_intel_person_association_by_contact_id method
        mock_delete_intel_association = mocker.patch.object(
            contact_service.research_agent_service,
            "delete_intel_person_association_by_contact_id",
            new_callable=AsyncMock,
        )

        # Create a contact with a LinkedIn URL
        default_stage_list = await select_list_service.find_default_select_list_dto_by_application_code_name(
            organization_id=self.organization_id,
            application_code_name=StdSelectListIdentifier.contact_stage,
        )
        assert default_stage_list and default_stage_list.default_or_initial_active_value

        # Create a contact
        create_contact_api_request = CreateContactApiRequest(
            owner_user_id=self.user_id,
            primary_email="<EMAIL>",
            linkedin_url="https://linkedin.com/in/oldprofile",
        )

        contact = await contact_service.create_contact_with_custom_field(
            create_contact_api_request=create_contact_api_request,
            organization_id=self.organization_id,
            user_id=self.user_id,
        )

        # Act
        # Update some other field but not LinkedIn URL
        patch_request = PatchContactRequest(
            first_name="Updated",
            # No linkedin_url field
        )

        updated_contact = await contact_service.patch_by_id(
            organization_id=self.organization_id,
            user_id=self.user_id,
            contact_id=contact.id,
            request=patch_request,
        )

        # Assert
        # Verify the LinkedIn URL remains unchanged
        assert updated_contact.linkedin_url == "https://linkedin.com/in/oldprofile"

        # Verify the delete_intel_person_association_by_contact_id method was not called
        mock_delete_intel_association.assert_not_called()
