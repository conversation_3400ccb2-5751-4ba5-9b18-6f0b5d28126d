from decimal import Decimal
from uuid import uuid4

from polyfactory.factories.pydantic_factory import ModelFactory

from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.transcript.types import TranscriptContainer
from salestech_be.db.models.account import Account
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.meeting import Meeting
from salestech_be.db.models.pipeline import Pipeline
from salestech_be.search.es.indexing.mapping.account import ESAccount
from salestech_be.search.es.indexing.mapping.contact import ESContact
from salestech_be.search.es.indexing.mapping.meeting import ESMeeting
from salestech_be.search.es.indexing.mapping.pipeline import ESPipeline
from salestech_be.search.indexing.converter import (
    convert_account_to_es,
    convert_contact_v2_to_es,
    convert_meeting_and_transcript_to_es,
    convert_pipeline_to_es,
)
from salestech_be.util.time import zoned_utc_now


class MeetingFactory(ModelFactory[Meeting]):
    pass


class AccountFactory(ModelFactory[Account]):
    pass


class ContactFactory(ModelFactory[Contact]):
    pass


class PipelineFactory(ModelFactory[Pipeline]):
    pass


class MeetingV2Factory(ModelFactory[MeetingV2]):
    pass


class AccountV2Factory(ModelFactory[AccountV2]):
    pass


class ContactV2Factory(ModelFactory[ContactV2]):
    pass


class PipelineV2Factory(ModelFactory[PipelineV2]):
    pass


class TranscriptContainerFactory(ModelFactory[TranscriptContainer]):
    pass


class ESMeetingFactory:
    @staticmethod
    def build() -> ESMeeting:
        meeting = MeetingFactory.build(
            deleted_at=None,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
        # transcript_container = TranscriptContainerFactory.build()
        return convert_meeting_and_transcript_to_es(
            meeting, uuid4(), transcript_container=None
        )


class ESAccountFactory:
    @staticmethod
    def build() -> ESAccount:
        account = AccountFactory.build(
            display_name=str(uuid4()),
            archived_at=None,
            archived_by_user_id=None,
            integrity_job_finished_at=None,
            integrity_job_finished_by_user_id=None,
            integrity_job_started_at=None,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            system_updated_at=zoned_utc_now(),
            estimated_annual_revenue=Decimal(12345),
            estimated_employee_count=100,
        )
        return convert_account_to_es(account)


class ESContactFactory:
    @staticmethod
    def build() -> ESContact:
        contact_v2 = ContactV2Factory.build(
            display_name=str(uuid4()),
            archived_at=None,
            archived_by_user_id=None,
            integrity_job_finished_at=None,
            integrity_job_finished_by_user_id=None,
            integrity_job_started_at=None,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            sys_updated_at=zoned_utc_now(),
        )
        return convert_contact_v2_to_es(contact=contact_v2)


class ESPipelineFactory:
    @staticmethod
    def build() -> ESPipeline:
        pipeline = PipelineFactory.build(
            display_name=str(uuid4()),
            archived_at=None,
            archived_by_user_id=None,
            integrity_job_finished_at=None,
            integrity_job_finished_by_user_id=None,
            integrity_job_started_at=None,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
            sys_updated_at=zoned_utc_now(),
        )
        return convert_pipeline_to_es(pipeline)
