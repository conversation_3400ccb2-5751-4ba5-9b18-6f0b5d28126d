from unittest.mock import AsyncMock
from uuid import UUID, uuid4

import pytest

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.sequence.service.sequence_query_service import (
    SequenceQueryService,
)
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.db.models.sequence import (
    SequenceStatsBySequenceId,
    SequenceVisibility,
)
from salestech_be.db.models.sequence import SequenceV2 as DbSequenceV2
from tests.util.factories import SequenceV2Factory
from tests.util.service_test import ServiceTest


class TestSequenceQueryService(ServiceTest):
    async def test_is_entity_viewable_by_user_private_sequence_owner_user(
        self,
        sequence_v2_factory: SequenceV2Factory,
        sequence_query_service: SequenceQueryService,
    ) -> None:
        sequence = sequence_v2_factory.build(visibility=SequenceVisibility.PRIVATE)
        # Create user auth context with the same user ID as the sequence owner
        user_auth_context = UserAuthContext(
            user_id=sequence.owner_user_id, organization_id=sequence.organization_id
        )

        is_viewable = await sequence_query_service.is_entity_viewable_by_user(
            user_auth_context=user_auth_context,
            domain_object=SequenceV2.from_db_model(sequence),
        )
        assert is_viewable is True

    async def test_is_entity_viewable_by_user_private_sequence_non_owner_user(
        self,
        sequence_v2_factory: SequenceV2Factory,
        sequence_query_service: SequenceQueryService,
    ) -> None:
        sequence = sequence_v2_factory.build(visibility=SequenceVisibility.PRIVATE)
        # Create user auth context with a different user ID than the sequence owner
        user_auth_context = UserAuthContext(
            user_id=uuid4(), organization_id=sequence.organization_id
        )

        is_viewable = await sequence_query_service.is_entity_viewable_by_user(
            user_auth_context=user_auth_context,
            domain_object=SequenceV2.from_db_model(sequence),
        )
        assert is_viewable is False

    @pytest.mark.parametrize(
        "visibility",
        [
            SequenceVisibility.TEAM_VIEWABLE,
            SequenceVisibility.TEAM_EDITABLE,
        ],
    )
    async def test_is_entity_viewable_by_user_team_visibility(
        self,
        sequence_v2_factory: SequenceV2Factory,
        sequence_query_service: SequenceQueryService,
        visibility: SequenceVisibility,
    ) -> None:
        sequence = sequence_v2_factory.build(visibility=visibility)
        # Create user auth context with a different user ID than the sequence owner
        user_auth_context = UserAuthContext(
            user_id=uuid4(), organization_id=sequence.organization_id
        )

        is_viewable = await sequence_query_service.is_entity_viewable_by_user(
            user_auth_context=user_auth_context,
            domain_object=SequenceV2.from_db_model(sequence),
        )
        assert is_viewable is True

    @pytest.mark.parametrize(
        "visibility",
        [
            SequenceVisibility.TEAM_VIEWABLE,
            SequenceVisibility.TEAM_EDITABLE,
            SequenceVisibility.PRIVATE,
        ],
    )
    async def test_is_entity_viewable_by_user_admin(
        self,
        sequence_v2_factory: SequenceV2Factory,
        sequence_query_service: SequenceQueryService,
        visibility: SequenceVisibility,
    ) -> None:
        sequence = sequence_v2_factory.build(visibility=visibility)
        # Create user auth context with a different user ID than the sequence owner
        user_auth_context = UserAuthContext(
            user_id=uuid4(), organization_id=sequence.organization_id, groups=["admin"]
        )

        is_viewable = await sequence_query_service.is_entity_viewable_by_user(
            user_auth_context=user_auth_context,
            domain_object=SequenceV2.from_db_model(sequence),
        )
        assert is_viewable is True

    async def test_get_sequence_by_id_not_found(
        self,
        sequence_repository: AsyncMock,
        sequence_query_service: SequenceQueryService,
    ) -> None:
        organization_id = uuid4()
        sequence_id = uuid4()
        mock_sequence_repository_find_sequences_by_ids_and_organization_id(
            mock_sequence_repository=sequence_repository,
            input_organization_id=organization_id,
            input_sequence_ids=[sequence_id],
            input_include_deleted=False,
            result=[],
        )
        with pytest.raises(ResourceNotFoundError):
            await sequence_query_service.get_sequence_by_id(
                sequence_id=sequence_id, organization_id=organization_id
            )

    async def test_get_sequence_by_id(
        self,
        sequence_v2_factory: SequenceV2Factory,
        sequence_repository: AsyncMock,
        sequence_query_service: SequenceQueryService,
    ) -> None:
        organization_id = uuid4()
        sequence_id = uuid4()
        sequence = sequence_v2_factory.build(id=sequence_id)
        mock_sequence_repository_find_sequences_by_ids_and_organization_id(
            mock_sequence_repository=sequence_repository,
            input_organization_id=organization_id,
            input_sequence_ids=[sequence_id],
            input_include_deleted=False,
            result=[sequence],
        )
        mock_sequence_repository_get_sequence_stats(
            mock_sequence_repository=sequence_repository,
            input_organization_id=organization_id,
            input_sequence_ids=[sequence_id],
            result=[],
        )
        expected = SequenceV2.from_db_model(sequence)
        result = await sequence_query_service.get_sequence_by_id(
            sequence_id=sequence_id, organization_id=organization_id
        )
        assert result == expected

    async def test_list_sequence_blueprints(
        self,
        sequence_v2_factory: SequenceV2Factory,
        sequence_repository: AsyncMock,
        sequence_query_service: SequenceQueryService,
    ) -> None:
        blueprint = sequence_v2_factory.build(is_blueprint=True)
        mock_sequence_repository_blueprint_lookup(
            mock_sequence_repository=sequence_repository,
            input_organization_id=blueprint.organization_id,
            result=[blueprint],
        )
        sequences = await sequence_query_service.list_sequence_blueprints(
            organization_id=blueprint.organization_id
        )
        expected = [SequenceV2.from_db_model(blueprint)]
        assert sequences == expected

    async def test_populate_sequence_no_stats(
        self,
        sequence_repository: AsyncMock,
        sequence_v2_factory: SequenceV2Factory,
        sequence_query_service: SequenceQueryService,
    ) -> None:
        sequence = sequence_v2_factory.build()
        mock_sequence_repository_get_sequence_stats(
            mock_sequence_repository=sequence_repository,
            input_organization_id=sequence.organization_id,
            input_sequence_ids=[sequence.id],
            result=[],
        )
        populated_sequence = await sequence_query_service.populate_sequence(
            db_sequence=sequence
        )

        expected = SequenceV2.from_db_model(db_sequence=sequence, stats=None)
        assert populated_sequence == expected


def mock_sequence_repository_blueprint_lookup(
    mock_sequence_repository: AsyncMock,
    input_organization_id: UUID,
    result: list[DbSequenceV2],
) -> None:
    async def lookup(organization_id: UUID) -> list[DbSequenceV2]:
        assert organization_id == input_organization_id
        return result

    mock_sequence_repository.find_blueprint_sequences_by_organization_id = lookup


def mock_sequence_repository_get_sequence_stats(
    mock_sequence_repository: AsyncMock,
    input_organization_id: UUID,
    input_sequence_ids: list[UUID],
    result: list[SequenceStatsBySequenceId],
) -> None:
    async def lookup(
        organization_id: UUID, sequence_ids: list[UUID]
    ) -> list[SequenceStatsBySequenceId]:
        assert organization_id == input_organization_id
        assert sequence_ids == input_sequence_ids
        return result

    mock_sequence_repository.get_sequence_stats = lookup


def mock_sequence_repository_find_sequences_by_ids_and_organization_id(
    mock_sequence_repository: AsyncMock,
    input_organization_id: UUID,
    input_sequence_ids: list[UUID],
    input_include_deleted: bool,
    result: list[DbSequenceV2],
) -> None:
    async def lookup(
        organization_id: UUID, sequence_ids: list[UUID], include_deleted: bool
    ) -> list[DbSequenceV2]:
        assert organization_id == input_organization_id
        assert sequence_ids == input_sequence_ids
        assert include_deleted == input_include_deleted
        return result

    mock_sequence_repository.find_sequences_by_ids_and_organization_id = lookup


@pytest.fixture
def sequence_repository() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def feature_flag_service() -> AsyncMock:
    return AsyncMock()


@pytest.fixture
def sequence_query_service(
    sequence_repository: AsyncMock, feature_flag_service: AsyncMock
) -> SequenceQueryService:
    return SequenceQueryService(
        sequence_repository=sequence_repository,
        feature_flag_service=feature_flag_service,
    )
