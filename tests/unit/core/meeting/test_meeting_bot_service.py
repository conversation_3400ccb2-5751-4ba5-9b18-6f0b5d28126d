from datetime import timedel<PERSON>
from unittest import mock
from unittest.mock import As<PERSON><PERSON><PERSON>, Mock, patch
from uuid import UUID, uuid4

import pytest
from asyncpg import UniqueViolationError
from httpx import TimeoutException

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.exception.exception import ExternalServiceError
from salestech_be.core.meeting.meeting_bot_service import (
    MeetingBotService,
    RecallTranscriptOptionsFactory,
)
from salestech_be.core.meeting.service_type import RecallStatusCode
from salestech_be.core.meeting.utils import calculate_bot_join_time
from salestech_be.db.models.meeting import (
    BotProvider,
    BotStatusEvent,
    BotStatusHistory,
    BotStatusSubCode,
    MeetingBot,
    MeetingBotStatus,
    MeetingProvider,
    RecallBotParticipant,
    RecallMeetingParticipants,
    RecallParticipantEvent,
    RecallRecording,
    RecallRecordingData,
    ScreenShareRange,
    TranscriptProvider,
)
from salestech_be.db.models.transcript import Transcript
from salestech_be.integrations.recallai.type import (
    AutomaticLeaveSettings,
    BotDetectionSettings,
    BotDetectionUsingParticipantNames,
    BotResponse,
    CreateRecallBotRequest,
    GmeetMeetingUrl,
    GoogleMeetOptions,
    RealtimeMedia,
    RealtimeTranscription,
    RecallTranscriptProvider,
    Recording,
    RecordingModeOptions,
    SendChatMessageRequest,
    StartRecordingOn,
    TranscriptionOptions,
    UpdateRecallBotRequest,
    VideoScreenShare,
)
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.meeting.schema import (
    RecallBotEventData,
    RecallRealtimeCallData,
)
from tests.unit.core.meeting.conftest import (
    RecallBotEventStatusFactory,
    RecallBotFactory,
)
from tests.unit.core.meeting.meeting_service_test import MeetingServiceTest
from tests.util.factories import MeetingBotFactory, TranscriptFactory
from tests.util.service_test import MockUpdateByTenantedPrimaryKeyInput


class TestRecallTranscriptOptionsFactory:
    async def test_recall_transcript_options_factory(self) -> None:
        result = RecallTranscriptOptionsFactory.generate_options(
            RecallTranscriptProvider.get("assembly_ai_async_chunked")
        )
        assert result.provider == RecallTranscriptProvider.ASSEMBLY_AI_ASYNC_CHUNKED
        assert result.assembly_ai_chunked is not None


class TestMeetingBotService(MeetingServiceTest):
    async def test_bot_joining_event_no_meeting_bot(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.JOINING_CALL
            ),
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=None,
        )
        await meeting_bot_service.bot_joining_event(data=recall_event)

    async def test_bot_joining_event(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.JOINING_CALL,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.SCHEDULED,
            external_meeting_bot_id="external_bot_id",
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=meeting_bot,
        )

        updated_meeting_bot = meeting_bot.copy(
            update={"status": MeetingBotStatus.JOINING}
        )
        self.mock_repo_append_bot_status_history_and_update_status(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            input_status=MeetingBotStatus.JOINING,
            result=updated_meeting_bot,
        )

        result = await meeting_bot_service.bot_joining_event(data=recall_event)
        assert result == updated_meeting_bot

    async def test_bot_joining_event_bot_status_update_skipped(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.JOINING_CALL,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )
        # Invalid start state for this function - we do not update state, instead only
        # recording history.
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.IN_CALL_RECORDING,
            external_meeting_bot_id="external_bot_id",
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=meeting_bot,
        )

        self.mock_repo_append_bot_status_history(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            result=meeting_bot,
        )

        result = await meeting_bot_service.bot_joining_event(data=recall_event)
        assert result == meeting_bot
        assert (
            meeting_repository.append_bot_status_history_and_update_status.call_count
            == 0
        )
        assert meeting_repository.append_bot_status_history.call_count == 1

    async def test_bot_in_waiting_room(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.IN_WAITING_ROOM,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )

        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.JOINING,
            external_meeting_bot_id="external_bot_id",
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=meeting_bot,
        )

        updated_meeting_bot = meeting_bot.copy(
            update={"status": MeetingBotStatus.WAITING}
        )
        self.mock_repo_append_bot_status_history_and_update_status(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            input_status=MeetingBotStatus.WAITING,
            result=updated_meeting_bot,
        )

        result = await meeting_bot_service.bot_in_waiting_room_event(data=recall_event)
        assert result == updated_meeting_bot

    async def test_bot_in_call_recording_event_no_recording_id(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.IN_CALL_RECORDING,
                recording_id=None,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )
        result = await meeting_bot_service.bot_in_call_recording_event(
            data=recall_event
        )
        assert result is None

    async def test_bot_in_call_recording_event_no_bot_found(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.IN_CALL_RECORDING,
                recording_id="recording_id",
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )

        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=None,
        )

        await meeting_bot_service.bot_in_call_recording_event(data=recall_event)

    async def test_bot_in_call_recording_event_from_joining(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.IN_CALL_RECORDING,
                recording_id="recording_id",
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )
        existing_meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.JOINING,
            external_meeting_bot_id="external_bot_id",
            external_recording_id=None,
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=existing_meeting_bot,
        )
        updated_meeting_bot = existing_meeting_bot.copy(
            update={"status": MeetingBotStatus.IN_CALL_RECORDING}
        )
        self.mock_repo_append_bot_status_history_and_update_status(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            input_status=MeetingBotStatus.IN_CALL_RECORDING,
            result=updated_meeting_bot,
        )
        expected = updated_meeting_bot.copy(update={"recording_id": "recording_id"})
        self.mock_repo_update_recording_id_by_bot_id(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_recording_id="recording_id",
            result=expected,
        )

        result = await meeting_bot_service.bot_in_call_recording_event(
            data=recall_event
        )
        assert result == expected
        assert (
            meeting_repository.append_bot_status_history_and_update_status.call_count
            == 1
        )
        assert meeting_repository.update_recording_id_by_bot_id.call_count == 1

    async def test_bot_in_call_recording_event_from_recording(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.IN_CALL_RECORDING,
                recording_id="recording_id",
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )
        existing_meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.IN_CALL_RECORDING,
            external_meeting_bot_id="external_bot_id",
            external_recording_id="recording_id",
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=existing_meeting_bot,
        )
        updated_meeting_bot = existing_meeting_bot.copy(
            update={"updated_at": zoned_utc_now()}
        )
        self.mock_repo_append_bot_status_history(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            result=updated_meeting_bot,
        )

        result = await meeting_bot_service.bot_in_call_recording_event(
            data=recall_event
        )
        assert result == updated_meeting_bot

    async def test_bot_recording_permission_denied(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.RECORDING_PERMISSION_ALLOWED,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )
        bot_for_event = meeting_bot_factory.build(
            status=MeetingBotStatus.JOINING, external_meeting_bot_id="external_bot_id"
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=bot_for_event,
        )

        updated_meeting_bot = bot_for_event.copy(
            update={"status": MeetingBotStatus.IN_CALL_RECORDING}
        )
        self.mock_repo_append_bot_status_history_and_update_status(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            input_status=MeetingBotStatus.IN_CALL_RECORDING_DENIED,
            result=updated_meeting_bot,
        )

        result = await meeting_bot_service.bot_recording_permission_denied(
            data=recall_event
        )
        assert result == updated_meeting_bot

    async def test_bot_call_ended_event_no_bot_found(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.CALL_ENDED,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )

        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=None,
        )
        result = await meeting_bot_service.bot_call_ended_event(data=recall_event)
        assert result is None
        assert (
            meeting_repository.append_bot_status_history_and_update_status.call_count
            == 0
        )

    @pytest.mark.parametrize("exited_by_timeout", [True, False])
    async def test_bot_call_ended_event(
        self,
        exited_by_timeout: bool,
        organization_id: UUID,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.CALL_ENDED,
                sub_code=BotStatusSubCode.CALL_ENDED_BY_PLATFORM_WAITING_ROOM_TIMEOUT
                if exited_by_timeout
                else None,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.IN_CALL_RECORDING,
            external_meeting_bot_id="external_bot_id",
            organization_id=organization_id,
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=meeting_bot,
        )

        self.mock_repo_append_bot_status_history_and_update_status(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            input_status=MeetingBotStatus.EXITED,
            result=meeting_bot.copy(update={"status": MeetingBotStatus.EXITED}),
        )

        bot_response = BotResponse(
            id=uuid4(),
            video_url=None,
            media_retention_end=None,
            status_changes=[],
            # One participant that matches what is set up in meeting fixture
            meeting_participants=[
                RecallBotParticipant(
                    id=0,
                    name="Test Reevo Name",
                    events=[],
                    is_host=True,
                    platform="Platform",
                )
            ],
            meeting_url=GmeetMeetingUrl(meeting_id="id", platform="gmeet"),
            join_at=zoned_utc_now(),
            recordings=[],
        )
        recallai_client_v1.get_bot = AsyncMock(return_value=bot_response)

        updated_meeting_bot = meeting_bot.copy(
            update={
                "status": MeetingBotStatus.EXITED,
                "status_history": BotStatusHistory(
                    status_history=[recall_event.get_db_status_event()]
                ),
                "meeting_participant": RecallMeetingParticipants(
                    meeting_participants=[
                        RecallBotParticipant(
                            id=0,
                            name="Test Reevo Name",
                            events=[],
                            is_host=True,
                            platform="Platform",
                        )
                    ]
                ),
            }
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "meeting_participant": updated_meeting_bot.meeting_participant
            },
            update_date_fields=["updated_at"],
            result=updated_meeting_bot,
        )

        result = await meeting_bot_service.bot_call_ended_event(data=recall_event)
        assert result == updated_meeting_bot

    async def test_bot_done_event_no_bot_found(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.DONE,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )

        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=None,
        )
        result = await meeting_bot_service.bot_done_event(data=recall_event)
        assert result is None
        assert (
            meeting_repository.append_bot_status_history_and_update_status.call_count
            == 0
        )

    async def test_bot_done_event(
        self,
        organization_id: UUID,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        recall_bot_factory: RecallBotFactory,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.DONE,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )

        meeting_bot = meeting_bot_factory.build(
            external_meeting_bot_id="external_bot_id", organization_id=organization_id
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=meeting_bot,
        )

        video_url = "http://localhost/video_url"
        recording_start = zoned_utc_now()
        recording_end = zoned_utc_now()
        bot_id = str(uuid4())
        bot_media_ends_at = zoned_utc_now()
        meeting_participants = [
            RecallBotParticipant(
                id=1,
                name="User 1",
                events=[
                    RecallParticipantEvent(
                        code="screen_on",
                        created_at=zoned_utc_now(),
                    ),
                    RecallParticipantEvent(
                        code="screen_off",
                        created_at=(zoned_utc_now() + timedelta(seconds=100)),
                    ),
                    RecallParticipantEvent(
                        code="screen_on",
                        created_at=(zoned_utc_now() + timedelta(seconds=300)),
                    ),
                    RecallParticipantEvent(
                        code="screen_off",
                        created_at=(zoned_utc_now() + timedelta(seconds=400)),
                    ),
                ],
                is_host=True,
                platform="platform",
            ),
            RecallBotParticipant(
                id=2,
                name="User 2",
                events=[
                    RecallParticipantEvent(
                        code="screen_on",
                        created_at=(zoned_utc_now() + timedelta(seconds=50)),
                    ),
                    RecallParticipantEvent(
                        code="screen_off",
                        created_at=(zoned_utc_now() + timedelta(seconds=150)),
                    ),
                ],
                is_host=False,
                platform="platform",
            ),
        ]
        recallai_client_v1.get_bot = AsyncMock(
            return_value=recall_bot_factory.build(
                id=bot_id,
                video_url=video_url,
                media_retention_end=bot_media_ends_at,
                meeting_participants=meeting_participants,
                recordings=[
                    Recording(
                        id="recording_id",
                        started_at=recording_start,
                        completed_at=recording_end,
                    )
                ],
            )
        )

        updated_meeting_bot = meeting_bot.copy(
            update={
                "external_media_url": video_url,
                "external_media_retention_ended_at": zoned_utc_now(),
            }
        )
        self.mock_repo_update_meeting_bot_recording_data(
            mock_meeting_repository=meeting_repository,
            input_organization_id=organization_id,
            input_external_meeting_bot_id="external_bot_id",
            input_external_media_url=video_url,
            input_recording_metadata=RecallRecordingData(
                recordings=[
                    RecallRecording(
                        id="recording_id",
                        started_at=recording_start,
                        completed_at=recording_end,
                    )
                ]
            ),
            intput_screen_share_ranges=[
                ScreenShareRange(start_offset=0, end_offset=150000),
                ScreenShareRange(start_offset=300000, end_offset=400000),
            ],
            input_external_media_retention_ended_at=bot_media_ends_at,
            result=updated_meeting_bot,
        )
        self.mock_repo_append_bot_status_history(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            result=updated_meeting_bot,
        )

        result = await meeting_bot_service.bot_done_event(data=recall_event)
        assert result == updated_meeting_bot

    async def test_bot_analysis_done(
        self,
        recall_bot_event_status_factory: RecallBotEventStatusFactory,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallBotEventData(
            bot_id="external_bot_id",
            status=recall_bot_event_status_factory.build(
                code=RecallStatusCode.ANALYSIS_DONE,
                created_at="2024-04-26T07:54:32.789587+00:00",
            ),
        )
        bot_for_event = meeting_bot_factory.build(
            status=MeetingBotStatus.EXITED, external_meeting_bot_id="external_bot_id"
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_provider(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            result=bot_for_event,
        )

        updated_meeting_bot = bot_for_event.copy(update={"updated_at": zoned_utc_now()})
        self.mock_repo_append_bot_status_history(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_status_event=recall_event.get_db_status_event(),
            result=updated_meeting_bot,
        )

        result = await meeting_bot_service.bot_analysis_done_event(data=recall_event)
        assert result == updated_meeting_bot

    async def test_copy_external_bot_media_to_s3(
        self,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        s3_key = "id/transcripts/another_id/key"
        await meeting_bot_service.copy_external_bot_media_to_s3(
            external_meeting_bot_id=str(uuid4()),
            organization_id=uuid4(),
            external_media_url="external_url",
            s3_key_prefix=s3_key,
            s3_title="title.mp4",
        )

    async def test_persist_external_media_data(
        self,
        meeting_bot_factory: MeetingBotFactory,
        file_service: Mock,  # Change from meeting_bot_service to file_service
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            external_meeting_bot_id="external_bot_id", organization_id=uuid4()
        )
        s3_key = "id/transcripts/another_id/key"
        updated_meeting_bot = meeting_bot.copy(update={"media_url": s3_key})

        file_service.create_and_upload_video_thumbnail_sprite = AsyncMock(
            return_value=("sprite_file_s3_path", "vtt_file_s3_path")
        )

        self.mock_repo_update_unique_by_column_values(
            mock_repository=meeting_repository,
            where_columns={
                "external_meeting_bot_id": "external_bot_id",
                "organization_id": meeting_bot.organization_id,
            },
            update_columns={"media_url": s3_key},
            update_date_fields=["updated_at"],
            result=updated_meeting_bot,
        )

        with mock.patch.object(
            file_service, "create_and_upload_video_thumbnail_sprite"
        ) as mocked:
            mocked.return_value = ("sprite_file_s3_path", "vtt_file_s3_path")
            await meeting_bot_service.persist_external_media_data(
                external_media_url="external_url",
                s3_key_prefix=s3_key,
                external_meeting_bot_id="external_bot_id",
                organization_id=meeting_bot.organization_id,
            )

    async def test_bot_realtime_participant_join_event_no_bot_found(
        self,
        meeting_repository: Mock,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallRealtimeCallData(
            bot_id="external_bot_id",
            participant_id=1,
            created_at="2024-04-26T07:54:32.789587+00:00",
        )
        meeting_id = uuid4()
        self.mock_repo_meeting_bot_lookup_by_external_id_and_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_meeting_id=meeting_id,
            result=None,
        )
        await meeting_bot_service.bot_realtime_participant_join_event(
            data=recall_event, meeting_id=meeting_id, realtime_event_token=None
        )
        assert recallai_client_v1.send_message.call_count == 0

    @pytest.mark.parametrize("realtime_event_token", [None, uuid4()])
    async def test_bot_realtime_participant_join_event_no_notification(
        self,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
        realtime_event_token: UUID | None,
    ) -> None:
        recall_event = RecallRealtimeCallData(
            bot_id="external_bot_id",
            participant_id=1,
            created_at="2024-04-26T07:54:32.789587+00:00",
        )
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.EXITED, realtime_event_token=realtime_event_token
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_meeting_id=meeting_id,
            result=meeting_bot,
        )
        await meeting_bot_service.bot_realtime_participant_join_event(
            data=recall_event,
            meeting_id=meeting_id,
            realtime_event_token=realtime_event_token,
        )
        assert recallai_client_v1.send_message.call_count == 0

    async def test_bot_realtime_participant_join_event_token_mismatch(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_event = RecallRealtimeCallData(
            bot_id="external_bot_id",
            participant_id=0,
            created_at="2024-04-26T07:54:32.789587+00:00",
        )
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.IN_CALL_RECORDING, realtime_event_token=uuid4()
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_meeting_id=meeting_id,
            result=meeting_bot,
        )

        await meeting_bot_service.bot_realtime_participant_join_event(
            data=recall_event, meeting_id=meeting_id, realtime_event_token=uuid4()
        )
        assert recallai_client_v1.send_message.call_count == 0

    @pytest.mark.parametrize("realtime_event_token", [None, uuid4()])
    async def test_bot_realtime_participant_join_event_unmatched_participant(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
        realtime_event_token: UUID | None,
    ) -> None:
        recall_event = RecallRealtimeCallData(
            bot_id="external_bot_id",
            participant_id=0,
            created_at="2024-04-26T07:54:32.789587+00:00",
        )
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.IN_CALL_RECORDING,
            realtime_event_token=realtime_event_token,
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_meeting_id=meeting_id,
            result=meeting_bot,
        )

        bot_response = BotResponse(
            id=uuid4(),
            video_url=None,
            media_retention_end=None,
            status_changes=[],
            # One participant that matches what is set up in meeting fixture
            meeting_participants=[
                RecallBotParticipant(
                    id=1,  # Different from event
                    name="Test Reevo",
                    events=[],
                    is_host=True,
                    platform="Platform",
                )
            ],
            meeting_url=GmeetMeetingUrl(meeting_id="id", platform="gmeet"),
            join_at=zoned_utc_now(),
            recordings=[],
        )
        recallai_client_v1.get_bot = AsyncMock(return_value=bot_response)

        await meeting_bot_service.bot_realtime_participant_join_event(
            data=recall_event,
            meeting_id=meeting_id,
            realtime_event_token=realtime_event_token,
        )
        assert recallai_client_v1.send_message.call_count == 0

    @pytest.mark.parametrize("realtime_event_token", [None, uuid4()])
    async def test_bot_realtime_participant_join_event_host_participant(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
        realtime_event_token: UUID | None,
    ) -> None:
        recall_event = RecallRealtimeCallData(
            bot_id="external_bot_id",
            participant_id=0,
            created_at="2024-04-26T07:54:32.789587+00:00",
        )
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.IN_CALL_RECORDING,
            realtime_event_token=realtime_event_token,
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_meeting_id=meeting_id,
            result=meeting_bot,
        )

        bot_response = BotResponse(
            id=uuid4(),
            video_url=None,
            media_retention_end=None,
            status_changes=[],
            # One participant that matches what is set up in meeting fixture
            meeting_participants=[
                RecallBotParticipant(
                    id=0,
                    name="Test Reevo",
                    events=[],
                    is_host=True,  # Skipped for host
                    platform="Platform",
                )
            ],
            meeting_url=GmeetMeetingUrl(meeting_id="id", platform="gmeet"),
            join_at=zoned_utc_now(),
            recordings=[],
        )
        recallai_client_v1.get_bot = AsyncMock(return_value=bot_response)

        await meeting_bot_service.bot_realtime_participant_join_event(
            data=recall_event,
            meeting_id=meeting_id,
            realtime_event_token=realtime_event_token,
        )
        assert recallai_client_v1.send_message.call_count == 0

    @pytest.mark.parametrize("realtime_event_token", [None, uuid4()])
    async def test_bot_realtime_participant_join_event_with_participant(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
        realtime_event_token: UUID | None,
    ) -> None:
        recall_event = RecallRealtimeCallData(
            bot_id="external_bot_id",
            participant_id=0,
            created_at=zoned_utc_now().strftime("%Y-%m-%dT%H:%M:%S.%f+00:00"),
        )
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.IN_CALL_RECORDING,
            realtime_event_token=realtime_event_token,
        )
        self.mock_repo_meeting_bot_lookup_by_external_id_and_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_external_meeting_bot_id="external_bot_id",
            input_meeting_id=meeting_id,
            result=meeting_bot,
        )

        bot_response = BotResponse(
            id=uuid4(),
            video_url=None,
            media_retention_end=None,
            status_changes=[],
            # One participant that matches what is set up in meeting fixture
            meeting_participants=[
                RecallBotParticipant(
                    id=0,
                    name="Test Reevo",
                    events=[],
                    is_host=False,
                    platform="Platform",
                )
            ],
            meeting_url=GmeetMeetingUrl(meeting_id="id", platform="gmeet"),
            join_at=zoned_utc_now(),
            recordings=[],
        )
        recallai_client_v1.get_bot = AsyncMock(return_value=bot_response)

        await meeting_bot_service.bot_realtime_participant_join_event(
            data=recall_event,
            meeting_id=meeting_id,
            realtime_event_token=realtime_event_token,
        )
        assert recallai_client_v1.send_message.call_count == 1

    async def test_get_latest_meeting_bot_by_meeting_id(
        self,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        created_at = zoned_utc_now()
        meeting_bots = [
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI,
                status=MeetingBotStatus.EXITED,
                created_at=created_at + timedelta(hours=6),
            ),
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI,
                status=MeetingBotStatus.CANCELED,
                created_at=created_at + timedelta(hours=4),
            ),
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI,
                status=MeetingBotStatus.SCHEDULED,
                created_at=created_at + timedelta(hours=2),
            ),
        ]

        self.mock_repo_meeting_bot_lookup_by_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_meeting_id=meeting_id,
            input_organization_id=organization_id,
            result=meeting_bots,
        )
        result = await meeting_bot_service.get_latest_meeting_bot_by_meeting_id(
            meeting_id=meeting_id, organization_id=organization_id
        )
        assert result == meeting_bots[0]

    async def test_get_latest_meeting_bot_by_meeting_id_single_bot(
        self,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        meeting_bots = [
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI, status=MeetingBotStatus.EXITED
            ),
        ]

        self.mock_repo_meeting_bot_lookup_by_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_meeting_id=meeting_id,
            input_organization_id=organization_id,
            result=meeting_bots,
        )
        result = await meeting_bot_service.get_latest_meeting_bot_by_meeting_id(
            meeting_id=meeting_id, organization_id=organization_id
        )
        assert result == meeting_bots[0]

    async def test_get_latest_meeting_bot_by_meeting_id_no_bots(
        self, meeting_repository: Mock, meeting_bot_service: MeetingBotService
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        self.mock_repo_meeting_bot_lookup_by_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_meeting_id=meeting_id,
            input_organization_id=organization_id,
            result=[],
        )
        result = await meeting_bot_service.get_latest_meeting_bot_by_meeting_id(
            meeting_id=meeting_id, organization_id=organization_id
        )
        assert result is None

    async def test_get_active_meeting_bot_by_meeting_id_active_found(
        self,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        meeting_bots = [
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI, status=MeetingBotStatus.EXITED
            ),
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI, status=MeetingBotStatus.CANCELED
            ),
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI, status=MeetingBotStatus.SCHEDULED
            ),
        ]

        self.mock_repo_meeting_bot_lookup_by_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_meeting_id=meeting_id,
            input_organization_id=organization_id,
            result=meeting_bots,
        )
        result = await meeting_bot_service.get_active_meeting_bot_by_meeting_id(
            meeting_id=meeting_id, organization_id=organization_id
        )
        assert result == meeting_bots[-1]

    async def test_get_active_meeting_bot_by_meeting_id_no_active_bots(
        self,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        meeting_bots = [
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI, status=MeetingBotStatus.EXITED
            ),
            meeting_bot_factory.build(
                provider=BotProvider.RECALLAI, status=MeetingBotStatus.CANCELED
            ),
        ]

        self.mock_repo_meeting_bot_lookup_by_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_meeting_id=meeting_id,
            input_organization_id=organization_id,
            result=meeting_bots,
        )
        result = await meeting_bot_service.get_active_meeting_bot_by_meeting_id(
            meeting_id=meeting_id, organization_id=organization_id
        )
        assert result is None

    async def test_get_active_meeting_bot_by_meeting_id_no_bots(
        self, meeting_repository: Mock, meeting_bot_service: MeetingBotService
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        self.mock_repo_meeting_bot_lookup_by_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_meeting_id=meeting_id,
            input_organization_id=organization_id,
            result=[],
        )
        result = await meeting_bot_service.get_active_meeting_bot_by_meeting_id(
            meeting_id=meeting_id, organization_id=organization_id
        )
        assert result is None

    async def test_get_all_meeting_bots_by_meeting_id(
        self,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_id = uuid4()
        organization_id = uuid4()
        meeting_bots = [
            meeting_bot_factory.build(provider=BotProvider.RECALLAI) for _ in range(3)
        ]

        self.mock_repo_meeting_bot_lookup_by_meeting_id(
            mock_meeting_repository=meeting_repository,
            input_meeting_id=meeting_id,
            input_organization_id=organization_id,
            result=meeting_bots,
        )
        result = await meeting_bot_service.get_all_meeting_bots_by_meeting_id(
            meeting_id=meeting_id, organization_id=organization_id
        )
        assert result == meeting_bots

    async def test_filter_latest_meeting_bot_empty_list(self) -> None:
        assert MeetingBotService.filter_latest_meeting_bot(meeting_bots=[]) is None

    async def test_filter_latest_meeting_bot_single_bot(
        self,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(provider=BotProvider.RECALLAI)
        assert (
            MeetingBotService.filter_latest_meeting_bot(meeting_bots=[meeting_bot])
            == meeting_bot
        )

    async def test_filter_latest_meeting_bot_other_provider(
        self,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(provider="Something")
        assert (
            MeetingBotService.filter_latest_meeting_bot(
                meeting_bots=[meeting_bot], bot_provider=BotProvider.RECALLAI
            )
            is None
        )

    async def test_filter_latest_meeting_bot_multiple_bots(
        self,
        meeting_bot_factory: MeetingBotFactory,
    ) -> None:
        earlier_meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI, created_at=zoned_utc_now()
        )
        later_meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI,
            created_at=zoned_utc_now() + timedelta(hours=1),
        )
        assert (
            MeetingBotService.filter_latest_meeting_bot(
                meeting_bots=[earlier_meeting_bot, later_meeting_bot]
            )
            == later_meeting_bot
        )

    async def test_update_bot_settings_in_provider_bot_not_found(
        self,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot_id = uuid4()
        organization_id = uuid4()

        self.mock_repo_find_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            input_lookup_fields={"id": meeting_bot_id},
            result=None,
        )

        result = await meeting_bot_service.update_bot_settings_in_provider(
            meeting_bot_id=meeting_bot_id,
            organization_id=organization_id,
        )
        assert result is False

    async def test_update_bot_settings_in_provider_success(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot_id = uuid4()
        meeting_bot_id = uuid4()
        organization_id = uuid4()
        meeting_bot = meeting_bot_factory.build(
            id=meeting_bot_id,
            external_meeting_bot_id="external_bot_id",
        )

        self.mock_repo_find_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            input_lookup_fields={"id": meeting_bot_id},
            result=meeting_bot,
        )

        self.mock_recall_update_bot(
            mock_recall_client=recallai_client_v1,
            input_external_bot_id="external_bot_id",
            input_request=UpdateRecallBotRequest(
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
            ),
            result="external_bot_id",
        )

        result = await meeting_bot_service.update_bot_settings_in_provider(
            meeting_bot_id=meeting_bot_id,
            organization_id=organization_id,
        )
        assert result is True

    async def test_change_name_for_scheduled_bots_no_bots_to_modify(
        self,
        organization_id: UUID,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        user_id = uuid4()
        self.mock_repo_find_future_meeting_bots_by_meeting_user_id(
            mock_meeting_repository=meeting_repository,
            input_organization_id=organization_id,
            input_user_id=user_id,
            result=[],
        )
        result = await meeting_bot_service.change_name_for_scheduled_bots(
            organization_id=organization_id,
            user_id=user_id,
            new_bot_name="new_bot_name",
        )
        assert result == []

    async def test_change_name_for_scheduled_bots(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        meeting_repository: Mock,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        user_id = uuid4()
        meeting_bot = meeting_bot_factory.build(
            status=MeetingBotStatus.SCHEDULED,
            provider=BotProvider.RECALLAI,
            external_meeting_bot_id="external_meeting_bot_id",
        )
        self.mock_repo_find_future_meeting_bots_by_meeting_user_id(
            mock_meeting_repository=meeting_repository,
            input_organization_id=organization_id,
            input_user_id=user_id,
            result=[meeting_bot],
        )

        self.mock_recall_update_bot(
            mock_recall_client=recallai_client_v1,
            input_external_bot_id="external_meeting_bot_id",
            input_request=UpdateRecallBotRequest(
                bot_name="new_bot_name",
            ),
            result="external_meeting_bot_id",
        )
        result_meeting_bot = meeting_bot.model_copy(update={"name": "new_bot_name"})
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "name": "new_bot_name",
            },
            update_date_fields=["updated_at"],
            result=result_meeting_bot,
        )
        result = await meeting_bot_service.change_name_for_scheduled_bots(
            organization_id=organization_id,
            user_id=user_id,
            new_bot_name="new_bot_name",
        )
        assert result == [result_meeting_bot]

    async def test_create_scheduled_meeting_bot_unsupported_provider(
        self,
        organization_id: UUID,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        with pytest.raises(InvalidArgumentError):
            await meeting_bot_service.create_scheduled_meeting_bot(
                meeting_id=uuid4(),
                meeting_url="url",
                bot_name="bot",
                meeting_provider=MeetingProvider.VOICE,
                scheduled_at=zoned_utc_now(),
                organization_id=organization_id,
            )

    async def test_create_scheduled_meeting_bot_no_optional_parameters(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        transcript_factory: TranscriptFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_starts_at = zoned_utc_now()
        bot_join_time = meeting_starts_at - timedelta(minutes=2)
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build()

        self.mock_recall_create_bot(
            mock_recall_client=recallai_client_v1,
            input_request=CreateRecallBotRequest(
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                join_at=bot_join_time,
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
                metadata={"meeting_id": str(meeting_id)},
            ),
            input_idempotency_key=f"{organization_id}-https://meet.localhost-{meeting_bot.id}-{bot_join_time}",
            result="external_bot_id",
        )

        self.mock_repo_meeting_bot_and_transcript_insert(
            mock_meeting_repository=meeting_repository,
            input_bot_provider=BotProvider.RECALLAI,
            input_meeting_bot_name="bot name",
            input_transcript_provider=TranscriptProvider(
                settings.recall_default_transcript_provider
            ),
            input_organization_id=organization_id,
            input_scheduled_at=bot_join_time,
            input_meeting_id=meeting_id,
            input_meeting_url="https://meet.localhost",
            result=(meeting_bot, transcript_factory.build()),
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "status": MeetingBotStatus.SCHEDULED,
                "external_meeting_bot_id": "external_bot_id",
            },
            update_date_fields=["updated_at"],
            result=meeting_bot,
        )
        result = await meeting_bot_service.create_scheduled_meeting_bot(
            meeting_id=meeting_id,
            meeting_url="https://meet.localhost",
            bot_name="bot name",
            meeting_provider=MeetingProvider.GMEET,
            scheduled_at=bot_join_time,
            organization_id=organization_id,
        )
        assert result == meeting_bot

    @patch("salestech_be.core.meeting.meeting_bot_service.settings")
    async def test_create_scheduled_meeting_bot_exists_retry_disabled(
        self,
        mock_settings: Mock,
        organization_id: UUID,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        mock_settings.enable_google_meet_authenticated_bot = False
        mock_settings.recall_realtime_transcript_enabled = False
        mock_settings.enable_recall_call_events = False
        mock_settings.recall_default_transcript_provider = "assemblyai"
        mock_settings.enable_recall_recording_mode_options = False

        meeting_starts_at = zoned_utc_now()
        meeting_id = uuid4()

        meeting_repository.create_meeting_bot_transcript_initial_rows = AsyncMock(
            side_effect=UniqueViolationError
        )
        with pytest.raises(UniqueViolationError):
            await meeting_bot_service.create_scheduled_meeting_bot(
                meeting_id=meeting_id,
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                meeting_provider=MeetingProvider.GMEET,
                scheduled_at=meeting_starts_at,
                organization_id=organization_id,
            )

    @patch("salestech_be.core.meeting.meeting_bot_service.settings")
    async def test_create_scheduled_meeting_bot_exists_not_in_pending(
        self,
        mock_settings: Mock,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        transcript_factory: TranscriptFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        mock_settings.enable_google_meet_authenticated_bot = False
        mock_settings.recall_realtime_transcript_enabled = False
        mock_settings.enable_recall_call_events = False
        mock_settings.recall_default_transcript_provider = "assemblyai"
        mock_settings.enable_recall_recording_mode_options = False

        meeting_starts_at = zoned_utc_now()
        bot_join_time = meeting_starts_at - timedelta(minutes=2)
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build(status=MeetingBotStatus.SCHEDULED)

        self.mock_recall_create_bot(
            mock_recall_client=recallai_client_v1,
            input_request=CreateRecallBotRequest(
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                join_at=bot_join_time,
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
                metadata={"meeting_id": str(meeting_id)},
            ),
            input_idempotency_key=f"{organization_id}-https://meet.localhost-{meeting_bot.id}-{bot_join_time}",
            result="external_bot_id",
        )

        meeting_repository.create_meeting_bot_transcript_initial_rows = AsyncMock(
            side_effect=UniqueViolationError
        )

        self.mock_repo_find_meeting_bots_by_meeting_url_and_time(
            mock_meeting_repository=meeting_repository,
            input_organization_id=organization_id,
            input_meeting_id=meeting_id,
            input_meeting_url="https://meet.localhost",
            input_scheduled_at=bot_join_time,
            result=[meeting_bot],
        )

        self.mock_repo_find_transcript_for_meeting_bot(
            mock_meeting_repository=meeting_repository,
            input_organization_id=organization_id,
            input_meeting_bot_id=meeting_bot.id,
            result=transcript_factory.build(),
        )
        with pytest.raises(UniqueViolationError):
            await meeting_bot_service.create_scheduled_meeting_bot(
                meeting_id=meeting_id,
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                meeting_provider=MeetingProvider.GMEET,
                scheduled_at=bot_join_time,
                organization_id=organization_id,
            )

    @patch("salestech_be.core.meeting.meeting_bot_service.settings")
    async def test_create_scheduled_meeting_bot_exists_retry_enabled(
        self,
        mock_settings: Mock,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        transcript_factory: TranscriptFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        mock_settings.enable_google_meet_authenticated_bot = False
        mock_settings.recall_realtime_transcript_enabled = False
        mock_settings.enable_recall_call_events = False
        mock_settings.recall_default_transcript_provider = "assemblyai"
        mock_settings.enable_recall_recording_mode_options = False

        meeting_starts_at = zoned_utc_now()
        bot_join_time = meeting_starts_at - timedelta(minutes=2)
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build(status=MeetingBotStatus.PENDING)

        self.mock_recall_create_bot(
            mock_recall_client=recallai_client_v1,
            input_request=CreateRecallBotRequest(
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                join_at=bot_join_time,
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
                metadata={"meeting_id": str(meeting_id)},
            ),
            input_idempotency_key=f"{organization_id}-https://meet.localhost-{meeting_bot.id}-{bot_join_time}",
            result="external_bot_id",
        )

        meeting_repository.create_meeting_bot_transcript_initial_rows = AsyncMock(
            side_effect=UniqueViolationError
        )

        self.mock_repo_find_meeting_bots_by_meeting_url_and_time(
            mock_meeting_repository=meeting_repository,
            input_organization_id=organization_id,
            input_meeting_id=meeting_id,
            input_meeting_url="https://meet.localhost",
            input_scheduled_at=bot_join_time,
            result=[meeting_bot],
        )

        self.mock_repo_find_transcript_for_meeting_bot(
            mock_meeting_repository=meeting_repository,
            input_organization_id=organization_id,
            input_meeting_bot_id=meeting_bot.id,
            result=transcript_factory.build(),
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "status": MeetingBotStatus.SCHEDULED,
                "external_meeting_bot_id": "external_bot_id",
            },
            update_date_fields=["updated_at"],
            result=meeting_bot,
        )
        result = await meeting_bot_service.create_scheduled_meeting_bot(
            meeting_id=meeting_id,
            meeting_url="https://meet.localhost",
            bot_name="bot name",
            meeting_provider=MeetingProvider.GMEET,
            scheduled_at=bot_join_time,
            organization_id=organization_id,
        )
        assert result == meeting_bot

    @patch("salestech_be.core.meeting.meeting_bot_service.settings")
    async def test_create_scheduled_meeting_bot_with_realtime_events(
        self,
        mock_settings: Mock,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        transcript_factory: TranscriptFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_google_login_group_id = str(uuid4())
        mock_settings.recall_realtime_transcript_enabled = False
        mock_settings.enable_recall_call_events = True
        mock_settings.enable_recall_recording_mode_options = False
        mock_settings.recall_realtime_transcript_webhook_url = "https://localhost/bot"
        mock_settings.enable_google_meet_authenticated_bot = True
        mock_settings.recall_google_login_group_id = recall_google_login_group_id
        mock_settings.recall_async_transcriber_org_overrides = {}
        mock_settings.recall_default_transcript_provider = "assemblyai"

        meeting_starts_at = zoned_utc_now()
        bot_join_time = meeting_starts_at - timedelta(minutes=2)
        meeting_id = uuid4()
        realtime_event_token = uuid4()
        meeting_bot = meeting_bot_factory.build()

        self.mock_recall_create_bot(
            mock_recall_client=recallai_client_v1,
            input_request=CreateRecallBotRequest(
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                join_at=bot_join_time,
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
                metadata={"meeting_id": str(meeting_id)},
                real_time_media=RealtimeMedia(
                    webhook_call_events_destination_url=f"https://localhost/bot?meeting_id={meeting_id}&realtime_event_token={realtime_event_token}"
                ),
                google_meet=GoogleMeetOptions(
                    login_required=True,
                    google_login_group_id=recall_google_login_group_id,
                ),
            ),
            input_idempotency_key=f"{organization_id}-https://meet.localhost-{meeting_bot.id}-{bot_join_time}",
            result="external_bot_id",
        )

        self.mock_repo_meeting_bot_and_transcript_insert(
            mock_meeting_repository=meeting_repository,
            input_bot_provider=BotProvider.RECALLAI,
            input_meeting_bot_name="bot name",
            input_transcript_provider=TranscriptProvider.ASSEMBLYAI,
            input_organization_id=organization_id,
            input_scheduled_at=bot_join_time,
            input_meeting_id=meeting_id,
            input_meeting_url="https://meet.localhost",
            result=(meeting_bot, transcript_factory.build()),
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "status": MeetingBotStatus.SCHEDULED,
                "external_meeting_bot_id": "external_bot_id",
            },
            update_date_fields=["updated_at"],
            result=meeting_bot,
        )

        with mock.patch("uuid.uuid4", return_value=realtime_event_token):
            result = await meeting_bot_service.create_scheduled_meeting_bot(
                meeting_id=meeting_id,
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                meeting_provider=MeetingProvider.GMEET,
                scheduled_at=bot_join_time,
                organization_id=organization_id,
            )
        assert result == meeting_bot

    @patch("salestech_be.core.meeting.meeting_bot_service.settings")
    async def test_create_scheduled_meeting_bot_with_realtime_transcript(
        self,
        mock_settings: Mock,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        transcript_factory: TranscriptFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_google_login_group_id = str(uuid4())
        mock_settings.recall_realtime_transcript_enabled = True
        mock_settings.enable_recall_call_events = False
        mock_settings.enable_recall_recording_mode_options = False
        mock_settings.recall_realtime_transcript_webhook_url = "https://localhost/bot"
        mock_settings.recall_realtime_transcript_provider = "assembly_ai"
        mock_settings.recall_realtime_partial_results = False
        mock_settings.enable_google_meet_authenticated_bot = True
        mock_settings.recall_google_login_group_id = recall_google_login_group_id
        mock_settings.recall_async_transcriber_org_overrides = {}
        mock_settings.recall_default_transcript_provider = "assemblyai"

        meeting_starts_at = zoned_utc_now()
        bot_join_time = meeting_starts_at - timedelta(minutes=2)
        meeting_id = uuid4()
        realtime_event_token = uuid4()
        meeting_bot = meeting_bot_factory.build()

        self.mock_recall_create_bot(
            mock_recall_client=recallai_client_v1,
            input_request=CreateRecallBotRequest(
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                join_at=bot_join_time,
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
                metadata={"meeting_id": str(meeting_id)},
                transcription_options=TranscriptionOptions(provider="assembly_ai"),
                real_time_transcription=RealtimeTranscription(
                    destination_url=f"https://localhost/bot?meeting_id={meeting_id}&realtime_event_token={realtime_event_token}",
                    partial_results=False,
                    enhanced_diarization=False,
                ),
                google_meet=GoogleMeetOptions(
                    login_required=True,
                    google_login_group_id=recall_google_login_group_id,
                ),
            ),
            input_idempotency_key=f"{organization_id}-https://meet.localhost-{meeting_bot.id}-{bot_join_time}",
            result="external_bot_id",
        )

        self.mock_repo_meeting_bot_and_transcript_insert(
            mock_meeting_repository=meeting_repository,
            input_bot_provider=BotProvider.RECALLAI,
            input_meeting_bot_name="bot name",
            input_transcript_provider=TranscriptProvider.ASSEMBLYAI,
            input_organization_id=organization_id,
            input_scheduled_at=bot_join_time,
            input_meeting_id=meeting_id,
            input_meeting_url="https://meet.localhost",
            result=(meeting_bot, transcript_factory.build()),
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "status": MeetingBotStatus.SCHEDULED,
                "external_meeting_bot_id": "external_bot_id",
            },
            update_date_fields=["updated_at"],
            result=meeting_bot,
        )

        with mock.patch("uuid.uuid4", return_value=realtime_event_token):
            result = await meeting_bot_service.create_scheduled_meeting_bot(
                meeting_id=meeting_id,
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                meeting_provider=MeetingProvider.GMEET,
                scheduled_at=bot_join_time,
                organization_id=organization_id,
            )
        assert result == meeting_bot

    @patch("salestech_be.core.meeting.meeting_bot_service.settings")
    async def test_create_scheduled_meeting_bot_with_recording_options(
        self,
        mock_settings: Mock,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        transcript_factory: TranscriptFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        recall_google_login_group_id = str(uuid4())
        mock_settings.recall_realtime_transcript_enabled = False
        mock_settings.enable_recall_call_events = False
        mock_settings.enable_recall_recording_mode_options = True
        mock_settings.enable_google_meet_authenticated_bot = True
        mock_settings.recall_google_login_group_id = recall_google_login_group_id
        mock_settings.recall_async_transcriber_org_overrides = {}
        mock_settings.recall_default_transcript_provider = "assemblyai"

        meeting_starts_at = zoned_utc_now()
        bot_join_time = meeting_starts_at - timedelta(minutes=2)
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build()

        self.mock_recall_create_bot(
            mock_recall_client=recallai_client_v1,
            input_request=CreateRecallBotRequest(
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                join_at=bot_join_time,
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
                metadata={"meeting_id": str(meeting_id)},
                recording_mode_options=RecordingModeOptions(
                    start_recording_on=StartRecordingOn.CALL_JOIN,
                    participant_video_when_screenshare=VideoScreenShare.BESIDE,
                ),
                google_meet=GoogleMeetOptions(
                    login_required=True,
                    google_login_group_id=recall_google_login_group_id,
                ),
            ),
            input_idempotency_key=f"{organization_id}-https://meet.localhost-{meeting_bot.id}-{bot_join_time}",
            result="external_bot_id",
        )

        self.mock_repo_meeting_bot_and_transcript_insert(
            mock_meeting_repository=meeting_repository,
            input_bot_provider=BotProvider.RECALLAI,
            input_meeting_bot_name="bot name",
            input_transcript_provider=TranscriptProvider.ASSEMBLYAI,
            input_organization_id=organization_id,
            input_scheduled_at=bot_join_time,
            input_meeting_id=meeting_id,
            input_meeting_url="https://meet.localhost",
            result=(meeting_bot, transcript_factory.build()),
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "status": MeetingBotStatus.SCHEDULED,
                "external_meeting_bot_id": "external_bot_id",
            },
            update_date_fields=["updated_at"],
            result=meeting_bot,
        )

        result = await meeting_bot_service.create_scheduled_meeting_bot(
            meeting_id=meeting_id,
            meeting_url="https://meet.localhost",
            bot_name="bot name",
            meeting_provider=MeetingProvider.GMEET,
            scheduled_at=bot_join_time,
            organization_id=organization_id,
        )
        assert result == meeting_bot

    @patch("salestech_be.core.meeting.meeting_bot_service.settings")
    async def test_create_scheduled_meeting_bot_with_bot_detection(
        self,
        mock_settings: Mock,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        transcript_factory: TranscriptFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        mock_settings.recall_realtime_transcript_enabled = False
        mock_settings.enable_recall_call_events = False
        mock_settings.enable_recall_recording_mode_options = False
        mock_settings.enable_google_meet_authenticated_bot = False
        mock_settings.recall_async_transcriber_org_overrides = {}
        mock_settings.recall_default_transcript_provider = "assemblyai"

        meeting_starts_at = zoned_utc_now()
        bot_join_time = meeting_starts_at - timedelta(minutes=2)
        meeting_id = uuid4()
        meeting_bot = meeting_bot_factory.build()

        self.mock_recall_create_bot(
            mock_recall_client=recallai_client_v1,
            input_request=CreateRecallBotRequest(
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                join_at=bot_join_time,
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
                metadata={"meeting_id": str(meeting_id)},
            ),
            input_idempotency_key=f"{organization_id}-https://meet.localhost-{meeting_bot.id}-{bot_join_time}",
            result="external_bot_id",
        )

        self.mock_repo_meeting_bot_and_transcript_insert(
            mock_meeting_repository=meeting_repository,
            input_bot_provider=BotProvider.RECALLAI,
            input_meeting_bot_name="bot name",
            input_transcript_provider=TranscriptProvider.ASSEMBLYAI,
            input_organization_id=organization_id,
            input_scheduled_at=bot_join_time,
            input_meeting_id=meeting_id,
            input_meeting_url="https://meet.localhost",
            result=(meeting_bot, transcript_factory.build()),
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "status": MeetingBotStatus.SCHEDULED,
                "external_meeting_bot_id": "external_bot_id",
            },
            update_date_fields=["updated_at"],
            result=meeting_bot,
        )

        result = await meeting_bot_service.create_scheduled_meeting_bot(
            meeting_id=meeting_id,
            meeting_url="https://meet.localhost",
            bot_name="bot name",
            meeting_provider=MeetingProvider.GMEET,
            scheduled_at=bot_join_time,
            organization_id=organization_id,
        )
        assert result == meeting_bot

    async def test_update_scheduled_meeting_bot_invalid_provider(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        with pytest.raises(InvalidArgumentError):
            await meeting_bot_service.update_scheduled_meeting_bot(
                meeting_bot=meeting_bot_factory.build(),
                meeting_id=uuid4(),
                meeting_url="url",
                bot_name="bot",
                meeting_provider=MeetingProvider.VOICE,
                scheduled_at=calculate_bot_join_time(zoned_utc_now()),
                organization_id=organization_id,
            )

    async def test_update_scheduled_meeting_bot_future_start_time(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI,
            status=MeetingBotStatus.SCHEDULED,
            external_meeting_bot_id="external_bot_id",
        )
        meeting_starts_at = zoned_utc_now() + timedelta(minutes=15)
        bot_join_time = calculate_bot_join_time(meeting_starts_at)
        self.mock_recall_update_bot(
            mock_recall_client=recallai_client_v1,
            input_external_bot_id="external_bot_id",
            input_request=UpdateRecallBotRequest(
                meeting_url="url",
                bot_name="bot name",
                join_at=bot_join_time,
            ),
            result="external_bot_id_update",
        )
        result_meeting_bot = meeting_bot.copy(
            update={
                "scheduled_at": bot_join_time,
                "external_meeting_bot_id": "external_bot_id_update",
                "name": "bot name",
            }
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "external_meeting_bot_id": "external_bot_id_update",
                "scheduled_at": bot_join_time,
                "name": "bot name",
            },
            update_date_fields=["updated_at"],
            result=result_meeting_bot,
        )
        result = await meeting_bot_service.update_scheduled_meeting_bot(
            meeting_bot=meeting_bot,
            meeting_id=meeting_bot.meeting_id,
            meeting_url="url",
            bot_name="bot name",
            meeting_provider=MeetingProvider.GMEET,
            scheduled_at=calculate_bot_join_time(meeting_starts_at),
            organization_id=organization_id,
        )
        assert result == result_meeting_bot

    async def test_update_scheduled_meeting_bot_scheduled_to_within_five_minutes(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        transcript_factory: TranscriptFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI,
            status=MeetingBotStatus.SCHEDULED,
            external_meeting_bot_id="external_bot_id",
            organization_id=organization_id,
        )
        meeting_starts_at = zoned_utc_now() + timedelta(minutes=5)
        bot_join_time = calculate_bot_join_time(meeting_starts_at)

        # Old gets deleted
        self.mock_recall_delete_bot(
            mock_recall_client=recallai_client_v1, input_bot_id="external_bot_id"
        )
        deleted_meeting_bot = meeting_bot.copy(
            update={
                "status": MeetingBotStatus.CANCELED,
            }
        )

        # New one takes its place
        meeting_id = uuid4()
        new_meeting_bot = meeting_bot_factory.build()
        self.mock_recall_create_bot(
            mock_recall_client=recallai_client_v1,
            input_request=CreateRecallBotRequest(
                meeting_url="https://meet.localhost",
                bot_name="bot name",
                join_at=bot_join_time,
                automatic_leave=AutomaticLeaveSettings(
                    everyone_left_timeout=30,
                    waiting_room_timeout=1500,
                    bot_detection=BotDetectionSettings(
                        using_participant_names=BotDetectionUsingParticipantNames(
                            matches=["Notetaker", "Recorder"],
                            timeout=30,
                            activate_after=30,
                        )
                    ),
                ),
                metadata={"meeting_id": str(meeting_id)},
            ),
            input_idempotency_key=f"{organization_id}-https://meet.localhost-{new_meeting_bot.id}-{bot_join_time}",
            result="external_bot_id_2",
        )

        self.mock_repo_meeting_bot_and_transcript_insert(
            mock_meeting_repository=meeting_repository,
            input_bot_provider=BotProvider.RECALLAI,
            input_meeting_bot_name="bot name",
            input_transcript_provider=TranscriptProvider(
                settings.recall_default_transcript_provider
            ),
            input_organization_id=organization_id,
            input_scheduled_at=bot_join_time,
            input_meeting_id=meeting_id,
            input_meeting_url="https://meet.localhost",
            result=(new_meeting_bot, transcript_factory.build()),
        )

        self.mock_repo_update_by_tenanted_primary_key_sequence(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            repo_mock_sequence=[
                MockUpdateByTenantedPrimaryKeyInput(
                    where_columns={"id": meeting_bot.id},
                    update_columns={"status": MeetingBotStatus.CANCELED},
                    update_date_fields=["updated_at", "deleted_at"],
                    result=deleted_meeting_bot,
                ),
                MockUpdateByTenantedPrimaryKeyInput(
                    where_columns={"id": new_meeting_bot.id},
                    update_columns={
                        "status": MeetingBotStatus.SCHEDULED,
                        "external_meeting_bot_id": "external_bot_id_2",
                    },
                    update_date_fields=["updated_at"],
                    result=new_meeting_bot,
                ),
            ],
        )

        result = await meeting_bot_service.update_scheduled_meeting_bot(
            meeting_bot=meeting_bot,
            meeting_id=meeting_id,
            meeting_url="https://meet.localhost",
            bot_name="bot name",
            meeting_provider=MeetingProvider.GMEET,
            scheduled_at=calculate_bot_join_time(meeting_starts_at),
            organization_id=organization_id,
        )
        assert result == new_meeting_bot

    async def test_remove_bot_from_meeting_with_message(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI,
            status=MeetingBotStatus.IN_CALL_RECORDING,
            external_meeting_bot_id="external_bot_id",
            organization_id=organization_id,
        )
        self.mock_recall_send_message(
            mock_recall_client=recallai_client_v1,
            input_bot_id="external_bot_id",
            input_request=SendChatMessageRequest(message="Terminate message"),
        )
        self.mock_recall_leave_call(
            mock_recall_client=recallai_client_v1, input_bot_id="external_bot_id"
        )
        removed_meeting_bot = meeting_bot.copy(
            update={
                "status": MeetingBotStatus.EXITED,
            }
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "status": MeetingBotStatus.EXITED,
            },
            update_date_fields=["updated_at"],
            result=removed_meeting_bot,
        )
        with patch("asyncio.sleep", return_value=AsyncMock()):
            result = await meeting_bot_service.remove_bot_from_meeting(
                meeting_bot=meeting_bot,
                is_consent_denied=False,
                chat_notification_message="Terminate message",
            )
            assert result == removed_meeting_bot

    async def test_remove_bot_from_meeting_no_action_needed(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI,
            status=MeetingBotStatus.EXITED,
            external_meeting_bot_id="external_bot_id",
            organization_id=organization_id,
        )
        self.mock_recall_send_message(
            mock_recall_client=recallai_client_v1,
            input_bot_id="external_bot_id",
            input_request=SendChatMessageRequest(message="Terminate message"),
        )

        result = await meeting_bot_service.remove_bot_from_meeting(
            meeting_bot=meeting_bot,
            is_consent_denied=False,
            chat_notification_message="Terminate message",
        )
        assert result == meeting_bot

    async def test_remove_bot_from_meeting_bot_not_found_in_provider(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI,
            status=MeetingBotStatus.SCHEDULED,
            external_meeting_bot_id="external_bot_id",
            organization_id=organization_id,
        )
        recallai_client_v1.delete_bot = AsyncMock(
            side_effect=ExternalServiceError(return_http_code=404)
        )
        removed_meeting_bot = meeting_bot.copy(
            update={
                "status": MeetingBotStatus.CANCELED,
            }
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={
                "status": MeetingBotStatus.CANCELED,
            },
            update_date_fields=["updated_at", "deleted_at"],
            result=removed_meeting_bot,
        )
        with patch("asyncio.sleep", return_value=AsyncMock()):
            result = await meeting_bot_service.remove_bot_from_meeting(
                meeting_bot=meeting_bot,
                is_consent_denied=False,
                chat_notification_message=None,
            )
            assert result == removed_meeting_bot

    async def test_pause_bot_recording_with_message_success(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            external_meeting_bot_id="external_bot_id"
        )
        self.mock_recall_pause_recording(
            mock_recall_client=recallai_client_v1, input_bot_id="external_bot_id"
        )
        self.mock_recall_send_message(
            mock_recall_client=recallai_client_v1,
            input_bot_id="external_bot_id",
            input_request=SendChatMessageRequest(message="Pause message"),
        )
        result = await meeting_bot_service.pause_bot_recording_with_message(
            meeting_bot=meeting_bot,
            chat_message="Pause message",
            raise_provider_error=True,
        )
        assert result is True

    async def test_pause_bot_recording_with_message_error_raised(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            external_meeting_bot_id="external_bot_id"
        )
        recallai_client_v1.pause_recording.side_effect = TimeoutException(
            message="timeout"
        )
        with pytest.raises(TimeoutException):
            await meeting_bot_service.pause_bot_recording_with_message(
                meeting_bot=meeting_bot,
                chat_message="Pause message",
                raise_provider_error=True,
            )

    async def test_pause_bot_recording_with_message_error_but_not_raised(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            external_meeting_bot_id="external_bot_id"
        )
        recallai_client_v1.pause_recording.side_effect = TimeoutException(
            message="timeout"
        )
        result = await meeting_bot_service.pause_bot_recording_with_message(
            meeting_bot=meeting_bot,
            chat_message="Pause message",
            raise_provider_error=False,
        )
        assert result is False

    def mock_recall_pause_recording(
        self, mock_recall_client: Mock, input_bot_id: str
    ) -> None:
        def pause(bot_id: str) -> None:
            assert bot_id == input_bot_id

        mock_recall_client.pause_recording.side_effect = pause

    async def test_resume_bot_recording_with_message(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            external_meeting_bot_id="external_bot_id"
        )
        self.mock_recall_resume_recording(
            mock_recall_client=recallai_client_v1, input_bot_id="external_bot_id"
        )
        self.mock_recall_send_message(
            mock_recall_client=recallai_client_v1,
            input_bot_id="external_bot_id",
            input_request=SendChatMessageRequest(message="Resume message"),
        )
        await meeting_bot_service.resume_bot_recording_with_message(
            meeting_bot=meeting_bot, chat_message="Resume message"
        )

    async def test_start_bot_recording_with_message(
        self,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            external_meeting_bot_id="external_bot_id"
        )
        self.mock_recall_start_recording(
            mock_recall_client=recallai_client_v1, input_bot_id="external_bot_id"
        )
        self.mock_recall_send_message(
            mock_recall_client=recallai_client_v1,
            input_bot_id="external_bot_id",
            input_request=SendChatMessageRequest(message="Start message"),
        )
        await meeting_bot_service.start_bot_recording_with_message(
            meeting_bot=meeting_bot, chat_message="Start message"
        )

    async def test_halt_bot_recording_for_consent_currently_recording(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            organization_id=organization_id,
            external_meeting_bot_id="external_bot_id",
            status=MeetingBotStatus.IN_CALL_RECORDING,
        )

        self.mock_recall_pause_recording(
            mock_recall_client=recallai_client_v1, input_bot_id="external_bot_id"
        )
        self.mock_recall_send_message(
            mock_recall_client=recallai_client_v1,
            input_bot_id="external_bot_id",
            input_request=SendChatMessageRequest(
                message="Not all participants consent to recording, so I will stop recording now."
            ),
        )
        updated_meeting_bot = meeting_bot.copy(
            update={"status": MeetingBotStatus.IN_CALL_PAUSED}
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={"status": MeetingBotStatus.IN_CALL_PAUSED},
            update_date_fields=["updated_at", "consent_declined_at"],
            result=updated_meeting_bot,
        )
        result = await meeting_bot_service.halt_bot_recording_for_consent(
            meeting_bot=meeting_bot
        )
        assert result == updated_meeting_bot

    async def test_halt_bot_recording_for_consent_currently_recording_recall_error(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            organization_id=organization_id,
            external_meeting_bot_id="external_bot_id",
            status=MeetingBotStatus.IN_CALL_RECORDING,
        )
        recallai_client_v1.pause_recording.side_effect = TimeoutException("timeout")
        result = await meeting_bot_service.halt_bot_recording_for_consent(
            meeting_bot=meeting_bot
        )
        assert result == meeting_bot

    async def test_halt_bot_recording_for_consent_waiting(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            organization_id=organization_id,
            external_meeting_bot_id="external_bot_id",
            status=MeetingBotStatus.WAITING,
        )

        self.mock_recall_stop_recording(
            mock_recall_client=recallai_client_v1, input_bot_id="external_bot_id"
        )
        updated_meeting_bot = meeting_bot.copy(
            update={"status": MeetingBotStatus.IN_CALL_NOT_RECORDING}
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=meeting_repository,
            input_organization_id=organization_id,
            where_columns={"id": meeting_bot.id},
            update_columns={"status": MeetingBotStatus.IN_CALL_NOT_RECORDING},
            update_date_fields=["updated_at", "consent_declined_at"],
            result=updated_meeting_bot,
        )
        result = await meeting_bot_service.halt_bot_recording_for_consent(
            meeting_bot=meeting_bot
        )
        assert result == updated_meeting_bot

    async def test_halt_bot_recording_for_consent_waiting_recall_error(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        recallai_client_v1: Mock,
        meeting_repository: Mock,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            organization_id=organization_id,
            external_meeting_bot_id="external_bot_id",
            status=MeetingBotStatus.WAITING,
        )

        recallai_client_v1.stop_recording.side_effect = TimeoutException("timeout")
        result = await meeting_bot_service.halt_bot_recording_for_consent(
            meeting_bot=meeting_bot
        )
        assert result == meeting_bot

    async def test_halt_bot_recording_for_consent_no_bot_action(
        self,
        organization_id: UUID,
        meeting_bot_factory: MeetingBotFactory,
        meeting_bot_service: MeetingBotService,
    ) -> None:
        meeting_bot = meeting_bot_factory.build(
            organization_id=organization_id,
            external_meeting_bot_id="external_bot_id",
            status=MeetingBotStatus.EXITED,
        )

        result = await meeting_bot_service.halt_bot_recording_for_consent(
            meeting_bot=meeting_bot
        )
        assert result == meeting_bot

    def mock_recall_stop_recording(
        self, mock_recall_client: Mock, input_bot_id: str
    ) -> None:
        def stop(bot_id: str) -> None:
            assert bot_id == input_bot_id

        mock_recall_client.stop_recording = AsyncMock(side_effect=stop)

    def mock_recall_start_recording(
        self, mock_recall_client: Mock, input_bot_id: str
    ) -> None:
        def start(bot_id: str) -> None:
            assert bot_id == input_bot_id

        mock_recall_client.start_recording = AsyncMock(side_effect=start)

    def mock_recall_resume_recording(
        self, mock_recall_client: Mock, input_bot_id: str
    ) -> None:
        def resume(bot_id: str) -> None:
            assert bot_id == input_bot_id

        mock_recall_client.resume_recording = AsyncMock(side_effect=resume)

    def mock_recall_leave_call(
        self, mock_recall_client: Mock, input_bot_id: str
    ) -> None:
        def leave(bot_id: str) -> None:
            assert bot_id == input_bot_id

        mock_recall_client.leave_call = AsyncMock(side_effect=leave)

    def mock_recall_send_message(
        self,
        mock_recall_client: Mock,
        input_bot_id: str,
        input_request: SendChatMessageRequest,
    ) -> None:
        def send(bot_id: str, request: SendChatMessageRequest) -> None:
            assert bot_id == input_bot_id
            assert request == input_request

        mock_recall_client.send_message = AsyncMock(side_effect=send)

    def mock_recall_delete_bot(
        self,
        mock_recall_client: Mock,
        input_bot_id: str,
    ) -> None:
        async def delete(bot_id: str) -> None:
            assert bot_id == input_bot_id

        mock_recall_client.delete_bot = AsyncMock(side_effect=delete)

    def mock_recall_update_bot(
        self,
        mock_recall_client: Mock,
        input_external_bot_id: str,
        input_request: UpdateRecallBotRequest,
        result: str,
    ) -> None:
        async def update(bot_id: str, request: UpdateRecallBotRequest) -> str:
            assert bot_id == input_external_bot_id
            assert request == input_request
            return result

        mock_recall_client.update_bot = AsyncMock(side_effect=update)

    def mock_recall_create_bot(
        self,
        mock_recall_client: Mock,
        input_request: CreateRecallBotRequest,
        input_idempotency_key: str | None,
        result: str,
    ) -> None:
        async def create(
            request: CreateRecallBotRequest, idempotency_key: str | None = None
        ) -> str:
            assert idempotency_key == input_idempotency_key
            assert request.model_dump(exclude={"metadata"}) == input_request.model_dump(
                exclude={"metadata"}
            )
            assert request.metadata
            assert input_request.metadata
            assert request.metadata["meeting_id"] == str(
                input_request.metadata["meeting_id"]
            )
            assert request.metadata["created_at"]  # Generated
            return result

        mock_recall_client.create_bot = AsyncMock(side_effect=create)

    def mock_repo_update_recording_id_by_bot_id(
        self,
        mock_meeting_repository: Mock,
        input_external_meeting_bot_id: str,
        input_recording_id: str,
        result: MeetingBot,
    ) -> None:
        async def update(
            external_meeting_bot_id: str,
            recording_id: str,
        ) -> MeetingBot:
            assert external_meeting_bot_id == input_external_meeting_bot_id
            assert recording_id == input_recording_id
            return result

        mock_meeting_repository.update_recording_id_by_bot_id = AsyncMock(
            side_effect=update
        )

    def mock_repo_meeting_bot_lookup_by_meeting_id(
        self,
        mock_meeting_repository: Mock,
        input_meeting_id: UUID,
        input_organization_id: UUID,
        result: list[MeetingBot],
    ) -> None:
        def lookup(meeting_id: UUID, organization_id: UUID) -> list[MeetingBot]:
            assert meeting_id == input_meeting_id
            assert organization_id == input_organization_id
            return result

        mock_meeting_repository.find_meeting_bots_by_meeting = AsyncMock(
            side_effect=lookup
        )

    def mock_repo_meeting_bot_lookup_by_external_id_and_provider(
        self,
        mock_meeting_repository: Mock,
        input_external_meeting_bot_id: str,
        result: MeetingBot | None,
    ) -> None:
        async def lookup(
            external_meeting_bot_id: str, provider: BotProvider
        ) -> MeetingBot | None:
            assert external_meeting_bot_id == input_external_meeting_bot_id
            assert provider == BotProvider.RECALLAI
            return result

        mock_meeting_repository.get_meeting_bot_by_external_id = AsyncMock(
            side_effect=lookup
        )

    def mock_repo_meeting_bot_lookup_by_external_id_and_meeting_id(
        self,
        mock_meeting_repository: Mock,
        input_external_meeting_bot_id: str,
        input_meeting_id: UUID,
        result: MeetingBot | None,
    ) -> None:
        async def lookup(
            external_meeting_bot_id: str,
            meeting_id: UUID,
        ) -> MeetingBot | None:
            assert external_meeting_bot_id == input_external_meeting_bot_id
            assert meeting_id == input_meeting_id
            return result

        mock_meeting_repository.get_meeting_bot_by_external_id_and_meeting_id = (
            AsyncMock(side_effect=lookup)
        )

    def mock_repo_find_future_meeting_bots_by_meeting_user_id(
        self,
        mock_meeting_repository: Mock,
        input_organization_id: UUID,
        input_user_id: UUID,
        result: list[MeetingBot],
    ) -> None:
        async def lookup(organization_id: UUID, user_id: UUID) -> list[MeetingBot]:
            assert organization_id == input_organization_id
            assert user_id == input_user_id
            return result

        mock_meeting_repository.find_future_meeting_bots_by_meeting_user_id = AsyncMock(
            side_effect=lookup
        )

    def mock_repo_append_bot_status_history_and_update_status(
        self,
        mock_meeting_repository: Mock,
        input_external_meeting_bot_id: str,
        input_status_event: BotStatusEvent,
        input_status: MeetingBotStatus,
        result: MeetingBot,
    ) -> None:
        async def update(
            external_meeting_bot_id: str,
            bot_provider: BotProvider,
            status_event: BotStatusEvent,
            status: MeetingBotStatus,
        ) -> MeetingBot:
            assert external_meeting_bot_id == input_external_meeting_bot_id
            assert bot_provider == BotProvider.RECALLAI
            assert status_event == input_status_event
            assert status == input_status
            return result

        mock_meeting_repository.append_bot_status_history_and_update_status = AsyncMock(
            side_effect=update
        )

    def mock_repo_update_meeting_bot_recording_data(
        self,
        mock_meeting_repository: Mock,
        input_organization_id: UUID,
        input_external_meeting_bot_id: str,
        input_external_media_url: str,
        input_external_media_retention_ended_at: ZoneRequiredDateTime,
        input_recording_metadata: RecallRecordingData,
        intput_screen_share_ranges: list[ScreenShareRange],
        result: MeetingBot | None,
    ) -> None:
        def update(
            organization_id: UUID,
            external_meeting_bot_id: str,
            external_media_url: str,
            external_media_retention_ended_at: ZoneRequiredDateTime,
            recording_metadata: RecallRecordingData,
            screen_share_ranges: list[ScreenShareRange],
            updated_at: ZoneRequiredDateTime,
            completed_at: ZoneRequiredDateTime,
        ) -> MeetingBot | None:
            assert organization_id == input_organization_id
            assert external_meeting_bot_id == input_external_meeting_bot_id
            assert external_media_url == input_external_media_url
            assert (
                external_media_retention_ended_at
                == input_external_media_retention_ended_at
            )
            assert recording_metadata == input_recording_metadata
            assert screen_share_ranges == intput_screen_share_ranges
            return result

        mock_meeting_repository.update_meeting_bot_recording_data = AsyncMock(
            side_effect=update
        )

    def mock_repo_meeting_bot_and_transcript_insert(
        self,
        mock_meeting_repository: Mock,
        input_meeting_id: UUID,
        input_bot_provider: BotProvider,
        input_meeting_bot_name: str,
        input_transcript_provider: TranscriptProvider,
        input_organization_id: UUID,
        input_scheduled_at: ZoneRequiredDateTime,
        input_meeting_url: str,
        result: tuple[MeetingBot, Transcript],
    ) -> None:
        async def insert(
            meeting_id: UUID,
            external_bot_id: str,
            bot_provider: BotProvider,
            meeting_bot_name: str,
            transcript_provider: TranscriptProvider,
            organization_id: UUID,
            scheduled_at: ZoneRequiredDateTime,
            realtime_event_token: UUID,
            meeting_url: str,
        ) -> tuple[MeetingBot, Transcript]:
            assert meeting_id == input_meeting_id
            assert external_bot_id == ""
            assert bot_provider == input_bot_provider
            assert transcript_provider == input_transcript_provider
            assert organization_id == input_organization_id
            assert scheduled_at == input_scheduled_at
            assert meeting_bot_name == input_meeting_bot_name
            assert meeting_url == input_meeting_url
            return result

        mock_meeting_repository.create_meeting_bot_transcript_initial_rows = AsyncMock(
            side_effect=insert
        )

    def mock_repo_find_meeting_bots_by_meeting_url_and_time(
        self,
        mock_meeting_repository: Mock,
        input_organization_id: UUID,
        input_meeting_id: UUID,
        input_meeting_url: str,
        input_scheduled_at: ZoneRequiredDateTime,
        result: list[MeetingBot],
    ) -> None:
        def lookup(
            organization_id: UUID,
            meeting_id: UUID,
            meeting_url: str,
            scheduled_at: ZoneRequiredDateTime,
        ) -> list[MeetingBot]:
            assert organization_id == input_organization_id
            assert meeting_id == input_meeting_id
            assert meeting_url == input_meeting_url
            assert scheduled_at == input_scheduled_at
            return result

        mock_meeting_repository.find_meeting_bots_by_meeting_url_and_time = AsyncMock(
            side_effect=lookup
        )

    def mock_repo_find_transcript_for_meeting_bot(
        self,
        mock_meeting_repository: Mock,
        input_organization_id: UUID,
        input_meeting_bot_id: UUID,
        result: Transcript | None,
    ) -> None:
        def lookup(organization_id: UUID, meeting_bot_id: UUID) -> Transcript | None:
            assert organization_id == input_organization_id
            assert meeting_bot_id == input_meeting_bot_id
            return result

        mock_meeting_repository.find_transcript_for_meeting_bot = AsyncMock(
            side_effect=lookup
        )

    # Toast toast generation tests for Google Meet notifications
    def test_generate_toast_message(self) -> None:
        """Comprehensive test for toast message generation for Google Meet notifications."""

        # Test 1: Short names with different status messages
        assert (
            MeetingBotService._generate_toast_message("John", "recording")
            == "Welcome John! FYI I am recording."
        )
        assert (
            MeetingBotService._generate_toast_message("John", "not recording")
            == "Welcome John! FYI I am not recording."
        )

        # Test 2: Verify message length limits (52 chars total)
        not_recording_msg = MeetingBotService._generate_toast_message(
            "A" * 30, "not recording"
        )
        assert len(not_recording_msg) <= 52

        # Test 3: Real-world examples
        # These should not be truncated
        assert (
            MeetingBotService._generate_toast_message("Alice", "recording")
            == "Welcome Alice! FYI I am recording."
        )
        assert (
            MeetingBotService._generate_toast_message("+1 (555) 123-4567", "recording")
            == "Welcome +1 (555) 123-4567! FYI I am recording."
        )

        # Test 4: Long names that need truncation
        long_name_result = MeetingBotService._generate_toast_message(
            "David - Reevo Unlimited Blade Works Corporation", "recording"
        )
        assert long_name_result == "Welcome David - Reevo...! FYI I am recording."

        # Test 5: Edge cases for name truncation
        # Empty name
        assert (
            MeetingBotService._generate_toast_message("", "recording")
            == "Welcome ! FYI I am recording."
        )

        # Test 6: Additional edge cases
        # Names that fit exactly within available space
        twenty_char_name = "A" * 20
        assert (
            MeetingBotService._generate_toast_message(twenty_char_name, "recording")
            == f"Welcome {twenty_char_name}! FYI I am recording."
        )

        # Names that exceed available space by one character
        twenty_one_char_name = "A" * 21
        result = MeetingBotService._generate_toast_message(
            twenty_one_char_name, "recording"
        )
        assert result == f"Welcome {'A' * 20}...! FYI I am recording."


@pytest.fixture
def meeting_repository() -> Mock:
    return AsyncMock()


@pytest.fixture
def recallai_client_v1() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_s3_manager() -> Mock:
    return AsyncMock()


@pytest.fixture
def file_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_bot_service(
    meeting_repository: Mock,
    meeting_s3_manager: Mock,
    recallai_client_v1: Mock,
    file_service: Mock,
) -> MeetingBotService:
    return MeetingBotService(
        meeting_repository=meeting_repository,
        meeting_s3_manager=meeting_s3_manager,
        recallai_client_v1=recallai_client_v1,
        file_service=file_service,
    )
