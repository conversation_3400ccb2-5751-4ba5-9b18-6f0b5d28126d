from collections.abc import Generator
from unittest.mock import As<PERSON><PERSON><PERSON>, Mo<PERSON>, patch
from uuid import UUID, uuid4

import pytest
from polyfactory.pytest_plugin import register_fixture

from salestech_be.core.domain_crm_association.types import (
    DeleteDomainCrmAssociation,
)
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
)
from salestech_be.core.meeting.service.meeting_reference_type_strategy import (
    _ReferenceTypeStrategyFactory,
)
from salestech_be.db.models.activity import (
    ActivitySubType,
)
from salestech_be.db.models.domain_crm_association import (
    DomainCRMAssociationRole,
    DomainType,
)
from salestech_be.db.models.meeting import (
    BotProvider,
    MeetingBotStatus,
    MeetingInvitee,
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingStatus,
)
from salestech_be.web.api.meeting.schema import (
    CreateMeetingRequest,
    Invitee,
    PatchMeetingRequest,
)
from tests.test_util import BasePydanticFactory
from tests.unit.core.meeting.meeting_service_test import MeetingServiceTest
from tests.util.factories import (
    ContactFactory,
    MeetingBotFactory,
    MeetingFactory,
    UserDTOFactory,
)


@register_fixture(scope="session")
class CreateMeetingRequestFactory(BasePydanticFactory[CreateMeetingRequest]):
    pass


class TestMeetingServiceCRMAssociation(MeetingServiceTest):
    async def test_create_meeting_crm_association_skip_voice_call_v2(
        self,
        organization_id: UUID,
        meeting_factory: MeetingFactory,
        domain_crm_association_service: AsyncMock,
        meeting_service: MeetingService,
    ) -> None:
        # Arrange
        meeting = meeting_factory.build(
            organization_id=organization_id,
            meeting_platform=MeetingProvider.VOICE,
            reference_id_type=MeetingReferenceIdType.VOICE_V2,
        )
        user_id = UUID("********-1111-1111-1111-********1111")

        # Act
        await meeting_service._create_meeting_crm_association(
            meeting=meeting,
            organization_id=organization_id,
            user_id=user_id,
            user_calendar_event_id=None,
        )

        # Assert
        domain_crm_association_service.bulk_create_domain_crm_associations.assert_not_called()

    async def test_create_meeting_crm_association_with_invitees(
        self,
        organization_id: UUID,
        meeting_factory: MeetingFactory,
        domain_crm_association_service: AsyncMock,
        meeting_service: MeetingService,
    ) -> None:
        # Arrange
        user_id = UUID("********-1111-1111-1111-********1111")
        contact_id1 = UUID("*************-5555-5555-************")
        contact_id2 = UUID("*************-6666-6666-************")
        meeting = meeting_factory.build(
            organization_id=organization_id,
            meeting_platform=MeetingProvider.ZOOM,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            reference_id=str(uuid4()),
            invitees=[
                MeetingInvitee(
                    contact_id=contact_id1, contact_email="<EMAIL>"
                ),
                MeetingInvitee(
                    contact_id=contact_id2, contact_email="<EMAIL>"
                ),
                MeetingInvitee(
                    user_id=user_id,
                    is_organizer=True,
                ),
            ],
        )

        pipeline_id = UUID("*************-2222-2222-************")
        account_id = UUID("*************-4444-4444-************")
        user_calendar_event_id = UUID("*************-7777-7777-************")

        # Act
        with patch(
            "salestech_be.core.meeting.meeting_service.settings"
        ) as mock_settings:
            mock_settings.enable_meeting_domain_crm_association = True
            await meeting_service._create_meeting_crm_association(
                meeting=meeting,
                organization_id=organization_id,
                user_id=user_id,
                pipeline_id=pipeline_id,
                account_id=account_id,
                user_calendar_event_id=user_calendar_event_id,
            )

        # Assert
        domain_crm_association_service.bulk_create_domain_crm_associations.assert_called_once()
        call_args = (
            domain_crm_association_service.bulk_create_domain_crm_associations.call_args
        )
        associations = call_args[0][0]
        assert len(associations) == 3

        # Sort associations by contact_id to ensure consistent test results
        associations.sort(key=lambda a: str(a.contact_id))

        # Check first association
        assert associations[0].organization_id == organization_id
        assert associations[0].meeting_id == meeting.id
        assert associations[0].contact_id == contact_id1
        assert associations[0].user_id is None
        assert associations[0].pipeline_id == pipeline_id
        assert associations[0].account_id == account_id
        assert associations[0].created_by_user_id == user_id
        assert associations[0].user_calendar_event_id == user_calendar_event_id
        assert associations[0].email == "<EMAIL>"

        # Check second association
        assert associations[1].organization_id == organization_id
        assert associations[1].meeting_id == meeting.id
        assert associations[1].contact_id == contact_id2
        assert associations[1].user_id is None
        assert associations[1].pipeline_id == pipeline_id
        assert associations[1].account_id == account_id
        assert associations[1].created_by_user_id == user_id
        assert associations[1].user_calendar_event_id == user_calendar_event_id
        assert associations[1].email == "<EMAIL>"

        # Check third association
        assert associations[2].organization_id == organization_id
        assert associations[2].meeting_id == meeting.id
        assert associations[2].user_id == user_id
        assert associations[2].pipeline_id == pipeline_id
        assert associations[2].account_id == account_id
        assert associations[2].created_by_user_id == user_id
        assert associations[2].contact_id is None
        assert associations[2].association_role == DomainCRMAssociationRole.ORGANIZER

    async def test_update_meeting_crm_associations_updated(
        self,
        organization_id: UUID,
        meeting_factory: MeetingFactory,
        domain_crm_association_service: AsyncMock,
        meeting_service: MeetingService,
    ) -> None:
        # Arrange
        user_id = UUID("*************-2222-2222-************")
        contact_id = UUID("*************-3333-3333-************")
        existing_meeting = meeting_factory.build(
            organization_id=organization_id,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            meeting_platform=MeetingProvider.GMEET,
            invitees=[
                MeetingInvitee(
                    user_id=UUID("********-1111-1111-1111-********1111"),
                    is_organizer=True,
                ),
                MeetingInvitee(
                    contact_id=contact_id,
                    contact_email="<EMAIL>",
                ),
            ],
        )

        # New invitee for the meeting
        new_contact_id = UUID("*************-4444-4444-************")

        # Updated meeting with new invitees list that removes one contact and adds another
        updated_meeting = existing_meeting.copy(
            update={
                "invitees": [
                    MeetingInvitee(
                        user_id=UUID("********-1111-1111-1111-********1111"),
                        is_organizer=True,
                    ),
                    MeetingInvitee(
                        contact_id=new_contact_id,
                        contact_email="<EMAIL>",
                    ),
                ],
            }
        )

        # Mock the patch request
        patch_request = PatchMeetingRequest(
            invitees=[
                Invitee(
                    user_id=UUID("********-1111-1111-1111-********1111"),
                    is_organizer=True,
                ),
                Invitee(contact_id=new_contact_id, email="<EMAIL>"),
            ]
        )
        associations_to_delete = [
            DeleteDomainCrmAssociation(
                domain_type=DomainType.MEETING,
                organization_id=organization_id,
                meeting_id=existing_meeting.id,
                contact_id=contact_id,
                email="<EMAIL>",
                deleted_by_user_id=user_id,
            )
        ]

        # Act
        with patch(
            "salestech_be.core.meeting.meeting_service.settings"
        ) as mock_settings:
            mock_settings.enable_meeting_domain_crm_association = True
            await meeting_service._update_meeting_crm_associations(
                existing_meeting=existing_meeting,
                updated_meeting=updated_meeting,
                patch_request=patch_request,
                organization_id=organization_id,
                user_id=user_id,
            )

        # Assert
        # Verify bulk_create_domain_crm_associations is called to create association for new contact
        domain_crm_association_service.bulk_create_domain_crm_associations.assert_called_once()
        create_args = domain_crm_association_service.bulk_create_domain_crm_associations.call_args[
            0
        ][0]
        assert len(create_args) == 1
        assert create_args[0].contact_id == new_contact_id
        assert create_args[0].email == "<EMAIL>"
        assert create_args[0].meeting_id == updated_meeting.id

        # Verify bulk_delete_domain_crm_associations is called to delete association for removed contact
        domain_crm_association_service.bulk_delete_domain_crm_associations.assert_called_once_with(
            domain_crm_associations=associations_to_delete,
            deleted_by_user_id=user_id,
        )

    async def test_update_meeting_crm_associations_skipped_for_voice_v2(
        self,
        organization_id: UUID,
        meeting_factory: MeetingFactory,
        domain_crm_association_service: AsyncMock,
        meeting_service: MeetingService,
    ) -> None:
        # Arrange
        user_id = UUID("*************-2222-2222-************")
        existing_meeting = meeting_factory.build(
            organization_id=organization_id,
            meeting_platform=MeetingProvider.VOICE,
            reference_id_type=MeetingReferenceIdType.VOICE_V2,
        )

        updated_meeting = meeting_factory.build(
            id=existing_meeting.id,
            organization_id=organization_id,
            meeting_platform=MeetingProvider.VOICE,
            reference_id_type=MeetingReferenceIdType.VOICE_V2,
        )

        patch_request = PatchMeetingRequest(
            invitees=[
                Invitee(
                    user_id=UUID("********-1111-1111-1111-********1111"),
                    is_organizer=True,
                ),
                Invitee(
                    contact_id=UUID("*************-4444-4444-************"),
                    email="<EMAIL>",
                ),
            ]
        )
        # Act
        await meeting_service._update_meeting_crm_associations(
            existing_meeting=existing_meeting,
            updated_meeting=updated_meeting,
            patch_request=patch_request,
            organization_id=organization_id,
            user_id=user_id,
        )

        # Assert - CRM association should be skipped for voice calls
        domain_crm_association_service.bulk_create_domain_crm_associations.assert_not_called()
        domain_crm_association_service.bulk_delete_by_ids.assert_not_called()

    async def test_patch_meeting_with_invitee_updates_crm_associations(
        self,
        organization_id: UUID,
        meeting_factory: MeetingFactory,
        user_dto_factory: UserDTOFactory,
        contact_factory: ContactFactory,
        meeting_bot_service: Mock,
        meeting_repository: Mock,
        domain_crm_association_service: AsyncMock,
        meeting_service: MeetingService,
        contact_query_service: Mock,
        contact_repository: Mock,
        contact_resolve_service: Mock,
        user_service: Mock,
        meeting_bot_factory: MeetingBotFactory,
        activity_service: Mock,
    ) -> None:
        # Arrange
        user_id = UUID("*************-2222-2222-************")
        existing_contact_id = UUID("*************-3333-3333-************")
        new_contact_id = UUID("*************-4444-4444-************")

        meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI,
            status=MeetingBotStatus.SCHEDULED,
            external_meeting_bot_id="external_bot_id",
        )

        # Create existing meeting
        existing_meeting = meeting_factory.build(
            organization_id=organization_id,
            organizer_user_id=user_id,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            invitees=[
                MeetingInvitee(
                    user_id=user_id,
                    is_organizer=True,
                ),
                MeetingInvitee(
                    contact_id=existing_contact_id,
                    contact_email="<EMAIL>",
                ),
            ],
            status=MeetingStatus.SCHEDULED,
            meeting_url="https://example.com",
            meeting_platform=MeetingProvider.GMEET,
            meeting_bot=meeting_bot,
            created_by_user_id=user_id,
        )
        # Mock contact queries
        contact_query_service.list_by_ids.return_value = [
            contact_factory.build(id=existing_contact_id, email="<EMAIL>"),
            contact_factory.build(id=new_contact_id, email="<EMAIL>"),
        ]
        contact_query_service.get_primary_emails_by_contact_ids.return_value = {
            existing_contact_id: "<EMAIL>",
            new_contact_id: "<EMAIL>",
        }
        contact_resolve_service.resolve_account_by_contacts.return_value = {}

        meeting_bot_service.get_active_meeting_bot_by_meeting_id.return_value = (
            meeting_bot
        )

        # Configure repository to return the existing meeting and then an updated meeting
        meeting_repository.find_by_tenanted_primary_key.return_value = existing_meeting

        user_service.get_by_id_and_organization_id.return_value = (
            user_dto_factory.build(id=user_id)
        )

        activity_service.insert_activity.return_value = None

        # Updated meeting after patch
        updated_meeting = existing_meeting.copy(
            update={
                "invitees": [
                    MeetingInvitee(
                        user_id=user_id,
                        is_organizer=True,
                    ),
                    MeetingInvitee(
                        contact_id=new_contact_id,
                        contact_email="<EMAIL>",
                    ),
                ],
            }
        )

        associations_to_delete = [
            DeleteDomainCrmAssociation(
                domain_type=DomainType.MEETING,
                organization_id=organization_id,
                meeting_id=existing_meeting.id,
                contact_id=existing_contact_id,
                email="<EMAIL>",
                deleted_by_user_id=user_id,
            )
        ]

        meeting_repository.update_by_tenanted_primary_key.return_value = updated_meeting

        activity = self.get_expected_activity(
            meeting=updated_meeting,
            sub_type=ActivitySubType.MEETING_UPDATED,
            sub_references=[],
            metadata=None,
        )

        self.mock_activity_creation_empty(
            mock_activity_service=activity_service,
            input_base_activity=activity,
            input_organization_id=organization_id,
        )

        # Create the patch request that changes invitees
        patch_request = PatchMeetingRequest(
            invitees=[
                Invitee(
                    user_id=user_id,
                    is_organizer=True,
                ),
                Invitee(contact_id=new_contact_id, email="<EMAIL>"),
            ]
        )

        # Act
        with patch(
            "salestech_be.core.meeting.meeting_service.settings"
        ) as mock_settings:
            mock_settings.enable_meeting_domain_crm_association = True
            mock_settings.enable_patch_meeting_pipeline_account_update = False
            mock_settings.enable_patch_meeting_pipeline_account_update_org_ids = []
            await meeting_service.patch_meeting(
                meeting_id=existing_meeting.id,
                organization_id=organization_id,
                request=patch_request,
                user_id=user_id,
            )

        # Assert
        # Verify the CRM association service was called to create and delete associations
        domain_crm_association_service.bulk_create_domain_crm_associations.assert_called_once()
        domain_crm_association_service.bulk_delete_domain_crm_associations.assert_called_once_with(
            domain_crm_associations=associations_to_delete,
            deleted_by_user_id=user_id,
        )

    async def test_patch_meeting_with_invitee_role_changes(
        self,
        organization_id: UUID,
        meeting_factory: MeetingFactory,
        user_dto_factory: UserDTOFactory,
        contact_factory: ContactFactory,
        meeting_bot_service: Mock,
        meeting_repository: Mock,
        domain_crm_association_service: AsyncMock,
        meeting_service: MeetingService,
        contact_query_service: Mock,
        contact_repository: Mock,
        contact_resolve_service: Mock,
        user_service: Mock,
        meeting_bot_factory: MeetingBotFactory,
        activity_service: Mock,
    ) -> None:
        # Arrange
        user_id = UUID("*************-2222-2222-************")
        contact_id = UUID("*************-3333-3333-************")

        meeting_bot = meeting_bot_factory.build(
            provider=BotProvider.RECALLAI,
            status=MeetingBotStatus.SCHEDULED,
            external_meeting_bot_id="external_bot_id",
        )

        # Create existing meeting with a user as organizer and a contact as participant
        existing_meeting = meeting_factory.build(
            organization_id=organization_id,
            organizer_user_id=user_id,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            invitees=[
                MeetingInvitee(
                    user_id=user_id,
                    is_organizer=True,  # User is initially organizer
                ),
                MeetingInvitee(
                    contact_id=contact_id,
                    contact_email="<EMAIL>",
                    is_organizer=False,  # Contact is initially participant
                ),
            ],
            status=MeetingStatus.SCHEDULED,
            meeting_url="https://example.com",
            meeting_platform=MeetingProvider.GMEET,
            meeting_bot=meeting_bot,
            created_by_user_id=user_id,
        )

        # Mock contact queries
        contact_query_service.list_by_ids.return_value = [
            contact_factory.build(id=contact_id, email="<EMAIL>"),
        ]
        contact_query_service.get_primary_emails_by_contact_ids.return_value = {
            contact_id: "<EMAIL>",
        }
        contact_resolve_service.resolve_account_by_contacts.return_value = {}

        meeting_bot_service.get_active_meeting_bot_by_meeting_id.return_value = (
            meeting_bot
        )

        # Configure repository to return the existing meeting
        meeting_repository.find_by_tenanted_primary_key.return_value = existing_meeting

        user_service.get_by_id_and_organization_id.return_value = (
            user_dto_factory.build(id=user_id)
        )

        activity_service.insert_activity.return_value = None

        # Updated meeting after patch with swapped roles:
        # - User is now participant
        # - Contact is now organizer
        updated_meeting = existing_meeting.copy(
            update={
                "invitees": [
                    MeetingInvitee(
                        user_id=user_id,
                        is_organizer=False,  # User is now participant
                    ),
                    MeetingInvitee(
                        contact_id=contact_id,
                        contact_email="<EMAIL>",
                        is_organizer=True,  # Contact is now organizer
                    ),
                ],
            }
        )

        # Expected association changes for role changes
        # 1. Delete old user association (organizer)
        # 2. Create new user association (participant)
        # 3. Delete old contact association (participant)
        # 4. Create new contact association (organizer)
        user_delete = DeleteDomainCrmAssociation(
            domain_type=DomainType.MEETING,
            organization_id=organization_id,
            meeting_id=existing_meeting.id,
            user_id=user_id,
            deleted_by_user_id=user_id,
        )

        contact_delete = DeleteDomainCrmAssociation(
            domain_type=DomainType.MEETING,
            organization_id=organization_id,
            meeting_id=existing_meeting.id,
            email="<EMAIL>",
            deleted_by_user_id=user_id,
            contact_id=contact_id,
        )

        meeting_repository.update_by_tenanted_primary_key.return_value = updated_meeting

        activity = self.get_expected_activity(
            meeting=updated_meeting,
            sub_type=ActivitySubType.MEETING_UPDATED,
            sub_references=[],
            metadata=None,
        )

        self.mock_activity_creation_empty(
            mock_activity_service=activity_service,
            input_base_activity=activity,
            input_organization_id=organization_id,
        )

        # Create the patch request that changes invitee roles
        patch_request = PatchMeetingRequest(
            invitees=[
                Invitee(
                    user_id=user_id,
                    is_organizer=False,  # Change user to participant
                ),
                Invitee(
                    contact_id=contact_id,
                    email="<EMAIL>",
                    is_organizer=True,  # Change contact to organizer
                ),
            ]
        )

        # Act
        with patch(
            "salestech_be.core.meeting.meeting_service.settings"
        ) as mock_settings:
            mock_settings.enable_meeting_domain_crm_association = True
            mock_settings.enable_patch_meeting_pipeline_account_update = False
            mock_settings.enable_patch_meeting_pipeline_account_update_org_ids = []
            await meeting_service.patch_meeting(
                meeting_id=existing_meeting.id,
                organization_id=organization_id,
                request=patch_request,
                user_id=user_id,
            )

        # Assert
        # Verify the CRM association service was called to handle the role changes

        # Check that bulk_create_domain_crm_associations was called with both new roles
        domain_crm_association_service.bulk_create_domain_crm_associations.assert_called_once()
        create_args = domain_crm_association_service.bulk_create_domain_crm_associations.call_args[
            0
        ][0]

        assert len(create_args) == 2

        # Check that associations were created with correct roles
        created_user_assoc = next(a for a in create_args if a.user_id == user_id)
        created_contact_assoc = next(
            a for a in create_args if a.contact_id == contact_id
        )

        assert (
            created_user_assoc.association_role == DomainCRMAssociationRole.PARTICIPANT
        )
        assert (
            created_contact_assoc.association_role == DomainCRMAssociationRole.ORGANIZER
        )

        # Check the bulk_delete_domain_crm_associations was called with the right roles to delete
        domain_crm_association_service.bulk_delete_domain_crm_associations.assert_called_once()
        delete_args = domain_crm_association_service.bulk_delete_domain_crm_associations.call_args.kwargs[
            "domain_crm_associations"
        ]

        assert len(delete_args) == 2

        # Sort the delete associations to check them consistently
        delete_args.sort(key=lambda x: str(x.user_id or ""))

        # Check that old roles were properly deleted
        assert delete_args[0] == contact_delete
        assert delete_args[1] == user_delete


@pytest.fixture
def meeting_repository() -> Mock:
    return AsyncMock()


@pytest.fixture
def mock_permissions(
    meeting_service: MeetingService,
) -> Generator[AsyncMock, None, None]:
    with patch.object(
        meeting_service, "error_if_no_entity_access", new_callable=AsyncMock
    ) as mock_method:
        mock_method.return_value = None
        yield mock_method


@pytest.fixture
def activity_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def transcript_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_insight_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def user_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def contact_repository() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_bot_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_s3_manager() -> Mock:
    return AsyncMock()


@pytest.fixture
def call_recording_s3() -> Mock:
    return AsyncMock()


@pytest.fixture
def call_recording_v2_s3() -> Mock:
    return AsyncMock()


@pytest.fixture
def user_calendar_repository() -> Mock:
    return AsyncMock()


@pytest.fixture
def custom_object_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_query_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_stats_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def job_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def contact_query_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def file_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def file_s3_manager() -> Mock:
    return AsyncMock()


@pytest.fixture
def contact_resolve_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def pipeline_query_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def task_v2_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def sequence_enrollment_query_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def domain_crm_association_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_ai_rec_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def meeting_service(
    meeting_repository: Mock,
    meeting_bot_service: Mock,
    activity_service: Mock,
    meeting_insight_service: Mock,
    task_v2_service: Mock,
    user_service: Mock,
    transcript_service: Mock,
    meeting_s3_manager: Mock,
    call_recording_s3: Mock,
    call_recording_v2_s3: Mock,
    user_calendar_repository: Mock,
    custom_object_service: Mock,
    meeting_query_service: Mock,
    meeting_stats_service: Mock,
    job_service: Mock,
    contact_query_service: Mock,
    contact_resolve_service: Mock,
    file_service: Mock,
    file_s3_manager: Mock,
    pipeline_query_service: Mock,
    sequence_enrollment_query_service: Mock,
    domain_crm_association_service: Mock,
    meeting_ai_rec_service: Mock,
) -> MeetingService:
    return MeetingService(
        meeting_repository=meeting_repository,
        meeting_bot_service=meeting_bot_service,
        meeting_s3_manager=meeting_s3_manager,
        call_recording_s3=call_recording_s3,
        meeting_insight_service=meeting_insight_service,
        user_service=user_service,
        task_v2_service=task_v2_service,
        activity_service=activity_service,
        transcript_service=transcript_service,
        user_calendar_repository=user_calendar_repository,
        custom_object_service=custom_object_service,
        meeting_query_service=meeting_query_service,
        meeting_stats_service=meeting_stats_service,
        job_service=job_service,
        reference_type_strategy_factory=_ReferenceTypeStrategyFactory(
            meeting_bot_service=meeting_bot_service,
            user_service=user_service,
            contact_query_service=contact_query_service,
            meeting_repository=meeting_repository,
            meeting_s3_manager=meeting_s3_manager,
            call_recording_s3=call_recording_s3,
            call_recording_v2_s3=call_recording_v2_s3,
            user_calendar_repository=user_calendar_repository,
            file_service=file_service,
            file_s3_manager=file_s3_manager,
        ),
        contact_query_service=contact_query_service,
        contact_resolve_service=contact_resolve_service,
        pipeline_query_service=pipeline_query_service,
        sequence_enrollment_query_service=sequence_enrollment_query_service,
        domain_crm_association_service=domain_crm_association_service,
        meeting_ai_rec_service=meeting_ai_rec_service,
    )
