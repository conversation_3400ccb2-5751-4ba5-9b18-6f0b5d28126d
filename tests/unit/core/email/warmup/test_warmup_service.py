from unittest.mock import AsyncMock
from uuid import UUID, uuid4

import pytest

from salestech_be.common.exception import ConflictResourceError
from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.core.email.account.types import CreateWarmUpRequest, WarmupConfig
from salestech_be.core.email.warmup.schema import CampaignResponse
from salestech_be.core.email.warmup.service import MailiveryWarmupService
from salestech_be.db.models.email_account import EmailAccount, EmailAccountType
from salestech_be.db.models.email_account_warm_up import (
    EmailAccountWarmUpCampaign,
    MailboxWarmUpService,
    MailboxWarmUpSpeed,
    MailboxWarmUpStatus,
)
from salestech_be.integrations.mailivery.type import Campaign, StatusCode
from salestech_be.util.time import zoned_utc_now
from tests.util.service_test import ServiceTest


class TestMailiveryWarmupService(ServiceTest):
    @pytest.fixture
    def email_account_warm_up_campaign_repository(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def email_account_service_ext(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def mailivery_client(self) -> AsyncMock:
        return AsyncMock()

    @pytest.fixture
    def warmup_service(
        self,
        email_account_warm_up_campaign_repository: AsyncMock,
        email_account_service_ext: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> MailiveryWarmupService:
        service = MailiveryWarmupService(
            email_account_warm_up_campaign_repository=email_account_warm_up_campaign_repository,
            email_account_service_ext=email_account_service_ext,
        )
        # Replace the mailivery client with our mock
        service.mailivery_client = mailivery_client
        return service

    async def test_create_warm_up_job_success(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        email_account_service_ext: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test successful creation of a warmup job."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        outbound_domain_id = uuid4()
        external_id = "mailivery_campaign_123"
        now = zoned_utc_now()

        request = CreateWarmUpRequest(
            email_account_id=email_account_id,
            email_per_day=50,
            response_rate=20,
            with_rampup=True,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
        )

        # Mock no existing campaign
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id.return_value = []

        # Mock decrypted email account
        decrypted_email_account = EmailAccount(
            id=email_account_id,
            email="<EMAIL>",
            type=EmailAccountType.OUTBOUND,
            active=True,
            is_default=False,
            seconds_delay_between_emails=30,
            first_name="Test",
            last_name="User",
            outbound_domain_id=outbound_domain_id,
            imap_host="imap.example.com",
            imap_port=993,
            imap_username="<EMAIL>",
            imap_password=None,
            smtp_host="smtp.example.com",
            smtp_port=587,
            smtp_username="<EMAIL>",
            smtp_password=None,
            organization_id=organization_id,
            owner_user_id=user_id,
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_service_ext.get_decrypted_email_account_by_id.return_value = (
            decrypted_email_account
        )

        # Mock mailivery campaign creation
        mailivery_campaign = Campaign(
            id=external_id,
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            status_code=StatusCode.ACTIVE,
            email_per_day=50,
            response_rate=20,
            response_today=0,
        )
        mailivery_client.create_campaign.return_value = mailivery_campaign

        # Mock database campaign creation
        db_campaign = EmailAccountWarmUpCampaign(
            id=uuid4(),
            domain_id=outbound_domain_id,
            email_per_day=request.email_per_day,
            response_rate=request.response_rate,
            email_account_id=email_account_id,
            external_id=external_id,
            requested_by_user_id=user_id,
            requested_at=now,
            warm_up_service=MailboxWarmUpService.MAILIVERY,
            status=MailboxWarmUpStatus.IN_PROGRESS,
            with_rampup=request.with_rampup,
            rampup_speed=request.rampup_speed,
            organization_id=organization_id,
            created_at=now,
            created_by_user_id=user_id,
            last_started_at=now,
        )
        email_account_warm_up_campaign_repository.create_campaign.return_value = (
            db_campaign
        )

        # Act
        result = await warmup_service.create_warm_up_job(
            organization_id=organization_id,
            user_id=user_id,
            request=request,
        )

        # Assert
        assert isinstance(result, CampaignResponse)
        assert result.id == db_campaign.id
        assert result.email_account_id == email_account_id
        assert result.external_id == external_id
        assert result.status == MailboxWarmUpStatus.IN_PROGRESS

        # Verify repository calls
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id.assert_called_once_with(
            email_account_id
        )
        email_account_service_ext.get_decrypted_email_account_by_id.assert_called_once_with(
            email_account_id=email_account_id
        )
        mailivery_client.create_campaign.assert_called_once()
        email_account_warm_up_campaign_repository.create_campaign.assert_called_once()

    async def test_create_warm_up_job_campaign_already_exists(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
    ) -> None:
        """Test creation fails when campaign already exists."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        existing_campaign_id = uuid4()
        now = zoned_utc_now()

        request = CreateWarmUpRequest(
            email_account_id=email_account_id,
            email_per_day=50,
            response_rate=20,
            with_rampup=True,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
        )

        # Mock existing campaign
        existing_campaign = EmailAccountWarmUpCampaign(
            id=existing_campaign_id,
            domain_id=uuid4(),
            email_per_day=50,
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=user_id,
            requested_at=now,
            external_id="existing_external_id",
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            organization_id=organization_id,
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id.return_value = [
            existing_campaign
        ]

        # Act & Assert
        with pytest.raises(ConflictResourceError):
            await warmup_service.create_warm_up_job(
                organization_id=organization_id,
                user_id=user_id,
                request=request,
            )

        # Verify only the check was called
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id.assert_called_once_with(
            email_account_id
        )

    async def test_create_warm_up_job_email_account_not_found(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        email_account_service_ext: AsyncMock,
    ) -> None:
        """Test creation fails when email account is not found."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()

        request = CreateWarmUpRequest(
            email_account_id=email_account_id,
            email_per_day=50,
            response_rate=20,
            with_rampup=True,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
        )

        # Mock no existing campaign
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id.return_value = []

        # Mock email account not found
        email_account_service_ext.get_decrypted_email_account_by_id.return_value = None

        # Act & Assert
        with pytest.raises(ResourceNotFoundError) as exc_info:
            await warmup_service.create_warm_up_job(
                organization_id=organization_id,
                user_id=user_id,
                request=request,
            )

        assert f"Unable to find email account {email_account_id}" in str(exc_info.value)

    async def test_create_warm_up_job_missing_outbound_domain(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        email_account_service_ext: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test creation fails when email account has no outbound domain."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        now = zoned_utc_now()

        request = CreateWarmUpRequest(
            email_account_id=email_account_id,
            email_per_day=50,
            response_rate=20,
            with_rampup=True,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
        )

        # Mock no existing campaign
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id.return_value = []

        # Mock email account without outbound domain
        decrypted_email_account = EmailAccount(
            id=email_account_id,
            email="<EMAIL>",
            type=EmailAccountType.OUTBOUND,
            active=True,
            is_default=False,
            seconds_delay_between_emails=30,
            outbound_domain_id=None,  # Missing outbound domain
            organization_id=organization_id,
            owner_user_id=user_id,
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_service_ext.get_decrypted_email_account_by_id.return_value = (
            decrypted_email_account
        )

        # Mock mailivery campaign creation
        mailivery_campaign = Campaign(
            id="mailivery_123",
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            status_code=StatusCode.ACTIVE,
            email_per_day=50,
            response_rate=20,
            response_today=0,
        )
        mailivery_client.create_campaign.return_value = mailivery_campaign

        # Act & Assert
        with pytest.raises(ResourceNotFoundError) as exc_info:
            await warmup_service.create_warm_up_job(
                organization_id=organization_id,
                user_id=user_id,
                request=request,
            )

        assert (
            f"Unable to find outbound domain for email account {email_account_id}"
            in str(exc_info.value)
        )

    async def test_update_campaign_success(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test successful campaign update."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        campaign_id = uuid4()
        external_id = "mailivery_123"
        now = zoned_utc_now()

        warmup_configs = {
            email_account_id: WarmupConfig(
                warmup_limit=100,
                warmup_status=MailboxWarmUpStatus.IN_PROGRESS,
            )
        }

        # Mock existing campaign
        existing_campaign = EmailAccountWarmUpCampaign(
            id=campaign_id,
            domain_id=uuid4(),
            email_per_day=50,  # Different from new limit
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=user_id,
            requested_at=now,
            organization_id=organization_id,
            external_id=external_id,
            status=MailboxWarmUpStatus.PAUSED,  # Different from new status
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            is_mock_record=False,
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = existing_campaign

        # Act
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs,
        )

        # Assert
        # Verify mailivery client calls
        mailivery_client.update_email_per_day.assert_called_once_with(
            campaign_id=external_id,
            email_per_day=100,
        )
        mailivery_client.start_warmup.assert_called_once_with(
            campaign_id=external_id,
        )

        # Verify repository update calls
        assert email_account_warm_up_campaign_repository.update_campaign.call_count == 2

        # Check first call (email_per_day update)
        first_call = (
            email_account_warm_up_campaign_repository.update_campaign.call_args_list[0]
        )
        assert first_call[1]["user_id"] == user_id
        assert first_call[1]["campaign_id"] == campaign_id
        assert first_call[1]["column_to_update"]["email_per_day"] == 100

        # Check second call (status update with last_started_at)
        second_call = (
            email_account_warm_up_campaign_repository.update_campaign.call_args_list[1]
        )
        assert second_call[1]["user_id"] == user_id
        assert second_call[1]["campaign_id"] == campaign_id
        assert (
            second_call[1]["column_to_update"]["status"]
            == MailboxWarmUpStatus.IN_PROGRESS
        )
        assert "last_started_at" in second_call[1]["column_to_update"]

    async def test_update_campaign_pause_status(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test campaign update to pause status."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        campaign_id = uuid4()
        external_id = "mailivery_123"
        now = zoned_utc_now()

        warmup_configs = {
            email_account_id: WarmupConfig(
                warmup_status=MailboxWarmUpStatus.PAUSED,
            )
        }

        # Mock existing campaign
        existing_campaign = EmailAccountWarmUpCampaign(
            id=campaign_id,
            domain_id=uuid4(),
            email_per_day=50,
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=user_id,
            requested_at=now,
            organization_id=organization_id,
            external_id=external_id,
            status=MailboxWarmUpStatus.IN_PROGRESS,  # Different from new status
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            is_mock_record=False,
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = existing_campaign

        # Act
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs,
        )

        # Assert
        mailivery_client.pause_warmup.assert_called_once_with(
            campaign_id=external_id,
        )

        # Verify repository update call (status update without last_started_at)
        email_account_warm_up_campaign_repository.update_campaign.assert_called_once()
        call_args = email_account_warm_up_campaign_repository.update_campaign.call_args
        assert call_args[1]["user_id"] == user_id
        assert call_args[1]["campaign_id"] == campaign_id
        assert call_args[1]["column_to_update"]["status"] == MailboxWarmUpStatus.PAUSED
        assert "last_started_at" not in call_args[1]["column_to_update"]

    async def test_update_campaign_mock_record_no_external_calls(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test campaign update for mock record doesn't make external calls but still updates database."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        campaign_id = uuid4()
        now = zoned_utc_now()

        warmup_configs = {
            email_account_id: WarmupConfig(
                warmup_limit=100,
                warmup_status=MailboxWarmUpStatus.IN_PROGRESS,
            )
        }

        # Mock existing mock campaign
        existing_campaign = EmailAccountWarmUpCampaign(
            id=campaign_id,
            domain_id=uuid4(),
            email_per_day=50,
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=user_id,
            requested_at=now,
            organization_id=organization_id,
            external_id="mock_external_id",
            status=MailboxWarmUpStatus.PAUSED,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            is_mock_record=True,  # This is a mock record
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = existing_campaign

        # Act
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs,
        )

        # Assert
        # No external mailivery calls should be made for mock records
        mailivery_client.update_email_per_day.assert_not_called()
        mailivery_client.start_warmup.assert_not_called()
        mailivery_client.pause_warmup.assert_not_called()

        # But database updates should still happen for both email_per_day and status (including last_started_at)
        assert email_account_warm_up_campaign_repository.update_campaign.call_count == 2

        # Check first call (email_per_day update)
        first_call = (
            email_account_warm_up_campaign_repository.update_campaign.call_args_list[0]
        )
        assert first_call[1]["user_id"] == user_id
        assert first_call[1]["campaign_id"] == campaign_id
        assert first_call[1]["column_to_update"]["email_per_day"] == 100

        # Check second call (status update with last_started_at for mock record)
        second_call = (
            email_account_warm_up_campaign_repository.update_campaign.call_args_list[1]
        )
        assert second_call[1]["user_id"] == user_id
        assert second_call[1]["campaign_id"] == campaign_id
        assert (
            second_call[1]["column_to_update"]["status"]
            == MailboxWarmUpStatus.IN_PROGRESS
        )
        assert "last_started_at" in second_call[1]["column_to_update"]

    async def test_update_campaign_no_changes_needed(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test campaign update when no changes are needed."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        campaign_id = uuid4()
        now = zoned_utc_now()

        warmup_configs = {
            email_account_id: WarmupConfig(
                warmup_limit=50,  # Same as existing
                warmup_status=MailboxWarmUpStatus.IN_PROGRESS,  # Same as existing
            )
        }

        # Mock existing campaign with same values
        existing_campaign = EmailAccountWarmUpCampaign(
            id=campaign_id,
            domain_id=uuid4(),
            email_per_day=50,  # Same as new limit
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=user_id,
            requested_at=now,
            organization_id=organization_id,
            external_id="mailivery_123",
            status=MailboxWarmUpStatus.IN_PROGRESS,  # Same as new status
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            is_mock_record=False,
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = existing_campaign

        # Act
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs,
        )

        # Assert
        # No external calls should be made
        mailivery_client.update_email_per_day.assert_not_called()
        mailivery_client.start_warmup.assert_not_called()
        mailivery_client.pause_warmup.assert_not_called()

        # No database updates should be made
        email_account_warm_up_campaign_repository.update_campaign.assert_not_called()

    async def test_update_campaign_empty_configs(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
    ) -> None:
        """Test campaign update with empty configs returns early."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        warmup_configs: dict[UUID, WarmupConfig] = {}

        # Act
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs,
        )

        # Assert
        # No repository calls should be made
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.assert_not_called()

    async def test_update_campaign_campaign_not_found(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test campaign update when campaign is not found."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()

        warmup_configs = {
            email_account_id: WarmupConfig(
                warmup_limit=100,
            )
        }

        # Mock campaign not found
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = None

        # Act
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs,
        )

        # Assert
        # No external calls should be made
        mailivery_client.update_email_per_day.assert_not_called()
        mailivery_client.start_warmup.assert_not_called()

        # No database updates should be made
        email_account_warm_up_campaign_repository.update_campaign.assert_not_called()

    async def test_get_warmup_campaign_by_email_account_id(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
    ) -> None:
        """Test getting warmup campaign by email account ID."""
        # Arrange
        organization_id = uuid4()
        email_account_id = uuid4()
        campaign_id = uuid4()
        now = zoned_utc_now()

        expected_campaign = EmailAccountWarmUpCampaign(
            id=campaign_id,
            domain_id=uuid4(),
            email_per_day=50,
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=uuid4(),
            requested_at=now,
            external_id="external_123",
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            organization_id=organization_id,
            created_at=now,
            created_by_user_id=uuid4(),
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = expected_campaign

        # Act
        result = await warmup_service.get_warmup_campaign_by_email_account_id(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

        # Assert
        assert result == expected_campaign
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.assert_called_once_with(
            email_account_id=email_account_id,
            organization_id=organization_id,
            warm_up_service=MailboxWarmUpService.MAILIVERY,
        )

    async def test_update_warmup_campaign_status_by_email_account_ids(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
    ) -> None:
        """Test updating warmup campaign status by email account IDs."""
        # Arrange
        organization_id = uuid4()
        email_account_ids = [uuid4(), uuid4()]
        status = MailboxWarmUpStatus.PAUSED
        now = zoned_utc_now()

        expected_campaigns = [
            EmailAccountWarmUpCampaign(
                id=uuid4(),
                domain_id=uuid4(),
                email_per_day=50,
                response_rate=20,
                email_account_id=email_account_ids[0],
                requested_by_user_id=uuid4(),
                requested_at=now,
                external_id="external_123",
                status=status,
                rampup_speed=MailboxWarmUpSpeed.NORMAL,
                organization_id=organization_id,
                created_at=now,
                created_by_user_id=uuid4(),
            ),
            EmailAccountWarmUpCampaign(
                id=uuid4(),
                domain_id=uuid4(),
                email_per_day=50,
                response_rate=20,
                email_account_id=email_account_ids[1],
                requested_by_user_id=uuid4(),
                requested_at=now,
                external_id="external_456",
                status=status,
                rampup_speed=MailboxWarmUpSpeed.NORMAL,
                organization_id=organization_id,
                created_at=now,
                created_by_user_id=uuid4(),
            ),
        ]
        email_account_warm_up_campaign_repository.update_campaign_status_by_email_account_ids.return_value = expected_campaigns

        # Act
        result = (
            await warmup_service.update_warmup_campaign_status_by_email_account_ids(
                email_account_ids=email_account_ids,
                organization_id=organization_id,
                status=status,
            )
        )

        # Assert
        assert result == expected_campaigns
        email_account_warm_up_campaign_repository.update_campaign_status_by_email_account_ids.assert_called_once_with(
            email_account_ids=email_account_ids,
            organization_id=organization_id,
            status=status,
        )

    async def test_create_campaign_request_from_warmup_request_and_decrypted_email_account(
        self,
        warmup_service: MailiveryWarmupService,
    ) -> None:
        """Test creating campaign request from warmup request and email account."""
        # Arrange
        email_account_id = uuid4()
        organization_id = uuid4()
        user_id = uuid4()
        now = zoned_utc_now()

        create_warmup_request = CreateWarmUpRequest(
            email_account_id=email_account_id,
            email_per_day=50,
            response_rate=20,
            with_rampup=True,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
        )

        db_email_account = EmailAccount(
            id=email_account_id,
            email="<EMAIL>",
            type=EmailAccountType.OUTBOUND,
            active=True,
            is_default=False,
            seconds_delay_between_emails=30,
            first_name="Test",
            last_name="User",
            imap_host="imap.example.com",
            imap_port=993,
            imap_username="<EMAIL>",
            imap_password=None,
            smtp_host="smtp.example.com",
            smtp_port=587,
            smtp_username="<EMAIL>",
            smtp_password=None,
            organization_id=organization_id,
            owner_user_id=user_id,
            created_at=now,
            created_by_user_id=user_id,
        )

        # Act
        result = warmup_service.create_campaign_request_from_warmup_request_and_decrypted_email_account(
            create_warmup_request=create_warmup_request,
            db_email_account=db_email_account,
        )

        # Assert
        assert result.email == db_email_account.email
        assert result.email_per_day == create_warmup_request.email_per_day
        assert result.response_rate == create_warmup_request.response_rate
        assert result.with_rampup == create_warmup_request.with_rampup
        assert result.rampup_speed == create_warmup_request.rampup_speed
        assert result.first_name == db_email_account.first_name
        assert result.last_name == db_email_account.last_name
        assert result.imap_email == db_email_account.email
        assert result.imap_host == db_email_account.imap_host
        assert result.imap_port == db_email_account.imap_port
        assert result.imap_username == db_email_account.imap_username
        assert result.imap_password == db_email_account.imap_password
        assert result.smtp_host == db_email_account.smtp_host
        assert result.smtp_port == db_email_account.smtp_port
        assert result.smtp_username == db_email_account.smtp_username
        assert result.smtp_password == db_email_account.smtp_password

    async def test_last_started_at_timestamp_behavior(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test that last_started_at timestamp is set correctly when status changes to IN_PROGRESS."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        campaign_id = uuid4()
        external_id = "mailivery_123"
        now = zoned_utc_now()

        # Test case 1: Status change from PAUSED to IN_PROGRESS should set last_started_at
        warmup_configs_start = {
            email_account_id: WarmupConfig(
                warmup_status=MailboxWarmUpStatus.IN_PROGRESS,
            )
        }

        existing_campaign_paused = EmailAccountWarmUpCampaign(
            id=campaign_id,
            domain_id=uuid4(),
            email_per_day=50,
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=user_id,
            requested_at=now,
            organization_id=organization_id,
            external_id=external_id,
            status=MailboxWarmUpStatus.PAUSED,  # Currently paused
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            is_mock_record=False,
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = existing_campaign_paused

        # Act - Start the campaign
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs_start,
        )

        # Assert - last_started_at should be included when starting
        email_account_warm_up_campaign_repository.update_campaign.assert_called_once()
        call_args = email_account_warm_up_campaign_repository.update_campaign.call_args
        assert call_args[1]["user_id"] == user_id
        assert call_args[1]["campaign_id"] == campaign_id
        assert (
            call_args[1]["column_to_update"]["status"]
            == MailboxWarmUpStatus.IN_PROGRESS
        )
        assert "last_started_at" in call_args[1]["column_to_update"]
        # Verify the timestamp is recent (within last few seconds)
        last_started_timestamp = call_args[1]["column_to_update"]["last_started_at"]
        time_diff = abs((zoned_utc_now() - last_started_timestamp).total_seconds())
        assert time_diff < 5  # Should be within 5 seconds

        # Reset mock for next test
        email_account_warm_up_campaign_repository.reset_mock()

        # Test case 2: Status change from IN_PROGRESS to PAUSED should NOT set last_started_at
        warmup_configs_pause = {
            email_account_id: WarmupConfig(
                warmup_status=MailboxWarmUpStatus.PAUSED,
            )
        }

        existing_campaign_active = EmailAccountWarmUpCampaign(
            id=campaign_id,
            domain_id=uuid4(),
            email_per_day=50,
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=user_id,
            requested_at=now,
            organization_id=organization_id,
            external_id=external_id,
            status=MailboxWarmUpStatus.IN_PROGRESS,  # Currently active
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            is_mock_record=False,
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = existing_campaign_active

        # Act - Pause the campaign
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs_pause,
        )

        # Assert - last_started_at should NOT be included when pausing
        email_account_warm_up_campaign_repository.update_campaign.assert_called_once()
        call_args = email_account_warm_up_campaign_repository.update_campaign.call_args
        assert call_args[1]["user_id"] == user_id
        assert call_args[1]["campaign_id"] == campaign_id
        assert call_args[1]["column_to_update"]["status"] == MailboxWarmUpStatus.PAUSED
        assert "last_started_at" not in call_args[1]["column_to_update"]

    async def test_last_started_at_set_for_mock_records(
        self,
        warmup_service: MailiveryWarmupService,
        email_account_warm_up_campaign_repository: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        """Test that last_started_at IS set for mock records when status changes to IN_PROGRESS."""
        # Arrange
        organization_id = uuid4()
        user_id = uuid4()
        email_account_id = uuid4()
        campaign_id = uuid4()
        now = zoned_utc_now()

        warmup_configs = {
            email_account_id: WarmupConfig(
                warmup_status=MailboxWarmUpStatus.IN_PROGRESS,
            )
        }

        # Mock record (is_mock_record=True)
        existing_mock_campaign = EmailAccountWarmUpCampaign(
            id=campaign_id,
            domain_id=uuid4(),
            email_per_day=50,
            response_rate=20,
            email_account_id=email_account_id,
            requested_by_user_id=user_id,
            requested_at=now,
            organization_id=organization_id,
            external_id="mock_external_id",
            status=MailboxWarmUpStatus.PAUSED,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            is_mock_record=True,  # This is a mock record
            created_at=now,
            created_by_user_id=user_id,
        )
        email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service.return_value = existing_mock_campaign

        # Act
        await warmup_service.update_campaign(
            user_id=user_id,
            organization_id=organization_id,
            warmup_configs=warmup_configs,
        )

        # Assert - Database update should happen for mock records, but no external API calls
        email_account_warm_up_campaign_repository.update_campaign.assert_called_once()
        mailivery_client.start_warmup.assert_not_called()

        # Verify the database update includes last_started_at
        call_args = email_account_warm_up_campaign_repository.update_campaign.call_args
        assert call_args[1]["user_id"] == user_id
        assert call_args[1]["campaign_id"] == campaign_id
        assert (
            call_args[1]["column_to_update"]["status"]
            == MailboxWarmUpStatus.IN_PROGRESS
        )
        assert "last_started_at" in call_args[1]["column_to_update"]

        # Verify the timestamp is recent (within last few seconds)
        last_started_timestamp = call_args[1]["column_to_update"]["last_started_at"]
        time_diff = abs((zoned_utc_now() - last_started_timestamp).total_seconds())
        assert time_diff < 5  # Should be within 5 seconds
