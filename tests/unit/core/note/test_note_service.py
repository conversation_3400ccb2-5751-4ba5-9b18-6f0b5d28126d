from unittest.mock import AsyncMock, Mock
from uuid import UUID, uuid4

import pytest
from fastapi import HTTPException

from salestech_be.common.exception import ConflictResourceError, ResourceNotFoundError
from salestech_be.common.type.patch_request import UNSET
from salestech_be.core.note.service.note_service import NoteService
from salestech_be.core.note.types import Note
from salestech_be.db.models.attachment import Attachment
from salestech_be.db.models.note import Note as DbNote
from salestech_be.db.models.note import NoteReference, NoteReferenceIdType, NoteType
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.note.schema import CreateNoteRequest, PatchNoteRequest
from tests.util.factories import (
    AttachmentFactory,
    NoteFactory,
    NoteReferenceFactory,
)
from tests.util.service_test import ServiceTest


class TestNoteService(ServiceTest):
    async def test_insert_note_invalid_request(self, note_service: NoteService) -> None:
        with pytest.raises(HTTPException):
            await note_service.insert_note(
                user_id=uuid4(),
                organization_id=uuid4(),
                create_note_request=CreateNoteRequest(note_html=""),
            )

    async def test_insert_note_primary_exists(
        self,
        note_factory: NoteFactory,
        note_reference_factory: NoteReferenceFactory,
        note_repository: Mock,
        account_engagement_repository: Mock,
        domain_crm_association_service: Mock,
        note_service: NoteService,
    ) -> None:
        pipeline_id = uuid4()
        organization_id = uuid4()

        existing_note = note_factory.build()
        mock_find_notes_by_reference_id_and_type(
            mock_note_repo=note_repository,
            input_organization_id=organization_id,
            input_reference_id=pipeline_id,
            input_note_type=NoteType.DEAL_PRIMARY_NOTE,
            result=[existing_note],
        )

        self.mock_repo_find_by_tenanted_primary_key(
            mock_repository=note_repository,
            input_organization_id=organization_id,
            input_lookup_fields={"id": existing_note.id},
            result=existing_note,
        )
        note_reference = note_reference_factory.build(
            reference_id=str(pipeline_id),
            reference_id_type=NoteReferenceIdType.PIPELINE_ID,
        )
        mock_find_note_references_by_note_ids(
            mock_note_repo=note_repository,
            input_note_ids=[existing_note.id],
            input_organization_id=organization_id,
            result=[note_reference],
        )

        domain_crm_association_service.bulk_create_domain_crm_associations = AsyncMock(
            return_value=[]
        )

        with pytest.raises(ConflictResourceError):
            await note_service.insert_note(
                user_id=uuid4(),
                organization_id=organization_id,
                create_note_request=CreateNoteRequest(
                    note_html="note",
                    note_type=NoteType.DEAL_PRIMARY_NOTE,
                    type_reference_id=pipeline_id,
                ),
            )

        domain_crm_association_service.bulk_create_domain_crm_associations.assert_not_called()

    async def test_insert_note_invalid_attachment_id(
        self,
        file_service: Mock,
        domain_crm_association_service: Mock,
        note_service: NoteService,
    ) -> None:
        file_service.get_attachment = AsyncMock(side_effect=ResourceNotFoundError)

        domain_crm_association_service.bulk_create_domain_crm_associations = AsyncMock(
            return_value=[]
        )

        with pytest.raises(ResourceNotFoundError):
            await note_service.insert_note(
                user_id=uuid4(),
                organization_id=uuid4(),
                create_note_request=CreateNoteRequest(
                    note_html="note",
                    note_type=NoteType.GENERAL,
                    type_reference_id=uuid4(),
                    attachment_ids=[uuid4()],
                ),
            )

        domain_crm_association_service.bulk_create_domain_crm_associations.assert_not_called()

    @pytest.mark.parametrize("attachment_ids", [[uuid4()], [], None])
    async def test_insert_note_general(
        self,
        attachment_ids: list[UUID] | None,
        note_factory: NoteFactory,
        note_reference_factory: NoteReferenceFactory,
        attachment_factory: AttachmentFactory,
        file_service: Mock,
        account_engagement_repository: Mock,
        note_repository: Mock,
        domain_crm_association_service: Mock,
        note_service: NoteService,
    ) -> None:
        organization_id = uuid4()
        pipeline_id = uuid4()
        user_id = uuid4()

        inserted_note = note_factory.build()
        note_repository.insert_note = AsyncMock(return_value=inserted_note)
        note_repository.find_by_tenanted_primary_key = AsyncMock(
            return_value=inserted_note
        )
        note_reference = note_reference_factory.build(
            reference_id=str(pipeline_id),
            reference_id_type=NoteReferenceIdType.PIPELINE_ID,
        )
        note_repository.find_note_references_by_note_ids = AsyncMock(
            return_value=[note_reference]
        )

        domain_crm_association_service.bulk_create_domain_crm_associations = AsyncMock(
            return_value=[]
        )

        if attachment_ids:
            mock_file_service_get_attachment(
                mock_file_service=file_service,
                input_attachment_id=attachment_ids[0],
                input_organization_id=organization_id,
                result=attachment_factory.build(),
            )

        result = await note_service.insert_note(
            user_id=user_id,
            organization_id=organization_id,
            create_note_request=CreateNoteRequest(
                note_html="note",
                note_type=NoteType.GENERAL,
                type_reference_id=pipeline_id,
                attachment_ids=attachment_ids,
            ),
        )
        expected = Note.map_from_db(
            db_note=inserted_note,
            db_references=[note_reference],
        )
        assert result == expected

        domain_crm_association_service.bulk_create_domain_crm_associations.assert_called_once()

    @pytest.mark.parametrize("exclude_types", [None, []])
    async def test_list_all(
        self,
        exclude_types: list[NoteType] | None,
        note_factory: NoteFactory,
        note_reference_factory: NoteReferenceFactory,
        note_repository: Mock,
        note_service: NoteService,
    ) -> None:
        organization_id = uuid4()
        note_id = uuid4()
        task_id = uuid4()

        note = note_factory.build(id=note_id, type=NoteType.DEAL_PRIMARY_NOTE)
        other_note = note_factory.build(id=uuid4(), type=NoteType.GENERAL)
        mock_find_notes_by_organization_id(
            mock_note_repo=note_repository,
            input_organization_id=organization_id,
            input_exclude_deleted_or_archived=True,
            result=[note, other_note],
        )

        note_task_reference = note_reference_factory.build(
            note_id=note_id,
            reference_id=str(task_id),
            reference_id_type=NoteReferenceIdType.TASK_ID,
        )

        mock_find_note_references_by_note_ids(
            mock_note_repo=note_repository,
            input_note_ids=[note_id, other_note.id],
            input_organization_id=organization_id,
            result=[note_task_reference],
        )

        result = await note_service.list_all(
            organization_id=organization_id, exclude_types=exclude_types
        )
        expected = [
            Note.map_from_db(
                db_note=note,
                db_references=[note_task_reference],
            ),
            Note.map_from_db(db_note=other_note, db_references=[]),
        ]
        assert result == expected

    async def test_list_all_override_exclude(
        self,
        note_factory: NoteFactory,
        note_reference_factory: NoteReferenceFactory,
        note_repository: Mock,
        note_service: NoteService,
    ) -> None:
        organization_id = uuid4()
        note_id = uuid4()
        task_id = uuid4()

        note = note_factory.build(id=note_id, type=NoteType.DEAL_PRIMARY_NOTE)
        task_comment_note = note_factory.build(id=uuid4(), type=NoteType.TASK_COMMENT)
        meeting_comment_note = note_factory.build(
            id=uuid4(), type=NoteType.MEETING_COMMENT
        )

        mock_find_notes_by_organization_id(
            mock_note_repo=note_repository,
            input_organization_id=organization_id,
            input_exclude_deleted_or_archived=True,
            result=[note, task_comment_note, meeting_comment_note],
        )

        note_task_reference = note_reference_factory.build(
            note_id=note_id,
            reference_id=str(task_id),
            reference_id_type=NoteReferenceIdType.TASK_ID,
        )

        mock_find_note_references_by_note_ids(
            mock_note_repo=note_repository,
            input_note_ids=[note_id, task_comment_note.id, meeting_comment_note.id],
            input_organization_id=organization_id,
            result=[note_task_reference],
        )

        # Override default - explicitly set to not exclude any, so task comment is in
        # the result
        result = await note_service.list_all(
            organization_id=organization_id, exclude_types=[]
        )
        expected = [
            Note.map_from_db(
                db_note=note,
                db_references=[note_task_reference],
            ),
            Note.map_from_db(db_note=task_comment_note, db_references=[]),
            Note.map_from_db(db_note=meeting_comment_note, db_references=[]),
        ]
        assert result == expected

    async def test_list_all_default_exclude(
        self,
        note_factory: NoteFactory,
        note_reference_factory: NoteReferenceFactory,
        note_repository: Mock,
        note_service: NoteService,
    ) -> None:
        organization_id = uuid4()
        note_id = uuid4()
        task_id = uuid4()

        note = note_factory.build(id=note_id)
        task_comment_note = note_factory.build(id=uuid4(), type=NoteType.TASK_COMMENT)
        meeting_comment_note = note_factory.build(
            id=uuid4(), type=NoteType.MEETING_COMMENT
        )
        mock_find_notes_by_organization_id(
            mock_note_repo=note_repository,
            input_organization_id=organization_id,
            input_exclude_deleted_or_archived=True,
            result=[note, task_comment_note, meeting_comment_note],
        )

        note_task_reference = note_reference_factory.build(
            note_id=note_id,
            reference_id=str(task_id),
            reference_id_type=NoteReferenceIdType.TASK_ID,
        )

        mock_find_note_references_by_note_ids(
            mock_note_repo=note_repository,
            input_note_ids=[note_id],
            input_organization_id=organization_id,
            result=[note_task_reference],
        )

        result = await note_service.list_all(organization_id=organization_id)
        expected = [
            Note.map_from_db(
                db_note=note,
                db_references=[note_task_reference],
            ),
        ]  # task comment note and meeting comment note not included as they are filtered out
        assert result == expected

    async def test_patch_by_id_update_html(
        self,
        note_factory: NoteFactory,
        note_reference_factory: NoteReferenceFactory,
        note_repository: Mock,
        note_service: NoteService,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        pipeline_id = uuid4()
        existing_note = note_factory.build(
            attachment_ids=[uuid4()], note_html="Initial text"
        )
        note_reference = note_reference_factory.build(
            reference_id=str(pipeline_id),
            reference_id_type=NoteReferenceIdType.PIPELINE_ID,
        )
        mock_find_note_references_by_note_ids(
            mock_note_repo=note_repository,
            input_note_ids=[existing_note.id],
            input_organization_id=organization_id,
            result=[note_reference],
        )

        updated_note = existing_note.copy(
            update={
                "updated_at": zoned_utc_now(),
                "updated_by_user_id": user_id,
                "note_html": "Updated text",
            }
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=note_repository,
            input_organization_id=organization_id,
            where_columns={"id": existing_note.id},
            update_columns={"updated_by_user_id": user_id, "note_html": "Updated text"},
            update_date_fields=["updated_at"],
            result=updated_note,
        )

        self.mock_repo_find_by_tenanted_primary_key_lookup_sequence(
            mock_repository=note_repository,
            input_organization_id=organization_id,
            input_lookup_fields={"id": existing_note.id},
            results=[existing_note, updated_note],
        )

        result = await note_service.patch_by_id(
            organization_id=organization_id,
            user_id=user_id,
            note_id=existing_note.id,
            patch_note_request=PatchNoteRequest(
                note_html="Updated text",
                title=UNSET,
                account_id=UNSET,
                pipeline_id=UNSET,
                meeting_id=UNSET,
                contact_ids=UNSET,
                user_ids=UNSET,
                attachment_ids=UNSET,
            ),
        )
        expected_note = Note.map_from_db(
            db_note=updated_note,
            db_references=[note_reference],
        )
        assert result == expected_note

    async def test_patch_by_id_update_attachment_ids(
        self,
        note_factory: NoteFactory,
        note_reference_factory: NoteReferenceFactory,
        attachment_factory: AttachmentFactory,
        note_repository: Mock,
        file_service: Mock,
        note_service: NoteService,
    ) -> None:
        organization_id = uuid4()
        user_id = uuid4()
        pipeline_id = uuid4()
        existing_attachment_ids = [uuid4()]
        existing_note = note_factory.build(
            attachment_ids=existing_attachment_ids,
            note_html="Note",
        )

        note_reference = note_reference_factory.build(
            reference_id=str(pipeline_id),
            reference_id_type=NoteReferenceIdType.PIPELINE_ID,
        )
        mock_find_note_references_by_note_ids(
            mock_note_repo=note_repository,
            input_note_ids=[existing_note.id],
            input_organization_id=organization_id,
            result=[note_reference],
        )

        new_attachment_id = uuid4()
        mock_file_service_get_attachment(
            mock_file_service=file_service,
            input_attachment_id=new_attachment_id,
            input_organization_id=organization_id,
            result=attachment_factory.build(),
        )
        updated_note = existing_note.copy(
            update={
                "updated_at": zoned_utc_now(),
                "updated_by_user_id": user_id,
                "attachment_ids": [*existing_attachment_ids, new_attachment_id],
            }
        )
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=note_repository,
            input_organization_id=organization_id,
            where_columns={"id": existing_note.id},
            update_columns={
                "updated_by_user_id": user_id,
                "attachment_ids": [*existing_attachment_ids, new_attachment_id],
            },
            update_date_fields=["updated_at"],
            result=updated_note,
        )
        self.mock_repo_find_by_tenanted_primary_key_lookup_sequence(
            mock_repository=note_repository,
            input_organization_id=organization_id,
            input_lookup_fields={"id": existing_note.id},
            results=[existing_note, updated_note],
        )

        result = await note_service.patch_by_id(
            organization_id=organization_id,
            user_id=user_id,
            note_id=existing_note.id,
            patch_note_request=PatchNoteRequest(
                note_html=UNSET,
                title=UNSET,
                account_id=UNSET,
                pipeline_id=UNSET,
                meeting_id=UNSET,
                contact_ids=UNSET,
                user_ids=UNSET,
                attachment_ids=[*existing_attachment_ids, new_attachment_id],
            ),
        )
        expected_note = Note.map_from_db(
            db_note=updated_note,
            db_references=[note_reference],
        )
        assert result == expected_note

    async def test_delete_by_id(
        self,
        note_factory: NoteFactory,
        note_repository: Mock,
        note_service: NoteService,
    ) -> None:
        organization_id = uuid4()
        note_id = uuid4()
        user_id = uuid4()
        updated_note = note_factory.build()
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=note_repository,
            input_organization_id=organization_id,
            where_columns={"id": note_id},
            update_columns={"deleted_by_user_id": user_id},
            update_date_fields=["deleted_at"],
            result=updated_note,
        )

        result = await note_service.delete_by_id(
            user_id=user_id, organization_id=organization_id, note_id=note_id
        )
        assert result == updated_note

    async def test_delete_by_id_not_found(
        self, note_repository: Mock, note_service: NoteService
    ) -> None:
        organization_id = uuid4()
        note_id = uuid4()
        user_id = uuid4()
        self.mock_repo_update_by_tenanted_primary_key(
            mock_repository=note_repository,
            input_organization_id=organization_id,
            where_columns={"id": note_id},
            update_columns={"deleted_by_user_id": user_id},
            update_date_fields=["deleted_at"],
            result=None,
        )

        with pytest.raises(ResourceNotFoundError):
            await note_service.delete_by_id(
                user_id=user_id, organization_id=organization_id, note_id=note_id
            )


def mock_find_notes_by_organization_id(
    mock_note_repo: Mock,
    input_organization_id: UUID,
    input_exclude_deleted_or_archived: bool | None,
    result: list[DbNote],
) -> None:
    def lookup(
        organization_id: UUID,
        exclude_deleted_or_archived: bool | None,
    ) -> list[DbNote]:
        assert organization_id == input_organization_id
        assert exclude_deleted_or_archived == input_exclude_deleted_or_archived
        return result

    mock_note_repo.find_notes_by_organization_id = AsyncMock(side_effect=lookup)


def mock_find_notes_by_reference_id_and_type(
    mock_note_repo: Mock,
    input_organization_id: UUID,
    input_reference_id: UUID,
    input_note_type: NoteType,
    result: list[DbNote],
) -> None:
    def lookup(
        organization_id: UUID, reference_id: UUID, note_type: NoteType
    ) -> list[DbNote]:
        assert organization_id == input_organization_id
        assert reference_id == input_reference_id
        assert note_type == input_note_type
        return result

    mock_note_repo.find_notes_by_reference_id_and_type = AsyncMock(side_effect=lookup)


def mock_find_note_references_by_note_ids(
    mock_note_repo: Mock,
    input_note_ids: list[UUID],
    input_organization_id: UUID,
    result: list[NoteReference],
) -> None:
    def lookup(note_ids: list[UUID], organization_id: UUID) -> list[NoteReference]:
        assert note_ids == input_note_ids
        assert organization_id == input_organization_id
        return result

    mock_note_repo.find_note_references_by_note_ids = AsyncMock(side_effect=lookup)


def mock_file_service_get_attachment(
    mock_file_service: Mock,
    input_attachment_id: UUID,
    input_organization_id: UUID,
    result: Attachment,
) -> None:
    def lookup(attachment_id: UUID, organization_id: UUID) -> Attachment:
        assert attachment_id == input_attachment_id
        assert organization_id == input_organization_id
        return result

    mock_file_service.get_attachment = AsyncMock(side_effect=lookup)


@pytest.fixture
def note_repository() -> Mock:
    return AsyncMock()


@pytest.fixture
def note_reference_repository() -> Mock:
    return AsyncMock()


@pytest.fixture
def account_engagement_repository() -> Mock:
    return AsyncMock()


@pytest.fixture
def file_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def domain_crm_association_service() -> Mock:
    return AsyncMock()


@pytest.fixture
def note_service(
    file_service: Mock,
    note_repository: Mock,
    note_reference_repository: Mock,
    account_engagement_repository: Mock,
    domain_crm_association_service: Mock,
) -> NoteService:
    return NoteService(
        activity_service=AsyncMock(),
        file_service=file_service,
        note_repository=note_repository,
        note_reference_repository=note_reference_repository,
        domain_crm_association_service=domain_crm_association_service,
    )
