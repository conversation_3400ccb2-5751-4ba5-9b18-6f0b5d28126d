from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

import pytest

from salestech_be.core.email.outbound_domain.schema import DNSCheckResult
from salestech_be.core.email.outbound_domain.service import OutboundDomainService
from salestech_be.core.email.outbound_domain.types_v2 import (
    OutboundDomainHealth,
)
from salestech_be.core.email.pool.service import EmailAccountPoolService
from salestech_be.core.quota.service.quota_policy_service import QuotaPolicyService
from salestech_be.core.quota.service.quota_service import QuotaService
from salestech_be.db.dao.email_account import (
    EmailAccountPoolRepository,
    EmailAccountRepository,
)
from salestech_be.db.dao.email_account_warm_up_campaign_repository import (
    EmailAccountWarmUpCampaignRepository,
)
from salestech_be.db.dao.outbound_repository import (
    DomainHealthRepository,
    OutboundDomainRepository,
)
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.models.core.types import EntityDnsRecord
from salestech_be.db.models.outbound import (
    DomainHealth,
    OutboundDomain,
    OutboundDomainStatus,
)
from salestech_be.integrations.infraforge.async_infraforge_client import (
    AsyncInfraForgeClient,
)
from salestech_be.integrations.mailivery.async_mailivery_client import (
    AsyncMailiveryClient,
)
from salestech_be.util.time import zoned_utc_now


@pytest.fixture
def email_account_repo() -> AsyncMock:
    return AsyncMock(spec=EmailAccountRepository)


@pytest.fixture
def email_account_pool_repo() -> AsyncMock:
    return AsyncMock(spec=EmailAccountPoolRepository)


@pytest.fixture
def email_account_warm_up_campaign_repo() -> AsyncMock:
    return AsyncMock(spec=EmailAccountWarmUpCampaignRepository)


@pytest.fixture
def outbound_domain_repo() -> AsyncMock:
    return AsyncMock(spec=OutboundDomainRepository)


@pytest.fixture
def domain_health_repo() -> AsyncMock:
    return AsyncMock(spec=DomainHealthRepository)


@pytest.fixture
def user_repo() -> AsyncMock:
    return AsyncMock(spec=UserRepository)


@pytest.fixture
def quota_service() -> AsyncMock:
    return AsyncMock(spec=QuotaService)


@pytest.fixture
def quota_policy_service() -> AsyncMock:
    return AsyncMock(spec=QuotaPolicyService)


@pytest.fixture
def email_account_pool_service() -> AsyncMock:
    return AsyncMock(spec=EmailAccountPoolService)


@pytest.fixture
def async_infraforge_client() -> AsyncMock:
    return AsyncMock(spec=AsyncInfraForgeClient)


@pytest.fixture
def async_mailivery_client() -> AsyncMock:
    return AsyncMock(spec=AsyncMailiveryClient)


@pytest.fixture
def temporal_client() -> MagicMock:
    # Client is a sync object with some async methods
    client = MagicMock()
    client.start_workflow = AsyncMock()
    client.create_schedule = AsyncMock()

    # Create a mock workflow handle with an awaitable signal method
    mock_handle = MagicMock()
    mock_handle.signal = AsyncMock()

    # get_workflow_handle is synchronous and returns the mock handle
    client.get_workflow_handle = MagicMock(return_value=mock_handle)

    return client


@pytest.fixture
def mock_get_temporal_client(temporal_client: MagicMock) -> AsyncMock:
    mock_func = AsyncMock()
    mock_func.return_value = temporal_client
    return mock_func


# New fixtures for perform_domain_health_checks testing


@pytest.fixture
def mock_check_dns() -> AsyncMock:
    """Mock for the check_dns function from dns_check_service"""
    return AsyncMock()


@pytest.fixture
def mock_activity_info() -> MagicMock:
    """Mock for temporalio activity.info()"""
    mock_info = MagicMock()
    mock_info.attempt = 1  # Default to first attempt
    return mock_info


@pytest.fixture
def mock_activity() -> MagicMock:
    """Mock for temporalio activity module"""
    return MagicMock()


@pytest.fixture
def sample_outbound_domain() -> OutboundDomain:
    """Sample OutboundDomain for testing"""
    return OutboundDomain(
        id=uuid4(),
        organization_id=uuid4(),
        domain="test.com",
        external_id="external_123",
        created_by_user_id=uuid4(),
        status=OutboundDomainStatus.ACTIVE,
        workspace_id=uuid4(),
        forward_to_domain="forward.example.com",
        is_mock_record=False,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )


@pytest.fixture
def empty_dns_check_result() -> DNSCheckResult:
    """Empty DNSCheckResult for testing pending health scenarios"""
    return DNSCheckResult(
        mx_records=[],
        spf_records=[],
        dkim_record=[],
        dmarc_record=[],
    )


@pytest.fixture
def valid_dns_check_result() -> DNSCheckResult:
    """Valid DNSCheckResult for testing healthy domain scenarios"""

    return DNSCheckResult(
        mx_records=[
            EntityDnsRecord(record="10 mx1.example.com"),
            EntityDnsRecord(record="20 mx2.example.com"),
        ],
        spf_records=[EntityDnsRecord(record="v=spf1 include:_spf.example.com ~all")],
        dkim_record=[EntityDnsRecord(record="v=DKIM1; k=rsa; p=validpublickey...")],
        dmarc_record=[
            EntityDnsRecord(record="v=DMARC1; p=reject; rua=mailto:<EMAIL>")
        ],
    )


# Example test cases for perform_domain_health_checks


async def test_perform_domain_health_checks_pending_health_retry(
    domain_health_repo: AsyncMock,
    mock_check_dns: AsyncMock,
    sample_outbound_domain: OutboundDomain,
    empty_dns_check_result: DNSCheckResult,
    mock_activity_info: MagicMock,
) -> None:
    """Test perform_domain_health_checks with pending health that should retry"""
    # Arrange

    # Create domain health with missing records (empty)
    incomplete_domain_health = DomainHealth(
        id=uuid4(),
        domain_id=sample_outbound_domain.id,
        mx_records=empty_dns_check_result.mx_records,  # Empty
        spf_records=empty_dns_check_result.spf_records,  # Empty
        dkim_record=empty_dns_check_result.dkim_record,  # Empty
        dmarc_record=empty_dns_check_result.dmarc_record,  # Empty
        is_mock_record=False,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )

    # Set up mocks - the key is that check_dns returns empty DNS records
    mock_check_dns.return_value = empty_dns_check_result
    domain_health_repo.upsert_domain_health.return_value = incomplete_domain_health

    # Mock the OutboundDomainService
    service = MagicMock()
    service.domain_health_repository = domain_health_repo

    # Import and patch necessary modules
    with (
        patch(
            "salestech_be.core.email.outbound_domain.service.check_dns", mock_check_dns
        ),
        patch(
            "salestech_be.core.email.outbound_domain.service.DomainConfigurationModel"
        ) as mock_domain_config,
        patch(
            "salestech_be.core.email.outbound_domain.service.OutboundDomainV2"
        ) as mock_outbound_domain_v2,
    ):
        # Configure mocks
        mock_domain_config.from_db_model.return_value = MagicMock()
        expected_result = MagicMock()
        mock_outbound_domain_v2.from_db_model.return_value = expected_result

        # Configure the expected_result mock with actual values
        expected_result.status = sample_outbound_domain.status
        expected_result.id = sample_outbound_domain.id
        expected_result.organization_id = sample_outbound_domain.organization_id
        expected_result.domain = sample_outbound_domain.domain
        expected_result.created_by_user_id = sample_outbound_domain.created_by_user_id
        expected_result.domain_health = (
            OutboundDomainHealth.PENDING
        )  # This gets set to PENDING
        expected_result.total_mailbox_count = 0
        expected_result.active_mailbox_count = 0
        expected_result.sequence_count = 0
        expected_result.purchased_at = sample_outbound_domain.created_at
        expected_result.updated_at = sample_outbound_domain.updated_at
        expected_result.deleted_at = None
        expected_result.archived_at = None

        # Act - Should return OutboundDomainV2 with PENDING status
        result = await OutboundDomainService.perform_domain_health_checks(
            service, sample_outbound_domain
        )

        # Assert the OutboundDomainV2 object is as expected
        assert result is not None
        assert result == expected_result
        assert result.status == OutboundDomainStatus.ACTIVE
        assert (
            result.domain_health == OutboundDomainHealth.PENDING
        )  # Health gets set to PENDING
        assert result.id == sample_outbound_domain.id
        assert result.organization_id == sample_outbound_domain.organization_id
        assert result.domain == sample_outbound_domain.domain
        assert result.created_by_user_id == sample_outbound_domain.created_by_user_id

        # Test method calls
        mock_check_dns.assert_called_once_with("test.com.")
        domain_health_repo.upsert_domain_health.assert_called_once()

        # Verify OutboundDomainV2.from_db_model was called with PENDING health
        mock_outbound_domain_v2.from_db_model.assert_called_once()
        call_args = mock_outbound_domain_v2.from_db_model.call_args
        assert call_args.kwargs["domain_health"] == OutboundDomainHealth.PENDING
        assert call_args.kwargs["domain"] == sample_outbound_domain


async def test_perform_domain_health_checks_valid_dns_returns_healthy(
    domain_health_repo: AsyncMock,
    mock_check_dns: AsyncMock,
    sample_outbound_domain: OutboundDomain,
    valid_dns_check_result: DNSCheckResult,
    mock_activity_info: MagicMock,
) -> None:
    """Test perform_domain_health_checks with valid DNS records returns HEALTHY"""
    # Arrange

    # Create domain health that will be returned from upsert_domain_health
    # The service creates this from the DNS check result
    complete_domain_health = DomainHealth(
        id=uuid4(),
        domain_id=sample_outbound_domain.id,
        mx_records=valid_dns_check_result.mx_records,
        spf_records=valid_dns_check_result.spf_records,
        dkim_record=valid_dns_check_result.dkim_record,
        dmarc_record=valid_dns_check_result.dmarc_record,
        is_mock_record=False,
        created_at=zoned_utc_now(),
        updated_at=zoned_utc_now(),
    )

    # Set up mocks - the key is that check_dns returns valid DNS records
    mock_check_dns.return_value = valid_dns_check_result
    domain_health_repo.upsert_domain_health.return_value = complete_domain_health

    # Mock the OutboundDomainService
    service = MagicMock()
    service.domain_health_repository = domain_health_repo

    # Import and patch necessary modules
    with (
        patch(
            "salestech_be.core.email.outbound_domain.service.check_dns", mock_check_dns
        ),
        patch(
            "salestech_be.core.email.outbound_domain.service.DomainConfigurationModel"
        ) as mock_domain_config,
        patch(
            "salestech_be.core.email.outbound_domain.service.OutboundDomainV2"
        ) as mock_outbound_domain_v2,
    ):
        # Configure mocks
        mock_domain_config.from_db_model.return_value = MagicMock()
        expected_result = MagicMock()
        mock_outbound_domain_v2.from_db_model.return_value = expected_result

        # Configure the expected_result mock with actual values
        expected_result.status = (
            sample_outbound_domain.status
        )  # Should be ACTIVE from the fixture
        expected_result.id = sample_outbound_domain.id
        expected_result.organization_id = sample_outbound_domain.organization_id
        expected_result.domain = sample_outbound_domain.domain
        expected_result.created_by_user_id = sample_outbound_domain.created_by_user_id
        expected_result.domain_health = (
            OutboundDomainHealth.HEALTHY
        )  # This is what gets set to HEALTHY
        expected_result.total_mailbox_count = 0
        expected_result.active_mailbox_count = 0
        expected_result.sequence_count = 0
        expected_result.purchased_at = sample_outbound_domain.created_at
        expected_result.updated_at = sample_outbound_domain.updated_at
        expected_result.deleted_at = None
        expected_result.archived_at = None
        # Import the actual method after patching

        # Act - This should NOT raise an exception
        result = await OutboundDomainService.perform_domain_health_checks(
            service, sample_outbound_domain
        )

        # Assert the OutboundDomainV2 object is as expected
        assert result is not None
        assert result == expected_result
        assert result.status == OutboundDomainStatus.ACTIVE
        assert (
            result.domain_health == OutboundDomainHealth.HEALTHY
        )  # Health gets set to HEALTHY
        assert result.id == sample_outbound_domain.id
        assert result.organization_id == sample_outbound_domain.organization_id
        assert result.domain == sample_outbound_domain.domain
        assert result.created_by_user_id == sample_outbound_domain.created_by_user_id
        assert result.total_mailbox_count == 0
        assert result.active_mailbox_count == 0
        assert result.sequence_count == 0
        assert result.purchased_at == sample_outbound_domain.created_at
        assert result.updated_at == sample_outbound_domain.updated_at
        assert result.deleted_at is None
        assert result.archived_at is None

        # Test method calls
        mock_check_dns.assert_called_once_with("test.com.")
        domain_health_repo.upsert_domain_health.assert_called_once()

        # Verify OutboundDomainV2.from_db_model was called with HEALTHY health (this is the function called at return)
        mock_outbound_domain_v2.from_db_model.assert_called_once()
        call_args = mock_outbound_domain_v2.from_db_model.call_args
        assert call_args.kwargs["domain_health"] == OutboundDomainHealth.HEALTHY
        assert call_args.kwargs["domain"] == sample_outbound_domain
        # the dns configuration is not set here, it's set in the workflow and it's a mock value for testing
