from datetime import timed<PERSON><PERSON>
from typing import Any
from unittest.mock import AsyncMock, patch
from uuid import uuid4

import pytest
from temporalio.client import Client

from salestech_be.core.email.account.email_account_health_service import (
    EmailAccountHealthScoreData,
    EmailAccountHealthScoreParams,
    EmailAccountHealthService,
)
from salestech_be.core.email.warmup.service import MailiveryWarmupService
from salestech_be.db.dao.email_account import (
    EmailAccountHealthRepository,
    EmailAccountRepository,
)
from salestech_be.db.dao.email_event_repository import EmailEventRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.email_account import (
    EmailAccount,
    EmailAccountHealth,
    EmailAccountHealthHistory,
    EmailAccountType,
)
from salestech_be.db.models.email_account_warm_up import (
    EmailAccountWarmUpCampaign,
    MailboxWarmUpSpeed,
    MailboxWarmUpStatus,
)
from salestech_be.db.models.sequence import EmailEventType
from salestech_be.integrations.mailivery.type import (
    DailyMetrics,
    LandedIn,
    MailiveryMetricsResponse,
    Replies,
    WarmUp,
)
from salestech_be.util.time import zoned_utc_now
from tests.util.service_test import ServiceTest


@pytest.fixture
def email_account_health_repo() -> AsyncMock:
    return AsyncMock(spec=EmailAccountHealthRepository)


@pytest.fixture
def email_event_repo() -> AsyncMock:
    return AsyncMock(spec=EmailEventRepository)


@pytest.fixture
def email_account_repo() -> AsyncMock:
    return AsyncMock(spec=EmailAccountRepository)


@pytest.fixture
def email_account_warm_up_service() -> AsyncMock:
    return AsyncMock(spec=MailiveryWarmupService)


@pytest.fixture
def temporal_client() -> AsyncMock:
    client = AsyncMock(spec=Client)
    client.create_schedule = AsyncMock()
    client.get_schedule_handle = AsyncMock()
    return client


@pytest.fixture
def test_data() -> dict[str, Any]:
    return {
        "organization_id": uuid4(),
        "email_account_id": uuid4(),
        "email_account_health_id": uuid4(),
        "current_time": zoned_utc_now(),
    }


@pytest.fixture
def mailivery_client() -> AsyncMock:
    return AsyncMock()


class TestEmailAccountHealthService(ServiceTest):
    @pytest.fixture(autouse=True)
    def setup(
        self,
        email_account_health_repo: AsyncMock,
        email_event_repo: AsyncMock,
        email_account_repo: AsyncMock,
        email_account_warm_up_service: AsyncMock,
        mailivery_client: AsyncMock,
    ) -> None:
        # Setup engine with proper async mocks
        engine = AsyncMock(spec=DatabaseEngine)
        engine.one = AsyncMock()
        engine.execute = AsyncMock()
        engine.begin = AsyncMock()

        self.email_account_health_repo = email_account_health_repo
        self.email_event_repo = email_event_repo
        self.email_account_repo = email_account_repo
        self.email_account_warm_up_service = email_account_warm_up_service
        self.mailivery_client_mock = mailivery_client

        # Create service implementation with all dependencies injected
        self.service = EmailAccountHealthService(
            email_account_health_repository=email_account_health_repo,
            email_event_repository=email_event_repo,
            email_account_repository=email_account_repo,
            email_account_warm_up_service=email_account_warm_up_service,
            mailivery_client=mailivery_client,
        )

    async def test_calculate_health_score(self, test_data: dict[str, Any]) -> None:
        # Setup test data
        organization_id = test_data["organization_id"]
        email_account_id = test_data["email_account_id"]
        created_time = zoned_utc_now() - timedelta(days=10)  # More than 3 days old

        # Create test email account
        email_account = EmailAccount(
            id=email_account_id,
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            organization_id=organization_id,
            created_at=created_time,
            owner_user_id=uuid4(),
            created_by_user_id=uuid4(),
            type=EmailAccountType.REGULAR,
            active=True,
            is_default=False,
            seconds_delay_between_emails=30,
        )

        # Mock email account repository
        self.email_account_repo.find_by_tenanted_primary_key.return_value = (
            email_account
        )

        # Setup mock for email event counts
        event_counts = {
            EmailEventType.BOUNCE_DETECTED: 2,
            EmailEventType.OPENED: 5,
            EmailEventType.LINK_CLICKED: 3,
            EmailEventType.REPLIED: 2,
            EmailEventType.UNSUBSCRIBED: 0,
            EmailEventType.SEND_ATTEMPTED: 10,
        }
        self.email_event_repo.get_email_event_count_by_types_and_email_account_id.return_value = event_counts

        # Setup warmup campaign
        warmup_campaign = EmailAccountWarmUpCampaign(
            id=uuid4(),
            domain_id=uuid4(),
            email_per_day=10,
            response_rate=30,
            email_account_id=email_account_id,
            organization_id=organization_id,
            external_id="12345",
            requested_by_user_id=uuid4(),
            requested_at=created_time,
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            created_at=created_time,
            created_by_user_id=uuid4(),
        )

        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.return_value = warmup_campaign

        # Mock the warmup health score calculation using patch.object
        warmup_health_score = 40
        today = zoned_utc_now().date()
        mock_mailivery_metrics = [
            DailyMetrics.model_construct(
                event_date=today,
                warm_up=WarmUp.model_construct(
                    total=10,
                    landed_in=LandedIn.model_construct(
                        inbox=8,
                        others=2,
                        failed_to_read=0,
                        not_found=0,
                    ),
                ),
                replies=Replies.model_construct(total=0, landed_in=None),
            )
        ]
        with patch.object(
            self.service,
            "calculate_warmup_health_score",
            AsyncMock(return_value=(warmup_health_score, mock_mailivery_metrics)),
        ):
            # Call the method under test
            result = await self.service.calculate_health_score(
                email_account_id=email_account_id,
                organization_id=organization_id,
            )

            # Verify repository calls
            self.email_account_repo.find_by_tenanted_primary_key.assert_called_once_with(
                EmailAccount, id=email_account_id, organization_id=organization_id
            )
            self.email_event_repo.get_email_event_count_by_types_and_email_account_id.assert_called_once()

            # Calculate expected score with the new algorithm:
            # Get weights from EmailAccountHealthScoreParams
            bounce_weight = EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                EmailEventType.BOUNCE_DETECTED
            ]
            opened_weight = EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                EmailEventType.OPENED
            ]
            link_clicked_weight = (
                EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                    EmailEventType.LINK_CLICKED
                ]
            )
            replied_weight = EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                EmailEventType.REPLIED
            ]
            unsubscribed_weight = (
                EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                    EmailEventType.UNSUBSCRIBED
                ]
            )
            warmup_weight = (
                EmailAccountHealthScoreParams.WARM_UP_CAMPAIGN_HEALTH_SCORE_WEIGHT
            )

            # Component scores: (2*bounce_weight + 5*opened_weight + 3*link_clicked_weight + 2*replied_weight + 0*unsubscribed_weight) / 10
            component_score = (
                2 * bounce_weight
                + 5 * opened_weight
                + 3 * link_clicked_weight
                + 2 * replied_weight
                + 0 * unsubscribed_weight
            ) / 10
            # Normalized component score: max(0, min(100, component_score))
            normalized_component_score = max(0, min(100, component_score))
            # Warmup score: 40 (already on 0-100 scale)
            # Component weight: 1 - (warmup_weight/100)
            component_weight = 1 - (warmup_weight / 100)
            # Warmup weight: warmup_weight/100
            warmup_weight_ratio = warmup_weight / 100
            # Final score: (normalized_component_score * component_weight) + (40 * warmup_weight_ratio)
            final_score = (normalized_component_score * component_weight) + (
                40 * warmup_weight_ratio
            )
            # Rounded
            expected_score = round(final_score)

            # Verify the result is an EmailAccountHealthScoreData object
            assert isinstance(result, EmailAccountHealthScoreData)

            # Verify the composite health score
            assert result.composite_health_score == expected_score

            # Verify other fields
            assert result.warmup_health_score == warmup_health_score
            assert result.warmup_health_score_weight == round(warmup_weight_ratio * 100)
            assert result.deliverability_health_score == round(
                normalized_component_score
            )
            assert result.deliverability_health_score_weight == round(
                component_weight * 100
            )
            assert result.mailivery_metrics == mock_mailivery_metrics
            assert result.email_event_metrics == event_counts

    async def test_update_email_account_health_existing(
        self, test_data: dict[str, Any]
    ) -> None:
        # Setup test data
        organization_id = test_data["organization_id"]
        email_account_id = test_data["email_account_id"]
        health_id = test_data["email_account_health_id"]
        current_time = test_data["current_time"]

        existing_health = EmailAccountHealth(
            id=health_id,
            email_account_id=email_account_id,
            organization_id=organization_id,
            health_score=2,
            warmup_health_score=30,
            deliverability_health_score=40,
            warmup_health_score_weight=30,
            deliverability_health_score_weight=70,
            mailivery_metrics=[
                DailyMetrics.model_construct(
                    event_date=zoned_utc_now().date(),
                    warm_up=WarmUp.model_construct(
                        total=10,
                        landed_in=LandedIn.model_construct(
                            inbox=8,
                            others=2,
                            failed_to_read=0,
                            not_found=0,
                        ),
                    ),
                    replies=Replies.model_construct(total=0, landed_in=None),
                )
            ],
            email_event_metrics={
                EmailEventType.BOUNCE_DETECTED: 2,
                EmailEventType.OPENED: 5,
                EmailEventType.LINK_CLICKED: 3,
                EmailEventType.REPLIED: 2,
                EmailEventType.UNSUBSCRIBED: 0,
                EmailEventType.SEND_ATTEMPTED: 10,
            },
            created_at=current_time,
        )

        # Configure mocks
        self.email_account_health_repo.get_email_account_health.return_value = (
            existing_health
        )

        # Create health score data
        health_score_data = EmailAccountHealthScoreData(
            composite_health_score=5,
            warmup_health_score=35,
            warmup_health_score_weight=30,  # 30%
            deliverability_health_score=45,
            deliverability_health_score_weight=70,  # 70%
            mailivery_metrics=[
                DailyMetrics.model_construct(
                    event_date=zoned_utc_now().date(),
                    warm_up=WarmUp.model_construct(
                        total=10,
                        landed_in=LandedIn.model_construct(
                            inbox=9,
                            others=1,
                            failed_to_read=0,
                            not_found=0,
                        ),
                    ),
                    replies=Replies.model_construct(total=0, landed_in=None),
                )
            ],
            email_event_metrics={
                EmailEventType.BOUNCE_DETECTED: 1,
                EmailEventType.OPENED: 6,
                EmailEventType.LINK_CLICKED: 4,
                EmailEventType.REPLIED: 3,
                EmailEventType.UNSUBSCRIBED: 0,
                EmailEventType.SEND_ATTEMPTED: 12,
            },
        )

        # Call the method under test
        await self.service.update_email_account_health(
            email_account_id=email_account_id,
            organization_id=organization_id,
            health_score_data=health_score_data,
        )

        # Verify repository calls
        self.email_account_health_repo.get_email_account_health.assert_called_once_with(
            email_account_id, organization_id
        )

        # Verify history record was created
        self.email_account_health_repo.insert.assert_called_once()
        history_record = self.email_account_health_repo.insert.call_args[0][0]
        assert isinstance(history_record, EmailAccountHealthHistory)
        assert history_record.email_account_id == email_account_id
        assert history_record.organization_id == organization_id
        assert history_record.health_score == 2
        assert history_record.warmup_health_score == 30
        assert history_record.deliverability_health_score == 40
        assert history_record.warmup_health_score_weight == 30
        assert history_record.deliverability_health_score_weight == 70
        assert history_record.mailivery_metrics is not None
        assert history_record.email_event_metrics is not None

        # Verify health record was updated
        self.email_account_health_repo.upsert_pk.assert_called_once()
        updated_health = self.email_account_health_repo.upsert_pk.call_args[0][0]
        assert isinstance(updated_health, EmailAccountHealth)
        assert updated_health.id == health_id
        assert updated_health.email_account_id == email_account_id
        assert updated_health.organization_id == organization_id
        assert updated_health.health_score == 5
        assert updated_health.warmup_health_score == 35
        assert updated_health.deliverability_health_score == 45
        assert updated_health.warmup_health_score_weight == 30
        assert updated_health.deliverability_health_score_weight == 70
        assert updated_health.mailivery_metrics is not None
        assert updated_health.email_event_metrics is not None

    async def test_update_email_account_health_new(
        self, test_data: dict[str, Any]
    ) -> None:
        # Setup test data
        organization_id = test_data["organization_id"]
        email_account_id = test_data["email_account_id"]

        # Configure mocks for new health record
        self.email_account_health_repo.get_email_account_health.return_value = None

        # Create health score data
        health_score_data = EmailAccountHealthScoreData(
            composite_health_score=5,
            warmup_health_score=35,
            warmup_health_score_weight=30,  # 30%
            deliverability_health_score=45,
            deliverability_health_score_weight=70,  # 70%
            mailivery_metrics=[
                DailyMetrics.model_construct(
                    event_date=zoned_utc_now().date(),
                    warm_up=WarmUp.model_construct(
                        total=10,
                        landed_in=LandedIn.model_construct(
                            inbox=9,
                            others=1,
                            failed_to_read=0,
                            not_found=0,
                        ),
                    ),
                    replies=Replies.model_construct(total=0, landed_in=None),
                )
            ],
            email_event_metrics={
                EmailEventType.BOUNCE_DETECTED: 1,
                EmailEventType.OPENED: 6,
                EmailEventType.LINK_CLICKED: 4,
                EmailEventType.REPLIED: 3,
                EmailEventType.UNSUBSCRIBED: 0,
                EmailEventType.SEND_ATTEMPTED: 12,
            },
        )

        # Call the method under test
        await self.service.update_email_account_health(
            email_account_id=email_account_id,
            organization_id=organization_id,
            health_score_data=health_score_data,
        )

        # Verify repository calls
        self.email_account_health_repo.get_email_account_health.assert_called_once_with(
            email_account_id, organization_id
        )

        # Verify new health record was created
        self.email_account_health_repo.insert.assert_called_once()
        new_health = self.email_account_health_repo.insert.call_args[0][0]
        assert isinstance(new_health, EmailAccountHealth)
        assert new_health.email_account_id == email_account_id
        assert new_health.organization_id == organization_id
        assert new_health.health_score == 5
        assert new_health.warmup_health_score == 35
        assert new_health.deliverability_health_score == 45
        assert new_health.warmup_health_score_weight == 30
        assert new_health.deliverability_health_score_weight == 70
        assert new_health.mailivery_metrics is not None
        assert new_health.email_event_metrics is not None

    async def test_sync_email_account_health(self, test_data: dict[str, Any]) -> None:
        # Setup test data
        organization_id = test_data["organization_id"]
        email_account_id = test_data["email_account_id"]
        owner_user_id = uuid4()
        created_by_user_id = uuid4()
        warmup_health_id = uuid4()
        current_time = test_data["current_time"]
        created_time = current_time - timedelta(days=10)  # More than 3 days old
        existing_health_id = uuid4()

        # Create email accounts to return
        email_account = EmailAccount(
            id=email_account_id,
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            organization_id=organization_id,
            created_at=created_time,
            owner_user_id=owner_user_id,
            created_by_user_id=created_by_user_id,
            type=EmailAccountType.REGULAR,
            active=True,
            is_default=False,
            seconds_delay_between_emails=30,
        )

        # Set up existing health record
        existing_health = EmailAccountHealth(
            id=existing_health_id,
            email_account_id=email_account_id,
            organization_id=organization_id,
            health_score=1,  # Current health score
            created_at=current_time,
        )

        # Set up warmup health record
        warmup_health = EmailAccountHealth(
            id=warmup_health_id,
            email_account_id=email_account_id,
            organization_id=organization_id,
            health_score=2,
            created_at=current_time,
        )

        # Configure mocks
        self.email_account_repo._find_by_column_values.return_value = [email_account]
        self.email_account_repo.find_by_tenanted_primary_key.return_value = (
            email_account
        )
        self.email_account_health_repo.get_email_account_health.return_value = (
            existing_health
        )
        self.email_account_health_repo.get_warmup_health.return_value = warmup_health

        # Setup event count data
        event_counts = {
            EmailEventType.BOUNCE_DETECTED: 1,
            EmailEventType.OPENED: 4,
            EmailEventType.LINK_CLICKED: 2,
            EmailEventType.REPLIED: 3,
            EmailEventType.UNSUBSCRIBED: 0,
            EmailEventType.SEND_ATTEMPTED: 10,
        }
        self.email_event_repo.get_email_event_count_by_types_and_email_account_id.return_value = event_counts

        # Mock the warmup health score calculation
        warmup_health_score = 30
        mock_mailivery_metrics = [
            DailyMetrics.model_construct(
                event_date=zoned_utc_now().date(),
                warm_up=WarmUp.model_construct(
                    total=10,
                    landed_in=LandedIn.model_construct(
                        inbox=8,
                        others=2,
                        failed_to_read=0,
                        not_found=0,
                    ),
                ),
                replies=Replies.model_construct(total=0, landed_in=None),
            )
        ]

        with patch.object(
            self.service,
            "calculate_warmup_health_score",
            AsyncMock(return_value=(warmup_health_score, mock_mailivery_metrics)),
        ):
            # Call the method under test
            await self.service.sync_email_account_health(organization_id)

            # Verify repository call to find email accounts
            self.email_account_repo._find_by_column_values.assert_called_once()

            # Verify email events were requested
            self.email_event_repo.get_email_event_count_by_types_and_email_account_id.assert_called_once()

            # Verify history record was created
            history_call = None
            for call in self.email_account_health_repo.insert.call_args_list:
                if isinstance(call[0][0], EmailAccountHealthHistory):
                    history_call = call
                    break

            assert history_call is not None
            history_record = history_call[0][0]
            assert isinstance(history_record, EmailAccountHealthHistory)
            assert history_record.email_account_id == email_account_id
            assert history_record.organization_id == organization_id
            # The warmup_health_id field is no longer used in the updated implementation

            # Verify the health record was updated
            # Calculate expected score with the new algorithm:
            # Get weights from EmailAccountHealthScoreParams
            bounce_weight = EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                EmailEventType.BOUNCE_DETECTED
            ]
            opened_weight = EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                EmailEventType.OPENED
            ]
            link_clicked_weight = (
                EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                    EmailEventType.LINK_CLICKED
                ]
            )
            replied_weight = EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                EmailEventType.REPLIED
            ]
            unsubscribed_weight = (
                EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS[
                    EmailEventType.UNSUBSCRIBED
                ]
            )
            warmup_weight = (
                EmailAccountHealthScoreParams.WARM_UP_CAMPAIGN_HEALTH_SCORE_WEIGHT
            )

            # Component scores: (1*bounce_weight + 4*opened_weight + 2*link_clicked_weight + 3*replied_weight + 0*unsubscribed_weight) / 10
            component_score = (
                1 * bounce_weight
                + 4 * opened_weight
                + 2 * link_clicked_weight
                + 3 * replied_weight
                + 0 * unsubscribed_weight
            ) / 10
            # Normalized component score: max(0, min(100, component_score))
            normalized_component_score = max(0, min(100, component_score))
            # Warmup score: 30 (already on 0-100 scale)
            # Component weight: 1 - (warmup_weight/100)
            component_weight = 1 - (warmup_weight / 100)
            # Warmup weight: warmup_weight/100
            warmup_weight_ratio = warmup_weight / 100
            # Final score: (normalized_component_score * component_weight) + (30 * warmup_weight_ratio)
            final_score = (normalized_component_score * component_weight) + (
                30 * warmup_weight_ratio
            )
            # Rounded
            expected_health_score = round(final_score)

            update_call = self.email_account_health_repo.upsert_pk.call_args
            assert update_call is not None
            updated_health = update_call[0][0]
            assert isinstance(updated_health, EmailAccountHealth)
            assert updated_health.id == existing_health_id
            assert updated_health.health_score == expected_health_score

    async def test_sync_email_account_health_no_accounts(
        self, test_data: dict[str, Any]
    ) -> None:
        # Setup test data
        organization_id = test_data["organization_id"]

        # Configure mocks - no email accounts found
        self.email_account_repo._find_by_column_values.return_value = []

        # Call the method under test
        await self.service.sync_email_account_health(organization_id)

        # Verify repository call to find email accounts
        self.email_account_repo._find_by_column_values.assert_called_once()

    async def test_calculate_warmup_health_score_with_no_campaign(self) -> None:
        # Setup test data
        email_account_id = uuid4()
        organization_id = uuid4()

        # Mock the warmup campaign response to return None
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.return_value = None

        # Call the method under test
        result = await self.service.calculate_warmup_health_score(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

        # Verify the result
        assert result is None
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.assert_called_once_with(
            email_account_id, organization_id
        )

    async def test_calculate_warmup_health_score_no_external_id(self) -> None:
        # Setup test data
        email_account_id = uuid4()
        organization_id = uuid4()
        current_time = zoned_utc_now()
        user_id = uuid4()

        # Create a warmup campaign without external_id
        campaign = EmailAccountWarmUpCampaign(
            id=uuid4(),
            domain_id=uuid4(),
            email_per_day=10,
            response_rate=30,
            email_account_id=email_account_id,
            organization_id=organization_id,
            external_id=None,  # No external ID
            requested_by_user_id=user_id,
            requested_at=current_time,
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            created_at=current_time,
            created_by_user_id=user_id,
        )

        # Mock the warmup campaign response
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.return_value = campaign

        # Call the method under test
        result = await self.service.calculate_warmup_health_score(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

        # Verify the result
        assert result is None
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.assert_called_once_with(
            email_account_id, organization_id
        )

    async def test_calculate_warmup_health_score_with_metrics(self) -> None:
        # Setup test data
        email_account_id = uuid4()
        organization_id = uuid4()
        external_id = "12345"
        current_time = zoned_utc_now()
        user_id = uuid4()

        # Create a warmup campaign with external_id
        campaign = EmailAccountWarmUpCampaign(
            id=uuid4(),
            domain_id=uuid4(),
            email_per_day=10,
            response_rate=30,
            email_account_id=email_account_id,
            organization_id=organization_id,
            external_id=external_id,
            requested_by_user_id=user_id,
            requested_at=current_time,
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            created_at=current_time,
            created_by_user_id=user_id,
        )

        # Mock the warmup campaign response
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.return_value = campaign

        # Create mock metrics data for the past 14 days
        today = zoned_utc_now().date()
        metrics_data = {}

        # Variables to track expected calculation
        total_emails_sent = 0
        total_emails_in_inbox = 0

        # Generate metrics for each day in the window
        for day_offset in range(EmailAccountHealthScoreParams.WINDOW_SIZE_DAYS):
            current_date = today - timedelta(days=day_offset)

            # Day 0 (today) - 10 sent, 8 in inbox (80%)
            # Day 1 - 10 sent, 7 in inbox (70%)
            # Day 2 - 10 sent, 6 in inbox (60%)
            # And so on...
            emails_sent = 10
            inbox_placement = max(
                1, 10 - day_offset
            )  # Decreasing inbox placement over time

            # Track totals for expected calculation
            total_emails_sent += emails_sent
            total_emails_in_inbox += inbox_placement

            metrics_data[current_date] = DailyMetrics.model_construct(
                event_date=current_date,
                warm_up=WarmUp.model_construct(
                    total=emails_sent,
                    landed_in=LandedIn.model_construct(
                        inbox=inbox_placement,
                        others=emails_sent - inbox_placement,
                        failed_to_read=0,
                        not_found=0,
                    ),
                ),
                replies=Replies.model_construct(total=0, landed_in=None),
            )

        # Create mock metrics response
        metrics_response = MailiveryMetricsResponse(
            code="200",
            message="Success",
            status=200,
            success=True,
            data={
                date_obj.isoformat(): metrics_data[date_obj].model_dump(by_alias=True)
                for date_obj in metrics_data
            },
        )

        # Mock the get_metrics call
        self.mailivery_client_mock.get_metrics.return_value = metrics_response

        # Call the method under test
        result = await self.service.calculate_warmup_health_score(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

        # Calculate expected result based on the service logic:
        # Inbox rate: (total_emails_in_inbox / total_emails_sent) * 100
        expected_inbox_rate = (total_emails_in_inbox / total_emails_sent) * 100
        expected_score = round(expected_inbox_rate)

        # Verify the result is a tuple
        assert isinstance(result, tuple)
        score, metrics = result
        assert score == expected_score
        assert isinstance(metrics, list)
        assert len(metrics) == EmailAccountHealthScoreParams.WINDOW_SIZE_DAYS
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.assert_called_once_with(
            email_account_id, organization_id
        )
        self.mailivery_client_mock.get_metrics.assert_called_once_with(external_id)

    async def test_calculate_warmup_health_score_with_high_placement(self) -> None:
        # Setup test data
        email_account_id = uuid4()
        organization_id = uuid4()
        external_id = "12345"
        current_time = zoned_utc_now()
        user_id = uuid4()

        # Create a warmup campaign with external_id
        campaign = EmailAccountWarmUpCampaign(
            id=uuid4(),
            domain_id=uuid4(),
            email_per_day=10,
            response_rate=30,
            email_account_id=email_account_id,
            organization_id=organization_id,
            external_id=external_id,
            requested_by_user_id=user_id,
            requested_at=current_time,
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            created_at=current_time,
            created_by_user_id=user_id,
        )

        # Mock the warmup campaign response
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.return_value = campaign

        # Create mock metrics data for the past 14 days
        today = zoned_utc_now().date()
        metrics_data = {}

        # Variables to track expected calculation
        total_sent = 0
        total_in_inbox = 0

        # Generate metrics with high inbox placement rate (90%+)
        for day_offset in range(EmailAccountHealthScoreParams.WINDOW_SIZE_DAYS):
            current_date = today - timedelta(days=day_offset)

            emails_sent = 10
            inbox_placement = 9  # 90% placement

            # Track totals for expected calculation
            total_sent += emails_sent
            total_in_inbox += inbox_placement

            metrics_data[current_date] = DailyMetrics.model_construct(
                event_date=current_date,
                warm_up=WarmUp.model_construct(
                    total=emails_sent,
                    landed_in=LandedIn.model_construct(
                        inbox=inbox_placement,
                        others=emails_sent - inbox_placement,
                        failed_to_read=0,
                        not_found=0,
                    ),
                ),
                replies=Replies.model_construct(total=0, landed_in=None),
            )

        # Create mock metrics response
        metrics_response = MailiveryMetricsResponse(
            code="200",
            message="Success",
            status=200,
            success=True,
            data={
                date_obj.isoformat(): metrics_data[date_obj].model_dump(by_alias=True)
                for date_obj in metrics_data
            },
        )

        # Mock the get_metrics call
        self.mailivery_client_mock.get_metrics.return_value = metrics_response

        # Call the method under test
        result = await self.service.calculate_warmup_health_score(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

        # Calculate expected score based on inbox percentage rate
        expected_inbox_rate = (total_in_inbox / total_sent) * 100
        expected_score = round(expected_inbox_rate)

        # Verify the result is a tuple
        assert isinstance(result, tuple)
        score, metrics = result
        assert score == expected_score
        assert isinstance(metrics, list)
        assert len(metrics) == EmailAccountHealthScoreParams.WINDOW_SIZE_DAYS

    async def test_calculate_warmup_health_score_simple(self) -> None:
        """Test with a very simple data set to debug the calculation."""
        # Setup test data
        email_account_id = uuid4()
        organization_id = uuid4()
        external_id = "12345"
        current_time = zoned_utc_now()
        user_id = uuid4()

        # Create a warmup campaign with external_id
        campaign = EmailAccountWarmUpCampaign(
            id=uuid4(),
            domain_id=uuid4(),
            email_per_day=10,
            response_rate=30,
            email_account_id=email_account_id,
            organization_id=organization_id,
            external_id=external_id,
            requested_by_user_id=user_id,
            requested_at=current_time,
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            created_at=current_time,
            created_by_user_id=user_id,
        )

        # Mock the warmup campaign response
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.return_value = campaign

        # Create metrics data with exactly ONE day for simplicity
        today = zoned_utc_now().date()
        metrics_data = {}

        # Just create one day with 10 sent emails and 5 in inbox (50% placement)
        total_sent = 10
        total_in_inbox = 5

        metrics_data[today] = DailyMetrics.model_construct(
            event_date=today,
            warm_up=WarmUp.model_construct(
                total=total_sent,
                landed_in=LandedIn.model_construct(
                    inbox=total_in_inbox,
                    others=total_sent - total_in_inbox,
                    failed_to_read=0,
                    not_found=0,
                ),
            ),
            replies=Replies.model_construct(total=0, landed_in=None),
        )

        # Create mock metrics response
        metrics_response = MailiveryMetricsResponse(
            code="200",
            message="Success",
            status=200,
            success=True,
            data={
                date_obj.isoformat(): metrics_data[date_obj].model_dump(by_alias=True)
                for date_obj in metrics_data
            },
        )

        # Mock the get_metrics call
        self.mailivery_client_mock.get_metrics.return_value = metrics_response

        # Call the method under test
        result = await self.service.calculate_warmup_health_score(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

        # Calculate expected score: (5/10) * 100 = 50%
        expected_inbox_rate = (total_in_inbox / total_sent) * 100
        expected_score = round(expected_inbox_rate)

        # Verify the result is a tuple
        assert isinstance(result, tuple)
        score, metrics = result
        assert score == expected_score
        assert isinstance(metrics, list)
        assert len(metrics) == 1  # Only one day of data in this test

    async def test_calculate_warmup_health_score_api_exception(self) -> None:
        # Setup test data
        email_account_id = uuid4()
        organization_id = uuid4()
        external_id = "12345"
        current_time = zoned_utc_now()
        user_id = uuid4()

        # Create a warmup campaign with external_id
        campaign = EmailAccountWarmUpCampaign(
            id=uuid4(),
            domain_id=uuid4(),
            email_per_day=10,
            response_rate=30,
            email_account_id=email_account_id,
            organization_id=organization_id,
            external_id=external_id,
            requested_by_user_id=user_id,
            requested_at=current_time,
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            created_at=current_time,
            created_by_user_id=user_id,
        )

        # Mock the warmup campaign response
        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.return_value = campaign

        # Mock the get_metrics call to raise an exception
        self.mailivery_client_mock.get_metrics.side_effect = Exception("API Error")

        # Call the method under test
        result = await self.service.calculate_warmup_health_score(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

        # Should return None on error (not 0 anymore)
        assert result is None
        self.mailivery_client_mock.get_metrics.assert_called_once_with(external_id)

    async def test_email_account_health_score_data(self) -> None:
        """Test the EmailAccountHealthScoreData class directly."""
        # Create test data
        today = zoned_utc_now().date()

        # Create metrics data
        mailivery_metrics = [
            DailyMetrics.model_construct(
                event_date=today,
                warm_up=WarmUp.model_construct(
                    total=10,
                    landed_in=LandedIn.model_construct(
                        inbox=8,
                        others=2,
                        failed_to_read=0,
                        not_found=0,
                    ),
                ),
                replies=Replies.model_construct(total=0, landed_in=None),
            )
        ]

        # Create email event metrics
        email_event_metrics = {
            EmailEventType.BOUNCE_DETECTED: 1,
            EmailEventType.OPENED: 6,
            EmailEventType.LINK_CLICKED: 4,
            EmailEventType.REPLIED: 3,
            EmailEventType.UNSUBSCRIBED: 0,
            EmailEventType.SEND_ATTEMPTED: 12,
        }

        # Calculate the expected score first
        warmup_score = 80
        deliverability_score = 70
        warmup_weight = 30  # 30%
        deliverability_weight = 70  # 70%

        expected_score = (deliverability_score * deliverability_weight / 100) + (
            warmup_score * warmup_weight / 100
        )
        rounded_score = round(expected_score)

        # Create health score data with the calculated score
        health_score_data = EmailAccountHealthScoreData(
            composite_health_score=rounded_score,
            warmup_health_score=warmup_score,
            warmup_health_score_weight=warmup_weight,
            deliverability_health_score=deliverability_score,
            deliverability_health_score_weight=deliverability_weight,
            mailivery_metrics=mailivery_metrics,
            email_event_metrics=email_event_metrics,
        )

        # Verify the data
        assert health_score_data.composite_health_score == rounded_score
        assert health_score_data.warmup_health_score == warmup_score
        assert health_score_data.warmup_health_score_weight == warmup_weight
        assert health_score_data.deliverability_health_score == deliverability_score
        assert (
            health_score_data.deliverability_health_score_weight
            == deliverability_weight
        )
        assert health_score_data.mailivery_metrics == mailivery_metrics
        assert health_score_data.email_event_metrics == email_event_metrics

        # Verify the calculation matches the expected formula
        # composite_health_score = (deliverability_health_score * deliverability_health_score_weight / 100) +
        #                          (warmup_health_score * warmup_health_score_weight / 100)
        calculated_score = (deliverability_score * deliverability_weight / 100) + (
            warmup_score * warmup_weight / 100
        )
        assert health_score_data.composite_health_score == round(calculated_score)

    async def test_calculate_health_score_no_emails_sent(
        self, test_data: dict[str, Any]
    ) -> None:
        """Test health score calculation when no emails have been sent (emails_sent == 0)."""
        # Setup test data
        organization_id = test_data["organization_id"]
        email_account_id = test_data["email_account_id"]
        created_time = zoned_utc_now() - timedelta(days=10)  # More than 3 days old

        # Create test email account
        email_account = EmailAccount(
            id=email_account_id,
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            organization_id=organization_id,
            created_at=created_time,
            owner_user_id=uuid4(),
            created_by_user_id=uuid4(),
            type=EmailAccountType.REGULAR,
            active=True,
            is_default=False,
            seconds_delay_between_emails=30,
        )

        # Mock email account repository
        self.email_account_repo.find_by_tenanted_primary_key.return_value = (
            email_account
        )

        # Setup mock for email event counts with NO emails sent
        event_counts = {
            EmailEventType.BOUNCE_DETECTED: 0,
            EmailEventType.OPENED: 0,
            EmailEventType.LINK_CLICKED: 0,
            EmailEventType.REPLIED: 0,
            EmailEventType.UNSUBSCRIBED: 0,
            EmailEventType.SEND_ATTEMPTED: 0,  # No emails sent
        }
        self.email_event_repo.get_email_event_count_by_types_and_email_account_id.return_value = event_counts

        # Setup warmup campaign
        warmup_campaign = EmailAccountWarmUpCampaign(
            id=uuid4(),
            domain_id=uuid4(),
            email_per_day=10,
            response_rate=30,
            email_account_id=email_account_id,
            organization_id=organization_id,
            external_id="12345",
            requested_by_user_id=uuid4(),
            requested_at=created_time,
            status=MailboxWarmUpStatus.IN_PROGRESS,
            rampup_speed=MailboxWarmUpSpeed.NORMAL,
            created_at=created_time,
            created_by_user_id=uuid4(),
        )

        self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id.return_value = warmup_campaign

        # Mock the warmup health score calculation
        warmup_health_score = 85
        today = zoned_utc_now().date()
        mock_mailivery_metrics = [
            DailyMetrics.model_construct(
                event_date=today,
                warm_up=WarmUp.model_construct(
                    total=10,
                    landed_in=LandedIn.model_construct(
                        inbox=8,
                        others=2,
                        failed_to_read=0,
                        not_found=0,
                    ),
                ),
                replies=Replies.model_construct(total=0, landed_in=None),
            )
        ]
        with patch.object(
            self.service,
            "calculate_warmup_health_score",
            AsyncMock(return_value=(warmup_health_score, mock_mailivery_metrics)),
        ):
            # Call the method under test
            result = await self.service.calculate_health_score(
                email_account_id=email_account_id,
                organization_id=organization_id,
            )

            # Verify the result is an EmailAccountHealthScoreData object
            assert isinstance(result, EmailAccountHealthScoreData)

            # When emails_sent == 0, the final score should be 100% warmup score
            assert result.composite_health_score == warmup_health_score

            # Verify the weights are correct for no emails sent scenario
            assert result.warmup_health_score == warmup_health_score
            assert result.warmup_health_score_weight == 100  # 100% warmup weight
            assert result.deliverability_health_score == 0  # No deliverability data
            assert (
                result.deliverability_health_score_weight == 0
            )  # 0% deliverability weight
            assert result.mailivery_metrics == mock_mailivery_metrics
            assert result.email_event_metrics == event_counts
