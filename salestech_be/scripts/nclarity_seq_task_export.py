import asyncio
import csv
from pathlib import Path
from uuid import UUID

from sqlalchemy import text

from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.core.base import DBModel
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


class ExportRow(DBModel):
    task_name: str
    task_description: str
    task_status: str
    sequence_name: str
    contact_name: str
    disposition: str | None = None


async def get_db_engine() -> DatabaseEngine:
    engine = DatabaseEngine(
        str(settings.db_url),
        echo=settings.db_echo,
        pool_size=settings.db_pool_size,
        max_overflow=settings.db_max_overflow,
    )
    if settings.db_conn_prewarm:
        await engine.prewarm_db_connection()

    return engine


async def db_query(organization_id: UUID) -> list[ExportRow]:
    engine = await get_db_engine()

    stmt = text(
        """
        SELECT t.title as task_name,
               t.note as task_description,
               t.status as task_status,
               s.name as sequence_name,
               c.display_name as contact_name,
               t.disposition as disposition
        FROM task t
        JOIN task_reference tr ON tr.task_id = t.id AND tr.reference_id_type = 'SEQUENCE_ID' AND tr.deleted_at IS NULL
        JOIN sequence_v2 s ON s.id = tr.reference_id::uuid
        LEFT JOIN task_reference trc ON trc.task_id = t.id AND trc.reference_id_type = 'CONTACT_ID' AND trc.deleted_at IS NULL
        LEFT JOIN contact c ON c.id = trc.reference_id::uuid
        WHERE t.organization_id = :organization_id
        AND t.archived_at IS NULL
        GROUP BY s.name, t.title, t.note, t.status, c.display_name, t.disposition
        """
    ).bindparams(organization_id=organization_id)

    rows = await engine.all(stmt)
    return await ExportRow.bulk_from_rows(rows=rows)


def export_to_csv(results: list[ExportRow], filename: str) -> None:
    """Export results to a CSV file"""
    filepath = Path(filename)

    with open(filepath, "w", newline="") as csvfile:
        fieldnames = [
            "task_name",
            "task_description",
            "task_status",
            "sequence_name",
            "contact_name",
            "disposition",
        ]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        writer.writeheader()
        for row in results:
            writer.writerow(row.model_dump())

    logger.info(f"Exported {len(results)} tasks to {filepath}")


async def main(organization_id: UUID, output_file: str = "task_export_2.csv") -> None:
    results = await db_query(organization_id)

    # Log results
    for row in results:
        logger.info(
            f"{row.task_name}, {row.sequence_name}, {row.contact_name}, {row.task_status}"
        )
    logger.info(f"Found {len(results)} tasks")

    # Export to CSV
    export_to_csv(results, output_file)

    logger.info("Done")


if __name__ == "__main__":
    # Replace with actual organization ID when running
    org_id = UUID("e7397cd0-89e9-4015-b165-2c556e45c9ad")
    asyncio.run(main(org_id))
