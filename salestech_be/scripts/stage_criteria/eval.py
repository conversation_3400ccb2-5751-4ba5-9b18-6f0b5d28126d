# mypy: disable-error-code="explicit-any"
import asyncio
import csv
import json
import os
from datetime import UTC, datetime, timedelta
from typing import Any, Literal, override
from uuid import UUID

from pydantic import BaseModel
from pydantic.json_schema import (
    DEFAULT_REF_TEMPLATE,
    GenerateJsonSchema,
    JsonSchemaMode,
)
from sqlalchemy import text

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    anthropic_completion,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.core.ai.common.llm_types import VertexModelTypes
from salestech_be.core.metadata.types import ContactPipelineRole
from salestech_be.core.opportunity_stage_criteria.standard_criteria.competition import (
    CompetitionContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_criteria import (
    DecisionCriteriaContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.decision_process import (
    DecisionProcessContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.identified_pain import (
    IdentifiedPainPointContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.metric import (
    MetricContext,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.paper_process import (
    PaperProcessContext,
)
from salestech_be.core.stage_criteria.constants import (
    get_standard_sales_action_requirement,
)
from salestech_be.core.transcript.types import TranscriptContainer
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.core.base import Column, DBModel, JsonColumn
from salestech_be.db.models.crm_ai_rec import CrmAIRecType
from salestech_be.integrations.s3.s3_bucket_manager import (
    S3BucketManager,
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class UpdateEvalRecord(DBModel):
    """
    Represents the result of the UpdateRecEvalRecord query.
    This model contains data from crm_property_ai_recs joined with meeting and transcript data.
    """

    organization_id: Column[UUID]
    ai_rec_id: Column[UUID]
    sobject_name: Column[str]
    sobject_field_path: Column[str]
    created_at: Column[ZoneRequiredDateTime]
    source_id: Column[UUID]
    source_type: Column[str]
    original_value: JsonColumn[dict[str, Any] | None]
    rec_type: Column[CrmAIRecType]
    pipeline_id: Column[UUID | None]
    citation_metadata: JsonColumn[list[dict[str, Any]]]
    rec_value: JsonColumn[dict[str, Any]]
    record_id: Column[UUID]
    transcript_s3_processed_key: Column[str | None]


class CreateEvalRecord(DBModel):
    """
    Represents the result of the CreateRecEvalRecord query.
    This model contains data from crm_object_ai_recs joined with meeting and transcript data.
    """

    organization_id: Column[UUID]
    ai_rec_id: Column[UUID]
    sobject_name: Column[str]
    created_at: Column[ZoneRequiredDateTime]
    source_id: Column[UUID]
    source_type: Column[str]
    citation_metadata: JsonColumn[list[dict[str, Any]]]
    pipeline_id: Column[UUID]
    rec_value: JsonColumn[dict[str, Any]]
    transcript_s3_processed_key: Column[str | None]


def get_eval_result_field_descriptions(  # noqa: C901
    sobject_name: str | None = None,
) -> dict[str, str]:
    """Generate field descriptions for EvalResult based on the sobject_name."""
    # Default descriptions for when no sobject_name is provided
    if not sobject_name:
        return {
            "is_the_recommendation_valid": "Whether the recommendation is valid and accurate.",
            "is_it_accurate": "Is it accurate?",
            "is_it_correct": "Is it correct?",
            "should_we_capture_this": "Should we capture this?",
            "explanation": "Explanation of the recommendation's validity.",
            "evidence": "Evidence from the transcript.",
            "rating": "Rating on a scale of 1-5.",
            "improvements": "Suggestions for improvement.",
        }

    # Try to get a valid StdObjectIdentifiers
    try:
        std_object = StdObjectIdentifiers(sobject_name)
    except ValueError:
        # If sobject_name is not a valid StdObjectIdentifiers, use default descriptions
        return get_eval_result_field_descriptions(None)

    # Base descriptions that will be customized
    descriptions = {
        "is_the_recommendation_valid": "Whether the recommendation is valid and accurate.",
        "explanation": "Explanation of the recommendation's validity.",
        "evidence": "Evidence from the transcript.",
        "rating": "Rating on a scale of 1-5.",
        "improvements": "Suggestions for improvement.",
    }

    # Customize descriptions based on sobject_name
    match std_object:
        case StdObjectIdentifiers.metric | StdObjectIdentifiers.metric_item:
            descriptions["is_it_accurate"] = (
                "Is the metric accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what a metric is?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this as a metric?"
            )
        case (
            StdObjectIdentifiers.decision_criteria
            | StdObjectIdentifiers.decision_criteria_item
        ):
            descriptions["is_it_accurate"] = (
                "Is the decision criteria accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what a decision criteria is?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this as a decision criteria?"
            )
        case (
            StdObjectIdentifiers.decision_process
            | StdObjectIdentifiers.decision_process_item
        ):
            descriptions["is_it_accurate"] = (
                "Is the decision process accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what a decision process is?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this as a decision process?"
            )
        case (
            StdObjectIdentifiers.paper_process | StdObjectIdentifiers.paper_process_item
        ):
            descriptions["is_it_accurate"] = (
                "Is the paper process accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what a paper process is?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this as a paper process?"
            )
        case (
            StdObjectIdentifiers.identified_pain
            | StdObjectIdentifiers.identified_pain_item
        ):
            descriptions["is_it_accurate"] = (
                "Is the identified pain point accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what an identified pain point is?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this as an identified pain point?"
            )
        case StdObjectIdentifiers.competition | StdObjectIdentifiers.competitor:
            descriptions["is_it_accurate"] = (
                "Is the competition information accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what competition information is?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this as competition information?"
            )
        case StdObjectIdentifiers.contact_pipeline_role:
            descriptions["is_it_accurate"] = (
                "Is the sales role accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what a sales role is?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this as a sales role?"
            )
        case StdObjectIdentifiers.meeting | StdObjectIdentifiers.global_message:
            descriptions["is_it_accurate"] = (
                "Is the meeting information accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what meeting information should be?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this meeting information?"
            )
        case _:
            # For any other object types, use generic descriptions
            descriptions["is_it_accurate"] = (
                "Is it accurately identified from the conversation?"
            )
            descriptions["is_it_correct"] = (
                "Is it correct, correct as in does it honor the definition of what it should be?"
            )
            descriptions["should_we_capture_this"] = (
                "Should we capture this information?"
            )

    return descriptions


class EvalResult(BaseModel):
    """Model for evaluation results that adapts field descriptions based on sobject_name."""

    is_the_recommendation_valid: bool
    is_it_accurate: bool
    is_it_correct: bool
    is_the_rec_type_correct: bool
    are_citations_accurate: bool
    should_we_capture_this: bool
    explanation: str
    evidence: list[str]
    rating: int
    improvements: list[str]

    @override
    @classmethod
    def model_json_schema(
        cls,
        by_alias: bool = True,
        ref_template: str = DEFAULT_REF_TEMPLATE,
        schema_generator: type[GenerateJsonSchema] = GenerateJsonSchema,
        mode: JsonSchemaMode = "validation",
        **kwargs: Any,
    ) -> dict[str, Any]:
        """Override model_json_schema to customize field descriptions based on sobject_name.

        Args:
            by_alias: Whether to use attribute aliases or not.
            ref_template: The reference template.
            schema_generator: To override the logic used to generate the JSON schema
            mode: The mode in which to generate the schema.
            **kwargs: Additional keyword arguments, including sobject_name if provided.

        Returns:
            The JSON schema for the model with customized field descriptions.
        """
        # Extract sobject_name from kwargs if provided
        sobject_name = kwargs.pop("sobject_name", None)

        # Call the parent method to get the base schema
        schema = super().model_json_schema(
            by_alias=by_alias,
            ref_template=ref_template,
            schema_generator=schema_generator,
            mode=mode,
        )

        # Get descriptions based on sobject_name
        descriptions = get_eval_result_field_descriptions(sobject_name)

        # Update schema properties with descriptions
        for field_name, description in descriptions.items():
            if field_name in schema.get("properties", {}):
                schema["properties"][field_name]["description"] = description

        return schema


def sobject_name_to_context_text(sobject_name: str) -> dict[str, Any]:  # noqa: C901, PLR0911
    if sobject_name not in StdObjectIdentifiers:
        raise ValueError(f"Invalid sobject_name: {sobject_name}")
    match StdObjectIdentifiers(sobject_name):
        case StdObjectIdentifiers.metric | StdObjectIdentifiers.metric_item:
            return MetricContext.model_json_schema()
        case (
            StdObjectIdentifiers.decision_criteria
            | StdObjectIdentifiers.decision_criteria_item
        ):
            return DecisionCriteriaContext.model_json_schema()
        case (
            StdObjectIdentifiers.decision_process
            | StdObjectIdentifiers.decision_process_item
        ):
            return DecisionProcessContext.model_json_schema()
        case (
            StdObjectIdentifiers.paper_process | StdObjectIdentifiers.paper_process_item
        ):
            return PaperProcessContext.model_json_schema()
        case (
            StdObjectIdentifiers.identified_pain
            | StdObjectIdentifiers.identified_pain_item
        ):
            return IdentifiedPainPointContext.model_json_schema()
        case StdObjectIdentifiers.competition | StdObjectIdentifiers.competitor:
            return CompetitionContext.model_json_schema()
        case StdObjectIdentifiers.contact_pipeline_role:
            return ContactPipelineRole.model_json_schema()
        case StdObjectIdentifiers.meeting | StdObjectIdentifiers.global_message:
            descriptions = {}
            for sales_action_type in StandardSalesActionType:
                descriptions[sales_action_type.value] = (
                    get_standard_sales_action_requirement(sales_action_type).description
                )
            return descriptions
        case _:
            raise ValueError(f"Invalid sobject_name: {sobject_name}")


class EvalRepo(GenericRepository):
    async def list_create_eval_records(
        self, organization_id: UUID, rec_created_after: datetime, limit: int = 10
    ) -> list[CreateEvalRecord]:
        stmt = text(
            """
            with base as (select o.id                                          as organization_id,
                                 r.id,
                                 r.sobject_name,
                                 r.created_at,
                                 (r.parent_record_ids ->> 'pipeline_id')::uuid as pipeline_id,
                                 c.source_id,
                                 c.source_type,
                                 jsonb_agg(c.metadata order by c.created_at)   as citation_metadata
                          from crm_object_ai_recs r
                                   left join citation c on c.id = any (r.rec_citation_ids)
                                   join organization o on o.id = r.organization_id
                          where r.created_at > :rec_created_after
                          and r.organization_id = :organization_id
                          group by o.id, r.id, c.source_id, c.source_type),
                 recs
                     as (select distinct on (b.sobject_name, b.source_id, b.source_type) b.*
                         from base b
                         order by b.sobject_name, b.source_id, b.source_type,
                                  b.created_at desc)
            select recs.organization_id   as organization_id,
                   recs.id                as ai_rec_id,
                   recs.sobject_name      as sobject_name,
                   recs.created_at        as created_at,
                   recs.source_id         as source_id,
                   recs.source_type       as source_type,
                   recs.citation_metadata as citation_metadata,
                   recs.pipeline_id       as pipeline_id,
                   r.rec_value            as rec_value,
                   t.s3_processed_key     as transcript_s3_processed_key
            from recs
                     join crm_object_ai_recs r on recs.id = r.id
                     join meeting m
                          on recs.source_id = m.id
                              and recs.source_type = 'MEETING'
                     join
                 meeting_bot mb on m.id = mb.meeting_id and mb.deleted_at is null
                     join
                 transcript t on t.reference_id = mb.id::text
                     and t.reference_id_type = 'meeting_bot'
                     and t.deleted_at is null
            where m.deleted_at is null
              and t.s3_processed_key is not null
              and m.organization_id = mb.organization_id
              and mb.organization_id = t.organization_id
              and m.pipeline_id is not null
            order by recs.created_at desc
            limit :limit;
            """
        ).bindparams(
            rec_created_after=rec_created_after,
            limit=limit,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await CreateEvalRecord.bulk_from_rows(rows=rows)

    async def list_update_eval_records(
        self, organization_id: UUID, rec_created_after: datetime, limit: int = 10
    ) -> list[UpdateEvalRecord]:
        stmt = text(
            """
            with base as (select o.id                                        as organization_id,
                                 r.id,
                                 r.sobject_name,
                                 r.sobject_field_path,
                                 r.created_at,
                                 r.rec_type,
                                 c.source_id,
                                 c.source_type,
                                 p.pipeline_id,
                                 p.value as original_value,
                                 jsonb_agg(c.metadata order by c.created_at) as citation_metadata
                          from crm_property_ai_recs r
                                   left join citation c on c.id = any (r.rec_citation_ids)
                                   left join pipeline_qualification_property p on p.id = r.record_id
                                   join organization o on o.id = r.organization_id
                          where r.created_at > :rec_created_after
                          and r.organization_id = :organization_id
                          group by o.id, r.id, c.source_id, c.source_type, p.pipeline_id, p.value),
                 recs
                     as (select distinct on (b.sobject_name, b.source_id, b.source_type) b.*
                         from base b
                         order by b.sobject_name, b.source_id, b.source_type,
                                  b.created_at desc)
            select recs.organization_id   as organization_id,
                   recs.id                as ai_rec_id,
                   recs.sobject_name      as sobject_name,
                   recs.sobject_field_path as sobject_field_path,
                   recs.created_at        as created_at,
                   recs.source_id         as source_id,
                   recs.source_type       as source_type,
                   recs.original_value    as original_value,
                   recs.rec_type          as rec_type,
                   recs.citation_metadata as citation_metadata,
                   recs.pipeline_id       as pipeline_id,
                   r.rec_value            as rec_value,
                   r.record_id            as record_id,
                   t.s3_processed_key     as transcript_s3_processed_key
            from recs
                     join crm_property_ai_recs r on recs.id = r.id
                     join meeting m
                          on recs.source_id = m.id
                              and recs.source_type = 'MEETING'
                     join
                 meeting_bot mb on m.id = mb.meeting_id and mb.deleted_at is null
                     join
                 transcript t on t.reference_id = mb.id::text
                     and t.reference_id_type = 'meeting_bot'
                     and t.deleted_at is null
            where m.deleted_at is null
              and t.s3_processed_key is not null
              and m.organization_id = mb.organization_id
              and mb.organization_id = t.organization_id
              and m.pipeline_id is not null
            order by recs.created_at desc
            limit :limit;
            """
        ).bindparams(
            rec_created_after=rec_created_after,
            limit=limit,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await UpdateEvalRecord.bulk_from_rows(rows=rows)

    async def list_users_in_organization(self, organization_id: UUID) -> list[str]:
        stmt = text(
            """
            select u.first_name
            from user_organization_association uoa
                    join public."user" u on u.id = uoa.user_id
            where uoa.status = 'ACTIVE'
              and uoa.organization_id = :organization_id
              and u.first_name is not null;
            """
        ).bindparams(organization_id=organization_id)
        rows = await self.engine.all(stmt)
        return [str(row.first_name) for row in rows]


async def analyze_create_eval_records(
    repo: EvalRepo,
    s3_manager: S3BucketManager,
    organization_id: UUID,
    seller_names: list[str],
    rec_created_after: datetime,
    limit: int = 10,
    output_format: Literal["console", "csv"] = "console",
    output_path: str | None = None,
) -> None:
    """
    Analyze the create eval records and print the result out.

    This function:
    1. Fetches create eval records from the database
    2. For each record, retrieves the associated transcript from S3
    3. Processes the transcript into a structured format
    4. Sends the transcript and recommendation details to an LLM for analysis
    5. Logs and displays the analysis results
    """

    # Fetch create eval records
    records = await repo.list_create_eval_records(
        organization_id=organization_id,
        rec_created_after=rec_created_after,
        limit=limit,
    )

    if not records:
        logger.info("No create eval records found")
        return

    # List to collect results for CSV output if needed
    csv_results = []
    batched_results = []
    # Process records in batches of 5 concurrently
    batch_size = 10
    for i in range(0, len(records), batch_size):
        batch = records[i : i + batch_size]
        logger.info(
            f"Processing batch of {len(batch)} records (batch {i // batch_size + 1} of {(len(records) + batch_size - 1) // batch_size})"
        )

        # Process batch concurrently
        tasks = [
            llm_analyze_create_record(
                record=record,
                s3_manager=s3_manager,
                seller_names=seller_names,
            )
            for record in batch
        ]

        # Wait for all tasks in the batch to complete
        batched_results.extend(await asyncio.gather(*tasks, return_exceptions=False))

    # Process each record
    for eval_result, record in batched_results:
        logger.info(
            f"Processing eval_result record: {record.ai_rec_id} for {record.sobject_name}"
        )

        # Skip if no transcript key
        if not eval_result:
            logger.warning(f"No eval_result for {record.ai_rec_id}")
            continue

        try:
            # Prepare CSV data
            csv_row = {
                "record_id": str(record.ai_rec_id),
                "object_type": record.sobject_name,
                "created_at": record.created_at.isoformat(),
                "source_id": str(record.source_id),
                "pipeline_id": str(record.pipeline_id),
                "source_type": record.source_type,
                "is_valid": eval_result.is_the_recommendation_valid,
                "is_accurate": eval_result.is_it_accurate,
                "is_correct": eval_result.is_it_correct,
                "are_citations_accurate": eval_result.are_citations_accurate,
                "should_capture": eval_result.should_we_capture_this,
                "rating": eval_result.rating,
                "actual_recommendation": json.dumps(record.rec_value),
                "citation_metadata": json.dumps(record.citation_metadata),
                "explanation": eval_result.explanation,
                "evidence": "; ".join(eval_result.evidence)
                if eval_result.evidence
                else "",
                "improvements": "; ".join(eval_result.improvements)
                if eval_result.improvements
                else "",
            }
            csv_results.append(csv_row)

            # If console output is requested, format and display
            if output_format == "console":
                # Format output for console with color and structure
                separator = "=" * 80
                header = f"ANALYSIS FOR RECORD: {record.ai_rec_id}\nOBJECT TYPE: {record.sobject_name}\nPIPELINE: {record.pipeline_id}"
                divider = "-" * 80

                # Create a structured output based on the EvalResult
                structured_output = [separator, header, divider]

                # Add validity assessment
                validity = (
                    "✅ VALID"
                    if eval_result.is_the_recommendation_valid
                    else "❌ INVALID"
                )
                structured_output.append(f"\n🔍 VALIDITY: {validity}")

                # Add accuracy assessment
                accuracy = (
                    "✅ ACCURATE" if eval_result.is_it_accurate else "❌ INACCURATE"
                )
                structured_output.append(f"\n🎯 ACCURACY: {accuracy}")

                # Add correctness assessment
                correctness = (
                    "✅ CORRECT" if eval_result.is_it_correct else "❌ INCORRECT"
                )
                structured_output.append(f"\n✓ CORRECTNESS: {correctness}")

                # Add capture assessment
                should_capture = (
                    "✅ SHOULD CAPTURE"
                    if eval_result.should_we_capture_this
                    else "❌ SHOULD NOT CAPTURE"
                )
                structured_output.append(f"\n📥 CAPTURE: {should_capture}")

                structured_output.append(
                    f"\nACTUAL RECOMMENDATION: {json.dumps(record.rec_value)}"
                )
                # Add explanation
                structured_output.append(
                    f"\n📊 EXPLANATION:\n{eval_result.explanation}"
                )

                # Add evidence
                if eval_result.evidence:
                    evidence_text = "\n".join(
                        [f"- {evidence}" for evidence in eval_result.evidence]
                    )
                    structured_output.append(f"\n🔍 EVIDENCE:\n{evidence_text}")

                # Add rating
                structured_output.append(f"\n⭐ RATING: {eval_result.rating}/5")

                # Add improvements
                if eval_result.improvements:
                    improvements_text = "\n".join(
                        [f"- {improvement}" for improvement in eval_result.improvements]
                    )
                    structured_output.append(f"\n💡 IMPROVEMENTS:\n{improvements_text}")

                structured_output.append(separator)

                # Join the structured output and log it
                formatted_output = "\n".join(structured_output)
                logger.info(formatted_output)

        except Exception as e:
            logger.error(f"Error analyzing record {record.ai_rec_id}: {e!r}")
            continue

    # If CSV output is requested, save results to file
    if output_format == "csv" and csv_results and output_path:
        save_eval_results_to_csv(csv_results, output_path)


async def llm_analyze_create_record(
    record: CreateEvalRecord,
    seller_names: list[str],
    s3_manager: S3BucketManager,
) -> tuple[EvalResult | None, CreateEvalRecord]:
    logger.info(f"Analyzing record: {record.ai_rec_id} for {record.sobject_name}")

    # Skip if no transcript key
    if not record.transcript_s3_processed_key:
        logger.warning(f"No transcript key for record {record.ai_rec_id}")
        return None, record

    # Fetch and parse transcript from S3
    transcript_json = s3_manager.read_object(record.transcript_s3_processed_key)
    transcript_container = TranscriptContainer.model_validate_json(transcript_json)

    # Prepare context for LLM
    transcript_text = transcript_container.transcript.compact()

    # Prepare recommendation context
    recommendation_context = {
        "recommendation_id": str(record.ai_rec_id),
        "object_type": record.sobject_name,
        "recommendation_value": record.rec_value,
        "citation_metadata": record.citation_metadata,
        "created_at": record.created_at.isoformat(),
        "source_type": record.source_type,
        "source_id": str(record.source_id),
        "pipeline_id": str(record.pipeline_id),
    }

    # Prepare prompt for LLM
    prompt = f"""
            You are an AI evaluation assistant. Your task is to analyze if a recommendation made to a seller's CRM system makes sense based on the meeting transcript.
            The recommendation is for the following CRM object type as defined in the schema:
            {json.dumps(sobject_name_to_context_text(record.sobject_name), indent=2)}


            RECOMMENDATION DETAILS:
            {json.dumps(recommendation_context, indent=2)}

            MEETING TRANSCRIPT:
            {transcript_text}

            EVALUATION TASK:
            1. Analyze if the recommendation makes sense based on the transcript content and the seller and potential buyer roles in the meeting.
            2. Check if for the reccomendation made, the citations used to support the recommendation are accurate based on the transcript content.
            3. Seller roles are: {", ".join(seller_names)}. Whereas the potential buyer roles are any speakers in the transcript that are not the seller.
            4. Provide specific evidence from the transcript that supports or contradicts the recommendation
            5. Rate the recommendation quality on a scale of 1-5 (where 5 is excellent)
            7. Suggest improvements if applicable
            6. Important: you should use the tool `evaluate_create_eval_record` to return the result.

            Provide your analysis in a structured format.
            """

    # Add retry mechanism for the entire LLM call and processing
    retry_count = 0
    max_retries = 1
    success = False

    while retry_count <= max_retries and not success:
        try:
            # Send to LLM for analysis
            response = await anthropic_completion(
                model=VertexModelTypes.CLAUDE_3_7_SONNET_20250219,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=settings.max_citation_output_tokens,
                metadata=LLMTraceMetadata(
                    trace_name="eval.analyze_create_eval_records"
                    + (".retry" if retry_count > 0 else ""),
                    custom_fields=ReeTraceMetadata(
                        organization_id=str(record.organization_id),
                    ),
                ),
                tools=[
                    {
                        "input_schema": EvalResult.model_json_schema(
                            sobject_name=record.sobject_name
                        ),
                        "name": "evaluate_create_eval_record",
                        "description": "A tool that evaluates the create eval record based on the given transcript, property context and the seller and potential buyer roles in the meeting.",
                    }
                ],
            )

            # Log the analysis result
            logger.info(f"Analysis for record {record.ai_rec_id}:")
            logger.info(f"Recommendation: {recommendation_context}")

            # Check if the response contains a tool use
            if not response.content or not isinstance(response.content, list):
                logger.warning("No content in response or content is not a list")
                raise ValueError("No content in response or content is not a list")

            # Find the tool use in the content
            tool_use_content = None
            for content_block in response.content:
                if hasattr(content_block, "type") and content_block.type == "tool_use":
                    tool_use_content = content_block
                    break

            if not tool_use_content:
                logger.warning("No tool use found in response content")
                raise ValueError("No tool use found in response content")

            # Parse the tool input as EvalResult
            eval_result = EvalResult.model_validate(tool_use_content.input)
            return eval_result, record
        except Exception as e:
            error_msg = str(e)
            if retry_count < max_retries:
                logger.warning(
                    f"Error processing record {record.ai_rec_id}, retrying: {error_msg}"
                )
                retry_count += 1
            else:
                logger.error(
                    f"Error processing record {record.ai_rec_id} after retry: {error_msg}"
                )
                return None, record
    return None, record


async def analyze_update_eval_records(  # noqa: PLR0915, C901
    repo: EvalRepo,
    s3_manager: S3BucketManager,
    organization_id: UUID,
    seller_names: list[str],
    rec_created_after: datetime,
    limit: int = 10,
    output_format: Literal["console", "csv"] = "console",
    output_path: str | None = None,
) -> None:
    """
    Analyze the update eval records and print the result out.

    This function:
    1. Fetches update eval records from the database
    2. For each record, retrieves the associated transcript from S3
    3. Processes the transcript into a structured format
    4. Sends the transcript and recommendation details to an LLM for analysis
    5. Logs and displays the analysis results
    """

    # Fetch update eval records
    records = await repo.list_update_eval_records(
        organization_id=organization_id,
        rec_created_after=rec_created_after,
        limit=limit,
    )

    if not records:
        logger.info("No update eval records found")
        return

    # List to collect results for CSV output if needed
    csv_results: list[dict[str, Any]] = []

    batched_results = []
    # Process records in batches of 5 concurrently
    batch_size = 10
    for i in range(0, len(records), batch_size):
        batch = records[i : i + batch_size]
        logger.info(
            f"Processing batch of {len(batch)} records (batch {i // batch_size + 1} of {(len(records) + batch_size - 1) // batch_size})"
        )

        # Process batch concurrently
        tasks = [
            llm_analyze_update_record(
                record=record,
                s3_manager=s3_manager,
                seller_names=seller_names,
            )
            for record in batch
        ]

        # Wait for all tasks in the batch to complete
        batched_results.extend(await asyncio.gather(*tasks, return_exceptions=False))

    # Process each record
    for eval_result, record in batched_results:
        logger.info(f"Analyzing record: {record.ai_rec_id} for {record.sobject_name}")

        # Skip if no transcript key
        if not record.transcript_s3_processed_key:
            logger.warning(f"No transcript key for record {record.ai_rec_id}")
            continue

        if not eval_result:
            logger.warning(f"No eval_result for {record.ai_rec_id}")
            continue

        try:
            csv_row = {
                "record_id": str(record.ai_rec_id),
                "object_type": record.sobject_name,
                "object_field_path": record.sobject_field_path,
                "created_at": record.created_at.isoformat(),
                "source_id": str(record.source_id),
                "source_type": record.source_type,
                "pipeline_id": str(record.pipeline_id),
                "rec_type": str(record.rec_type),
                "is_valid": eval_result.is_the_recommendation_valid,
                "is_accurate": eval_result.is_it_accurate,
                "is_correct": eval_result.is_it_correct,
                "is_rec_type_correct": eval_result.is_the_rec_type_correct,
                "are_citations_accurate": eval_result.are_citations_accurate,
                "should_capture": eval_result.should_we_capture_this,
                "rating": eval_result.rating,
                "actual_recommendation": json.dumps(record.rec_value),
                "citation_metadata": json.dumps(record.citation_metadata),
                "explanation": eval_result.explanation,
                "evidence": "; ".join(eval_result.evidence)
                if eval_result.evidence
                else "",
                "improvements": "; ".join(eval_result.improvements)
                if eval_result.improvements
                else "",
            }
            csv_results.append(csv_row)

            # If console output is requested, format and display
            if output_format == "console":
                # Format output for console with color and structure
                separator = "=" * 80
                header = f"ANALYSIS FOR RECORD: {record.ai_rec_id}\nOBJECT TYPE: {record.sobject_name}\nPIPELINE: {record.pipeline_id}"
                divider = "-" * 80

                # Create a structured output based on the EvalResult
                structured_output: list[str] = [separator, header, divider]

                # Add validity assessment
                validity = (
                    "✅ VALID"
                    if eval_result.is_the_recommendation_valid
                    else "❌ INVALID"
                )
                structured_output.append(f"\n🔍 VALIDITY: {validity}")

                # Add accuracy assessment
                accuracy = (
                    "✅ ACCURATE" if eval_result.is_it_accurate else "❌ INACCURATE"
                )
                structured_output.append(f"\n🎯 ACCURACY: {accuracy}")

                # Add correctness assessment
                correctness = (
                    "✅ CORRECT" if eval_result.is_it_correct else "❌ INCORRECT"
                )
                structured_output.append(f"\n✓ CORRECTNESS: {correctness}")

                # Add capture assessment
                should_capture = (
                    "✅ SHOULD CAPTURE"
                    if eval_result.should_we_capture_this
                    else "❌ SHOULD NOT CAPTURE"
                )
                structured_output.append(f"\n📥 CAPTURE: {should_capture}")

                structured_output.append(
                    f"\nACTUAL RECOMMENDATION: {json.dumps(record.rec_value)}"
                )

                # Add explanation
                structured_output.append(
                    f"\n📊 EXPLANATION:\n{eval_result.explanation}"
                )

                # Add evidence
                if eval_result.evidence:
                    evidence_text = "\n".join(
                        [f"- {evidence}" for evidence in eval_result.evidence]
                    )
                    structured_output.append(f"\n🔍 EVIDENCE:\n{evidence_text}")

                # Add rating
                structured_output.append(f"\n⭐ RATING: {eval_result.rating}/5")

                # Add improvements
                if eval_result.improvements:
                    improvements_text = "\n".join(
                        [f"- {improvement}" for improvement in eval_result.improvements]
                    )
                    structured_output.append(f"\n💡 IMPROVEMENTS:\n{improvements_text}")

                structured_output.append(separator)

                # Join the structured output and log it
                formatted_output = "\n".join(structured_output)
                logger.info(formatted_output)

        except Exception as e:
            logger.error(f"Error analyzing record {record.ai_rec_id}: {e!r}")
            continue

    # If CSV output is requested, save results to file
    if output_format == "csv" and csv_results and output_path:
        save_eval_results_to_csv(csv_results, output_path)


async def llm_analyze_update_record(
    record: UpdateEvalRecord,
    s3_manager: S3BucketManager,
    seller_names: list[str],
) -> tuple[EvalResult | None, UpdateEvalRecord]:
    # Fetch and parse transcript from S3
    if not record.transcript_s3_processed_key:
        logger.warning(f"No transcript key for record {record.ai_rec_id}")
        return None, record
    transcript_json = s3_manager.read_object(record.transcript_s3_processed_key)
    transcript_container = TranscriptContainer.model_validate_json(transcript_json)

    # Prepare context for LLM
    transcript_text = transcript_container.transcript.compact()

    # Prepare recommendation context
    recommendation_context = {
        "recommendation_id": str(record.ai_rec_id),
        "object_type": record.sobject_name,
        "record_id": str(record.record_id),
        "sobject_name": record.sobject_name,
        "sobject_field_path": record.sobject_field_path,
        "pipeline_id": str(record.pipeline_id),
        "original_value": record.original_value,
        "rec_value": record.rec_value,
        "rec_type": record.rec_type,
        "citation_metadata": record.citation_metadata,
        "created_at": record.created_at.isoformat(),
        "source_type": record.source_type,
        "source_id": str(record.source_id),
    }

    # Prepare prompt for LLM
    prompt = f"""
                You are an AI evaluation assistant. Your task is to analyze if a property update recommendation makes sense based on the meeting transcript.
                The recommendation is for the following CRM object type as defined in the schema:
                {json.dumps(sobject_name_to_context_text(record.sobject_name), indent=2)}

                RECOMMENDATION DETAILS:
                {json.dumps(recommendation_context, indent=2)}

                MEETING TRANSCRIPT:
                {transcript_text}

                EVALUATION TASK:
                1. Analyze if the property update recommendation makes sense based on the transcript content and the seller and potential buyer roles in the meeting.
                2. Check if the rec_type is correct based on the original value and the rec_value, the possible rec_types are: {", ".join(list(map(str, CrmAIRecType)))}. If it isn't, explain why as part of the explanation field.
                3. Check if for the reccomendation made, the citations used to support the recommendation are accurate based on the transcript content.
                4. Seller roles are: {", ".join(seller_names)}. Whereas the potential buyer roles are any speakers in the transcript that are not the seller.
                5. Provide specific evidence from the transcript that supports or contradicts the recommendation
                6. Rate the recommendation quality on a scale of 1-5 (where 5 is excellent)
                6. Suggest improvements if applicable
                7. Important: you should use the tool `evaluate_update_eval_record` to return the result.
                Provide your analysis in a structured format.
                """

    # Add retry mechanism for the entire LLM call and processing
    retry_count = 0
    max_retries = 1
    success = False

    while retry_count <= max_retries and not success:
        try:
            # Send to LLM for analysis
            response = await anthropic_completion(
                model=VertexModelTypes.CLAUDE_3_7_SONNET_20250219,
                max_tokens=settings.max_citation_output_tokens,
                messages=[{"role": "user", "content": prompt}],
                metadata=LLMTraceMetadata(
                    trace_name="eval.analyze_update_recommendation_records"
                    + (".retry" if retry_count > 0 else ""),
                    custom_fields=ReeTraceMetadata(
                        organization_id=str(record.organization_id),
                    ),
                ),
                tools=[
                    {
                        "input_schema": EvalResult.model_json_schema(
                            sobject_name=record.sobject_name
                        ),
                        "name": "evaluate_update_eval_record",
                        "description": "A tool that evaluates the update eval record based on the given transcript, property context and the seller and potential buyer roles in the meeting.",
                    }
                ],
            )

            # Log the analysis result
            logger.info(f"Analysis for record {record.ai_rec_id}:")

            # Check if the response contains a tool use
            if not response.content or not isinstance(response.content, list):
                logger.warning("No content in response or content is not a list")
                raise ValueError("No content in response or content is not a list")

            # Find the tool use in the content
            tool_use_content = None
            for content_block in response.content:
                if hasattr(content_block, "type") and content_block.type == "tool_use":
                    tool_use_content = content_block
                    break

            if not tool_use_content:
                logger.warning("No tool use found in response content")
                raise ValueError("No tool use found in response content")

            # Parse the tool input as EvalResult
            return EvalResult.model_validate(tool_use_content.input), record
        except Exception as e:
            error_msg = str(e)
            if retry_count < max_retries:
                logger.warning(
                    f"Error processing record {record.ai_rec_id}, retrying: {error_msg}"
                )
                retry_count += 1
            else:
                logger.error(
                    f"Error processing record {record.ai_rec_id} after retry: {error_msg}"
                )
                return None, record
    return None, record


def save_eval_results_to_csv(results: list[dict[str, Any]], output_path: str) -> None:
    """Save evaluation results to a CSV file.

    Args:
        results: List of evaluation result dictionaries
        output_path: Path to save the CSV file
    """
    if not results:
        logger.warning("No results to save to CSV")
        return

    # Ensure directory exists
    os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)

    # Define CSV headers based on the first result
    fieldnames = list(results[0].keys())

    # Write results to CSV
    with open(output_path, "w", newline="") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(results)

    logger.info(f"Saved {len(results)} evaluation results to {output_path}")


async def main_analyze_create_eval_records(
    organization_id: str,
    output_format: Literal["console", "csv"] = "console",
    output_path: str | None = None,
) -> None:
    engine = DatabaseEngine(
        url=str(settings.db_url),
    )
    repo = EvalRepo(engine=engine)
    s3_manager = get_s3_bucket_manager_by_bucket_name(
        bucket_name=settings.transcript_bucket_name
    )
    org_id = UUID(organization_id)
    seller_names = await repo.list_users_in_organization(org_id)

    # Set default output path if CSV format is selected but no path provided
    if output_format == "csv" and not output_path:
        output_path = f"eval_results_create_{organization_id}_{datetime.now(tz=UTC).strftime('%Y%m%d_%H%M%S')}.csv"

    await analyze_create_eval_records(
        repo,
        s3_manager,
        organization_id=org_id,
        rec_created_after=zoned_utc_now() - timedelta(hours=1),
        limit=1000,
        seller_names=seller_names,
        output_format=output_format,
        output_path=output_path,
    )


async def main_analyze_update_eval_records(
    organization_id: str,
    output_format: Literal["console", "csv"] = "console",
    output_path: str | None = None,
) -> None:
    engine = DatabaseEngine(
        url=str(settings.db_url),
    )
    repo = EvalRepo(engine=engine)
    s3_manager = get_s3_bucket_manager_by_bucket_name(
        bucket_name=settings.transcript_bucket_name
    )
    org_id = UUID(organization_id)
    seller_names = await repo.list_users_in_organization(org_id)

    # Set default output path if CSV format is selected but no path provided
    if output_format == "csv" and not output_path:
        output_path = f"eval_results_update_{organization_id}_{datetime.now(tz=UTC).strftime('%Y%m%d_%H%M%S')}.csv"

    await analyze_update_eval_records(
        repo,
        s3_manager,
        organization_id=org_id,
        rec_created_after=zoned_utc_now() - timedelta(days=144),
        limit=1000,
        seller_names=seller_names,
        output_format=output_format,
        output_path=output_path,
    )


if __name__ == "__main__":
    # import argparse
    #
    # parser = argparse.ArgumentParser(description="Analyze evaluation records")
    # parser.add_argument("--org-id", type=str, default="4d29f892-7e25-4efa-ad0b-f348bd0fc0fc", help="Organization ID")
    # parser.add_argument("--type", type=str, choices=["create", "update"], default="update", help="Type of records to analyze")
    # parser.add_argument("--output", type=str, choices=["console", "csv"], default="console", help="Output format")
    # parser.add_argument("--output-path", type=str, help="Path to save CSV output (optional)")
    #
    # args = parser.parse_args()
    org_id = "4d29f892-7e25-4efa-ad0b-f348bd0fc0fc"

    root_path = "/Users/<USER>/Work/sales-tech/playground/eval_results"
    create_path = (
        root_path
        + "/"
        + org_id
        + "_create_"
        + datetime.now(tz=UTC).strftime("%Y%m%d_%H%M%S")
        + ".csv"
    )
    update_path = (
        root_path
        + "/"
        + org_id
        + "_update_"
        + datetime.now(tz=UTC).strftime("%Y%m%d_%H%M%S")
        + ".csv"
    )
    asyncio.run(
        main_analyze_update_eval_records(
            organization_id=org_id, output_format="csv", output_path=update_path
        )
    )
    asyncio.run(
        main_analyze_create_eval_records(
            organization_id=org_id, output_format="csv", output_path=create_path
        )
    )
