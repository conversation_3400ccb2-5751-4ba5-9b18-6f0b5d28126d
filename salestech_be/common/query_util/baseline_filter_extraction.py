from collections import defaultdict
from dataclasses import dataclass
from typing import Self

from salestech_be.common.query_util.filter_schema import (
    CompositeFilter,
    FilterSpec,
    ValueFilter,
)
from salestech_be.common.type.metadata.common import ObjectIdentifier
from salestech_be.common.type.metadata.schema import (
    FieldReference,
    QualifiedField,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


__all__ = [
    "BaselineFilterExtractor",
    "BaselineFilterGroup",
    "BaselineFilters",
    "baseline_filter_extractor",
]


@dataclass(frozen=True)
class BaselineFilters:
    """
    Baseline filters are a subset of filters that are parsed out from a FilterSpec,
    such that we deterministically know these filter can be used by downstream query providers (QueryService / Resolvers)
    to optimize their internal queries during DB fetch time.

    A BaselineFilter is not intended to represent a fully described filter specification from user or caller.
    It only serve the purpose for query optimization purpose.

    Often time, the query service will need to do post fetch filter based on the orginal filter specification (FilterSpec) to ensure
    the fetched data is what the user intended.
    """

    must_filters: list[ValueFilter]
    must_not_filters: list[ValueFilter]
    at_least_one_filters: list[ValueFilter]

    @classmethod
    def empty(cls) -> Self:
        return cls(
            must_filters=[],
            must_not_filters=[],
            at_least_one_filters=[],
        )


@dataclass(frozen=True)
class BaselineFilterGroup:
    """
    A BaselineFilterGroup is a group of BaselineFilters for a given object identifier.
    """

    baseline_filters_by_object_identifier: dict[ObjectIdentifier, BaselineFilters]


class BaselineFilterExtractor:
    def extract_baseline_filter_group(
        self,
        *,
        filter_spec: FilterSpec,
    ) -> BaselineFilterGroup:
        """Extract BaselineFilterGroup from a filter specification and group them by object identifier.

        What is BaselineFilterGroup?
        see BaselineFilterGroup for more details

        What is BaselineFilter?
        see BaselineFilter for more details

        This method processes a filter specification and organizes the filters into baseline filter groups
        based on their associated object Identified. It handles both simple value filters and composite filters.

        Args:
            organization_schema_descriptor: Schema descriptor containing metadata about the organization's data model
            filter_spec: Filter specification containing the filters to be processed

        Returns:
            BaselineFilterGroup containing filters organized by object identifier with:
                - must_filters: Filters that must match
                - must_not_filters: Filters that must not match
                - at_least_one_filters: Filters where at least one must match

        Example:
            ```python
            extractor = BaselineFilterExtractor()
            filter_group = extractor.extract_baseline_filter_group(
                organization_schema_descriptor=schema,
                filter_spec=FilterSpec(
                    primary_object_identifier=ObjectIdentifier("account"),
                    filter=CompositeFilter(...),
                ),
            )
            ```
        """

        _primary_object_identifier = filter_spec.primary_object_identifier
        # if the filter is a single value filter, we can directly return the baseline filters
        if isinstance(filter_spec.filter, ValueFilter):
            if isinstance(filter_spec.filter.field, QualifiedField):
                return BaselineFilterGroup(
                    baseline_filters_by_object_identifier={
                        _primary_object_identifier: BaselineFilters(
                            must_filters=[filter_spec.filter],
                            must_not_filters=[],
                            at_least_one_filters=[],
                        ),
                    },
                )
            # not supporting relational extraction yet
            return BaselineFilterGroup(
                baseline_filters_by_object_identifier={},
            )

        flattened_filters = self._extract_baseline_from_composite_filter(
            filter_spec.filter
        )
        must_filters_by_object_identifier: dict[ObjectIdentifier, list[ValueFilter]] = (
            self._group_filters_by_object_identifier(
                filters=flattened_filters.must_filters,
                primary_object_identifier=_primary_object_identifier,
                # organization_schema_descriptor=organization_schema_descriptor,
            )
        )
        must_not_filters_by_object_identifier: dict[
            ObjectIdentifier, list[ValueFilter]
        ] = self._group_filters_by_object_identifier(
            filters=flattened_filters.must_not_filters,
            primary_object_identifier=_primary_object_identifier,
            # organization_schema_descriptor=organization_schema_descriptor,
        )
        at_least_one_filters_by_object_identifier: dict[
            ObjectIdentifier, list[ValueFilter]
        ] = self._group_filters_by_object_identifier(
            filters=flattened_filters.at_least_one_filters,
            primary_object_identifier=_primary_object_identifier,
            # organization_schema_descriptor=organization_schema_descriptor,
        )

        result: dict[ObjectIdentifier, BaselineFilters] = {}
        for object_identifier in {
            *must_filters_by_object_identifier.keys(),
            *must_not_filters_by_object_identifier.keys(),
            *at_least_one_filters_by_object_identifier.keys(),
        }:
            must_filters = must_filters_by_object_identifier.get(object_identifier, [])
            must_not_filters = must_not_filters_by_object_identifier.get(
                object_identifier, []
            )
            at_least_one_filters = at_least_one_filters_by_object_identifier.get(
                object_identifier, []
            )
            result[object_identifier] = BaselineFilters(
                must_filters=must_filters,
                must_not_filters=must_not_filters,
                at_least_one_filters=at_least_one_filters,
            )

        logger.info(
            "Baseline filter group",
            baseline_filter_group=result,
        )
        return BaselineFilterGroup(
            baseline_filters_by_object_identifier=result,
        )

    def extract_must_filter_by_field_reference(
        self, *, filter_spec: FilterSpec, field: QualifiedField | FieldReference
    ) -> list[ValueFilter]:
        """
        Extract the must filters that are associated with the given field reference.
        """
        flattened_filters = self._extract_baseline_from_composite_filter(
            composite_filter=filter_spec.filter
            if isinstance(filter_spec.filter, CompositeFilter)
            else CompositeFilter(all_of=[filter_spec.filter])
        )
        result: list[ValueFilter] = [
            must_filter
            for must_filter in flattened_filters.must_filters
            if must_filter.field == field
        ]
        return result

    def _group_filters_by_object_identifier(
        self,
        filters: list[ValueFilter],
        primary_object_identifier: ObjectIdentifier,
        # organization_schema_descriptor: OrganizationSchemaDescriptor,
    ) -> dict[ObjectIdentifier, list[ValueFilter]]:
        filters_by_object_identifier: dict[ObjectIdentifier, list[ValueFilter]] = (
            defaultdict(list)
        )
        for f in filters:
            if isinstance(f.field, FieldReference):
                continue
                # _related_object_identifier = self._field_reference_to_object_identifier(
                #     field_reference=f.field,
                #     current_object_identifier=primary_object_identifier,
                #     organization_schema_descriptor=organization_schema_descriptor,
                # )
                # if _related_object_identifier:
                #     filters_by_object_identifier[_related_object_identifier].append(f)
            filters_by_object_identifier[primary_object_identifier].append(f)
        return dict(filters_by_object_identifier)

    def _extract_baseline_from_composite_filter(
        self,
        composite_filter: CompositeFilter,
    ) -> BaselineFilters:
        """
        for composite filters, depending on the mode, extract the value filters
        - and: I want to extract all the value filters that must be met
        - or: I want to extract all the value filters that at least one must be met
        - not: I want to extract all the value filters that must not be met
        """

        must_filters: list[ValueFilter] = []
        must_not_filters: list[ValueFilter] = []
        at_least_one_filters: list[ValueFilter] = []

        for f in composite_filter.all_of:
            if isinstance(f, ValueFilter):
                must_filters.append(f)
            else:
                _flattened_child_composite_filter = (
                    self._extract_baseline_from_composite_filter(f)
                )
                must_filters.extend(_flattened_child_composite_filter.must_filters)
                must_not_filters.extend(
                    _flattened_child_composite_filter.must_not_filters
                )
                if (
                    _flattened_child_composite_filter.at_least_one_filters
                    and len(_flattened_child_composite_filter.at_least_one_filters) == 1
                ):
                    must_filters.extend(
                        _flattened_child_composite_filter.at_least_one_filters
                    )
                # in a parent all_of context, at_least_one from sub filter shouldn't be included
                # as parent's at_least_one context, the desired behavior is
                # at least one from these child at_least_one filter need to be true
                # however adding them to parent's at_least_one will simply erase the boundary
                # at_least_one_filters.extend(
                #     _flattened_child_composite_filter.at_least_one_filters
                # )

        for f in composite_filter.any_of:
            # When there is a reference or composite filter in at least one filters,
            # we have to ignore the entire at least one filters, since the "at least one"
            # semantic is not applicable to the parent object anymore
            # i.e. the reference node could be True while all the parent node is False
            if isinstance(f, ValueFilter) and isinstance(f.field, QualifiedField):
                at_least_one_filters.append(f)
            else:
                at_least_one_filters.clear()
                break

        for f in composite_filter.none_of:
            if isinstance(f, ValueFilter):
                must_not_filters.append(f)

        return BaselineFilters(
            must_filters=must_filters,
            must_not_filters=must_not_filters,
            at_least_one_filters=at_least_one_filters,
        )

    # def _field_reference_to_object_identifier(
    # self,
    # field_reference: FieldReference,
    # current_object_identifier: ObjectIdentifier,
    # organization_schema_descriptor: OrganizationSchemaDescriptor,
    # ) -> ObjectIdentifier | None:
    #     if not (
    #         found := self._recursive_field_reference_to_object_identifier(
    #             field_reference=field_reference,
    #             current_object_identifier=current_object_identifier,
    #             organization_schema_descriptor=organization_schema_descriptor,
    #         )
    #     ):
    #         logger.exception(
    #             "No object identifier found for field reference within the target organization",
    #             field_reference=field_reference,
    #             current_object_identifier=current_object_identifier,
    #             organization_id=organization_schema_descriptor.organization_id,
    #         )
    #         return None
    #     return found

    # def _recursive_field_reference_to_object_identifier(
    #     self,
    #     field_reference: FieldReference,
    #     current_object_identifier: ObjectIdentifier,
    #     organization_schema_descriptor: OrganizationSchemaDescriptor,
    # ) -> ObjectIdentifier | None:
    #     primary_object_descriptor = next(
    #         (
    #             obj
    #             for obj in organization_schema_descriptor.objects
    #             if obj.object_identifier == current_object_identifier
    #         ),
    #         None,
    #     )
    #     if not primary_object_descriptor:
    #         logger.exception(
    #             "No object identifier found for field reference within the target organization in recursion path",
    #             field_reference=field_reference,
    #             current_object_identifier=current_object_identifier,
    #             organization_id=organization_schema_descriptor.organization_id,
    #         )
    #         return None

    #     relationship = primary_object_descriptor.relationship(
    #         relationship_id=field_reference.relationship_id,
    #     )
    #     if not relationship:
    #         logger.exception(
    #             "Relationship not found for field reference within the target organization in recursion path",
    #             field_reference=field_reference,
    #             current_object_identifier=current_object_identifier,
    #             organization_id=organization_schema_descriptor.organization_id,
    #         )
    #         return None

    #     if isinstance(field_reference.field, FieldReference):
    #         return self._recursive_field_reference_to_object_identifier(
    #             field_reference=field_reference.field,
    #             current_object_identifier=relationship.related_object_identifier,
    #             organization_schema_descriptor=organization_schema_descriptor,
    #         )
    #     return relationship.related_object_identifier


baseline_filter_extractor = BaselineFilterExtractor()
