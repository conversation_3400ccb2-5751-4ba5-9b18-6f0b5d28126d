import json
import os
import time
from collections.abc import Iterable
from typing import (
    Any,
    Generic,
    Literal,
    NotRequired,
    TypedDict,
    TypeVar,
    cast,
    overload,
)
from uuid import UUID

import anthropic
import litellm
from anthropic import NOT_GIVEN, Stream
from anthropic.types import (
    Message,
    MessageParam,
    RawMessageStreamEvent,
    TextBlockParam,
    ToolUnionParam,
)
from anthropic.types.message_create_params import MessageCreateParamsNonStreaming
from anthropic.types.messages.batch_create_params import Request
from google.oauth2.service_account import Credentials
from langfuse.decorators import langfuse_context, observe
from langfuse.model import PromptClient
from litellm.types.utils import ChatCompletionMessageToolCall
from pydantic import BaseModel, ConfigDict

from salestech_be.common.stats.metric import llm_metric
from salestech_be.core.ai.common.helpers.check_token_count import (
    get_token_count,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.core.ai.common.llm_types import (
    ModelTypes,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.core.prompt.types import (
    PromptUseCase,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.settings import settings
from salestech_be.util.litellm_response_util import (
    get_first_message_content,
    get_first_message_tool_calls,
)

# Set all API keys here
litellm.openai_key = settings.openai_api_key.get_secret_value()
litellm.anthropic_key = settings.anthropic_api_key.get_secret_value()
# weirdly litellm.vertex_credentials doesn't exist
vertex_credentials = settings.vertex_ai_cred_json.get_secret_value()

litellm.enable_json_schema_validation = True
litellm.drop_params = True  # drop params if they are not supported by LLM model

# Setup the Langfuse environment variables
os.environ["LANGFUSE_HOST"] = "https://us.cloud.langfuse.com"  # 🇺🇸 US region

if not settings.langfuse_litellm_callbacks_disabled:
    # Setup the Langfuse environment variables ONLY if not disabled
    os.environ["LANGFUSE_PUBLIC_KEY"] = settings.langfuse_public_key.get_secret_value()
    os.environ["LANGFUSE_SECRET_KEY"] = settings.langfuse_secret_key.get_secret_value()
    # LANGFUSE_HOST is set unconditionally above, but tracing environment can be conditional
    os.environ["LANGFUSE_TRACING_ENVIRONMENT"] = settings.environment

    if "langfuse" not in litellm.success_callback:
        litellm.success_callback.append("langfuse")
    if "langfuse" not in litellm.failure_callback:
        litellm.failure_callback.append("langfuse")
else:
    # Explicitly remove if present, in case they were set by other means or default
    if "langfuse" in litellm.success_callback:
        litellm.success_callback.remove("langfuse")
    if "langfuse" in litellm.failure_callback:
        litellm.failure_callback.remove("langfuse")

    # Optionally, unset the environment variables if they were set unconditionally before this block
    # This ensures they are not picked up by some other Langfuse mechanism if callbacks are disabled.
    if "LANGFUSE_PUBLIC_KEY" in os.environ:
        del os.environ["LANGFUSE_PUBLIC_KEY"]
    if "LANGFUSE_SECRET_KEY" in os.environ:
        del os.environ["LANGFUSE_SECRET_KEY"]
    # LANGFUSE_HOST might be needed by other direct Langfuse instantiations, so be cautious about unsetting it globally
    # if "LANGFUSE_HOST" in os.environ:
    #     del os.environ["LANGFUSE_HOST"]
    if "LANGFUSE_TRACING_ENVIRONMENT" in os.environ:
        del os.environ["LANGFUSE_TRACING_ENVIRONMENT"]


# Setup the Gemini environment variables
os.environ["GEMINI_API_KEY"] = settings.gemini_api_key.get_secret_value()

# Langfuse host
# os.environ["LANGFUSE_HOST"] = "https://cloud.langfuse.com"  # 🇪🇺 EU region

litellm.success_callback = ["langfuse"]
litellm.failure_callback = ["langfuse"]

# Lazy loaded credentials and clients
_vertex_credentials: Credentials | None = None
_anthropic_main_us: anthropic.AsyncAnthropicVertex | None = None
_anthropic_main_eu: anthropic.AsyncAnthropicVertex | None = None


def get_vertex_credentials() -> Credentials:
    """Get Vertex AI credentials, initializing them only when needed."""
    global _vertex_credentials  # noqa: PLW0603
    if _vertex_credentials is None:
        _vertex_credentials = Credentials.from_service_account_info(
            # type: ignore[no-untyped-call]
            json.loads(settings.vertex_ai_cred_json.get_secret_value()),
            scopes=["https://www.googleapis.com/auth/cloud-platform"],
        )
    return _vertex_credentials


def get_anthropic_main_us() -> anthropic.AsyncAnthropicVertex:
    """Get US Anthropic client, initializing it only when needed."""
    global _anthropic_main_us  # noqa: PLW0603
    if _anthropic_main_us is None:
        _anthropic_main_us = anthropic.AsyncAnthropicVertex(
            project_id=settings.vertex_ai_project_id,
            region="us-east5",
            credentials=get_vertex_credentials(),
        )
    return _anthropic_main_us


def get_anthropic_main_eu() -> anthropic.AsyncAnthropicVertex:
    """Get EU Anthropic client, initializing it only when needed."""
    global _anthropic_main_eu  # noqa: PLW0603
    if _anthropic_main_eu is None:
        _anthropic_main_eu = anthropic.AsyncAnthropicVertex(
            project_id=settings.vertex_ai_project_id,
            region="eu-west1",
            credentials=get_vertex_credentials(),
        )
    return _anthropic_main_eu


class ReeTraceMetadata(TypedDict):
    """
    Custom metadata fields used in our codebase. Including these fields helps
    devs find and debug relevant traces
    """

    organization_id: NotRequired[str]
    """Try to include organization_id in all traces"""
    organization_name: NotRequired[str]
    job_id: NotRequired[str]
    meeting_id: NotRequired[str]
    prompt_use_case: NotRequired[PromptUseCase]
    workflow_id: NotRequired[str]
    user_display_name: NotRequired[str]


class LangfuseLiteLLMMetadata(TypedDict):
    """
    Metadata fields supported by Langfuse for LiteLLM tracing.

    See https://docs.litellm.ai/docs/observability/langfuse_integration#trace--generation-parameters
    """

    generation_name: NotRequired[str]
    session_id: NotRequired[str | None]
    tags: NotRequired[list[str] | None]
    trace_id: NotRequired[str]
    trace_user_id: NotRequired[str | None]
    trace_name: NotRequired[str]
    trace_metadata: NotRequired[dict[str, Any] | ReeTraceMetadata | None]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    trace_version: NotRequired[str | None]
    prompt: NotRequired[PromptClient | None]


class LLMTraceMetadata(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    generation_name: str | None = None
    prompt: PromptClient | None = None
    """For Langfuse prompt management attribution"""
    session_id: str | UUID | None = None
    tags: list[str] | None = None
    trace_name: str
    user_id: UUID | None = None

    custom_fields: ReeTraceMetadata | None = None

    def to_litellm_metadata(self) -> LangfuseLiteLLMMetadata:
        """Convert metadata to format expected by Langfuse."""
        metadata: LangfuseLiteLLMMetadata = {
            "tags": [settings.environment, *(self.tags or [])],
            "trace_name": self.trace_name,
            "trace_metadata": self.custom_fields or {},
        }
        if self.generation_name:
            metadata["generation_name"] = self.generation_name

        if self.session_id:
            metadata["session_id"] = str(self.session_id)

        if self.user_id:
            metadata["trace_user_id"] = str(self.user_id)

        if self.prompt:
            metadata["prompt"] = self.prompt

        return metadata


ResponseContentT = TypeVar("ResponseContentT", bound=BaseModel | str)


class LLMResponse(BaseModel, Generic[ResponseContentT]):
    model_config = ConfigDict(arbitrary_types_allowed=True)

    raw_response: litellm.ModelResponse
    """Raw `ModelResponse` object from LiteLLM"""

    _response_format: type[ResponseContentT]

    def __init__(
        self, response: litellm.ModelResponse, response_format: type[ResponseContentT]
    ):
        super().__init__(raw_response=response)
        self._response_format = response_format

    @property
    def message_content(self) -> ResponseContentT:
        """
        Helper to safely access the first choice message content and deserialize it into
        a pydantic model (if response_format was provided).

        Equivalent to `response.choices[0].message.content` but raises a ValueError if
        any fields in the path are empty/None, appeasing the type checker (i.e. mypy).

        Raises:
            ValueError: If the response does not contain any choices, content is empty, or
                the response is a stream. Pydantic may also throw an error if a specific
                response format is expected but is malformed.
        """
        content = get_first_message_content(self.raw_response)
        response_format = self._response_format
        if issubclass(response_format, BaseModel):
            return cast(ResponseContentT, response_format.model_validate_json(content))

        return cast(ResponseContentT, content)

    @property
    def tool_calls(self) -> list[ChatCompletionMessageToolCall] | None:
        """
        Helper to safely access the first choice tool calls.
        Equivalent to `response.choices[0].message.tool_calls` but makes sure to appease
        type gods.

        Raises:
            ValueError: If the response does not contain any choices
        """
        return get_first_message_tool_calls(self.raw_response)

    @property
    def choices(self) -> list[litellm.Choices]:
        choices = self.raw_response.choices
        if not all(isinstance(choice, litellm.Choices) for choice in choices):
            raise TypeError("Expected all choices to be of type litellm.Choices")
        return cast(list[litellm.Choices], choices)


ResponseFormatT = TypeVar("ResponseFormatT", bound=BaseModel)


@overload
async def acompletion(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    model: str,
    *,
    metadata: LLMTraceMetadata,
    max_completion_tokens: int | None = None,
    messages: list[Any] | None = None,
    n: int | None = None,
    temperature: float = 0.0,
    tools: list[Any] | None = None,
    tool_choice: str | None = None,
    response_format: type[ResponseFormatT] | None = None,
    seed: int | None = None,
    stream: Literal[True],
    stream_options: dict[str, Any] | None = None,
) -> litellm.CustomStreamWrapper: ...


# NOTE
# The following overload methods differ in the responses they return.
@overload
async def acompletion(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    model: str,
    *,
    metadata: LLMTraceMetadata,
    max_completion_tokens: int | None = None,
    messages: list[Any] | None = None,
    n: int | None = None,
    temperature: float = 0.0,
    tools: list[Any] | None = None,
    tool_choice: str | None = None,
    response_format: type[ResponseFormatT],
    seed: int | None = None,
    stream: Literal[False] | None = None,
    stream_options: dict[str, Any] | None = None,
) -> LLMResponse[ResponseFormatT]: ...


@overload
async def acompletion(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    model: str,
    *,
    metadata: LLMTraceMetadata,
    max_completion_tokens: int | None = None,
    messages: list[Any] | None = None,
    n: int | None = None,
    temperature: float = 0.0,
    tools: list[Any] | None = None,
    tool_choice: str | None = None,
    response_format: None = None,
    seed: int | None = None,
    stream: Literal[False] | None = None,
    stream_options: dict[str, Any] | None = None,
) -> LLMResponse[str]: ...


async def acompletion(  # type: ignore[explicit-any] # TODO: fix-any-annotation
    model: str,
    *,
    metadata: LLMTraceMetadata,
    max_completion_tokens: int | None = None,
    messages: list[Any] | None = None,
    n: int | None = None,
    temperature: float = 0.0,
    tools: list[Any] | None = None,
    tool_choice: str | None = None,
    response_format: type[ResponseFormatT] | None = None,
    seed: int | None = None,
    stream: bool | None = None,
    stream_options: dict[str, Any] | None = None,
) -> LLMResponse[ResponseFormatT] | LLMResponse[str] | litellm.CustomStreamWrapper:
    # Before the comparison, get the max tokens once and check if it's an int
    str_msg = json.dumps(messages)
    token_count = get_token_count(message_content=str_msg, model=model)
    try:
        max_tokens = get_max_tokens(model=model)
    except Exception as e:
        raise e

    if isinstance(max_tokens, int) and token_count > max_tokens:
        raise ValueError("Token count is over the limit")

    kwargs = {}
    # check for vertex_ai similar to litellm implementation. only pass in vertex_credentials if so.
    custom_llm_provider = model.split("/", 1)[0]
    if custom_llm_provider == "vertex_ai":
        kwargs["vertex_credentials"] = vertex_credentials

    dd_tags = [f"model:{model}"]
    if metadata.trace_name:
        dd_tags.append(f"trace_name:{metadata.trace_name}")

    start_time = time.perf_counter()

    try:
        response = await litellm.acompletion(
            model=model,
            metadata=metadata.to_litellm_metadata(),
            max_completion_tokens=max_completion_tokens,
            messages=messages,
            n=n,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
            response_format=response_format,
            seed=seed,
            stream=stream,
            stream_options=stream_options,
            **kwargs,
        )
    except Exception as e:
        dd_tags.append(f"error:{e.__class__.__name__}")
        raise e
    finally:
        time_elapsed = time.perf_counter() - start_time
        llm_metric.timing(
            metric_name="llm.completion", value=time_elapsed, tags=dd_tags
        )

    if stream:
        return cast(litellm.CustomStreamWrapper, response)
    if response_format:
        return LLMResponse(response, response_format)
    return LLMResponse(response, str)


async def anthropic_completion(
    model: str,
    messages: Iterable[MessageParam],
    max_tokens: int,
    metadata: LLMTraceMetadata,
    tools: list[ToolUnionParam] | None = None,
    system: str | Iterable[TextBlockParam] | None = None,
) -> Message:
    """
    Separate Anthropic completion function for using the anthropic citations API, which
    is not yet supported by LiteLLM. Use the LiteLLM `acompletion` method for all other
    use cases.
    """
    model = model.replace("anthropic/", "")
    response: Message = await _anthropic_trace_wrapper(
        model=model,
        messages=messages,
        max_tokens=max_tokens,
        metadata=metadata,
        tools=tools,
        system=system,
    )
    return response


@observe()  # type: ignore
async def _anthropic_trace_wrapper(
    model: str,
    messages: Iterable[MessageParam],
    max_tokens: int,
    metadata: LLMTraceMetadata,
    system: str | Iterable[TextBlockParam] | None = None,
    tools: list[ToolUnionParam] | None = None,
) -> Message | Stream[RawMessageStreamEvent]:
    """
    This only exists to create the parent trace and to fix type issues caused by the
    @observe decorator, which currently causes the method to become untyped
    """
    langfuse_context.update_current_trace(
        tags=[settings.environment, *(metadata.tags or [])],
        name=metadata.trace_name,
        user_id=str(metadata.user_id) if metadata.user_id else None,
        session_id=str(metadata.session_id) if metadata.session_id else None,
        metadata=metadata.custom_fields or {},
    )
    response: Message | Stream[RawMessageStreamEvent] = await _anthropic_completion(
        model=model,
        max_tokens=max_tokens,
        system=system,
        messages=messages,
        tools=tools,
    )
    return response


@observe(as_type="generation", capture_input=False, capture_output=True)  # type: ignore
async def _anthropic_completion(
    model: str,
    messages: Iterable[MessageParam],
    max_tokens: int,
    system: str | Iterable[TextBlockParam] | None = None,
    tools: list[ToolUnionParam] | None = None,
) -> Message | Stream[RawMessageStreamEvent]:
    response = await get_anthropic_main_us().messages.create(
        model=model,
        max_tokens=max_tokens,
        system=system or NOT_GIVEN,
        messages=messages,
        tools=tools or NOT_GIVEN,
    )
    langfuse_context.update_current_observation(
        model=model,
        input=messages,
        model_parameters={
            "max_tokens": max_tokens,
        },
        usage_details={
            "input": response.usage.input_tokens,
            "output": response.usage.output_tokens,
        },
    )
    return response


async def anthropic_batch_completion(
    model: str,
    batch_inputs: list[tuple[str, MessageCreateParamsNonStreaming]],
) -> str:
    """
    Send a batch of message requests to Anthropic's API.

    Args:
        model: The model to use for completion
        batch_inputs: A list of tuples containing (custom_id, MessageCreateParamsNonStreaming)
        metadata: Metadata for tracing

    Returns:
        A batch ID string for tracking the batch request
    """
    batch_params = []
    for custom_id, params in batch_inputs:
        batch_params.append(Request(custom_id=custom_id, params=params))

    msg_batch = await get_anthropic_main_us().messages.batches.create(
        requests=batch_params
    )
    return msg_batch.id


class ReeLLMMaxTokenException(Exception):
    """
    Exception raised when the max tokens for a model does not behave as expected.
    """

    def __init__(self, message: str):
        self.message = message


def get_max_tokens(model: str) -> int:
    if model not in ModelTypes:
        raise ReeLLMMaxTokenException(
            f"Provided model {model} is not currently supported. Please add it to ModelTypes in llm_types.py. You may also need to support is in get_max_tokens and check_token_count."
        )

    local_to_litellm_model_name = {
        "anthropic/claude-3-5-sonnet-20240620": "claude-3-5-sonnet-20240620",
        "anthropic/claude-3-5-sonnet-20241022": "claude-3-5-sonnet-20241022",
        "anthropic/claude-3-7-sonnet-20250219": "claude-3-7-sonnet-20250219",
        "anthropic/claude-3-7-sonnet-latest": "claude-3-7-sonnet-latest",
        "rerank-2": "voyage/rerank-2",
        "vertex_ai/gemini-2.0-flash": "gemini/gemini-2.0-flash",
        "vertex_ai/gemini-2.0-flash-001": "gemini-2.0-flash-001",
        "voyage-3": "voyage/voyage-3",
    }
    if model in local_to_litellm_model_name:
        model = local_to_litellm_model_name[model]

    max_tokens: int | None = litellm.model_cost[model].get("max_input_tokens", None)

    if max_tokens is None:
        raise ReeLLMMaxTokenException(
            f"Could not retrieve max tokens for model {model}. Check if the model name matches what LiteLLM expects at https://raw.githubusercontent.com/BerriAI/litellm/main/model_prices_and_context_window.json. If not, add the conversion in get_max_tokens' local_to_litellm_model_name dictionary."
        )

    return max_tokens
