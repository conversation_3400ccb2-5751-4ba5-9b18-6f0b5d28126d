from temporalio import activity, workflow

from salestech_be.core.meeting.meeting_service import meeting_service_factory_general
from salestech_be.temporal.activity_decorator import with_tracing
from salestech_be.temporal.database import get_or_init_db_engine

with workflow.unsafe.imports_passed_through():
    from salestech_be.common.stats.metric import custom_metric
    from salestech_be.core.calendar.user_calendar_sync_service import (
        get_user_calendar_sync_service_by_db_engine,
    )
    from salestech_be.db.dao.meeting_repository import MeetingRepository
    from salestech_be.ree_logging import get_logger
    from salestech_be.settings import settings

MEETING_EVENT_START_SYNC_ERROR_METRIC = "meeting_event_start_sync_error"
MEETING_BOT_JOIN_SYNC_ERROR_METRIC = "meeting_bot_join_sync_error"
MEETING_URL_WITH_EXTRA_BOTS_METRIC = "meeting_url_with_extra_bots"
MEETING_OVERDUE_ANALYZING_METRIC = "meeting_overdue_analyzing"
MEETING_OVERDUE_ACTIVE_METRIC = "meeting_overdue_active"
DAYS_WINDOW = 7
CALENDAR_MINUTES_WINDOW = 30
OVERDUE_ANALYZING_MINUTES_WINDOW = 15
OVERDUE_ACTIVE_MINUTES_WINDOW = 90

logger = get_logger(__name__)


@activity.defn
@with_tracing
async def meeting_calendar_event_timing_errors_activity() -> int:
    logger.info("Activity for detecting meeting timing issues")
    if not settings.enable_meeting_timing_job:
        logger.info("Job disabled")
        return 0

    db_engine = await get_or_init_db_engine()
    meeting_repository = MeetingRepository(engine=db_engine)

    # Later switch these queries away from live system
    meetings_with_event_start_mismatch = (
        await meeting_repository.list_meetings_with_calendar_time_mismatch(
            minutes_window=CALENDAR_MINUTES_WINDOW
        )
    )
    custom_metric.gauge(
        metric_name=MEETING_EVENT_START_SYNC_ERROR_METRIC,
        value=len(meetings_with_event_start_mismatch),
    )
    if meetings_with_event_start_mismatch:
        logger.error(
            "Found meetings with a start time out of sync with calendar event times.",
            extra={
                "count": len(meetings_with_event_start_mismatch),
            },
        )

        try:
            user_calendar_sync_service = get_user_calendar_sync_service_by_db_engine(
                db_engine=db_engine
            )
            for meeting_reference_identifiers in meetings_with_event_start_mismatch:
                await user_calendar_sync_service.resync_calendar_event_to_meeting(
                    organization_id=meeting_reference_identifiers.organization_id,
                    group_key=meeting_reference_identifiers.reference_id,
                )
        except Exception as e:
            logger.error(
                "Unable to recover from meeting calendar event timing mismatch",
                exc_info=e,
            )

    return len(meetings_with_event_start_mismatch)


@activity.defn
@with_tracing
async def meeting_bot_timing_errors_activity() -> int:
    logger.info("Activity for detecting meeting bot timing issues")
    if not settings.enable_meeting_timing_job:
        logger.info("Job disabled")
        return 0

    db_engine = await get_or_init_db_engine()
    meeting_repository = MeetingRepository(engine=db_engine)

    # Later switch these queries away from live system
    join_window_seconds = 5 * 60
    bots_with_join_mismatch = (
        await meeting_repository.list_meeting_bots_with_meeting_time_mismatch(
            days_window=DAYS_WINDOW, join_offset_seconds=join_window_seconds
        )
    )
    custom_metric.gauge(
        metric_name=MEETING_BOT_JOIN_SYNC_ERROR_METRIC,
        value=len(bots_with_join_mismatch),
    )
    if bots_with_join_mismatch:
        logger.error(
            "Found meeting bots with a join time out of sync with meeting start time.",
            extra={
                "count": len(bots_with_join_mismatch),
                "bot_ids": [bot.id for bot in bots_with_join_mismatch],
            },
        )
    if bots_with_join_mismatch:
        logger.error(
            "Found meeting bots with a scheduled at time out of sync with associated meeting start time.",
            extra={
                "count": len(bots_with_join_mismatch),
            },
        )

        try:
            meeting_service = meeting_service_factory_general(db_engine=db_engine)
            for meeting_bot in bots_with_join_mismatch:
                await meeting_service.sync_scheduled_bot_to_meeting(
                    organization_id=meeting_bot.organization_id,
                    meeting_id=meeting_bot.meeting_id,
                    meeting_bot_id=meeting_bot.id,
                )
        except Exception as e:
            logger.error(
                "Unable to recover from meeting bot timing mismatch",
                exc_info=e,
            )

    return len(bots_with_join_mismatch)


@activity.defn
@with_tracing
async def detect_meeting_timing_errors_activity() -> dict[str, int] | None:
    logger.info("Activity for detecting meeting timing issues")
    if not settings.enable_meeting_timing_job:
        logger.info("Job disabled")
        return None

    db_engine = await get_or_init_db_engine()
    meeting_repository = MeetingRepository(engine=db_engine)

    # Check for meetings with extra scheduled bots
    meeting_urls_with_extra_bots = (
        await meeting_repository.list_meeting_urls_with_extra_scheduled_bots()
    )
    custom_metric.gauge(
        metric_name=MEETING_URL_WITH_EXTRA_BOTS_METRIC,
        value=len(meeting_urls_with_extra_bots),
    )
    if meeting_urls_with_extra_bots:
        logger.error(
            "Found meetings with extra scheduled bots.",
            extra={
                "count": len(meeting_urls_with_extra_bots),
                "meeting_urls": list(meeting_urls_with_extra_bots.keys()),
            },
        )

    overdue_analyzing_meetings = (
        await meeting_repository.list_overdue_analyzing_meetings(
            minutes_window=OVERDUE_ANALYZING_MINUTES_WINDOW
        )
    )
    custom_metric.gauge(
        metric_name=MEETING_OVERDUE_ANALYZING_METRIC,
        value=len(overdue_analyzing_meetings),
    )

    overdue_active_meetings = await meeting_repository.list_overdue_active_meetings(
        minutes_window=OVERDUE_ACTIVE_MINUTES_WINDOW
    )
    custom_metric.gauge(
        metric_name=MEETING_OVERDUE_ACTIVE_METRIC,
        value=len(overdue_active_meetings),
    )

    # return values from each metric
    return {
        MEETING_URL_WITH_EXTRA_BOTS_METRIC: len(meeting_urls_with_extra_bots),
        MEETING_OVERDUE_ANALYZING_METRIC: len(overdue_analyzing_meetings),
    }
