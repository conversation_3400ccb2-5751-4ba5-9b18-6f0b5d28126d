from temporalio import activity, workflow
from temporalio.exceptions import ApplicationError

with workflow.unsafe.imports_passed_through():
    from uuid import UUID

    from salestech_be.core.email.outbound_domain.service import (
        get_outbound_domain_service_general,
    )
    from salestech_be.core.email.outbound_domain.types_v2 import (
        DomainPurchaseWorkflowInput,
        OutboundDomainHealth,
    )
    from salestech_be.db.dao.email_account import EmailAccountRepository
    from salestech_be.db.models.email_account import EmailAccount
    from salestech_be.db.models.outbound import OutboundDomain
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_MAX_RETRY
    from salestech_be.ree_logging import get_logger
    from salestech_be.temporal.activity_decorator import with_tracing
    from salestech_be.temporal.database import get_or_init_db_engine

logger = get_logger()


@activity.defn
@with_tracing
async def purchase_domains(
    domain_purchase_input: DomainPurchaseWorkflowInput,
) -> OutboundDomain | None:
    db_engine = await get_or_init_db_engine()
    outbound_domain_service = get_outbound_domain_service_general(db_engine)
    return await outbound_domain_service.purchase_and_process_domains(
        request=domain_purchase_input.request,
        user_id=domain_purchase_input.user_id,
        organization_id=domain_purchase_input.organization_id,
        purchasable_domains=domain_purchase_input.purchasable_domains,
    )


@activity.defn
@with_tracing
async def perform_domain_health_checks(
    outbound_domain: OutboundDomain,
) -> UUID:
    db_engine = await get_or_init_db_engine()
    outbound_domain_service = get_outbound_domain_service_general(db_engine)
    current_attempt = activity.info().attempt
    outbound_domain_v2 = await outbound_domain_service.perform_domain_health_checks(
        outbound_domain,
    )
    if (
        current_attempt < DEFAULT_TASK_MAX_RETRY
        and outbound_domain_v2.domain_health == OutboundDomainHealth.PENDING
    ):
        raise ApplicationError(
            message="Domain health check failed after pending status, do a backoff and retry",
            non_retryable=False,
        )
    return outbound_domain_v2.id


@activity.defn
@with_tracing
async def get_pending_email_accounts_by_domain_id(
    domain_id: UUID,
    organization_id: UUID,
) -> list[EmailAccount]:
    """
    Get all pending email accounts that use a specific domain

    Args:
        domain_id: The ID of the domain to check
        organization_id: The organization ID

    Returns:
        List of pending email accounts using this domain
    """
    db_engine = await get_or_init_db_engine()
    email_account_repository = EmailAccountRepository(engine=db_engine)

    # Get all email accounts with this domain
    return await email_account_repository.find_pending_email_accounts_by_domain_id(
        outbound_domain_id=domain_id,
        organization_id=organization_id,
    )


@activity.defn
@with_tracing
async def signal_email_account_create_workflows(
    email_accounts: list[EmailAccount],
) -> None:
    db_engine = await get_or_init_db_engine()
    outbound_domain_service = get_outbound_domain_service_general(engine=db_engine)
    await outbound_domain_service.signal_email_account_create_workflows(email_accounts)
