from collections.abc import Callable
from enum import StrEnum
from typing import Any

from pydantic import BaseModel
from temporalio import workflow

from salestech_be.core.ai.email.activities.parse_and_classify_main_email_body_for_global_threads import (
    parse_and_classify_main_email_body_for_global_threads,
)
from salestech_be.temporal.activities.email.domain_purchase_activity import (
    get_pending_email_accounts_by_domain_id,
    perform_domain_health_checks,
    purchase_domains,
    signal_email_account_create_workflows,
)
from salestech_be.temporal.activities.email.email_account_lifecycle_activate import (
    get_outbound_domain_from_email_account_id,
)
from salestech_be.temporal.activities.sequence.enrollment_run_activities import (
    create_sequence_enrollment_run_and_enrollments_from_contacts,
    create_sequence_enrollment_run_and_enrollments_from_domain_object_list,
)
from salestech_be.temporal.workflows.email.domain_purchase_workflow import (
    DomainPurchaseWorkflow,
)
from salestech_be.temporal.workflows.email.email_account_lifecycle_deactivate_patch_workflow import (
    EmailAccountLifecycleDeactivatePatchWorkflow,
)
from salestech_be.temporal.workflows.sequence.enrollment_run_workflow import (
    SequenceEnrollmentRunWorkflow,
)
from salestech_be.temporal.workflows.sequence.sequence_enrollment_workflow_v2 import (
    SequenceEnrollmentWorkflowV2,
)

with workflow.unsafe.imports_passed_through():
    from salestech_be.integrations.temporal.config import (
        TemporalTaskQueue,
    )
    from salestech_be.settings import settings
    from salestech_be.temporal.activities.domain_crm_association.activity_attribution import (
        account_attribution_activity,
        account_capture_activity,
        contact_attribution_activity,
        email_to_contact_capture_activity,
        phone_number_to_contact_capture_activity,
        pipeline_attribution_activity,
        pipeline_capture_activity,
    )
    from salestech_be.temporal.activities.email.domain_health_check_per_domain_activity import (
        domain_health_check_per_domain,
    )
    from salestech_be.temporal.activities.email.email_account_lifecycle_activate import (
        get_and_persist_mailbox_credentials,
        start_warmup_campaign,
        update_warmup_campaign_status_to_completed,
    )
    from salestech_be.temporal.activities.email.email_account_lifecycle_deactivate_patch import (
        update_warmup_campaign,
    )
    from salestech_be.temporal.activities.email.email_account_remove import (
        handle_email_account_remove,
    )
    from salestech_be.temporal.activities.email.email_health_check_activity import (
        email_health_check,
    )
    from salestech_be.temporal.activities.email.email_sync_activity import (
        construct_global_thread_activity,
        create_bot_assistant_activity,
        generate_activity_insight_activity,
        generate_global_thread_insight_activity,
        sync_email_activity,
        sync_nylas_thread_activity,
    )
    from salestech_be.temporal.activities.email.imap_sync_activity import (
        sync_imap_activity,
    )
    from salestech_be.temporal.activities.email.notification_email import (
        send_notification_email,
    )
    from salestech_be.temporal.activities.email.nylas_tracking_event_activity import (
        handle_nylas_tracking_event_activity,
    )
    from salestech_be.temporal.activities.email.send_email import (
        persist_email_activity,
        send_email,
    )
    from salestech_be.temporal.activities.event_schedule_activities import (
        post_booking_update_activity,
        send_scheduler_email_notification,
        update_event_schedule_timezone,
    )
    from salestech_be.temporal.activities.event_setting_sync import (
        sync_event_settings_activity,
    )
    from salestech_be.temporal.activities.falkordb.indexing_activities import (
        get_accounts_count_activity,
        get_contact_account_roles_count_activity,
        get_contact_pipeline_roles_count_activity,
        get_contacts_count_activity,
        get_custom_objects_count_activity,
        get_domain_object_count_falkor_activity,
        get_pipelines_count_activity,
        index_all_accounts_in_batches_activity,
        index_all_contact_account_roles_in_batches_activity,
        index_all_contact_pipeline_roles_in_batches_activity,
        index_all_contacts_in_batches_activity,
        index_all_custom_objects_for_organization_in_batches_activity,
        index_all_pipelines_in_batches_activity,
        index_organization_relationships_activity,
        index_organization_relationships_activity_in_batches,
        index_users_activity,
        list_organization_and_user_ids_activity,
    )
    from salestech_be.temporal.activities.notification.notification_activities import (
        send_notification_activity,
        trigger_task_notification,
    )
    from salestech_be.temporal.activities.prospecting.bulk_enrich_contact import (
        bulk_enrich_contact,
    )
    from salestech_be.temporal.activities.prospecting.company_bulk_import import (
        add_accounts_to_domain_lists,
        convert_company_to_accounts,
    )
    from salestech_be.temporal.activities.prospecting.people_bulk_import import (
        add_contacts_to_domain_lists,
        add_contacts_to_sequence,
        convert_people_to_contacts,
        enrich_people_with_run,
        finish_prospecting_run,
    )
    from salestech_be.temporal.activities.scan_calendar_event_activity import (
        process_calendar_event_sync_task,
        process_sync_contact_task,
    )
    from salestech_be.temporal.activities.sequence.archive_domain_sequence_chain_activity import (
        archive_sequences_from_domain_and_sequence,
    )
    from salestech_be.temporal.activities.sequence.sequence_enrollment_activities import (
        complete_enrollment_workflow,
        fail_enrollment_workflow,
        fetch_sequence_enrollment,
        fetch_sequence_enrollment_v2,
        move_to_next_step,
        move_to_next_step_v2,
        process_sequence_step,
        process_sequence_step_v2,
        start_execute_step,
        start_execute_step_v2,
    )
    from salestech_be.temporal.activities.sequence.sequence_enrollment_termination import (
        end_all_sequence_enrollments_activity,
        end_sequence_enrollments_activity,
    )
    from salestech_be.temporal.activities.sequence.sequence_signal_activities import (
        signal_all_enrollments_activity,
    )
    from salestech_be.temporal.activities.user_calendar.meeting_sync_activity import (
        meeting_create_activity,
        meeting_sync_activity,
    )
    from salestech_be.temporal.activities.user_calendar.user_calendar_event_activity import (
        handle_user_calendar_event_activity,
    )
    from salestech_be.temporal.workflows.domain_crm_association.activity_attribution import (
        DomainCrmAssociationAttributionWorkflow,
    )
    from salestech_be.temporal.workflows.domain_crm_association.bulk_activity_attribution import (
        BulkActivityAttributionWorkflow,
    )
    from salestech_be.temporal.workflows.email.domain_health_check_per_domain_workflow import (
        DomainHealthCheckPerDomainWorkflow,
    )
    from salestech_be.temporal.workflows.email.email_account_lifecycle_activate_workflow import (
        EmailAccountLifecycleActivateWorkflow,
    )
    from salestech_be.temporal.workflows.email.email_account_lifecycle_deactivate_patch_workflow import (
        EmailAccountLifecycleDeactivatePatchWorkflow,
    )
    from salestech_be.temporal.workflows.email.email_account_remove import (
        EmailAccountRemoveWorkflow,
    )
    from salestech_be.temporal.workflows.email.email_health_check_workflow import (
        EmailHealthCheckWorkflow,
    )
    from salestech_be.temporal.workflows.email.email_insight_workflow import (
        EmailInsightWorkflow,
    )
    from salestech_be.temporal.workflows.email.email_notification import (
        EmailNotificationWorkflow,
    )
    from salestech_be.temporal.workflows.email.email_sync_workflow import (
        EmailSyncWorkflow,
        NylasThreadSyncWorkflow,
    )
    from salestech_be.temporal.workflows.email.imap_sync_workflow import (
        ImapSyncWorkflow,
    )
    from salestech_be.temporal.workflows.email.nylas_tracking_event_workflow import (
        NylasTrackingEventWorkflow,
    )
    from salestech_be.temporal.workflows.email.send_email import SendEmailWorkflow
    from salestech_be.temporal.workflows.event_schedule import (
        EventScheduleTimezoneChangeWorkflow,
        EventScheduleWorkflow,
    )
    from salestech_be.temporal.workflows.event_setting_sync import (
        EventSettingSyncWorkflow,
    )
    from salestech_be.temporal.workflows.falkordb.indexing_workflows import (
        IndexAllOrganizationsWorkflow,
        IndexCoverRightOrganizationsWorkflow,
        IndexCoverRightRelationshipsWorkflow,
        IndexMonitoringWorkflow,
        IndexSpecificOrganizationWorkflow,
    )
    from salestech_be.temporal.workflows.notification.notification_workflow import (
        NotificationWorkflow,
    )
    from salestech_be.temporal.workflows.notification.task_notification_workflow import (
        TaskNotificationWorkflow,
    )
    from salestech_be.temporal.workflows.prospecting.bulk_enrich_contact_workflow import (
        BulkEnrichContactWorkflow,
    )
    from salestech_be.temporal.workflows.prospecting.company_bulk_import_workflow import (
        CompanyBulkImportWorkflow,
    )
    from salestech_be.temporal.workflows.prospecting.people_bulk_import_workflow import (
        PeopleBulkImportWorkflow,
    )
    from salestech_be.temporal.workflows.scan_calendar_event_workflow import (
        ScanCalendarEventsWorkflow,
        SyncContactFromCalendarEventsWorkflow,
    )
    from salestech_be.temporal.workflows.sequence.archive_domain_to_sequence_workflow import (
        ArchiveDomainToSequenceWorkflow,
    )
    from salestech_be.temporal.workflows.sequence.sequence_enrollment_terminate_workflow import (
        SequenceEnrollmentTerminateAllWorkflow,
        SequenceEnrollmentTerminateWorkflow,
    )
    from salestech_be.temporal.workflows.sequence.sequence_enrollment_workflow import (
        SequenceEnrollmentWorkflow,
    )
    from salestech_be.temporal.workflows.sequence.sequence_signal_workflow import (
        SequenceSignalWorkflow,
    )
    from salestech_be.temporal.workflows.user_calendar.user_calendar_event_meeting_sync_workflow import (
        UserCalendarEventMeetingCreateWorkflow,
        UserCalendarEventMeetingSyncWorkflow,
    )
    from salestech_be.temporal.workflows.user_calendar.user_calendar_webhook_workflow import (
        UserCalendarWebhookWorkflow,
    )


class TemporalWorkerTask(StrEnum):
    MEETING_ALL = "meeting_all"
    CALENDAR_ALL = "calendar_all"
    NOTIFICATION_ALL = "notification_all"
    EMAIL_WORKFLOW_ALL = "email_workflow_all"
    EMAIL_ACTIVITY_ALL = "email_activity_all"
    EMAIL_IMAP = "email_imap"
    DEFAULT_ACTIVITY = "default_activity"
    DEFAULT_WORKFLOW = "default_workflow"
    AI_ALL = "ai_all"
    RESEARCH_ACTIVITY = "research_activity"
    RESEARCH_WF = "research_wf"
    RESEARCH_ACTIVITY_LOWPRI = "research_activity_lowpri"
    RESEARCH_WF_LOWPRI = "research_wf_lowpri"
    RESEARCH_ACTIVITY_THROTTLED = "research_activity_throttled"
    RESEARCH_ACTIVITY_THROTTLED_LOWPRI = "research_activity_throttled_lowpri"
    CRM_SYNC_ALL = "crm_sync_all"
    VOICE_ALL = "voice_all"
    PROSPECTING = "prospecting"
    DOMAIN_CRM_ASSOCIATION = "domain_crm_association"
    SEQUENCE_WORKFLOW = "sequence_workflow"
    SEQUENCE_ACTIVITY = "sequence_activity"
    FALKOR_WORKFLOW = "falkor_workflow"
    FALKOR_ACTIVITY = "falkor_activity"
    INTEGRITY_JOB_ALL = "integrity_job_all"


class TemporalWorkerConcurrency(BaseModel):
    max_concurrent_workflow_tasks: int
    max_concurrent_activities: int
    max_concurrent_workflow_task_polls: int = 5
    max_concurrent_activity_task_polls: int = 5


class TemporalWorkerConfig(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    task_queue: TemporalTaskQueue
    workflows: list[type]
    activities: list[Callable[..., Any]]  # type: ignore[explicit-any] # TODO: fix-any-annotation
    concurrency: TemporalWorkerConcurrency
    db_pool_size: int


##################################
# Temporal Worker Config
###################################

EMAIL_WORKFLOW_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
    workflows=[
        EmailSyncWorkflow,
        NylasThreadSyncWorkflow,
        SendEmailWorkflow,
        EmailInsightWorkflow,
        EmailNotificationWorkflow,
        EmailAccountLifecycleActivateWorkflow,
        EmailAccountLifecycleDeactivatePatchWorkflow,
        NylasTrackingEventWorkflow,
        EmailAccountRemoveWorkflow,
        EmailHealthCheckWorkflow,
        DomainHealthCheckPerDomainWorkflow,
        DomainPurchaseWorkflow,
    ],
    activities=[],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=settings.email_worker_wf_concurrent_size,
        max_concurrent_activities=10,
        max_concurrent_workflow_task_polls=settings.email_worker_wf_poll_size,
        max_concurrent_activity_task_polls=10,
    ),
    db_pool_size=1,
)

EMAIL_ACTIVITY_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
    workflows=[],
    activities=[
        create_bot_assistant_activity,
        sync_email_activity,
        sync_nylas_thread_activity,
        construct_global_thread_activity,
        generate_global_thread_insight_activity,
        generate_activity_insight_activity,
        send_email,
        persist_email_activity,
        send_notification_email,
        get_and_persist_mailbox_credentials,
        start_warmup_campaign,
        handle_nylas_tracking_event_activity,
        update_warmup_campaign,
        handle_email_account_remove,
        email_health_check,
        domain_health_check_per_domain,
        purchase_domains,
        perform_domain_health_checks,
        parse_and_classify_main_email_body_for_global_threads,
        update_warmup_campaign_status_to_completed,
        signal_email_account_create_workflows,
        get_pending_email_accounts_by_domain_id,
        get_outbound_domain_from_email_account_id,
    ],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=10,
        max_concurrent_activities=settings.email_worker_activity_concurrent_size,
        max_concurrent_workflow_task_polls=10,
        max_concurrent_activity_task_polls=settings.email_worker_activity_poll_size,
    ),
    db_pool_size=settings.email_worker_db_connection_pool_size,
)


EMAIL_IMAP_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.EMAIL_IMAP_TASK_QUEUE,
    workflows=[ImapSyncWorkflow],
    activities=[sync_imap_activity],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=settings.email_worker_wf_concurrent_size,
        max_concurrent_activities=settings.email_worker_activity_concurrent_size,
    ),
    db_pool_size=settings.email_worker_db_connection_pool_size,
)


CALENDAR_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.CALENDAR_TASK_QUEUE,
    workflows=[
        ScanCalendarEventsWorkflow,
        SyncContactFromCalendarEventsWorkflow,
        EventScheduleWorkflow,
        EventScheduleTimezoneChangeWorkflow,
        EventSettingSyncWorkflow,
        UserCalendarWebhookWorkflow,
        UserCalendarEventMeetingSyncWorkflow,
        UserCalendarEventMeetingCreateWorkflow,
    ],
    activities=[
        process_calendar_event_sync_task,
        process_sync_contact_task,
        post_booking_update_activity,
        send_scheduler_email_notification,
        update_event_schedule_timezone,
        sync_event_settings_activity,
        handle_user_calendar_event_activity,
        meeting_sync_activity,
        meeting_create_activity,
    ],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=settings.calendar_worker_concurrent_size,
        max_concurrent_activities=settings.calendar_worker_concurrent_size,
    ),
    db_pool_size=10,
)

NOTIFICATION_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.NOTIFICATION_TASK_QUEUE,
    workflows=[NotificationWorkflow, TaskNotificationWorkflow],
    activities=[send_notification_activity, trigger_task_notification],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=5,
        max_concurrent_activities=5,
    ),
    db_pool_size=10,
)

PROSPECTING_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.PROSPECTING_TASK_QUEUE,
    workflows=[
        PeopleBulkImportWorkflow,
        CompanyBulkImportWorkflow,
        BulkEnrichContactWorkflow,
    ],
    activities=[
        enrich_people_with_run,
        convert_people_to_contacts,
        add_contacts_to_sequence,
        add_contacts_to_domain_lists,
        finish_prospecting_run,
        convert_company_to_accounts,
        add_accounts_to_domain_lists,
        bulk_enrich_contact,
    ],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=settings.prospecting_worker_concurrent_size,
        max_concurrent_activities=settings.prospecting_worker_concurrent_size,
    ),
    db_pool_size=10,
)

SEQUENCE_WORKFLOW_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.SEQUENCE_TASK_QUEUE,
    workflows=[
        SequenceEnrollmentWorkflow,
        SequenceSignalWorkflow,
        ArchiveDomainToSequenceWorkflow,
        SequenceEnrollmentRunWorkflow,
        SequenceEnrollmentWorkflowV2,
        SequenceEnrollmentTerminateWorkflow,
        SequenceEnrollmentTerminateAllWorkflow,
    ],
    activities=[],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=settings.sequence_worker_concurrent_size,
        max_concurrent_activities=settings.sequence_worker_concurrent_size,
    ),
    db_pool_size=1,
)

SEQUENCE_ACTIVITY_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.SEQUENCE_TASK_QUEUE,
    workflows=[],
    activities=[
        fetch_sequence_enrollment,
        start_execute_step,
        process_sequence_step,
        complete_enrollment_workflow,
        move_to_next_step,
        signal_all_enrollments_activity,
        archive_sequences_from_domain_and_sequence,
        create_sequence_enrollment_run_and_enrollments_from_contacts,
        create_sequence_enrollment_run_and_enrollments_from_domain_object_list,
        fetch_sequence_enrollment_v2,
        start_execute_step_v2,
        process_sequence_step_v2,
        move_to_next_step_v2,
        end_sequence_enrollments_activity,
        fail_enrollment_workflow,
        end_all_sequence_enrollments_activity,
    ],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=settings.sequence_worker_concurrent_size,
        max_concurrent_activities=settings.sequence_worker_concurrent_size,
    ),
    db_pool_size=settings.sequence_worker_db_connection_pool_size,
)

# Domain-CRM Association Worker Config
DOMAIN_CRM_ASSOCIATION_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.DOMAIN_CRM_ASSOCIATION_TASK_QUEUE,
    workflows=[
        DomainCrmAssociationAttributionWorkflow,
        BulkActivityAttributionWorkflow,
    ],
    activities=[
        account_capture_activity,
        pipeline_capture_activity,
        email_to_contact_capture_activity,
        phone_number_to_contact_capture_activity,
        contact_attribution_activity,
        account_attribution_activity,
        pipeline_attribution_activity,
    ],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=settings.domain_crm_association_worker_concurrent_size,
        max_concurrent_activities=settings.domain_crm_association_worker_concurrent_size,
    ),
    db_pool_size=5,
)

# Add new worker config for FalkorDB
FALKOR_WORKFLOW_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
    workflows=[
        IndexAllOrganizationsWorkflow,
        IndexSpecificOrganizationWorkflow,
        IndexCoverRightOrganizationsWorkflow,
        IndexCoverRightRelationshipsWorkflow,
        IndexMonitoringWorkflow,
    ],
    activities=[],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=10,
        max_concurrent_activities=0,
    ),
    db_pool_size=20,
)

FALKOR_ACTIVITY_WORKER_CONFIG = TemporalWorkerConfig(
    task_queue=TemporalTaskQueue.FALKOR_TASK_QUEUE,
    workflows=[],
    activities=[
        list_organization_and_user_ids_activity,
        index_all_accounts_in_batches_activity,
        index_all_contacts_in_batches_activity,
        index_all_pipelines_in_batches_activity,
        index_all_contact_pipeline_roles_in_batches_activity,
        index_all_contact_account_roles_in_batches_activity,
        index_organization_relationships_activity,
        index_users_activity,
        index_organization_relationships_activity_in_batches,
        index_all_custom_objects_for_organization_in_batches_activity,
        get_contact_pipeline_roles_count_activity,
        get_contact_account_roles_count_activity,
        get_custom_objects_count_activity,
        get_accounts_count_activity,
        get_contacts_count_activity,
        get_pipelines_count_activity,
        get_domain_object_count_falkor_activity,
    ],
    concurrency=TemporalWorkerConcurrency(
        max_concurrent_workflow_tasks=0,
        max_concurrent_activities=20,
    ),
    db_pool_size=20,
)

TEMPORAL_WORKER_CONFIG_MAP: dict[TemporalWorkerTask, TemporalWorkerConfig] = {
    TemporalWorkerTask.EMAIL_WORKFLOW_ALL: EMAIL_WORKFLOW_WORKER_CONFIG,
    TemporalWorkerTask.EMAIL_ACTIVITY_ALL: EMAIL_ACTIVITY_WORKER_CONFIG,
    TemporalWorkerTask.EMAIL_IMAP: EMAIL_IMAP_WORKER_CONFIG,
    TemporalWorkerTask.CALENDAR_ALL: CALENDAR_WORKER_CONFIG,
    TemporalWorkerTask.NOTIFICATION_ALL: NOTIFICATION_WORKER_CONFIG,
    TemporalWorkerTask.PROSPECTING: PROSPECTING_WORKER_CONFIG,
    TemporalWorkerTask.DOMAIN_CRM_ASSOCIATION: DOMAIN_CRM_ASSOCIATION_WORKER_CONFIG,
    TemporalWorkerTask.SEQUENCE_WORKFLOW: SEQUENCE_WORKFLOW_WORKER_CONFIG,
    TemporalWorkerTask.SEQUENCE_ACTIVITY: SEQUENCE_ACTIVITY_WORKER_CONFIG,
    TemporalWorkerTask.FALKOR_WORKFLOW: FALKOR_WORKFLOW_WORKER_CONFIG,
    TemporalWorkerTask.FALKOR_ACTIVITY: FALKOR_ACTIVITY_WORKER_CONFIG,
}
