from salestech_be.common.query_util.field_walker import Qualified<PERSON>ield<PERSON>alker
from salestech_be.common.schema_manager.std_object_field_identifier import (
    ContactEmailField,
    ContactField,
)
from salestech_be.common.type.metadata.schema import FieldReference, QualifiedField


class DomainQualifiedFieldWalker(QualifiedFieldWalker):
    def __call__(self, field: QualifiedField) -> QualifiedField | FieldReference:
        if field.path == (ContactField.primary_email,):
            return QualifiedField(
                path=(
                    ContactField.contact_emails,
                    ContactEmailField.email,
                )
            )

        return field
