from collections.abc import Sequence
from typing import Annotated, Named<PERSON><PERSON>le, assert_never
from uuid import UUID

from fastapi import Depends

from salestech_be.common.exception import InvalidArgumentError, ResourceNotFoundError
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    ObjectIdentifier,
    StandardFieldIdentifier,
    StandardObjectIdentifier,
    field_identifier,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    Dict<PERSON>ieldProperty,
    FieldExternalProvider,
    FieldTypeProperty,
    ListFieldProperty,
    NestedObjectFieldProperty,
)
from salestech_be.common.type.metadata.schema import (
    CustomFieldDescriptor,
    FieldDescriptor,
    FieldPath,
    FieldReference,
    InboundRelationship,
    ObjectDescriptor,
    ObjectExternalProvider,
    OrganizationSchemaDescriptor,
    OutboundRelationship,
    QualifiedField,
    StandardFieldDescriptor,
    StandardObjectDescriptor,
)
from salestech_be.core.common.types import DomainModel
from salestech_be.core.custom_object.service.association_service import (
    AssociationService,
    get_association_service,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
    get_custom_object_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.metadata.converter import (
    custom_object_descriptor_from_custom_object_dto,
    recreate_standard_object_descriptor_with_updated_fields,
)
from salestech_be.core.metadata.service.default_schema_provider import (
    system_default_domain_schema_provider_v2,
)
from salestech_be.db.dao.crm_sync_repository import CRMSyncRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.custom_object_association import CustomObjectAssociation
from salestech_be.db.models.organization_external_sync import (
    CrmProvider,
    OrganizationExternalSyncSetting,
    SyncMode,
)
from salestech_be.integrations.field_origin.field_origin_map import field_origin_map
from salestech_be.integrations.field_origin.field_origin_types import (
    ProviderField,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.copy_util import strict_model_copy
from salestech_be.util.validation import not_none

logger = get_logger()


class ValidateFieldResult(NamedTuple):
    field_path: FieldPath
    object_descriptor: ObjectDescriptor


class MetadataService:
    def __init__(
        self,
        custom_object_service: Annotated[CustomObjectService, Depends()],
        association_service: Annotated[
            AssociationService, Depends(get_association_service)
        ],
        crm_sync_repository: Annotated[CRMSyncRepository, Depends()],
    ):
        super().__init__()
        self.default_domain_object_schema_provider = (
            system_default_domain_schema_provider_v2
        )
        self.custom_object_service = custom_object_service
        self.association_service = association_service
        self.crm_sync_repository = crm_sync_repository

        #  Convenience demo for now, remove later
        self.demo_external_provider_org_id: str = "77777777-7777-7777-7777-777777777777"
        # Feature flag
        self.ff_hubspot_sync_orgs_json: list[str] = [
            org_id.lower() for org_id in settings.hubspot_sync_orgs_json
        ] + [self.demo_external_provider_org_id]
        self.ff_hubspot_sync_full_enable: str = (
            "all"  # if present within hubspot_sync_orgs_json, enable for all orgs
        )
        self.supported_crm_providers: list[CrmProvider] = [CrmProvider.HUBSPOT]

    async def _get_organization_external_sync(
        self, organization_id: UUID
    ) -> OrganizationExternalSyncSetting | None:
        org_id_str = str(organization_id)
        if (
            org_id_str in self.ff_hubspot_sync_orgs_json
            or self.ff_hubspot_sync_full_enable in self.ff_hubspot_sync_orgs_json
        ):
            if org_id_str == self.demo_external_provider_org_id:
                return OrganizationExternalSyncSetting(
                    crm=CrmProvider.HUBSPOT,
                    sync_mode=SyncMode.PULL_ONLY,
                    sync_job_enabled=[],  # unused
                    sync_period_minutes=15,  # unused
                )
            try:
                if (
                    org_external_sync
                    := await self.crm_sync_repository.get_org_external_sync(
                        organization_id=organization_id
                    )
                ):
                    logger.bind(
                        org_external_sync=org_external_sync,
                        organization_id=organization_id,
                    ).info(
                        "crm_sync_repository.get_org_external_sync found valid org_external_sync"
                    )
                    sync_setting_to_return = org_external_sync.sync_setting
                    if sync_setting_to_return.crm in self.supported_crm_providers:
                        return sync_setting_to_return
            except Exception as e:
                logger.bind(organization_id=organization_id).error(
                    "An error occurred while getting the organization external sync setting. "
                    "Should never happen, but ignoring.",
                    error=e,
                )
                # pass. Even if retreiving the org_external_sync fails, no need
                #   to make first-class organizations __without__ external
                #   providers unusable.
        return None

    def _get_cardinality(self, max_records: int) -> InboundRelationship.Cardinality:
        """Convert max records to cardinality enum."""
        if max_records == 1:
            return InboundRelationship.Cardinality.ONE
        return InboundRelationship.Cardinality.MANY

    def add_relationships_to_descriptor(
        self,
        descriptor: ObjectDescriptor,
        associations: list[CustomObjectAssociation],
    ) -> ObjectDescriptor:
        """Add relationship information to an object descriptor."""
        # Start with existing relationships
        inbound_rels = list(descriptor.inbound_relationships)
        outbound_rels = list(descriptor.outbound_relationships)

        descriptor_identifier = descriptor.object_identifier

        for assoc in associations:
            # Handle outbound relationships (where this object is the source)
            if assoc.source_object_identifier == descriptor_identifier:
                outbound_rels.append(
                    OutboundRelationship(
                        id=assoc.id,
                        relation_type=OutboundRelationship.RelationType.LOOKUP,
                        relationship_name=assoc.association_name,
                        self_object_identifier=assoc.source_object_identifier,
                        related_object_identifier=assoc.target_object_identifier,
                        self_cardinality=self._get_cardinality(
                            assoc.max_source_records
                        ),
                        related_object_cardinality=self._get_cardinality(
                            assoc.max_target_records
                        ),
                        ordered_self_field_identifiers=(),  # These would come from the field mappings
                        ordered_related_field_identifiers=(),  # These would come from the field mappings
                    )
                )

            # Handle inbound relationships (where this object is the target)
            if assoc.target_object_identifier == descriptor_identifier:
                inbound_rels.append(
                    InboundRelationship(
                        id=assoc.id,
                        relation_type=InboundRelationship.RelationType.LOOKUP,
                        relationship_name=assoc.inverse_name,
                        self_object_identifier=assoc.target_object_identifier,
                        related_object_identifier=assoc.source_object_identifier,
                        self_cardinality=self._get_cardinality(
                            assoc.max_target_records
                        ),
                        related_object_cardinality=self._get_cardinality(
                            assoc.max_source_records
                        ),
                        ordered_self_field_identifiers=(),  # These would come from the field mappings
                        ordered_related_field_identifiers=(),  # These would come from the field mappings
                    )
                )

        return descriptor.model_copy(
            update={
                "inbound_relationships": tuple(
                    inbound_rels
                ),  # Convert to tuple for immutability
                "outbound_relationships": tuple(outbound_rels),
            }
        )

    async def get_organization_domain_object_schema(
        self, organization_id: UUID, ignore_nested_object_field: bool = False
    ) -> OrganizationSchemaDescriptor:
        default_standard_object_descriptors = (
            self.default_domain_object_schema_provider.all_std_object_default_descriptors_ignore_nested_object_field()
            if ignore_nested_object_field
            else self.default_domain_object_schema_provider.all_std_object_default_descriptors()
        )
        custom_object_dtos = (
            await self.custom_object_service.list_custom_objects_for_organization(
                organization_id=organization_id,
            )
        )
        associations = await self.association_service.get_associations_for_organization(
            organization_id=organization_id,
        )
        org_external_sync_setting: (
            OrganizationExternalSyncSetting | None
        ) = await self._get_organization_external_sync(organization_id)
        standalone_custom_object_dtos = sorted(
            [c_obj for c_obj in custom_object_dtos if not c_obj.is_extension],
            key=lambda c_obj: (
                c_obj.custom_object.updated_at or c_obj.custom_object.created_at,
                c_obj.custom_object.created_at,
            ),
            reverse=True,
        )
        extension_custom_object_dtos_by_parent_object_name = {
            c_obj.custom_object.parent_object_name: c_obj
            for c_obj in custom_object_dtos
            if c_obj.is_extension and c_obj.custom_object.parent_object_name
        }
        merged_standard_object_descriptors: list[ObjectDescriptor] = []
        for standard_object_descriptor in default_standard_object_descriptors:
            # After creation or re-creation of the standard object descriptor, it is frozen.
            #   Performing checks on whether recreation is needed
            object_name = standard_object_descriptor.object_identifier.object_name
            custom_object_dto = None
            if object_name in extension_custom_object_dtos_by_parent_object_name:
                custom_object_dto = extension_custom_object_dtos_by_parent_object_name[
                    ExtendableStandardObject(
                        standard_object_descriptor.object_identifier.object_name
                    )
                ]
            object_external_provider: ObjectExternalProvider | None = None
            # Complete list of fields, always constructed but used only if some are augmented
            updated_standard_fields_with_external_provider: list[
                StandardFieldDescriptor | CustomFieldDescriptor
            ] = []
            if org_external_sync_setting and field_origin_map.has_object_origin(
                object_name
            ):
                object_external_provider = (
                    self._update_standard_fields_with_external_provider(
                        org_external_sync_setting.sync_mode,
                        org_external_sync_setting.crm,
                        standard_object_descriptor,
                        object_name,
                        updated_standard_fields_with_external_provider,
                    )
                )
            if custom_object_dto is not None or object_external_provider:
                descriptor = recreate_standard_object_descriptor_with_updated_fields(
                    standard_object_descriptor=standard_object_descriptor,
                    custom_object_dto=custom_object_dto,
                    updated_standard_fields=updated_standard_fields_with_external_provider
                    if object_external_provider
                    else None,
                    updated_standard_fields_obj_ext_provider=object_external_provider,
                )
            else:
                descriptor = standard_object_descriptor

            # Add relationships to the descriptor
            descriptor_with_relationships = self.add_relationships_to_descriptor(
                descriptor=descriptor,
                associations=associations,
            )
            merged_standard_object_descriptors.append(descriptor_with_relationships)

        # Process standalone custom objects
        for c_obj in standalone_custom_object_dtos:
            c_descriptor = custom_object_descriptor_from_custom_object_dto(
                custom_object_dto=c_obj,
            )
            # Add relationships to custom object descriptor
            descriptor_with_relationships = self.add_relationships_to_descriptor(
                descriptor=c_descriptor,
                associations=associations,
            )
            merged_standard_object_descriptors.append(descriptor_with_relationships)

        final_object_descriptors = self._update_junction_object_relationships(
            merged_standard_object_descriptors
        )
        return OrganizationSchemaDescriptor(
            organization_id=organization_id,
            objects=final_object_descriptors,
        )

    def _update_junction_object_relationships(
        self,
        descriptors: Sequence[ObjectDescriptor],
    ) -> Sequence[ObjectDescriptor]:
        """
        Update the relationships of a junction object.
        """
        descriptors_by_object_id: dict[ObjectIdentifier, ObjectDescriptor] = {
            descriptor.object_identifier: descriptor for descriptor in descriptors
        }
        result: list[ObjectDescriptor] = []
        for descriptor in descriptors:
            is_inbound_relationship_updated = False
            updated_inbound_relationships: list[InboundRelationship] = []
            for relationship in descriptor.inbound_relationships:
                if (
                    related_object := descriptors_by_object_id.get(
                        relationship.related_object_identifier
                    )
                ) and (
                    related_object.is_junction_object
                    != relationship.is_related_object_junction
                ):
                    updated_inbound_relationships.append(
                        strict_model_copy(
                            relationship,
                            is_related_object_junction=related_object.is_junction_object,
                        )
                    )
                    is_inbound_relationship_updated = True
                else:
                    updated_inbound_relationships.append(relationship)
            if is_inbound_relationship_updated:
                result.append(
                    strict_model_copy(
                        descriptor,
                        inbound_relationships=tuple(updated_inbound_relationships),
                    )
                )
            else:
                result.append(descriptor)

        return result

    def _update_standard_fields_with_external_provider(
        self,
        default_org_sync_mode: SyncMode | None,
        default_external_provider: CrmProvider,
        standard_object_descriptor: StandardObjectDescriptor,
        object_name: str,
        updated_standard_fields: list[StandardFieldDescriptor | CustomFieldDescriptor],
    ) -> ObjectExternalProvider | None:
        """
        Returns an object-level external provider description if there are any
          fields augmented with an external provider.  Otherwise, if no fields
          have an external provider, returns None.
        """
        first_object_external_provider: ObjectExternalProvider | None = None
        for field in standard_object_descriptor.fields:
            field_identifier = field.field_identifier
            is_appended = False
            if isinstance(field_identifier, StandardFieldIdentifier):
                field_origin_provider = field_origin_map.get_first_field_origin(
                    reevo_obj=object_name,
                    field_name=field_identifier.field_name,
                )
                if (
                    field_origin_provider
                    and default_external_provider == field_origin_provider.provider
                ):
                    # Filter by external provider
                    updated_standard_fields.append(
                        self._recreate_field_with_external_provider(
                            field=field,
                            field_origin_provider=field_origin_provider,
                            sync_mode=default_org_sync_mode,
                        )
                    )
                    is_appended = True
                    if not first_object_external_provider:
                        first_object_external_provider = ObjectExternalProvider(
                            provider=field_origin_provider.provider,
                            provider_obj=field_origin_provider.provider_obj,
                            sync_mode=default_org_sync_mode,
                        )
                    elif (
                        first_object_external_provider.provider
                        != field_origin_provider.provider
                        or first_object_external_provider.provider_obj
                        != field_origin_provider.provider_obj
                        or first_object_external_provider.sync_mode
                        != default_org_sync_mode
                    ):
                        logger.bind(
                            reevo_obj=object_name,
                            first_object_external_provider=first_object_external_provider,
                            curr_field_name=field_identifier.field_name,
                            curr_field_origin_provider=field_origin_provider,
                        ).info(
                            "A curiosity. Found multiple provider sources populating a "
                            "single reevo object. Taking the first one and ignoring the "
                            "rest."
                        )
            if not is_appended:
                updated_standard_fields.append(field)  # no change to field
        return first_object_external_provider

    def _recreate_field_with_external_provider(
        self,
        field: FieldDescriptor,
        field_origin_provider: ProviderField,
        sync_mode: SyncMode | None,
    ) -> FieldDescriptor:
        return field.model_copy(
            deep=True,
            update={
                "field_type_property": field.field_type_property.model_copy(
                    deep=True,
                    update={
                        "external_provider": FieldExternalProvider(
                            provider=field_origin_provider.provider,
                            provider_obj=field_origin_provider.provider_obj
                            if field_origin_provider.provider_obj
                            else None,
                            provider_obj_field=field_origin_provider.provider_obj_field,
                            sync_mode=sync_mode,
                        )
                    },
                )
            },
        )

    async def get_object_descriptor_v2(
        self, organization_id: UUID, object_identifier: ObjectIdentifier
    ) -> ObjectDescriptor:
        if isinstance(object_identifier, StandardObjectIdentifier):
            if not (
                found
                := self.default_domain_object_schema_provider.std_object_default_descriptor_by_id(
                    object_id=object_identifier
                )
            ):
                raise ResourceNotFoundError(
                    f"Object descriptor not found for {object_identifier}"
                )

            extension_status = (
                await self.custom_object_service.get_extension_custom_object_statuses(
                    organization_id=organization_id
                )
            )
            if (
                object_identifier.object_name in ExtendableStandardObject
                and extension_status.get(
                    ExtendableStandardObject(object_identifier.object_name), None
                )
            ):
                extension_custom_object = (
                    await self.custom_object_service.get_custom_object(
                        organization_id=organization_id,
                        custom_object_id_or_parent_object_name=ExtendableStandardObject(
                            object_identifier.object_name
                        ),
                    )
                )
                found = recreate_standard_object_descriptor_with_updated_fields(
                    standard_object_descriptor=found,
                    custom_object_dto=extension_custom_object,
                    updated_standard_fields=None,
                    updated_standard_fields_obj_ext_provider=None,
                )
            return found
        else:
            raise NotImplementedError(
                f"Custom object descriptor not supported for {object_identifier}"
            )

    async def validate_field_path_by_std_object_model_v2(
        self,
        *,
        organization_id: UUID,
        object_model: type[DomainModel],
        field_path: FieldPath,
    ) -> None:
        if not (
            object_descriptor := (
                self.default_domain_object_schema_provider.std_object_default_descriptor_by_model(
                    object_model=object_model
                )
            )
        ):
            raise InvalidArgumentError(
                f"Object descriptor not found for {object_model}"
            )
        await self.validate_field_path_v2(
            organization_id=organization_id,
            object_identifier=object_descriptor.object_identifier,
            field_path=field_path,
        )

    async def validate_field_set(
        self,
        *,
        organization_id: UUID,
        object_identifier: ObjectIdentifier,
        fields: set[QualifiedField | FieldReference],
        validate_sortable: bool = False,
    ) -> None:
        for field in fields:
            await self.validate_field(
                organization_id=organization_id,
                object_identifier=object_identifier,
                field=field,
                validate_sortable=validate_sortable,
            )

    async def validate_field(
        self,
        *,
        organization_id: UUID,
        object_identifier: ObjectIdentifier,
        field: QualifiedField | FieldReference,
        validate_sortable: bool = False,
    ) -> ValidateFieldResult | None:
        if isinstance(object_identifier, CustomObjectIdentifier):
            return None
        if isinstance(field, QualifiedField):
            return await self.validate_field_path_v2(
                organization_id=organization_id,
                object_identifier=object_identifier,
                field_path=field.path,
                validate_sortable=validate_sortable,
            )
        elif isinstance(field, FieldReference):
            current_object_descriptor = await self.get_object_descriptor_v2(
                organization_id=organization_id, object_identifier=object_identifier
            )
            if isinstance(field.relationship_id, UUID):
                # Early return, since we don't support custom object yet
                return None
            elif not (
                relationship := current_object_descriptor.relationship(
                    relationship_id=field.relationship_id
                )
            ):
                raise InvalidArgumentError(
                    f"Relationship {field.relationship_id} not found for {field}"
                )
            return await self.validate_field(
                organization_id=organization_id,
                object_identifier=relationship.related_object_identifier,
                field=field.field,
                validate_sortable=validate_sortable,
            )
        else:
            assert_never(field)

    async def validate_field_path_v2(  # noqa: C901
        self,
        *,
        organization_id: UUID,
        object_identifier: ObjectIdentifier,
        field_path: FieldPath,
        validate_sortable: bool = False,
    ) -> ValidateFieldResult | None:
        last_field_part_idx = len(field_path) - 1
        result: ValidateFieldResult | None = None
        resulting_field_path_start_idx = 0
        if not field_path:
            return None
        # step 1: ensure current object descriptor is found
        _object_descriptor = await self.get_object_descriptor_v2(
            organization_id=organization_id,
            object_identifier=object_identifier,
        )
        _object_identifier: ObjectIdentifier | None = object_identifier
        _field_descriptor: FieldDescriptor | None = None
        _field_type_property: FieldTypeProperty | None = None
        for fp_idx, fp in enumerate(field_path):  # fp: short for 'field_part'
            # If there is no current object identifier,
            # but we are still in the field path,
            # it means the field path is invalid, since no next node to visit
            if not _object_identifier:
                raise InvalidArgumentError(
                    f"Field not found for '{fp}' in {field_path}"
                )

            # todo(xw): Not support custom object yet.
            if not isinstance(_object_identifier, StandardObjectIdentifier):
                raise NotImplementedError(
                    f"Custom object descriptor not supported for {object_identifier}"
                )

            # step 2: ensure current field descriptor is found
            if not (
                _field_descriptor := _object_descriptor.field_descriptor(
                    field_identifier(field_name_or_id=fp)
                )
            ):
                raise InvalidArgumentError(
                    f"Field descriptor not found for {fp} in {field_path} of "
                    f"object {object_identifier} and org {organization_id}"
                )
            _field_type_property = _field_descriptor.field_type_property

            # step 3: when field path reference an array or dict element,
            # we directly pass down the element field type property
            if isinstance(_field_type_property, ListFieldProperty):
                _field_type_property = _field_type_property.element_field_type_property
            elif isinstance(_field_type_property, DictFieldProperty):
                _field_type_property = _field_type_property.value_field_type_property

            # step 4: if validate_sortable is set and this is the last field in the field path,
            # we need to ensure the field is sortable, since we won't get to the
            # element fields of current field anymore
            if (
                validate_sortable
                and (fp_idx >= last_field_part_idx)
                and (not not_none(_field_type_property).is_sortable)
            ):
                raise InvalidArgumentError(
                    f"Field '{fp}' from '{field_path}' of {object_identifier} is not sortable"
                )

            # if we are finding a nested object, let next iteration handle it
            if isinstance(_field_type_property, NestedObjectFieldProperty):
                _object_identifier = _field_type_property.object_identifier
                resulting_field_path_start_idx = fp_idx + 1
                _object_descriptor = await self.get_object_descriptor_v2(
                    organization_id=organization_id,
                    object_identifier=_object_identifier,
                )
            else:
                # todo(xw): handle complex object field type such as GeoLocation
                _object_identifier = None
            # when we are at the last field part, we can assemble the result
            if fp_idx == last_field_part_idx:
                result = ValidateFieldResult(
                    object_descriptor=_object_descriptor,
                    field_path=field_path[resulting_field_path_start_idx:],
                )
        return result


def get_metadata_service(*, db_engine: DatabaseEngine) -> MetadataService:
    return MetadataService(
        custom_object_service=get_custom_object_service(db_engine=db_engine),
        association_service=get_association_service(db_engine=db_engine),
        crm_sync_repository=CRMSyncRepository(engine=db_engine),
    )
