import time
from collections.abc import Callable, Coroutine
from typing import cast
from uuid import uuid4

from salestech_be.common.events import (
    DomainEnrichedCDCEvent,
    EnrichedCDCEventProcessor,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.ai.event_handlers.pipeline_intel import (
    start_meeting_intel_workflow,
)
from salestech_be.core.contact.service.contact_query_service import ContactQueryService
from salestech_be.core.contact.service.contact_service import ContactService
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.logical_propagation.service.trigger import (
    LogicalPropagationTriggerService,
)
from salestech_be.core.logical_propagation.types import (
    CoreObjectChangeEvent,
)
from salestech_be.core.meeting.meeting_service import MeetingService
from salestech_be.core.research_agent.research_cdc_service import (
    person_research_handler,
)
from salestech_be.core.task.service.task_v2_service import TaskV2Service
from salestech_be.core.workflow.service.workflow_trigger_service import (
    WorkflowTriggerService,
)
from salestech_be.core.workflow.types.trigger_event_schema import (
    ResourceChangeTriggerEventSchema,
)
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.workflow import (
    WorkflowResourceAction,
    WorkflowTriggerResourceType,
)
from salestech_be.integrations.kafka.types import (
    CDCObject,
    CDCObjectState,
    ContactExtensionView,
    ContactView,
)
from salestech_be.ree_logging import get_logger
from salestech_be.search.indexing.cdc_event_handlers import (
    index_contact_handler,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import one_row_only

logger = get_logger(__name__)


HANDLER_TIMING_METRIC_NAME = "contact_cdc_event_processor_handler_timing"
HANDLER_TAG_NAME = "handler_name"


class ContactCDCEventProcessor(EnrichedCDCEventProcessor[Contact, ContactV2]):
    def __init__(
        self,
        contact_service: ContactService,
        trigger_event_service: WorkflowTriggerService,
        logical_propagation_trigger_service: LogicalPropagationTriggerService,
        contact_query_service: ContactQueryService,
        task_v2_service: TaskV2Service,
        meeting_service: MeetingService,
    ):
        self.contact_service = contact_service
        self.trigger_event_service = trigger_event_service
        self.logical_propagation_trigger_service = logical_propagation_trigger_service
        self.contact_query_service = contact_query_service
        self.task_v2_service = task_v2_service
        self.meeting_service = meeting_service

    def get_table_model(self, event: CDCObject) -> Contact:
        return Contact.model_validate(
            event.model_dump(
                exclude={
                    "view_model",
                    "addresses",
                    "contact_account_associations",
                    "contact_emails",
                    "contact_email_account_associations",
                }
            )
        )

    async def get_domain_model(
        self, event: CDCObject, event_state: CDCObjectState
    ) -> ContactV2:
        contact_view = cast(ContactView | ContactExtensionView, event)
        if event_state == CDCObjectState.BEFORE:
            return one_row_only(
                await self.contact_query_service._list_full_contacts_v2(  # noqa: SLF001
                    db_contacts=[self.get_table_model(event)],
                    organization_id=contact_view.organization_id,
                )
            )
        else:
            return await self.contact_service.get_contact_v2(
                contact_id=contact_view.id, organization_id=contact_view.organization_id
            )

    async def _handle_workflow_triggers(
        self, event: DomainEnrichedCDCEvent[Contact, ContactV2]
    ) -> None:
        """Handle workflow trigger events."""

        try:
            start_time = time.perf_counter()
            await self.trigger_event_service.process_resource_change_trigger_event(
                ResourceChangeTriggerEventSchema(
                    idempotent_key=str(uuid4()),
                    event_time=zoned_utc_now(),
                    organization_id=event.after.organization_id,
                    resource=WorkflowTriggerResourceType.CONTACT,
                    action=WorkflowResourceAction.UPDATE
                    if event.before
                    else WorkflowResourceAction.CREATE,
                    before=event.before.model_dump(mode="json")
                    if event.before
                    else None,
                    after=event.after.model_dump(mode="json"),
                )
            )
        except Exception as e:
            logger.error("Error processing workflow triggers", exc_info=e)
        finally:
            custom_metric.timing(
                metric_name=HANDLER_TIMING_METRIC_NAME,
                value=(time.perf_counter() - start_time) * 1000,
                tags=[f"{HANDLER_TAG_NAME}:_handle_workflow_triggers"],
            )

    async def _handle_logical_propagation(
        self, event: DomainEnrichedCDCEvent[Contact, ContactV2]
    ) -> None:
        """Handle logical propagation events."""

        try:
            start_time = time.perf_counter()
            await self.logical_propagation_trigger_service.process_core_object_changes_event(
                event=CoreObjectChangeEvent(
                    idempotent_key=str(uuid4()),
                    event_time=zoned_utc_now(),
                    organization_id=event.after.organization_id,
                    entity_type=StdObjectIdentifiers.contact,
                    entity_id=event.after.id,
                    before_domain_model=event.before_domain_model,
                    current_domain_model=event.current_domain_model,
                )
            )
        except Exception as e:
            logger.error("Error processing logical propagation", exc_info=e)
        finally:
            custom_metric.timing(
                metric_name=HANDLER_TIMING_METRIC_NAME,
                value=(time.perf_counter() - start_time) * 1000,
                tags=[f"{HANDLER_TAG_NAME}:_handle_logical_propagation"],
            )

    async def _handle_research_and_indexing(
        self, event: DomainEnrichedCDCEvent[Contact, ContactV2]
    ) -> None:
        """Handle research and indexing events."""
        try:
            start_time = time.perf_counter()
            if settings.enable_research_agent_on_person:
                await person_research_handler(event)
            if settings.enable_search_indexing:
                await index_contact_handler(event)
        except Exception as e:
            logger.error("Error processing research and indexing", exc_info=e)
        finally:
            custom_metric.timing(
                metric_name=HANDLER_TIMING_METRIC_NAME,
                value=(time.perf_counter() - start_time) * 1000,
                tags=[f"{HANDLER_TAG_NAME}:_handle_research_and_indexing"],
            )

    async def _handle_notifications(
        self, event: DomainEnrichedCDCEvent[Contact, ContactV2]
    ) -> None:
        """Handle notification events."""
        try:
            if event.before_domain_model:
                await self.contact_service.send_contact_change_notification(
                    event.before_domain_model, event.current_domain_model
                )
        except Exception as e:
            logger.error("Error processing notifications", exc_info=e)

    async def _handle_pipeline_intel(
        self, event: DomainEnrichedCDCEvent[Contact, ContactV2]
    ) -> None:
        """Handle pipeline intelligence events."""

        try:
            start_time = time.perf_counter()
            primary_account_id = event.current_domain_model.primary_account_id
            if not primary_account_id:
                return
            # Get active pipeline associations for the contact
            pipeline_identifiers = (
                await self.contact_service.list_active_pipelines_by_contact_id(
                    organization_id=event.after.organization_id,
                    contact_id=event.after.id,
                    account_id=primary_account_id,
                )
            )

            if pipeline_identifiers:
                # Start pipeline intel workflow for each active pipeline association
                for pipeline_identifier in pipeline_identifiers:
                    await start_meeting_intel_workflow(
                        pipeline_id=pipeline_identifier.pipeline_id,
                        organization_id=event.after.organization_id,
                        account_id=primary_account_id,
                        meeting_id=None,
                        contact_id=event.after.id,
                        global_thread_id=None,
                    )
        except Exception as e:
            logger.error("Error processing pipeline intel", exc_info=e)
        finally:
            custom_metric.timing(
                metric_name=HANDLER_TIMING_METRIC_NAME,
                value=(time.perf_counter() - start_time) * 1000,
                tags=[f"{HANDLER_TAG_NAME}:_handle_pipeline_intel"],
            )

    async def get_domain_enriched_event_handlers(
        self,
        domain_event: DomainEnrichedCDCEvent[Contact, ContactV2],
    ) -> list[
        Callable[
            [DomainEnrichedCDCEvent[Contact, ContactV2]], Coroutine[None, None, None]
        ]
    ]:
        """
        Returns a list of event handler functions that will process the domain event.

        The return type is a list of callable functions where:
        - Each function takes a DomainEnrichedCDCEvent[Contact, ContactV2] as its parameter
        - Each function returns a Coroutine that eventually resolves to None
        - These handlers will be executed concurrently when processing the event
        """
        return [
            self._handle_workflow_triggers,
            self._handle_logical_propagation,
            self._handle_research_and_indexing,
            self._handle_notifications,
            self._handle_pipeline_intel,
        ]

    async def process_domain_enriched_event(
        self, event: DomainEnrichedCDCEvent[Contact, ContactV2]
    ) -> ContactV2:
        logger.bind(event_before=event.before, event_after=event.after).info(
            "Processing domain[contact] enriched event"
        )

        # Use the base class's handler processing
        return await self.process_domain_enriched_event_handlers(event)
