from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import asyncio
    import time

    from salestech_be.common.stats.metric import custom_metric
    from salestech_be.core.ai.backfill.workflow_inputs.backfill_input import (
        BackfillInput,
    )
    from salestech_be.core.ai.backfill.workflows.backfill_workflow import (
        BackfillWorkflow,
    )
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
    from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def start_backfill_workflow(
    backfill_input: BackfillInput,
) -> None:
    """Start the workflow to backfill messages.

    Args:
        backfill_input: The input for the backfill workflow
    """
    process_start_time = time.perf_counter()
    client = await get_temporal_client()
    custom_metric.timing(
        metric_name="backfill_get_temporal_client",
        value=(time.perf_counter() - process_start_time) * 1000,
        tags=[
            "backfill_flow:start_backfill_workflow",
        ],
    )

    workflow_id = BackfillWorkflow.get_workflow_id()

    logger.info(
        "Starting backfill workflow",
        extra={
            "workflow_id": workflow_id,
        },
    )

    process_start_time = time.perf_counter()
    try:
        await client.start_workflow(
            BackfillWorkflow.run,
            id=workflow_id,
            task_queue=DEFAULT_TASK_QUEUE,
            args=[backfill_input],
        )
        custom_metric.timing(
            metric_name="backfill_workflow_start_workflow",
            value=(time.perf_counter() - process_start_time) * 1000,
            tags=[
                "workflow:backfill_workflow",
                "status:success",
            ],
        )
    except Exception as e:
        custom_metric.timing(
            metric_name="backfill_workflow_start_workflow",
            value=(time.perf_counter() - process_start_time) * 1000,
            tags=[
                "workflow:backfill_workflow",
                "status:error",
            ],
        )
        logger.error(
            "Failed to start backfill workflow",
            exc_info=e,
            extra={
                "workflow_id": workflow_id,
            },
        )
        raise


if __name__ == "__main__":
    asyncio.run(start_backfill_workflow(BackfillInput(force_reprocess=False)))
