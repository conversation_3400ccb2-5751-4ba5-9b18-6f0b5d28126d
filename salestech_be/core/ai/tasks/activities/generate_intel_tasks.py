import textwrap
from datetime import UTC, datetime, timedelta
from uuid import UUID

from anthropic.types import (
    CitationCharLocation,
    CitationContentBlockLocation,
    CitationPageLocation,
    TextBlock,
)
from pydantic import BaseModel, ValidationError
from temporalio import activity

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
    anthropic_completion,
    get_max_tokens,
)
from salestech_be.core.ai.activities.meeting_pipeline_intel_activities import (
    DedupRequestListFromLLM,
    TaskRequestFromLLM,
    TaskRequestListFromLLM,
)
from salestech_be.core.ai.citations.schema import (
    EmailTaskCitationDTO,
    MeetingTaskCitationDTO,
)
from salestech_be.core.ai.common.helpers.check_token_count import get_token_count
from salestech_be.core.ai.common.helpers.get_intel_context import get_intel_context
from salestech_be.core.ai.common.helpers.ree_llm_retry import (
    ReeLLMRetryError,
    ree_llm_retry,
)
from salestech_be.core.ai.common.llm_types import ModelTypes, VertexModelTypes
from salestech_be.core.ai.common.types import (
    EmailAugmented,
    IntelContext,
    MeetingAugmented,
    TaskPriorityResponse,
    VoiceCallAugmented,
)
from salestech_be.core.ai.email.activities.get_latest_message_from_global_thread import (
    get_latest_message,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.ai.prompt.utils import get_prompt_name
from salestech_be.core.ai.tasks.llm_calls.generate_task_ownership_llm_call import (
    generate_task_ownership_llm_call,
)
from salestech_be.core.ai.tasks.llm_calls.generate_tasks_from_conversation_llm_call import (
    generate_tasks_from_conversation_llm_call,
)
from salestech_be.core.ai.tasks.llm_calls.generate_tasks_from_email_llm_call import (
    generate_tasks_from_email_llm_call,
)
from salestech_be.core.ai.tasks.llm_calls.generate_tasks_from_objection_llm_call import (
    generate_tasks_from_objection_llm_call,
)
from salestech_be.core.ai.workflows.schema import (
    IntelInput,
    IntelTriggerObjectType,
)
from salestech_be.core.citation.service.citation_service import get_citation_service
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.ff.feature_flag_service import get_feature_flag_service
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.core.task.service.task_v2_service import (
    get_task_v2_service_general,
)
from salestech_be.core.task.types_v2 import TaskV2
from salestech_be.core.user.service.user_preference_service import (
    user_preference_service_from_engine,
)
from salestech_be.db.dao.message_metadata_repository import MessageMetadataRepository
from salestech_be.db.dao.thread_repository import ThreadRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.citation import (
    Citation,
    CitationForObjectType,
    CitationSourceType,
    EmailCitationMetadata,
    MeetingCitationMetadata,
)
from salestech_be.db.models.insight import Insight, InsightReferenceIdType
from salestech_be.db.models.message_metadata import MessageClassification
from salestech_be.db.models.task import (
    TaskPriority,
    TaskSourceType,
    TaskStatus,
)
from salestech_be.db.models.voice_v2 import Call
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.web.api.task.schema import (
    CreateTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.user.schema import (
    TaskGenerationPreferences,  # fmt: skip # tach-ignore(TODO: complete service layer isolation)
    UserPreferenceKeys,  # fmt: skip # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger()

langfuse_prompt_service = get_langfuse_prompt_service()
ff_service = get_feature_flag_service()


@ree_llm_retry(retry_delay=2)
async def dedup_tasks(
    intel_input: IntelInput,
    intel_context: IntelContext,
    task_request_list: TaskRequestListFromLLM,
    prompt_name: str,
    *,
    attempt_num: int = 0,
    last_error: ReeLLMRetryError | None = None,
    all_errors: list[ReeLLMRetryError] | None = None,
    bypass_dedup: bool = False,
) -> list[TaskRequestFromLLM]:
    """Remove duplicate tasks from the generated task list."""
    prompt_info = langfuse_prompt_service.get_prompt_info(prompt_name)
    dedup_prompt_vars = await intel_context.to_prompt_variables(
        prompt_name=PromptEnum.CHECK_GENERATED_TASKS_ALREADY_EXIST,
        prompt=prompt_info,
        generated_tasks=task_request_list.tasks,
    )

    dedup_prompt_response = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.CHECK_GENERATED_TASKS_ALREADY_EXIST,
            variables=dedup_prompt_vars,
        )
    )

    if last_error is not None:
        extra_caution = f"The previous attempt failed with the following error: {last_error.message}. Please be extra cautious and ensure the response has the correct structure and the response list length matches the number of new tasks"
    else:
        extra_caution = "Please be extra cautious and ensure the response has the correct structure and the response list length matches the number of new tasks"

    dedup_response = await acompletion(
        model=dedup_prompt_response.get_model(),
        messages=[
            *dedup_prompt_response.messages,
            {
                "role": "user",
                "content": extra_caution,
            },
        ],
        temperature=0,
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "dedup_tasks",
                    "description": "A tool that checks two lists of tasks to see if there are duplicates",
                    "parameters": DedupRequestListFromLLM.model_json_schema(),
                },
            }
        ],
        metadata=LLMTraceMetadata(
            trace_name="intel.dedup_tasks",
            session_id=intel_input.langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(intel_input.organization_id),
                prompt_use_case=PromptUseCase.INTEL_TASKS,
                workflow_id=activity.info().workflow_id
                if not intel_input.non_temporal
                else "test_stub",
            ),
        ),
        tool_choice="any",
    )

    if not dedup_response.choices or not dedup_response.tool_calls:
        return task_request_list.tasks

    dedup_task_json = dedup_response.tool_calls[0].function.arguments
    if not isinstance(dedup_task_json, str):
        raise ValueError("Dedup tool call arguments is not a string")

    try:
        is_dup_list = DedupRequestListFromLLM.model_validate_json(dedup_task_json)
    except ValidationError as e:
        raise ReeLLMRetryError(message="Invalid response structure") from e

    if len(is_dup_list.model_dump()["tasks"]) != len(task_request_list.tasks):
        raise ReeLLMRetryError(
            message="Response list length did not match input task list length"
        )

    # Go through task_list and if its corresponding entry in is_dup_list is true, remove it
    cleaned_tasks = []
    for i, task in enumerate(task_request_list.tasks):
        if not is_dup_list.tasks[i].is_duplicate or bypass_dedup:
            cleaned_tasks.append(task)
        else:
            logger.info(
                f"Skipping duplicate task '{task.title}'. Reason: {is_dup_list.tasks[i].reason}"
            )

    return cleaned_tasks


async def is_task_creation_enabled(
    organization_id: UUID,
    user_id: UUID,
    intel_context: IntelContext,
) -> bool:
    """Check if task creation is enabled for the given object type."""
    db_engine = await get_or_init_db_engine()
    user_preference_service = user_preference_service_from_engine(db_engine=db_engine)
    task_settings = await user_preference_service.get_user_preference(
        organization_id=organization_id,
        user_id=user_id,
        key=UserPreferenceKeys.TASKS,
    )
    if task_settings.tasks and isinstance(
        task_settings.tasks.task_generation, TaskGenerationPreferences
    ):
        task_generation_preferences = task_settings.tasks.task_generation
        match intel_context.primary_object:
            case MeetingAugmented():
                return (
                    task_generation_preferences.meeting_tasks_enabled
                    if task_generation_preferences.meeting_tasks_enabled is not None
                    else True
                )
            case EmailAugmented():
                return (
                    task_generation_preferences.email_tasks_enabled
                    if task_generation_preferences.email_tasks_enabled is not None
                    else True
                )
            case Call():
                return (
                    task_generation_preferences.call_tasks_enabled
                    if task_generation_preferences.call_tasks_enabled is not None
                    else True
                )
            case Insight():
                return (
                    task_generation_preferences.objection_tasks_enabled
                    if task_generation_preferences.objection_tasks_enabled is not None
                    else True
                )
            case _:
                return True
    return True


async def create_task_from_request(  # noqa: C901, PLR0912, PLR0915
    task_request: TaskRequestFromLLM,
    intel_input: IntelInput,
    intel_context: IntelContext,
    object_content: str,
    prefix: str = "",
) -> TaskV2 | None:
    db_engine: DatabaseEngine = await get_or_init_db_engine()
    task_v2_service = get_task_v2_service_general(db_engine=db_engine)
    contact_query_service = get_contact_query_service(db_engine=db_engine)

    """Create a single task from a task request."""
    email_thread_id = (
        intel_input.object_id
        if intel_input.object_type == IntelTriggerObjectType.GLOBAL_THREAD
        else None
    )
    meeting_id = (
        intel_input.object_id
        if intel_input.object_type == IntelTriggerObjectType.MEETING
        else None
    )
    insight_id = (
        intel_input.object_id
        if intel_input.object_type == IntelTriggerObjectType.OBJECTION
        else None
    )

    # Check if IntelContext is a VoiceCallAugmented
    if intel_context.primary_object and isinstance(
        intel_context.primary_object, VoiceCallAugmented
    ):
        voice_call_id = intel_context.primary_object.call.id
    else:
        voice_call_id = None

    priority = task_request.priority
    if settings.enable_task_priority:
        priority = await generate_task_priority(
            intel_input, task_request, object_content
        )
    due_date = None
    if settings.enable_task_due_date:
        try:
            due_date = await generate_task_due_date(
                intel_input, task_request, object_content
            )
        except Exception as e:
            logger.error(f"Error generating due date: {e}")
    # Default to pipeline owner or organization creator
    if intel_context.pipeline:
        ownership_id = intel_context.pipeline.owner_user_id
    elif intel_context.organization:
        ownership_id = intel_context.organization.created_by_user_id
    else:
        raise ValueError("No ownership id found")

    # Use task ownership from source of insight if it exists
    if (
        intel_input.object_type == IntelTriggerObjectType.OBJECTION
        and isinstance(intel_context.primary_object, Insight)
        and intel_context.primary_object.reference_id is not None
        and intel_context.primary_object.reference_type is not None
    ):
        if (
            intel_context.primary_object.reference_type
            == InsightReferenceIdType.MEETING
        ):
            intel_context_source_type = IntelTriggerObjectType.MEETING
        elif (
            intel_context.primary_object.reference_type == InsightReferenceIdType.THREAD
        ):
            intel_context_source_type = IntelTriggerObjectType.GLOBAL_THREAD
        else:
            raise ValueError("Invalid reference type")

        intel_input_source = IntelInput(
            organization_id=intel_input.organization_id,
            object_id=intel_context.primary_object.reference_id,
            object_type=intel_context_source_type,
            pipeline_id=intel_input.pipeline_id,
            account_ids=intel_input.account_ids,
            langfuse_session_id=intel_input.langfuse_session_id,
            bypass_dedup=intel_input.bypass_dedup,
            non_temporal=intel_input.non_temporal,
            test_stub=intel_input.test_stub,
            output_path=intel_input.output_path,
        )
        intel_context_source = await get_intel_context(intel_input_source)
    else:
        intel_input_source = intel_input
        intel_context_source = intel_context

    try:
        logger.info("Generating task ownership")
        ownership_id = UUID(
            await generate_task_ownership(
                intel_input=intel_input_source,
                task=task_request,
                intel_context=intel_context_source,
                object_content=object_content,
                workflow_id=activity.info().workflow_id
                if not intel_input.non_temporal
                else "test_stub",
            )
        )
    except Exception as e:
        logger.error("Error generating task ownership", exc_info=e)
        # Fall back to default ownership_id

    creation_enabled = await is_task_creation_enabled(
        intel_input.organization_id, ownership_id, intel_context
    )
    if not creation_enabled:
        logger.bind(
            organization_id=intel_input.organization_id,
            user_id=ownership_id,
            object_id=intel_input.object_id,
            object_type=intel_input.object_type,
        ).info("Task generation is disabled for this object")
        return None

    contact_ids = (
        [c.id for c in intel_context.contacts] if intel_context.contacts else None
    )

    account_id = None
    if intel_context.accounts:
        account_id = intel_context.accounts[0].id if intel_context.accounts else None
        # Determine which account id has a reference to contact_ids
        for account in intel_context.accounts:
            account_contacts = await contact_query_service.list_active_contact_associations_for_account(
                organization_id=intel_input.organization_id,
                account_id=account.id,
            )
            account_contact_ids = {assoc.contact_id for assoc in account_contacts}
            if contact_ids and set(contact_ids) & account_contact_ids:
                account_id = account.id
                break

    return await task_v2_service.insert_task_v2(
        created_by_user_id=ownership_id,
        organization_id=intel_input.organization_id,
        request=CreateTaskRequest(
            owner_user_id=ownership_id,
            title=f"{prefix}: {task_request.title}" if prefix else task_request.title,
            pipeline_id=intel_input.pipeline_id,
            meeting_id=meeting_id,
            account_id=account_id,
            contact_ids=contact_ids,
            due_at=due_date,
            email_thread_ids=[email_thread_id] if email_thread_id else [],
            status=TaskStatus.OPEN,
            priority=priority,
            type=task_request.type,
            note=task_request.note,
            source_type=TaskSourceType.SYSTEM,
            insight_id=insight_id,
            voice_call_id=voice_call_id,
        ),
    )


async def generate_task_citations(
    intel_input: IntelInput,
    tasks: list[TaskV2],
    intel_context: IntelContext,
) -> list[MeetingTaskCitationDTO] | list[EmailTaskCitationDTO] | list[None]:
    if intel_input.object_type == IntelTriggerObjectType.MEETING:
        return await generate_meeting_task_citations(intel_input, tasks, intel_context)
    elif intel_input.object_type == IntelTriggerObjectType.GLOBAL_THREAD:
        return await generate_email_task_citations(intel_input, tasks, intel_context)
    elif intel_input.object_type == IntelTriggerObjectType.OBJECTION:
        return await generate_objection_task_citations(
            intel_input, tasks, intel_context
        )
    else:
        return []


@activity.defn
async def generate_intel_tasks(  # noqa: C901, PLR0911, PLR0912
    intel_input: IntelInput,
) -> str:
    """Generate the tasks for the meeting pipeline intel."""
    db_engine = await get_or_init_db_engine()
    intel_context = await get_intel_context(intel_input)
    logger.info(f"Intel context: {intel_context}")
    message_metadata_repo = MessageMetadataRepository(engine=db_engine)
    bypass_dedup = intel_input.bypass_dedup

    # Check if intel_input.object_type is email and if so
    # check if the email is a SALES email
    # If not, return
    if intel_input.object_type == IntelTriggerObjectType.GLOBAL_THREAD:
        if intel_input.test_stub:
            thread_messages = intel_input.test_stub
        else:
            thread_repository = ThreadRepository(engine=db_engine)
            thread_messages = await thread_repository.list_messages_by_global_thread_id(
                global_thread_id=intel_input.object_id,
                organization_id=intel_input.organization_id,
            )

        logger.info(f"Thread messages: {thread_messages}")

        # Get latest message from global thread
        latest_message = await get_latest_message(
            messages=thread_messages,
        )
        logger.info(f"Latest message: {latest_message}")

        # Retrieve latest message in thread
        if latest_message is None:
            return "No messages found in global thread"

        message_metadata = (
            await message_metadata_repo.get_latest_message_metadata_by_message_id(
                organization_id=intel_input.organization_id,
                message_id=latest_message.id,
            )
        )
        logger.info(f"Message metadata: {message_metadata}")
        if (
            message_metadata is None
            or message_metadata.classification != MessageClassification.SALES
        ):
            return "Email is not a sales email"

    if intel_context.organization is None:
        raise ValueError("No organization found")

    prompt_name = get_prompt_name(intel_input.object_type)
    logger.info(f"Prompt name: {prompt_name}")
    logger.info(f"intel_context primary object: {intel_context.primary_object}")

    prompt_info = langfuse_prompt_service.get_prompt_info(prompt_name)
    logger.info(f"Prompt info: {prompt_info}")
    prompt_variables = await intel_context.to_prompt_variables(
        prompt_name=prompt_name,
        prompt=prompt_info,
    )
    logger.info(f"Prompt variables: {prompt_variables}")
    logger.info(
        f"Prompt variables existing tasks: {prompt_variables['existing_tasks']}"
    )
    if not prompt_variables["object_content"]:
        return "No object content found"

    if intel_input.object_type == IntelTriggerObjectType.GLOBAL_THREAD:
        task_request_list = await generate_tasks_from_email_llm_call(
            object_content=prompt_variables["object_content"],
            additional_context=prompt_variables["additional_context"],
            existing_tasks=prompt_variables["existing_tasks"],
            contacts=prompt_variables["contacts"],
            organization_id=intel_input.organization_id,
            langfuse_session_id=intel_input.langfuse_session_id,
            workflow_id=activity.info().workflow_id
            if not intel_input.non_temporal
            else "test_stub",
        )
    elif intel_input.object_type == IntelTriggerObjectType.MEETING:
        task_request_list = await generate_tasks_from_conversation_llm_call(
            object_content=prompt_variables["object_content"],
            additional_context=prompt_variables["additional_context"],
            existing_tasks=prompt_variables["existing_tasks"],
            contacts=prompt_variables["contacts"],
            organization_id=intel_input.organization_id,
            langfuse_session_id=intel_input.langfuse_session_id,
            workflow_id=activity.info().workflow_id
            if not intel_input.non_temporal
            else "test_stub",
        )
    elif intel_input.object_type == IntelTriggerObjectType.OBJECTION:
        task_request_list = await generate_tasks_from_objection_llm_call(
            object_content=prompt_variables["object_content"],
            additional_context=prompt_variables["additional_context"],
            existing_tasks=prompt_variables["existing_tasks"],
            contacts=prompt_variables["contacts"],
            organization_id=intel_input.organization_id,
            langfuse_session_id=intel_input.langfuse_session_id,
            workflow_id=activity.info().workflow_id
            if not intel_input.non_temporal
            else "test_stub",
        )
    else:
        raise ValueError(f"Unsupported object type: {intel_input.object_type}")

    if not task_request_list.tasks:
        return "No tasks generated"

    cleaned_tasks = await dedup_tasks(
        intel_input=intel_input,
        intel_context=intel_context,
        task_request_list=task_request_list,
        prompt_name=prompt_name,
        bypass_dedup=bypass_dedup,
    )
    if not cleaned_tasks:
        return "No unique tasks to generate"

    task_request_list.tasks = cleaned_tasks

    tasks: list[TaskV2] = []
    for task_request in task_request_list.tasks:
        task = await create_task_from_request(
            task_request=task_request,
            intel_input=intel_input,
            intel_context=intel_context,
            object_content=prompt_variables["object_content"],
            prefix="Objection Response"
            if intel_input.object_type == IntelTriggerObjectType.OBJECTION
            else "",
        )
        if task:
            tasks.append(task)

    if not tasks:
        return "No tasks generated"

    await generate_task_citations(intel_input, tasks, intel_context)

    return "\n".join([t.title for t in task_request_list.tasks])


class _TaskDueDateResponse(BaseModel):
    hours_from_now: int
    days_from_now: int
    explanation: str


async def generate_task_due_date(
    intel_input: IntelInput,
    task: TaskRequestFromLLM,
    object_content: str,
) -> datetime | None:
    """Generate the due date for the task."""

    prompt_variables = {
        "task_title": task.title,
        "task_note": task.note,
        "object_content": object_content,
    }

    prompt_response = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.GENERATE_TASK_DUE_DATE,
            variables=prompt_variables,
        )
    )
    # Get the due date suggestion from Claude
    llm_response = await acompletion(
        model="gpt-4o",  # using gpt-4o to avoid the anthropic tool vs response format issue
        messages=prompt_response.messages,
        temperature=0,
        max_completion_tokens=1024,
        response_format=_TaskDueDateResponse,
        metadata=LLMTraceMetadata(
            trace_name=PromptEnum.GENERATE_TASK_DUE_DATE,
            session_id=intel_input.langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(intel_input.organization_id),
                prompt_use_case=PromptUseCase.INTEL_TASKS,
                workflow_id=activity.info().workflow_id
                if not intel_input.non_temporal
                else "test_stub",
            ),
        ),
    )
    if not llm_response.choices:
        # Default to None if no response
        return None

    due_date_response = llm_response.message_content
    # Calculate the due date based on days_from_now
    return zoned_utc_now() + timedelta(
        days=due_date_response.days_from_now, hours=due_date_response.hours_from_now
    )


async def generate_meeting_task_citations(  # noqa: C901, PLR0912
    intel_input: IntelInput,
    tasks: list[TaskV2],
    intel_context: IntelContext,
) -> list[MeetingTaskCitationDTO]:
    """Generate citations for where in the meeting transcript each task was derived from."""
    if not isinstance(
        intel_context.primary_object, MeetingAugmented
    ) and not isinstance(intel_context.primary_object, VoiceCallAugmented):
        raise ValueError(
            "Primary object is not a MeetingAugmented or VoiceCallAugmented"
        )

    if not intel_context.organization:
        raise ValueError("Organization is required for generating citations")

    # prompt_variables = intel_context.to_prompt_variables(prompt_name=PromptEnum.GENERATE_TASKS_FROM_CONVERSATION)
    db_engine: DatabaseEngine = await get_or_init_db_engine()
    citation_service = get_citation_service(db_engine)

    transcript_sentences = None

    logger.info(f"Transcript sentences: {intel_context.primary_object}")
    if intel_context.primary_object.transcript_sentences:
        logger.info(f"Transcript sentences: {intel_context.primary_object}")
        transcript_sentences = intel_context.primary_object.transcript_sentences

    if not transcript_sentences:
        raise ValueError(
            "No transcript sentences found. It's impossible to generate citations for this task."
        )

    citation_dto_list: list[MeetingTaskCitationDTO] = []

    logger.info(f"tasks: {tasks}")
    for task in tasks:
        response = await anthropic_completion(
            model=VertexModelTypes.CLAUDE_3_7_SONNET_20250219,
            max_tokens=1024,
            messages=[
                {
                    "role": "user",
                    "content": [
                        # {
                        #     "type": "document",
                        #     "source": {
                        #         "type": "text",
                        #         "media_type": "text/plain",
                        #         "data": intel_context.meeting_transcript,
                        #     },
                        #     "title": "Individual Text",
                        #     "context": "This is the transcript from the meeting.",  # will not be used for citation
                        #     "citations": {"enabled": True},
                        # },
                        {
                            "type": "document",
                            "source": {
                                "type": "content",
                                "content": [
                                    {"type": "text", "text": sentence.text}
                                    for sentence in transcript_sentences
                                ],
                            },
                            "title": "Meeting Transcript",
                            "context": "This is the transcript from the meeting.",  # will not be used for citation
                            "citations": {"enabled": True},
                        },
                        {
                            "type": "text",
                            "text": textwrap.dedent(f"""
                                Please generate an individual citation for the following task of where it was derived from in the meeting transcript.
                                Only one citation, the most relevant one, is needed.
                                <task>
                                    <title>{task.title}</title>
                                    <note>{task.note}</note>
                                </task>
                            """),
                        },
                    ],
                },
            ],
            metadata=LLMTraceMetadata(
                trace_name="intel_activities.generate_meeting_task_citations",
                session_id=intel_input.langfuse_session_id,
                custom_fields=ReeTraceMetadata(
                    organization_id=str(intel_input.organization_id),
                    prompt_use_case=PromptUseCase.INTEL_TASKS,
                    workflow_id=activity.info().workflow_id
                    if not intel_input.non_temporal
                    else "test_stub",
                ),
            ),
        )
        content_blocks = response.content
        for content_block in content_blocks:
            if isinstance(content_block, TextBlock) and content_block.citations:
                for citation in content_block.citations:
                    match citation:
                        case CitationCharLocation():
                            logger.warning(
                                f"Shouldn't be creating CitationCharLocation: {citation}"
                            )
                        case CitationPageLocation():
                            logger.warning(
                                f"Shouldn't be creating CitationPageLocation: {citation}"
                            )
                        case CitationContentBlockLocation():
                            citation_dto_list.append(
                                MeetingTaskCitationDTO(
                                    task_id=str(task.id),
                                    cited_text=citation.cited_text,
                                    start_turn_id=citation.start_block_index,
                                    end_turn_id=citation.end_block_index,
                                )
                            )
                        case _:
                            logger.warning(f"Unknown citation type: {citation}")

        citations: list[Citation] = []
        for citation_dto in citation_dto_list:
            # Create citation using service
            metadata = MeetingCitationMetadata(
                type="meeting",
                source_text=citation_dto.cited_text,
                start_turn_id=citation_dto.start_turn_id,
                end_turn_id=citation_dto.end_turn_id,
            )
            db_citation = await citation_service.create_citation(
                organization_id=intel_input.organization_id,
                for_object_id=task.id,
                for_object_type=CitationForObjectType.TASK,
                source_type=CitationSourceType.MEETING,
                source_id=intel_input.object_id,
                metadata=metadata,
                created_by_user_id=intel_context.organization.created_by_user_id,
            )
            citations.append(db_citation)

        # await langfuse_prompt_service.create_dataset_item(
        #     dataset_name="raw_meeting_task_citations",
        #     dataset_input=prompt_variables,
        #     expected_output=[
        #         citation_dto.model_dump() for citation_dto in citation_dto_list
        #     ],
        #     metadata={
        #         "model": "anthropic/claude-3-5-sonnet-20241022",
        #         "organization_id": str(intel_input.organization_id),
        #         "pipeline_id": str(intel_input.pipeline_id),
        #         "object_id": str(intel_input.object_id),
        #         "object_type": str(intel_input.object_type),
        #         "prompt_name": "meeting_task_citations",
        #     },
        # )

        logger.info(f"end citation_dto_list: {citation_dto_list}")
        logger.info(f"end citations: {citations}")

    return citation_dto_list


def chunk_email_body(
    text: str, model: str, max_chunk_tokens: int, overlap_words: int = 30
) -> list[tuple[str, int]]:
    """Split text into chunks that fit within token limit, with word overlap.

    Args:
        text: The text to split
        model: The model to use for token counting
        max_chunk_tokens: Maximum tokens per chunk
        overlap_words: Number of words to overlap between chunks

    Returns:
        List of (chunk_text, chunk_start_position) tuples
    """
    words = text.split(" ")
    word_positions = []
    pos = 0

    # Get position of each word in original text
    for word in words:
        word_positions.append(pos)
        pos += len(word) + 1  # +1 for the space

    chunks = []
    start_idx = 0
    while start_idx < len(words):
        # Use approximate constant ratio of 0.75 words per token (1 token = 0.75 words)
        end_idx = min(start_idx + int(max_chunk_tokens * 0.75), len(words))
        chunk = " ".join(words[start_idx:end_idx])
        token_count = get_token_count(chunk, model)

        # If chunk is too big, reduce size based on actual token overage
        while token_count > max_chunk_tokens and end_idx > start_idx + 1:
            # Calculate how many words to reduce based on token overage
            excess_tokens = token_count - max_chunk_tokens
            words_to_reduce = int(
                excess_tokens * 0.75
            )  # Each token is approximately 0.75 words
            end_idx -= words_to_reduce
            chunk = " ".join(words[start_idx:end_idx])
            token_count = get_token_count(chunk, model)

        if end_idx > start_idx:
            chunks.append((chunk, word_positions[start_idx]))
            start_idx = end_idx - overlap_words if end_idx < len(words) else end_idx
        else:
            start_idx += 1  # Ensure progress

    return chunks


async def generate_email_task_citations(  # noqa: C901, PLR0912
    intel_input: IntelInput,
    tasks: list[TaskV2],
    intel_context: IntelContext,
) -> list[EmailTaskCitationDTO]:
    """Generate citations for where in the email thread each task was derived from."""
    if not isinstance(intel_context.primary_object, EmailAugmented):
        raise ValueError("Primary object is not an EmailAugmented")

    if not intel_context.organization:
        raise ValueError("Organization is required for generating citations")

    db_engine: DatabaseEngine = await get_or_init_db_engine()
    citation_service = get_citation_service(db_engine)

    # Only the last email message is used for citation
    if not intel_context.primary_object.messages:
        raise ValueError(
            "No email messages found to cite from. If a task was derived from an email, then there must be at least one email message to cite from."
        )
    sorted_messages = sorted(
        intel_context.primary_object.messages,
        key=lambda msg: msg.created_at
        if msg.created_at
        else datetime.min.replace(tzinfo=UTC),
        reverse=True,
    )[:1]
    message = sorted_messages[0]
    msg_content = message.body_html if message.body_html else message.body_text

    model = ModelTypes.CLAUDE_3_5_SONNET_20241022
    max_tokens = get_max_tokens(model)
    chunks = [(msg_content, 0)]

    # Calculate the maximum prompt size from the largest task including full message structure
    max_prompt_tokens = 0
    for task in tasks:
        full_prompt = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "document",
                        "source": {
                            "type": "text",
                            "media_type": "text/plain",
                            "data": "",  # We don't need actual content for token counting
                        },
                        "title": "Email",
                        "context": "This is the email content to cite from.",
                        "citations": {"enabled": True},
                    },
                    {
                        "type": "text",
                        "text": textwrap.dedent(f"""
                            For the following task, where in the email did it come from.
                            <task>
                                <title>{task.title}</title>
                                <note>{task.note}</note>
                            </task>
                            Only one citation, the most relevant one, is needed. Output the exact text from the source, it should be a one-to-one match to the source, don't include quotes unless they are from the source.
                            If you cannot find text in the source that is directly and obviously where the task came from, then respond with "No citation found", as it may be in a different section of the email.
                        """),
                    },
                ],
            }
        ]
        prompt_tokens = get_token_count(
            str(full_prompt), model
        )  # Convert to string to count tokens
        max_prompt_tokens = max(max_prompt_tokens, prompt_tokens)

    if get_token_count(msg_content, model) + max_prompt_tokens > max_tokens:
        chunks = chunk_email_body(
            text=msg_content,
            model=model,
            max_chunk_tokens=max_tokens - max_prompt_tokens,
        )

    for task in tasks:
        citation_found = False
        citation_dto_list: list[EmailTaskCitationDTO] = []
        citations: list[
            Citation
            | CitationCharLocation
            | CitationPageLocation
            | CitationContentBlockLocation
        ] = []
        for chunk, chunk_start in chunks:
            response = await anthropic_completion(
                model=VertexModelTypes.CLAUDE_3_7_SONNET_20250219,
                max_tokens=1024,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "document",
                                "source": {
                                    "type": "text",
                                    "media_type": "text/plain",
                                    "data": chunk,
                                },
                                "title": "Email",
                                "context": "This is the email content to cite from.",
                                "citations": {"enabled": True},
                            },
                            {
                                "type": "text",
                                "text": textwrap.dedent(f"""
                                    For the following task, where in the email did it come from.
                                    <task>
                                        <title>{task.title}</title>
                                        <note>{task.note}</note>
                                    </task>
                                    Only one citation, the most relevant one, is needed. Output the exact text from the source, it should be a one-to-one match to the source, don't include quotes unless they are from the source.
                                    If you cannot find text in the source that is directly and obviously where the task came from, then respond with "No citation found", as it may be in a different section of the email.
                                """),
                            },
                        ],
                    },
                ],
                metadata=LLMTraceMetadata(
                    trace_name="intel_activities.generate_email_task_citations",
                    session_id=intel_input.langfuse_session_id,
                    custom_fields=ReeTraceMetadata(
                        organization_id=str(intel_input.organization_id),
                        prompt_use_case=PromptUseCase.INTEL_TASKS,
                        workflow_id=activity.info().workflow_id
                        if not intel_input.non_temporal
                        else "test_stub",
                    ),
                ),
            )

            content_blocks = response.content
            for content_block in content_blocks:
                if isinstance(content_block, TextBlock) and content_block.citations:
                    for citation in content_block.citations:
                        match citation:
                            case CitationCharLocation():
                                citation_dto_list.append(
                                    EmailTaskCitationDTO(
                                        task_id=str(task.id),
                                        cited_text=citation.cited_text,
                                        started_at_char_index=citation.start_char_index
                                        + chunk_start,
                                        ended_at_char_index=citation.end_char_index
                                        + chunk_start,
                                        global_message_id=str(
                                            message.global_message_id
                                        ),
                                        generated_citation=content_block.text,
                                    )
                                )
                                citation_found = True
                            case _:
                                logger.warning(f"Unknown citation type: {citation}")

            if citation_found:
                break

        if not citation_found:
            logger.warning(f"No citation found for task: {task.title}")

        for citation_dto in citation_dto_list:
            metadata = EmailCitationMetadata(
                source_text=citation_dto.cited_text,
                started_at_char_index=citation_dto.started_at_char_index,
                ended_at_char_index=citation_dto.ended_at_char_index,
                global_message_id=citation_dto.global_message_id,
                generated_citation=citation_dto.generated_citation,
            )
            new_citation: (
                Citation
                | CitationCharLocation
                | CitationPageLocation
                | CitationContentBlockLocation
            ) = await citation_service.create_citation(
                organization_id=intel_input.organization_id,
                for_object_id=task.id,
                for_object_type=CitationForObjectType.TASK,
                source_type=CitationSourceType.EMAIL,
                source_id=intel_input.object_id,
                metadata=metadata,
                created_by_user_id=intel_context.organization.created_by_user_id,
            )
            citations.append(new_citation)
        # await langfuse_prompt_service.create_dataset_item(
        #     dataset_name="raw_email_task_citations",
        #     dataset_input=prompt_variables,
        #     expected_output=[
        #         citation_dto.model_dump() for citation_dto in citation_dto_list
        #     ],
        #     metadata={
        #         "model": "anthropic/claude-3-5-sonnet-20241022",
        #         "organization_id": str(intel_input.organization_id),
        #         "pipeline_id": str(intel_input.pipeline_id),
        #         "object_id": str(intel_input.object_id),
        #         "object_type": str(intel_input.object_type),
        #         "prompt_name": "email_task_citations",
        #     },
        # )

    return citation_dto_list


async def generate_objection_task_citations(
    intel_input: IntelInput,
    tasks: list[TaskV2],
    intel_context: IntelContext,
) -> list[MeetingTaskCitationDTO] | list[EmailTaskCitationDTO] | list[None]:
    """Generate the citations for the tasks based on the objection."""
    if not isinstance(intel_context.primary_object, Insight):
        raise ValueError("Primary object is not an Insight")

    if not intel_context.organization:
        raise ValueError("Organization is required for generating citations")

    logger.debug("Generating objection task citations")
    logger.debug(f"Tasks: {tasks}")

    object_type: IntelTriggerObjectType | None = None
    if intel_context.primary_object.reference_type == InsightReferenceIdType.MEETING:
        object_type = IntelTriggerObjectType.MEETING
    elif intel_context.primary_object.reference_type == InsightReferenceIdType.THREAD:
        object_type = IntelTriggerObjectType.GLOBAL_THREAD

    if (
        object_type is not None
        and intel_context.primary_object.reference_id is not None
    ):
        # Create new Intel Context for Source of Insight if valid object type
        source_of_insight_intel_input = IntelInput(
            organization_id=intel_input.organization_id,
            object_id=intel_context.primary_object.reference_id,
            object_type=object_type,
            pipeline_id=intel_input.pipeline_id,
            account_ids=intel_input.account_ids,
            langfuse_session_id=intel_input.langfuse_session_id,
            bypass_dedup=intel_input.bypass_dedup,
            non_temporal=intel_input.non_temporal,
            test_stub=intel_input.test_stub,
        )
        source_of_insight_intel_context = await get_intel_context(
            source_of_insight_intel_input
        )

        logger.debug(f"Source of insight intel input: {source_of_insight_intel_input}")
        logger.debug(
            f"Source of insight intel context: {source_of_insight_intel_context}"
        )

    if object_type == IntelTriggerObjectType.MEETING:
        logger.debug("Generating meeting task citations")
        return await generate_meeting_task_citations(
            source_of_insight_intel_input, tasks, source_of_insight_intel_context
        )
    elif object_type == IntelTriggerObjectType.GLOBAL_THREAD:
        logger.debug("Generating email task citations")
        return await generate_email_task_citations(
            source_of_insight_intel_input, tasks, source_of_insight_intel_context
        )
    else:
        logger.warning("No valid object type found for generating task citations")
        return []


async def generate_task_priority(
    intel_input: IntelInput,
    task: TaskRequestFromLLM,
    object_content: str,
) -> TaskPriority:
    """Generate the priority for the task based on content and context."""

    prompt_variables = {
        "task_title": task.title,
        "task_note": task.note,
        "object_content": object_content,
        "priority_options": "\n".join(
            [f"<priority>{p.value}</priority>" for p in TaskPriority]
        ),
    }

    prompt_response = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.GENERATE_TASK_PRIORITY,
            variables=prompt_variables,
        )
    )
    # Get the priority suggestion from Claude
    llm_response = await acompletion(
        model="gpt-4o",  # using gpt-4o to avoid the anthropic tool vs response format issue
        messages=prompt_response.messages,
        temperature=0,
        max_completion_tokens=1024,
        response_format=TaskPriorityResponse,
        metadata=LLMTraceMetadata(
            trace_name=PromptEnum.GENERATE_TASK_PRIORITY,
            session_id=intel_input.langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(intel_input.organization_id),
                prompt_use_case=PromptUseCase.INTEL_TASKS,
                workflow_id=activity.info().workflow_id
                if not intel_input.non_temporal
                else "test_stub",
            ),
        ),
    )
    if not llm_response.choices:
        # Default to MEDIUM if no response
        raise ValueError("No priority response")

    return llm_response.message_content.priority


async def generate_task_ownership(
    intel_input: IntelInput,
    task: TaskRequestFromLLM,
    intel_context: IntelContext,
    object_content: str,
    workflow_id: str,
) -> str:
    """Generate the ownership id for the task based on content and context."""
    users = ""
    if intel_context.users and len(intel_context.users) > 0:
        for user in intel_context.users:
            users += f"""
                <user_option>
                    <user_id>{user.id}</user_id>
                    <user_first_name>{user.first_name}</user_first_name>
                    <user_last_name>{user.last_name}</user_last_name>
                    <user_email>{user.email}</user_email>
                    <user_phone_number>{user.phone_number}</user_phone_number>
                </user_option>
            """

    account_owner = ""
    if intel_context.account_owner:
        account_owner = f"""
            <account_owner_user_option>
                <user_id>{intel_context.account_owner.id}</user_id>
                <user_first_name>{intel_context.account_owner.first_name}</user_first_name>
                <user_last_name>{intel_context.account_owner.last_name}</user_last_name>
                <user_email>{intel_context.account_owner.email}</user_email>
                <user_phone_number>{intel_context.account_owner.phone_number}</user_phone_number>
            </account_owner_user_option>
        """

    logger.info(f"Generating task ownership for {task.title}")
    logger.info(f"Generating output to {intel_input.output_path}")
    llm_response = await generate_task_ownership_llm_call(
        task=task,
        object_content=object_content,
        users=users,
        organization_id=intel_input.organization_id,
        langfuse_session_id=intel_input.langfuse_session_id,
        workflow_id=workflow_id,
        account_owner=account_owner,
        output_path=intel_input.output_path,
    )

    if not llm_response.choices:
        raise ValueError("No ownership response")

    user_list_in_context = (
        [str(user.id) for user in intel_context.users] if intel_context.users else []
    )
    if users and llm_response.message_content.ownership_id not in user_list_in_context:
        logger.bind(
            ownership_id=llm_response.message_content.ownership_id,
            user_list=user_list_in_context,
        ).error("User id does not match any of the users in the context")
        raise ValueError("Hallucinated user id")

    return llm_response.message_content.ownership_id
