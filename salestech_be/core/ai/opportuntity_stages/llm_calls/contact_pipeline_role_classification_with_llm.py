from uuid import UUID

from anthropic.types import ContentBlockParam, Message, MessageParam, TextCitation
from pydantic import BaseModel

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    anthropic_completion,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.type.metadata.common import StandardObjectIdentifier
from salestech_be.core.ai.common.llm_types import VertexModelTypes
from salestech_be.core.ai.common.types import (
    EmailAugmented,
    MeetingAugmented,
    SourceContext,
    VoiceCallAugmented,
)
from salestech_be.core.ai.opportuntity_stages.llm_calls.criteria_extraction_with_llm import (
    build_block_messages,
    convert_source_object_to_intel_context,
)
from salestech_be.core.ai.opportuntity_stages.llm_calls.sales_action_classification_with_llm import (
    add_sales_action_role_classification_citation_to_db,
)
from salestech_be.core.citation.service.citation_service import get_citation_service
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.crm_ai_rec.crm_ai_rec_service import get_crm_ai_rec_service
from salestech_be.core.crm_ai_rec.service_api_schema import (
    DomainObjectPreview,
    ListCrmObjectCreationRecsRequest,
)
from salestech_be.core.metadata.types import ContactPipelineRole
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaCitation,
    CriteriaExtractionSourceObjectId,
    KeepCriteriaChange,
    SourceObject,
)
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineRoleRequest,
    ContactPipelineRoleRequestFields,
)
from salestech_be.db.models.citation import CitationForObjectType
from salestech_be.db.models.crm_ai_rec import CrmAIRecType, ParentRecordIds
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore
)

logger = get_logger(__name__)


class ContactPipelineRoleClassificationRequest(BaseModel):
    organization_id: UUID
    pipeline_id: UUID
    source_object: SourceObject
    current_contact_pipeline_roles: list[ContactPipelineRole] | None = None


class LLMCreateContactPipelineRoleRequest(BaseModel):
    request: ContactPipelineRoleRequest
    citations: list[CriteriaCitation] | None = None
    field_update_types: dict[ContactPipelineRoleRequestFields, CrmAIRecType] | None


class LLMPatchContactPipelineRoleRequest(BaseModel):
    request: ContactPipelineRoleRequest
    citations: list[CriteriaCitation] | None = None
    field_update_types: dict[ContactPipelineRoleRequestFields, CrmAIRecType] | None


class LLMContactPipelineRoleClassificationResult(BaseModel):
    create_contact_pipeline_role_requests: (
        list[LLMCreateContactPipelineRoleRequest] | None
    ) = None
    patch_contact_pipeline_role_requests: (
        list[LLMPatchContactPipelineRoleRequest] | None
    ) = None
    delete_pending_recs: list[UUID] | None = None


async def contact_pipeline_role_classification_with_llm(  # noqa: C901, PLR0912, PLR0915 TODO: Put contact retrieval logic in a helper function for readability
    request: ContactPipelineRoleClassificationRequest,
) -> LLMContactPipelineRoleClassificationResult:
    source_object = request.source_object
    current_unassigned_contacts: str = ""
    pipeline_contacts: list[ContactV2] | None = None
    db_engine = await get_or_init_db_engine()
    contact_query_service = get_contact_query_service(db_engine=db_engine)
    crm_ai_rec_service = get_crm_ai_rec_service(db_engine=db_engine)
    source_context: SourceContext = []
    if isinstance(source_object, CriteriaExtractionSourceObjectId):
        langfuse_session_id = f"contact_pipeline_role_classification:{source_object.object_type}:{source_object.object_id}"
        source_context = await convert_source_object_to_intel_context(
            organization_id=request.organization_id,
            pipeline_id=request.pipeline_id,
            langfuse_session_id="",
            source_object=source_object,
        )
        account_ids = set()
        primary = source_context.primary_object
        if source_context.pipeline:
            account_ids.add(source_context.pipeline.account_id)
        activity_contact_ids = []
        if isinstance(primary, MeetingAugmented):
            if primary.attendees:
                activity_contact_ids = [
                    attendee.contact_id
                    for attendee in primary.attendees
                    if attendee.contact_id
                ]
        elif isinstance(primary, EmailAugmented):
            activity_contact_ids = [
                cid for cid in (primary.contact_ids or []) if cid is not None
            ]
        elif isinstance(primary, VoiceCallAugmented):
            if primary.meeting.attendees:
                activity_contact_ids = [
                    attendee.contact_id
                    for attendee in primary.meeting.attendees
                    if attendee.contact_id
                ]
        else:
            raise ValueError(f"Unsupported primary object type: {type(primary)}")
        contact_account_associations = (
            await contact_query_service.list_contact_account_roles_by_contact_id(
                organization_id=request.organization_id,
                account_ids=account_ids,
            )
        )
        # Need contacts that are part of account and activity but not in pipeline
        pipeline_contact_ids = [c.id for c in source_context.contacts or []]
        unassociated_contact_ids = [
            c.contact_id
            for c in contact_account_associations
            if c.contact_id in activity_contact_ids
            and (c.contact_id not in pipeline_contact_ids)
        ]
        unassociated_contacts = await contact_query_service.list_contacts_v2(
            organization_id=request.organization_id,
            only_include_contact_ids=set(unassociated_contact_ids),
        )
        current_unassigned_contacts = "\n".join(
            [
                f"""<contact>
                <first_name>{c.first_name}</first_name>
                <last_name>{c.last_name}</last_name>
                <id>{c.id}</id>
                </contact>"""
                for c in (unassociated_contacts or [])
            ]
        )
        pipeline_contacts = source_context.contacts or []
    else:
        langfuse_session_id = f"contact_pipeline_role_classification:multi_meeting_transcript:{request.pipeline_id}"
        pipeline_contacts = await contact_query_service.list_contacts_by_pipeline_id(
            organization_id=request.organization_id,
            pipeline_id=request.pipeline_id,
        )
        source_context = source_object

    messages = build_block_messages(source_context)
    if not messages:
        logger.warning("No messages found for primary object")
        return LLMContactPipelineRoleClassificationResult()

    current_pipeline_contacts = "\n".join(
        [
            f"""<contact>
                <first_name>{c.first_name}</first_name>
                <last_name>{c.last_name}</last_name>
                <id>{c.id}</id>
                </contact>"""
            for c in (pipeline_contacts or [])
        ]
    )
    current_contact_pipeline_roles = "\n".join(
        [
            f"""<contact>
            <contact_id>{c.contact_id}</contact_id>
            <is_primary_contact>{c.is_primary_contact}</is_primary_contact>
            <role_types>{c.role_types}</role_types>
            </contact>"""
            for c in (request.current_contact_pipeline_roles or [])
        ]
    )
    # Include pending contact pipeline role creations to dedup
    pending_recs = (
        await crm_ai_rec_service.list_crm_object_creation_recs_by_single_request(
            organization_id=request.organization_id,
            request=ListCrmObjectCreationRecsRequest(
                object_identifier=StandardObjectIdentifier(
                    object_name=StdObjectIdentifiers.contact_pipeline_role,
                ),
                parent_record_ids=[
                    ParentRecordIds(
                        pipeline_id=request.pipeline_id,
                    )
                ],
            ),
            max_result=50,
        )
    )
    llm_messages = build_contact_pipeline_role_classification_prompt(
        messages,
        current_contact_pipeline_roles,
        current_unassigned_contacts,
        current_pipeline_contacts,
        pending_recs,
    )
    llm_response: Message = await anthropic_completion(
        model=VertexModelTypes.CLAUDE_3_7_SONNET_20250219,
        messages=llm_messages,
        max_tokens=8192,
        tools=[
            {
                "input_schema": LLMContactPipelineRoleClassificationResult.model_json_schema(),
                "name": "update_contact_pipeline_role",
                "description": "A tool that updates the contact pipeline role classification from the given provided context.",
            }
        ],
        metadata=LLMTraceMetadata(
            trace_name="opportunity_stages.contact_pipeline_role_classification",
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(request.organization_id),
            ),
        ),
    )
    if not llm_response.content or llm_response.content[-1].type != "tool_use":
        return LLMContactPipelineRoleClassificationResult()
    response = LLMContactPipelineRoleClassificationResult.model_validate(
        llm_response.content[-1].input
    )
    citation_service = get_citation_service(db_engine)
    if response.create_contact_pipeline_role_requests:
        for (
            create_contact_pipeline_role_request
        ) in response.create_contact_pipeline_role_requests:
            citation = await create_contact_pipeline_role_classification_citation(
                messages=messages,
                current_contact_pipeline_roles=current_contact_pipeline_roles,
                current_unassigned_contacts=current_unassigned_contacts,
                current_pipeline_contacts=current_pipeline_contacts,
                contact_pipeline_role_change=" created new contact pipeline role: "
                + create_contact_pipeline_role_request.request.model_dump_json(),
                langfuse_session_id=langfuse_session_id,
                organization_id=request.organization_id,
            )
            if citation:
                # Add citation to DB
                criteria_citations = await add_sales_action_role_classification_citation_to_db(
                    source_context=source_context,
                    field_name="role_types",
                    for_object_type=CitationForObjectType.CONTACT_PIPELINE_ROLE_CLASSIFICATION,
                    organization_id=request.organization_id,
                    citation_service=citation_service,
                    citation=citation,
                )
                create_contact_pipeline_role_request.citations = criteria_citations
            else:
                # No citation found, so remove request from the list
                response.create_contact_pipeline_role_requests.remove(
                    create_contact_pipeline_role_request
                )
    if response.patch_contact_pipeline_role_requests:
        for (
            patch_contact_pipeline_role_request
        ) in response.patch_contact_pipeline_role_requests:
            citation = await create_contact_pipeline_role_classification_citation(
                messages=messages,
                current_contact_pipeline_roles=current_contact_pipeline_roles,
                current_unassigned_contacts=current_unassigned_contacts,
                current_pipeline_contacts=current_pipeline_contacts,
                contact_pipeline_role_change=" updated contact pipeline role: "
                + patch_contact_pipeline_role_request.request.model_dump_json(),
                langfuse_session_id=langfuse_session_id,
                organization_id=request.organization_id,
            )
            if citation:
                criteria_citations = await add_sales_action_role_classification_citation_to_db(
                    source_context=source_context,
                    field_name="role_types",
                    for_object_type=CitationForObjectType.CONTACT_PIPELINE_ROLE_CLASSIFICATION,
                    organization_id=request.organization_id,
                    citation_service=citation_service,
                    citation=citation,
                )
                patch_contact_pipeline_role_request.citations = criteria_citations
            else:
                # No citation found, so remove request from the list
                response.patch_contact_pipeline_role_requests.remove(
                    patch_contact_pipeline_role_request
                )
    return response


def build_contact_pipeline_role_classification_prompt(
    messages: list[str],
    current_contact_pipeline_roles: str,
    current_unassigned_contacts: str,
    current_pipeline_contacts: str,
    pending_recs: list[DomainObjectPreview],
) -> list[MessageParam]:
    pending_recs_str = "\n".join(
        [f"ID: {rec.ai_rec_id}, Data: {rec.preview_data}" for rec in pending_recs]
    )
    message_content: list[ContentBlockParam] = [
        {
            "type": "document",
            "source": {
                "type": "content",
                "content": [
                    {
                        "type": "text",
                        "text": text,
                    }
                    for text in messages
                ],
            },
            "title": "Context",
            "context": "This is the source content to extract contact pipeline role classification from.",
            "citations": {"enabled": True},
        },
        {
            "type": "text",
            "text": f"""This is the list of all the contacts currently associated with the activity {current_pipeline_contacts}. Reference this when patching existing contact pipeline roles.
            This is the list of all the current roles assigned to the contacts associated with the activity {current_contact_pipeline_roles}. Reference this when patching existing contact pipeline roles.
            This is the list of all the contacts that could be associated with the activity, and do not have a role assigned to them: {current_unassigned_contacts}. Reference this when creating new contact pipeline roles.
            This is the list of all the pending requests made from previous activities to associate contacts with the activity that have not been accepted yet: {pending_recs_str}. Do not create new contact pipeline roles for contacts that are already in the pending requests. If you believe a pending contact role is no longer correct based on the context, then delete the pending request using the delete_pending_recs field.
            Given the provided context, please classify the contact pipeline roles, and change the roles of the current contacts if needed. If you believe a contact role should be added, then create a new contact pipeline role for that contact.
            Follow these instructions in the order they are given:
            - Check if any contact roles that were not previously in the pipeline should be added to the pipeline, if so, create a new contact pipeline role for that contact, for any of the role_types you include, explain why you are adding this role.
            - Check if any pending contact roles should be deleted, if so, delete the pending request, for any of the role_types you include, explain why you are deleting this role.
            - Check if any existing contact roles should be updated, if so, update the role of the contact, for any role_type you add to the original list of roles for that contact, explain why you are adding this role.
            - Do not populate the citations field in the response.
            Only make a tool call after you have followed all the steps above.
            """,
        },
    ]
    message_params: list[MessageParam] = [
        {
            "role": "user",
            "content": message_content,
        }
    ]
    return message_params


async def create_contact_pipeline_role_classification_citation(
    messages: list[str],
    current_contact_pipeline_roles: str,
    current_unassigned_contacts: str,
    current_pipeline_contacts: str,
    contact_pipeline_role_change: str,
    langfuse_session_id: str,
    organization_id: UUID,
) -> TextCitation | None:
    result = await anthropic_completion(
        model=VertexModelTypes.CLAUDE_3_7_SONNET_20250219,
        max_tokens=8192,
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "document",
                        "source": {
                            "type": "content",
                            "content": [
                                {
                                    "type": "text",
                                    "text": text,
                                }
                                for text in messages
                            ],
                        },
                        "title": "Context",
                        "context": "This is the source content to extract contact pipeline role classification from.",
                        "citations": {"enabled": True},
                    },
                    {
                        "type": "text",
                        "text": f"""
                        This is the list of all the contacts currently associated with the activity {current_pipeline_contacts}. Reference this when patching existing contact pipeline roles.
                        This is the list of all the current roles assigned to the contacts associated with the activity {current_contact_pipeline_roles}. Reference this when patching existing contact pipeline roles.
                        This is the list of all the contacts that could be associated with the activity, and do not have a role assigned to them: {current_unassigned_contacts}. Reference this when creating new contact pipeline roles.
                        There has been a change to the contact pipeline role classification, {contact_pipeline_role_change}.
                        Check if that given the context, the change is valid. If so, please provide one citation for where this change could have come from. Only one citation is necessary, the most relevant one.
                        If you cannot find a citation, that means this update is not valid, and you should return None.
                        """,
                    },
                ],
            }
        ],
        tools=[
            {
                "input_schema": KeepCriteriaChange.model_json_schema(),
                "name": "keep_criteria_change",
                "description": "A tool that determines if a criteria change should be kept.",
            }
        ],
        metadata=LLMTraceMetadata(
            trace_name="opportunity_stages.citation_for_contact_pipeline_role_classification",
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
            ),
        ),
    )
    if not result.content:
        return None
    if result.content[-1].type == "tool_use":
        keep_criteria_change = KeepCriteriaChange.model_validate(
            result.content[-1].input
        )
        if not keep_criteria_change.should_keep_change:
            return None
    for content_block in result.content:
        if content_block.type == "text" and content_block.citations:
            return content_block.citations[0]
    return None
