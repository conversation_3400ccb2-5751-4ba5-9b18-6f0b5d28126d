import re
from uuid import UUID

from anthropic.types import (
    CitationContentBlockLocation,
    Message,
    TextCitation,
)

from salestech_be.core.ai.common.helpers.get_intel_context import get_intel_context
from salestech_be.core.ai.common.types import (
    EmailAugmented,
    IntelContext,
    MeetingAugmented,
    SourceContext,
    VoiceCallAugmented,
)
from salestech_be.core.ai.opportuntity_stages.utils.multi_meeting_transcript_helpers import (
    add_criteria_multi_meeting_citation,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.workflows.schema import (
    IntelInput,
    IntelTriggerObjectType,
)
from salestech_be.core.citation.service.citation_service import (
    CitationService,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaCitation,
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
    FieldCitationPair,
)
from salestech_be.db.models.citation import (
    CitationForObjectType,
    CitationSourceType,
    EmailCitationMetadata,
    MeetingCitationMetadata,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

langfuse_prompt_service = get_langfuse_prompt_service()
logger = get_logger(__name__)


async def convert_source_object_to_intel_context(
    organization_id: UUID,
    pipeline_id: UUID,
    langfuse_session_id: str,
    source_object: CriteriaExtractionSourceObjectId,
) -> IntelContext:
    """
    Convert source objects to prompt variables.
    """
    if source_object.object_type == CriteriaExtractionSourceObjectType.MEETING:
        intel_input = IntelInput(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            account_ids=None,
            object_id=source_object.object_id,
            object_type=IntelTriggerObjectType.MEETING,
            langfuse_session_id=langfuse_session_id,
        )
        return await get_intel_context(
            intel_input=intel_input,
        )

    elif source_object.object_type == CriteriaExtractionSourceObjectType.EMAIL:
        intel_input = IntelInput(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            account_ids=None,
            object_id=source_object.object_id,
            object_type=IntelTriggerObjectType.GLOBAL_THREAD,
            langfuse_session_id=langfuse_session_id,
        )
        return await get_intel_context(
            intel_input=intel_input,
        )
    else:
        raise ValueError(f"Invalid source object type: {source_object.object_type}")


def build_block_messages(
    source_context: SourceContext,
) -> list[str]:
    messages = []
    if isinstance(source_context, list):
        for citation in source_context:
            messages.append(
                f"[[Speaker: {citation.speaker}]] [[original line number: {citation.line_number}]] {citation.text}"
            )
        return messages

    primary = source_context.primary_object
    if (
        isinstance(primary, (MeetingAugmented, VoiceCallAugmented))
        and primary.transcript_sentences
    ):
        for sentence in primary.transcript_sentences:
            messages.append(f"[[{sentence.speaker}]] {sentence.text}")
    elif isinstance(primary, EmailAugmented) and primary.messages:
        for message in primary.messages:
            body = message.body_html or message.body_text or ""
            messages.append(body)
    else:
        logger.warning("Primary object is not a meeting, voice call, or email")
    return messages


def collect_criteria_citations(llm_response: Message) -> list[TextCitation]:
    citations = []
    for content_block in llm_response.content:
        if content_block.type == "text" and content_block.citations:
            citations.extend(content_block.citations)
    return citations


async def add_criteria_citations_to_db(
    source_context: SourceContext,
    field_citation_pairs: list[FieldCitationPair],
    organization_id: UUID,
    citation_service: CitationService,
) -> list[CriteriaCitation]:
    criteria_citations: list[CriteriaCitation] = []
    if isinstance(source_context, list):
        return await add_criteria_multi_meeting_citation(
            field_citation_pairs=field_citation_pairs,
            organization_id=organization_id,
            citation_service=citation_service,
            object_type=CitationForObjectType.OPPORTUNITY_STAGE_CRITERIA,
            multi_meeting_transcript=source_context,
        )

    for pair in field_citation_pairs:
        citation = pair.citation
        field = pair.field

        if not isinstance(citation, CitationContentBlockLocation):
            logger.error(f"Invalid citation type: {type(citation)}")
            continue

        if isinstance(
            source_context.primary_object, (MeetingAugmented, VoiceCallAugmented)
        ):
            source_id = source_context.primary_object.id
            if isinstance(source_context.primary_object, VoiceCallAugmented):
                source_id = source_context.primary_object.meeting.id

            db_citation = await citation_service.create_citation(
                organization_id=organization_id,
                for_object_id=UUID("00000000-0000-0000-0000-000000000000"),
                for_object_type=CitationForObjectType.OPPORTUNITY_STAGE_CRITERIA,
                source_type=CitationSourceType.MEETING,
                source_id=source_id,
                created_by_user_id=UUID(settings.intel_hardcoded_user_id),
                metadata=MeetingCitationMetadata(
                    source_text=remove_speaker_from_citation_text(citation.cited_text),
                    type="meeting",
                    start_turn_id=citation.start_block_index,
                    end_turn_id=citation.end_block_index,
                ),
                field_name=field.field_name,
                field_item_id=field.field_item_id,
            )
            criteria_citations.append(
                CriteriaCitation(
                    id=db_citation.id,
                    field_name=field.field_name,
                    field_item_id=field.field_item_id,
                )
            )
        elif isinstance(source_context.primary_object, EmailAugmented):
            for index in range(
                citation.start_block_index,
                citation.end_block_index,
            ):
                if index >= len(source_context.primary_object.messages):
                    logger.warning(f"Message index {index} out of range")
                    continue
                db_citation = await citation_service.create_citation(
                    organization_id=organization_id,
                    for_object_id=UUID("00000000-0000-0000-0000-000000000000"),
                    for_object_type=CitationForObjectType.OPPORTUNITY_STAGE_CRITERIA,
                    source_type=CitationSourceType.EMAIL,
                    source_id=source_context.primary_object.id,
                    created_by_user_id=UUID(settings.intel_hardcoded_user_id),
                    metadata=EmailCitationMetadata(
                        source_text=remove_speaker_from_citation_text(
                            citation.cited_text
                        ),
                        global_message_id=str(
                            source_context.primary_object.messages[
                                index
                            ].global_message_id
                        ),
                    ),
                    field_name=field.field_name,
                    field_item_id=field.field_item_id,
                )
                criteria_citations.append(
                    CriteriaCitation(
                        id=db_citation.id,
                        field_name=field.field_name,
                        field_item_id=field.field_item_id,
                    )
                )
        else:
            logger.bind(
                source_context_type=type(source_context.primary_object),
                source_id=source_context.primary_object.id,
            ).warning("Primary object is not a meeting, voice call, or email")
    return criteria_citations


def remove_speaker_from_citation_text(citation_text: str) -> str:
    # This regex matches [[...]] including the brackets and any content inside
    return re.sub(r"\[\[.*?\]\]", "", citation_text)
