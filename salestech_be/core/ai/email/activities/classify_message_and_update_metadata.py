from uuid import uuid4

from temporalio import activity

from salestech_be.core.ai.email.llm_calls.classify_email_content_with_llm import (
    EmailClassification,
    classify_email_content_with_llm,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.db.dao.message_metadata_repository import MessageMetadataRepository
from salestech_be.db.models.message import (
    Message,
)
from salestech_be.db.models.message_metadata import (
    MessageClassification,
    MessageMetadata,
)
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now

langfuse_prompt_service = get_langfuse_prompt_service()


@activity.defn
async def classify_message_and_update_metadata(
    message: Message,
) -> EmailClassification:
    """Classify the message and update the metadata."""
    engine = await get_or_init_db_engine()
    message_metadata_repository = MessageMetadataRepository(engine=engine)
    if (
        (message.main_body_html is None or len(message.main_body_html) == 0)
        and (message.main_body_text is None or len(message.main_body_text) == 0)
        and len(message.body_html) == 0
        and len(message.body_text) == 0
    ):
        classification = EmailClassification(
            category=MessageClassification.OTHER,
            confidence=1,
            explanation="No content to classify",
        )

    else:
        text_to_classify = (
            message.main_body_text
            if message.main_body_text is not None and len(message.main_body_text) > 0
            else message.main_body_html
            if message.main_body_html is not None and len(message.main_body_html) > 0
            else message.body_text
            if len(message.body_text) > 0
            else message.body_html
            if len(message.body_html) > 0
            else ""
        )

        if text_to_classify is None or len(text_to_classify) == 0:
            classification = EmailClassification(
                category=MessageClassification.OTHER,
                confidence=1,
                explanation="No content to classify",
            )
        else:
            # if body_html is None, use body_text and just extract the main body text
            classification = await classify_email_content_with_llm(
                email_content=text_to_classify,
                organization_id=message.organization_id,
                langfuse_session_id=f"cdc:EMAIL:{message.id}",
            )

    await message_metadata_repository.insert(
        MessageMetadata(
            id=uuid4(),
            message_id=message.id,
            organization_id=message.organization_id,
            classification=classification.category,
            created_at=zoned_utc_now(),
            updated_at=zoned_utc_now(),
        )
    )

    return classification
