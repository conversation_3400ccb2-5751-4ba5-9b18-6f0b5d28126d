#!/usr/bin/env python3

import asyncio
import uuid
from datetime import UTC, datetime

from salestech_be.core.ai.email.llm_calls.classify_email_content_with_llm import (
    classify_email_content_with_llm,
)
from salestech_be.core.email.global_email.global_thread_query_service import (
    get_global_thread_query_service,
)
from salestech_be.ree_logging import get_logger
from salestech_be.temporal.database import (
    get_or_init_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


async def main() -> None:
    # Initialize DB engine
    db_engine = await get_or_init_db_engine()

    # Create input
    organization_id = uuid.UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5")
    global_thread_query_service = get_global_thread_query_service(db_engine=db_engine)
    global_threads = await global_thread_query_service.list_global_threads(
        user_id=uuid.UUID("3b2f46a7-e4b3-42f1-a7f4-a232eb5dcfaa"),
        organization_id=organization_id,
        only_include_thread_ids={uuid.UUID("32e1a9c7-96cf-4604-8403-da1f9d2ef2c1")},
    )

    global_thread = global_threads[0]
    messages = sorted(
        global_thread.messages,
        key=lambda x: x.send_at if x.send_at else datetime.min.replace(tzinfo=UTC),
        reverse=True,
    )
    latest_message = messages[0] if len(messages) > 0 else None
    if latest_message is None:
        logger.info("No latest message found")
        return

    if latest_message.body_text is None:
        logger.info("No body_text found")
        return

    # Run the activity directly
    logger.info(f"Latest message: {latest_message.body_text}")
    result = await classify_email_content_with_llm(
        email_content=latest_message.body_text,
        organization_id=organization_id,
        langfuse_session_id=f"cdc:EMAIL:{latest_message.id}",
    )
    logger.info(f"Classification result: {result}")


if __name__ == "__main__":
    asyncio.run(main())
