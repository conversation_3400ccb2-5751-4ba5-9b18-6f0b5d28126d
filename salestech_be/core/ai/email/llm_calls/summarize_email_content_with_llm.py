from uuid import UUID

from salestech_be.common.ree_llm import (
    LLMTraceMetadata,
    ReeTraceMetadata,
    acompletion,
)
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptEnum, PromptRequest
from salestech_be.core.prompt.types import PromptUseCase
from salestech_be.ree_logging import get_logger

langfuse_prompt_service = get_langfuse_prompt_service()

logger = get_logger(__name__)


async def summarize_email_content_with_llm(
    email_content: str, organization_id: UUID, langfuse_session_id: str | None = None
) -> str:
    function_args = {
        "email_content": email_content,
        "organization_id": organization_id,
        "langfuse_session_id": langfuse_session_id,
    }

    prompt_variables = {"email_content": email_content}

    prompt_obj = await langfuse_prompt_service.get_prompt(
        request=PromptRequest(
            prompt_name=PromptEnum.SUMMARIZE_EMAIL_CONTENT, variables=prompt_variables
        )
    )

    llm_response = await acompletion(
        model=prompt_obj.get_model(),
        messages=prompt_obj.messages,
        temperature=0,
        metadata=LLMTraceMetadata(
            trace_name=PromptEnum.SUMMARIZE_EMAIL_CONTENT,
            session_id=langfuse_session_id,
            custom_fields=ReeTraceMetadata(
                organization_id=str(organization_id),
                prompt_use_case=PromptUseCase.EMAIL_SUMMARIZATION,
            ),
        ),
    )
    result = llm_response.message_content

    try:
        await langfuse_prompt_service.create_dataset_item(
            dataset_name=f"{PromptEnum.SUMMARIZE_EMAIL_CONTENT}-v0.1",
            dataset_input=function_args,
            expected_output={
                "summary": result,
            },
        )
    except Exception as e:
        logger.bind(dataset_name=f"{PromptEnum.SUMMARIZE_EMAIL_CONTENT}-v0.1").warning(
            f"Error creating dataset item: {e}"
        )

    return result
