from __future__ import annotations

from datetime import datetime
from typing import Any
from uuid import UUID

from pydantic import BaseModel

from salestech_be.db.models.note import Note as DbNote
from salestech_be.db.models.note import NoteReference as DbNoteReference
from salestech_be.db.models.note import NoteReferenceIdType, NoteType


class Note(BaseModel):
    """The note model."""

    id: UUID
    note_html: str
    contact_ids: list[UUID] = []
    user_ids: list[UUID] = []
    type: NoteType | None = None
    title: str | None = None
    account_id: UUID | None = None
    pipeline_id: UUID | None = None
    meeting_id: UUID | None = None
    created_at: datetime
    created_by_user_id: UUID
    updated_at: datetime | None
    updated_by_user_id: UUID | None
    deleted_at: datetime | None
    deleted_by_user_id: UUID | None
    organization_id: UUID
    attachment_ids: list[UUID] | None
    task_id: UUID | None

    @classmethod
    def map_from_db(
        cls,
        db_note: DbNote,
        db_references: list[DbNoteReference],
    ) -> Note:
        reference_map: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        account_id = None
        meeting_id = None
        pipeline_id = None
        task_id = None
        for db_reference in db_references:
            reference_type = db_reference.reference_id_type
            if not reference_map.get(reference_type):
                reference_map[reference_type] = []
            reference_map[reference_type].append(db_reference.reference_id)
            if reference_type == NoteReferenceIdType.PIPELINE_ID:
                pipeline_id = UUID(db_reference.reference_id)
            if reference_type == NoteReferenceIdType.ACCOUNT_ID:
                account_id = db_reference.reference_id
            if reference_type == NoteReferenceIdType.MEETING_ID:
                meeting_id = db_reference.reference_id
            if reference_type == NoteReferenceIdType.TASK_ID:
                task_id = UUID(db_reference.reference_id)

        return Note(
            id=db_note.id,
            note_html=db_note.note_html,
            contact_ids=reference_map.get(NoteReferenceIdType.CONTACT_ID.name) or [],
            account_id=UUID(account_id) if account_id else None,
            pipeline_id=pipeline_id,
            meeting_id=UUID(meeting_id) if meeting_id else None,
            user_ids=reference_map.get(NoteReferenceIdType.USER_ID.name) or [],
            title=db_note.title,
            type=db_note.type,
            created_at=db_note.created_at,
            created_by_user_id=db_note.created_by_user_id,
            updated_at=db_note.updated_at,
            updated_by_user_id=db_note.created_by_user_id,
            deleted_at=db_note.deleted_at,
            deleted_by_user_id=db_note.deleted_by_user_id,
            organization_id=db_note.organization_id,
            attachment_ids=db_note.attachment_ids,
            task_id=task_id,
        )

    def __eq__(self, other: Any) -> bool:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return other is not None and type(other) is Note and self.dict() == other.dict()

    def __hash__(self) -> int:
        return hash(self.id)
