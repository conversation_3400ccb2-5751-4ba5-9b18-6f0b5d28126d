from salestech_be.core.domain_crm_association.types import CreateNoteCrmAssociation
from salestech_be.core.note.types import Note
from salestech_be.db.models.domain_crm_association import DomainType


class _NoteCrmAssociationMapper:
    @staticmethod
    def map_note_to_associations_create(
        note: Note,
    ) -> list[CreateNoteCrmAssociation]:
        """Map note type to create note CRM associations.

        Args:
            note (Note): _description_

        Returns:
            list[CreateNoteCrmAssociation]: _description_
        """
        associations: list[CreateNoteCrmAssociation] = []

        for contact_id in note.contact_ids:
            associations.append(
                CreateNoteCrmAssociation(
                    organization_id=note.organization_id,
                    note_id=note.id,
                    domain_type=DomainType.NOTE,
                    contact_id=contact_id,
                    account_id=note.account_id,
                    pipeline_id=note.pipeline_id,
                    meeting_id=note.meeting_id,
                    task_id=note.task_id,
                    created_by_user_id=note.created_by_user_id,
                )
            )

        for user_id in note.user_ids:
            associations.append(
                CreateNoteCrmAssociation(
                    organization_id=note.organization_id,
                    note_id=note.id,
                    domain_type=DomainType.NOTE,
                    user_id=user_id,
                    account_id=note.account_id,
                    pipeline_id=note.pipeline_id,
                    meeting_id=note.meeting_id,
                    task_id=note.task_id,
                    created_by_user_id=note.created_by_user_id,
                )
            )

        return associations
