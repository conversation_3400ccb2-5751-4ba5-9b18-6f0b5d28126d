import uuid
from collections import defaultdict
from datetime import datetime
from uuid import UUID, uuid4

import pytz
from starlette.requests import Request

from salestech_be.common.exception import ConflictResourceError, ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import specified
from salestech_be.core.activity.service.activity_service import (
    ActivityService,
    get_activity_service_general,
)
from salestech_be.core.activity.types import (
    ActivityRequest,
    ActivitySubReferenceRequest,
)
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
)
from salestech_be.core.files.service.file_service import (
    FileService,
    file_service_from_engine,
)
from salestech_be.core.note.service.note_crm_association import (
    _NoteCrmAssociationMapper,
)
from salestech_be.core.note.types import Note
from salestech_be.db.dao.domain_crm_association_repository import (
    DomainCRMAssociationRepository,
)
from salestech_be.db.dao.note_repository import NoteReferenceRepository, NoteRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.activity import (
    ActivityPriority,
    ActivityReferenceIdType,
    ActivityStatus,
    ActivitySubReferenceType,
    ActivitySubType,
    ActivityType,
)
from salestech_be.db.models.note import (
    Note as DbNote,
)
from salestech_be.db.models.note import (
    NoteReference,
    NoteReferenceIdType,
    NoteType,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.note.schema import (  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateNoteRequest,
    PatchNoteRequest,
)

logger = get_logger(__name__)


class NoteService:
    def __init__(
        self,
        activity_service: ActivityService,
        file_service: FileService,
        note_repository: NoteRepository,
        note_reference_repository: NoteReferenceRepository,
        domain_crm_association_service: DomainCRMAssociationService,
    ):
        super().__init__()
        self.activity_service = activity_service
        self.file_service = file_service
        self.note_repository = note_repository
        self.note_reference_repository = note_reference_repository
        self.domain_crm_association_service = domain_crm_association_service

    async def get_by_id(
        self,
        note_id: UUID,
        organization_id: UUID,
    ) -> Note:
        db_note = not_none(
            await self.note_repository.find_by_tenanted_primary_key(
                table_model=DbNote,
                organization_id=organization_id,
                id=note_id,
            )
        )
        db_note_references = (
            await self.note_repository.find_note_references_by_note_ids(
                note_ids=[note_id],
                organization_id=organization_id,
            )
        )

        return Note.map_from_db(db_note, db_note_references)

    async def get_by_type_and_reference_id(
        self, note_type: NoteType, organization_id: uuid.UUID, reference_id: uuid.UUID
    ) -> Note:
        note_reference_id = reference_id
        referred_notes = await self.note_repository.find_notes_by_reference_id_and_type(
            organization_id=organization_id,
            reference_id=note_reference_id,
            note_type=note_type,
        )
        if not referred_notes:
            raise ResourceNotFoundError("No notes found for type and reference_id.")
        if len(referred_notes) > 1:
            logger.error(
                "Multiple records found for Notes type: "
                f"{note_type} and reference_id: {reference_id}"
            )
        return await self.get_by_id(
            note_id=referred_notes[0].id, organization_id=organization_id
        )

    async def list_all_by_reference_id_and_type(
        self,
        organization_id: UUID,
        note_type: NoteType,
        reference_id: UUID,
    ) -> list[Note]:
        db_notes = await self.note_repository.find_notes_by_reference_id_and_type(
            organization_id=organization_id,
            reference_id=reference_id,
            note_type=note_type,
        )
        return await self._form_notes_list(
            db_notes=db_notes, organization_id=organization_id
        )

    async def list_all_by_reference_ids_and_type(
        self,
        organization_id: UUID,
        note_type: NoteType,
        reference_ids: list[UUID],
    ) -> dict[UUID, list[Note]]:
        db_notes = await self.note_repository.find_notes_by_reference_ids_and_type(
            organization_id=organization_id,
            reference_ids=reference_ids,
            note_type=note_type,
        )
        note_id_to_reference_id_map = {}
        for db_note, reference_id in db_notes.items():
            note_id_to_reference_id_map[db_note.id] = reference_id
        notes = await self._form_notes_list(
            db_notes=list(db_notes.keys()), organization_id=organization_id
        )
        notes_by_reference_id = defaultdict(list)
        for note in notes:
            notes_by_reference_id[note_id_to_reference_id_map[note.id]].append(note)
        return notes_by_reference_id

    async def list_all(
        self,
        organization_id: UUID,
        include_archived: bool | None = False,
        exclude_types: list[NoteType] | None = None,
    ) -> list[Note]:
        exclude_note_types = (
            exclude_types
            if exclude_types is not None
            else [NoteType.TASK_COMMENT, NoteType.MEETING_COMMENT]
        )

        all_db_notes = await self.note_repository.find_notes_by_organization_id(
            organization_id=organization_id,
            exclude_deleted_or_archived=not include_archived,
        )
        db_notes = (
            [
                db_note
                for db_note in all_db_notes
                if db_note.type not in exclude_note_types
            ]
            if exclude_note_types
            else all_db_notes
        )
        return await self._form_notes_list(
            db_notes=db_notes, organization_id=organization_id
        )

    async def _form_notes_list(
        self, db_notes: list[DbNote], organization_id: UUID
    ) -> list[Note]:
        db_note_references = (
            await self.note_repository.find_note_references_by_note_ids(
                [db_note.id for db_note in db_notes],
                organization_id=organization_id,
            )
        )

        db_note_references_map: dict[UUID, list[NoteReference]] = defaultdict(list)
        for db_note_reference in db_note_references:
            db_note_references_map[db_note_reference.note_id].append(db_note_reference)
        return [
            Note.map_from_db(
                db_note=db_note,
                db_references=db_note_references_map[db_note.id],
            )
            for db_note in db_notes
        ]

    async def insert_note(
        self,
        user_id: UUID,
        organization_id: UUID,
        create_note_request: CreateNoteRequest,
    ) -> Note:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            request=create_note_request,
        ).info("Create note request")

        create_note_request.validate_request()
        # only one DEAL_PRIMARY_NOTE allowed
        if (
            create_note_request.note_type == NoteType.DEAL_PRIMARY_NOTE
            and create_note_request.type_reference_id
        ):
            referred_note = await self.get_by_type_and_reference_id(
                note_type=create_note_request.note_type,
                organization_id=organization_id,
                reference_id=create_note_request.type_reference_id,
            )
            if referred_note:
                raise ConflictResourceError(
                    f"Entity primary note already exists, note id: {referred_note.id}"
                )

        if create_note_request.attachment_ids:
            for attachment_id in create_note_request.attachment_ids:
                # Make sure any provided attachments exist.  Raises a
                # ResourceNotFoundError if no attachment found. We expect small number
                # of lookups.
                await self.file_service.get_attachment(
                    attachment_id=attachment_id, organization_id=organization_id
                )

        note_id = uuid4()
        # TODO: functionality-wise "note_reference" is equivalent to "domain_crm_association",
        #       we first do double write but need to move away from references.
        references = await self._map_note_request_to_references(
            create_note_request=create_note_request,
            user_id=user_id,
            organization_id=organization_id,
            note_id=note_id,
        )
        db_note = not_none(
            await self.note_repository.insert_note(
                note=DbNote(
                    id=note_id,
                    note_html=create_note_request.note_html,
                    title=create_note_request.title,
                    created_at=datetime.now(pytz.utc),
                    created_by_user_id=user_id,
                    organization_id=organization_id,
                    type=create_note_request.note_type,
                    attachment_ids=create_note_request.attachment_ids,
                ),
                references=references,
            )
        )

        associations_to_create = (
            _NoteCrmAssociationMapper.map_note_to_associations_create(
                note=Note.map_from_db(db_note, references)
            )
        )
        await self.domain_crm_association_service.bulk_create_domain_crm_associations(
            domain_crm_associations=associations_to_create,
        )

        # insert activity v2
        activity_sub_references: list[ActivitySubReferenceRequest] = []
        if create_note_request.contact_ids:
            for contact_id in create_note_request.contact_ids:
                activity_sub_references.append(
                    ActivitySubReferenceRequest(
                        type=ActivitySubReferenceType.NOTE_MENTION,
                        value=str(contact_id),
                        contact_id=contact_id,
                    )
                )
        await self.activity_service.insert_activity(
            organization_id=organization_id,
            insert_activity_request=ActivityRequest(
                type=ActivityType.NOTE,
                sub_type=ActivitySubType.NOTE_CREATED,
                priority=ActivityPriority.MEDIUM,
                status=ActivityStatus.NEW,
                owner_user_id=user_id,
                account_id=create_note_request.account_id,
                reference_id_type=ActivityReferenceIdType.NOTE_ID,
                reference_id=str(note_id),
                display_name="Note Created.",
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                sub_references=activity_sub_references,
            ),
        )
        return await not_none(self.get_by_id(note_id, organization_id))

    async def _map_note_request_to_references(
        self,
        create_note_request: CreateNoteRequest,
        user_id: UUID,
        organization_id: UUID,
        note_id: UUID,
    ) -> list[NoteReference]:
        references: list[NoteReference] = []
        if create_note_request.account_id:
            references.append(
                NoteReference(
                    id=uuid4(),
                    organization_id=organization_id,
                    note_id=note_id,
                    reference_id=str(create_note_request.account_id),
                    reference_id_type=NoteReferenceIdType.ACCOUNT_ID,
                    created_at=datetime.now(pytz.utc),
                    created_by_user_id=user_id,
                )
            )
        if create_note_request.meeting_id:
            references.append(
                NoteReference(
                    id=uuid4(),
                    organization_id=organization_id,
                    note_id=note_id,
                    reference_id=str(create_note_request.meeting_id),
                    reference_id_type=NoteReferenceIdType.MEETING_ID,
                    created_at=datetime.now(pytz.utc),
                    created_by_user_id=user_id,
                )
            )
        if create_note_request.contact_ids:
            for contact_id in create_note_request.contact_ids:
                references.append(
                    NoteReference(
                        id=uuid4(),
                        organization_id=organization_id,
                        note_id=note_id,
                        reference_id=str(contact_id),
                        reference_id_type=NoteReferenceIdType.CONTACT_ID,
                        created_at=datetime.now(pytz.utc),
                        created_by_user_id=user_id,
                    )
                )
        if create_note_request.user_ids:
            for _user_id in create_note_request.user_ids:
                references.append(
                    NoteReference(
                        id=uuid4(),
                        organization_id=organization_id,
                        note_id=note_id,
                        reference_id=str(_user_id),
                        reference_id_type=NoteReferenceIdType.USER_ID,
                        created_at=datetime.now(pytz.utc),
                        created_by_user_id=_user_id,
                    )
                )
        if create_note_request.task_id:
            references.append(
                NoteReference(
                    id=uuid4(),
                    organization_id=organization_id,
                    note_id=note_id,
                    reference_id=str(create_note_request.task_id),
                    reference_id_type=NoteReferenceIdType.TASK_ID,
                    created_at=datetime.now(pytz.utc),
                    created_by_user_id=user_id,
                )
            )
        return references

    async def patch_by_id(
        self,
        organization_id: UUID,
        user_id: UUID,
        note_id: UUID,
        patch_note_request: PatchNoteRequest,
    ) -> Note:
        logger.bind(
            organization_id=organization_id, user_id=user_id, note_id=note_id
        ).info("Patch note request")
        existing_note = await self.get_by_id(note_id, organization_id)

        note_fields_to_update = patch_note_request.model_dump(
            include={"title", "note_html", "attachment_ids"}
        )

        if "attachment_ids" in note_fields_to_update:
            for attachment_id in note_fields_to_update["attachment_ids"]:
                existing_attachment_ids = existing_note.attachment_ids or []
                if attachment_id not in existing_attachment_ids:
                    # Validate any new attachment_ids
                    await self.file_service.get_attachment(
                        attachment_id=attachment_id, organization_id=organization_id
                    )
        if note_fields_to_update:
            note_fields_to_update["updated_at"] = zoned_utc_now()
            note_fields_to_update["updated_by_user_id"] = user_id
            not_none(
                await self.note_repository.update_by_tenanted_primary_key(
                    table_model=DbNote,
                    primary_key_to_value={"id": note_id},
                    organization_id=organization_id,
                    column_to_update=note_fields_to_update,
                )
            )

        if specified(patch_note_request.contact_ids):
            await self._patch_note_reference(
                note_id=note_id,
                organization_id=organization_id,
                reference_id_type=NoteReferenceIdType.CONTACT_ID,
                user_id=user_id,
                reference_ids=patch_note_request.contact_ids,
                existing_reference_ids=existing_note.contact_ids,
            )
        if specified(patch_note_request.user_ids):
            await self._patch_note_reference(
                note_id=note_id,
                organization_id=organization_id,
                reference_id_type=NoteReferenceIdType.USER_ID,
                user_id=user_id,
                reference_ids=patch_note_request.user_ids,
                existing_reference_ids=existing_note.user_ids,
            )
        if specified(patch_note_request.account_id):
            await self._patch_note_reference(
                note_id=note_id,
                organization_id=organization_id,
                reference_id_type=NoteReferenceIdType.ACCOUNT_ID,
                user_id=user_id,
                reference_ids=[patch_note_request.account_id]
                if patch_note_request.account_id
                else [],
                existing_reference_ids=[existing_note.account_id]
                if existing_note.account_id
                else [],
            )
        return await not_none(self.get_by_id(note_id, organization_id))

    async def _patch_note_reference(
        self,
        note_id: UUID,
        organization_id: UUID,
        reference_id_type: NoteReferenceIdType,
        user_id: UUID,
        reference_ids: list[str] | None,
        existing_reference_ids: list[UUID],
    ) -> None:
        new_reference_set = set(reference_ids) if reference_ids else set()
        existing_reference_set = set(  # noqa: C403
            [str(reference_id) for reference_id in existing_reference_ids]
        )
        reference_ids_to_remove = existing_reference_set - new_reference_set
        for reference_id_to_remove in reference_ids_to_remove:
            await self.note_repository.delete_note_reference(
                note_id=note_id,
                organization_id=organization_id,
                reference_id=reference_id_to_remove,
                reference_id_type=reference_id_type,
                deleted_by_user_id=user_id,
                deleted_at=datetime.now(pytz.UTC),
            )
        reference_ids_to_add = new_reference_set - existing_reference_set
        for reference_id_to_add in reference_ids_to_add:
            await self.note_repository.insert(
                NoteReference(
                    id=uuid4(),
                    note_id=note_id,
                    organization_id=organization_id,
                    reference_id=reference_id_to_add,
                    reference_id_type=reference_id_type,
                    created_at=datetime.now(pytz.utc),
                    created_by_user_id=user_id,
                )
            )

    async def delete_by_id(
        self,
        user_id: UUID,
        organization_id: UUID,
        note_id: UUID,
    ) -> DbNote:
        logger.bind(
            user_id=user_id, organization_id=organization_id, note_id=note_id
        ).info("Delete note request")
        deleted_note = await self.note_repository.update_by_tenanted_primary_key(
            table_model=DbNote,
            organization_id=organization_id,
            primary_key_to_value={"id": note_id},
            column_to_update={
                "deleted_by_user_id": user_id,
                "deleted_at": datetime.now(pytz.UTC),
            },
        )
        if not deleted_note:
            raise ResourceNotFoundError

        return deleted_note


class SingletonNoteService(Singleton, NoteService):
    pass


def get_note_service_general(
    db_engine: DatabaseEngine,
) -> NoteService:
    return SingletonNoteService(
        activity_service=get_activity_service_general(db_engine=db_engine),
        file_service=file_service_from_engine(engine=db_engine),
        note_repository=NoteRepository(engine=db_engine),
        note_reference_repository=NoteReferenceRepository(engine=db_engine),
        domain_crm_association_service=DomainCRMAssociationService(
            domain_crm_association_repository=DomainCRMAssociationRepository(
                engine=db_engine
            ),
        ),
    )


def get_note_service(request: Request) -> NoteService:
    return get_note_service_general(db_engine=get_db_engine(request))
