import asyncio
import random
import re
from datetime import UTC, datetime
from uuid import UUID

import tldextract

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    ConflictResourceError,
    DatabaseError,
    ErrorDetails,
    InvalidArgumentError,
    PaymentError,
    ServiceError,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.util import format_cents_to_usd
from salestech_be.core.common.accounts_receivable import AccountsReceivable
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.email.account.service_v2 import (
    get_email_account_service_v2_by_db_engine,
)
from salestech_be.core.email.outbound_domain.dns_check_service import (
    check_dns,
    is_domain_available,
)
from salestech_be.core.email.outbound_domain.domain_health_check_schedule_service import (
    get_domain_health_check_schedule_service,
)
from salestech_be.core.email.outbound_domain.schema import (
    ArchiveDomainRequest,
    BuyDomainRequest,
    ContactDetails,
    DomainBillingCycle,
    DomainPaymentMethod,
    DomainResponse,
    FindDomainRequest,
    FindDomainResponse,
)
from salestech_be.core.email.outbound_domain.types_v2 import (
    DomainConfigurationModel,
    DomainPurchaseWorkflowInput,
    OutboundDomainHealth,
    OutboundDomainV2,
)
from salestech_be.core.organization.service.organization_service_v2 import (
    get_organization_service_v2_from_engine,
)
from salestech_be.core.quota.service.quota_policy_service import (
    get_quota_policy_service_from_engine,
)
from salestech_be.core.quota.service.quota_service import get_quota_service_by_db_engine
from salestech_be.core.quota.type.quota_policy_type import (
    OutboundEmailQuota,
    QuotaUsageWithActual,
)
from salestech_be.db.dao.email_account import EmailAccountRepository
from salestech_be.db.dao.outbound_repository import (
    DomainHealthRepository,
    OutboundDomainRepository,
    OutboundWorkspaceRepository,
)
from salestech_be.db.dao.quota_repository import QuotaPolicyRepository
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.core.types import EntityDnsRecord
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.outbound import (
    DomainHealth,
    OutboundDomain,
    OutboundDomainStatus,
    OutboundVendor,
    OutboundWorkspace,
)
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.integrations.infraforge.async_infraforge_client import (
    AsyncInfraForgeClient,
)
from salestech_be.integrations.infraforge.type import (
    ExternalBuyDomainsRequest,
    ExternalBuyDomainsResponse,
    ExternalCreateWorkspaceRequest,
    ExternalGetDomainsResponse,
    InfraforgeDomainStatus,
    OneTimeDomainPurchaseInvoice,
    SetupResponse,
)
from salestech_be.integrations.slack.slack_client import (
    SlackChannels,
    SlackClient,
    SlackWebhookUrls,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import (
    TemporalTaskQueue,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.workflows.email.email_account_lifecycle_constants import (
    EMAIL_ACCOUNT_LIFECYCLE_ACTIVATE_WORKFLOW_ID_PREFIX,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    SIGNAL_DOMAIN_ACTIVE,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger(__name__)

SPECIAL_OR_WHITESPACE_PATTERN = re.compile(
    r"[^a-zA-Z0-9\-]"
)  # this regex is used to check if a string contains any non-alphanumeric characters or whitespace (except for hyphens)


class OutboundDomainService:
    def __init__(self, engine: DatabaseEngine) -> None:
        self.infraforge_client = AsyncInfraForgeClient()
        self.outbound_domain_repository = OutboundDomainRepository(engine=engine)
        self.outbound_workspace_repository = OutboundWorkspaceRepository(engine=engine)
        self.domain_health_repository = DomainHealthRepository(engine=engine)
        self.organization_service_v2 = get_organization_service_v2_from_engine(
            db_engine=engine
        )
        self.domain_health_check_schedule_service = (
            get_domain_health_check_schedule_service(engine=engine)
        )
        self.quota_policy_repository = QuotaPolicyRepository(engine=engine)
        self.quota_policy_service = get_quota_policy_service_from_engine(engine=engine)
        self.quota_service = get_quota_service_by_db_engine(db_engine=engine)
        self.email_account_service_v2 = get_email_account_service_v2_by_db_engine(
            db_engine=engine
        )
        self.slack_client = SlackClient(
            webhook_url=SlackWebhookUrls.DOMAIN_PURCHASE_INVOICE
        )
        self.slack_alert_client = SlackClient(
            webhook_url=SlackWebhookUrls.DOMAIN_PURCHASE_SUCCESS_ALERT
        )
        self.email_account_repo = EmailAccountRepository(engine=engine)
        self.user_repository = UserRepository(engine=engine)

    async def get_or_create_workspace(
        self,
        organization_id: UUID,
        user_id: UUID,
        is_mock_record: bool = False,
    ) -> OutboundWorkspace:
        """
        Create a new workspace for the organization on attempt to buy the first domain. A workspace is a collection of domains that exists 1 to 1 with an organization and is only used for Infraforge's domain management.
        In this same function, we create all necessary quota policies for the organization, including domain quota, mailbox quota, and plan included cost domain and mailbox quota.
        """
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            is_mock_record=is_mock_record,
        ).info("[get_or_create_workspace] Get or create new workspace request")
        workspace = await self.outbound_workspace_repository.get_outbound_workspace_by_organization_id(
            organization_id=organization_id
        )
        if workspace:
            return workspace

        logger.bind(organization_id=organization_id).info(
            "[get_or_create_workspace] Creating new workspace."
        )
        fetch_org = await self.organization_service_v2.get_organization_by_id(
            organization_id
        )

        # Create quota_policy if it doesn't exist for the org
        await self.quota_policy_service.upsert_quota_policy(
            organization_id=organization_id,
            entity_id=organization_id,
            resource=QuotaConsumingResource.DOMAIN,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            applied_sub_entity_types=None,
            quota_limit=settings.quota_per_org_max_domains,
            period=QuotaPeriod.ANNUAL,
            user_id=user_id,
        )

        """
        As a part of our default offering, we will include (at no additional cost to the customer) up to and including <plan_included_domains_per_org> domains, at a cost up to and including < plan_included_domain_max_cost_per_org>, per organization.
        # Though the period specified is ANNUAL, it is effectively against the lifetime of the org until there is an ability to enable renewal or cancellation.
        """
        await self.quota_policy_service.upsert_quota_policy(
            organization_id=organization_id,
            entity_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            applied_sub_entity_types=None,
            quota_limit=settings.quota_per_org_plan_included_domains,
            period=QuotaPeriod.ANNUAL,
            user_id=user_id,
        )

        """
        As a part of our offering, we will include (at no additional cost to the customer) up to and including <PLAN_INCLUDED_MAILBOXES_PER_ORG> mailboxes, per organization.
        Though the period specified is ANNUAL, it is effectively against the lifetime of the org until there is an ability to enable renewal or cancellation.
        """
        await self.quota_policy_service.upsert_quota_policy(
            organization_id=organization_id,
            entity_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            applied_sub_entity_types=None,
            quota_limit=settings.quota_per_org_plan_included_mailboxes,
            period=QuotaPeriod.ANNUAL,
            user_id=user_id,
        )

        # Create quota_policy for mailboxes for an organization on workspace creation
        await self.quota_policy_service.upsert_quota_policy(
            organization_id=organization_id,
            entity_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            applied_sub_entity_types=None,
            quota_limit=settings.quota_per_org_max_mailboxes,  # up for debate
            period=QuotaPeriod.ANNUAL,  # TODO: change to some value that makes sense, but as long as the account has the mailbox, it counts toward the quota
            user_id=user_id,
        )

        if is_mock_record:
            logger.bind(organization_id=organization_id).info(
                "[get_or_create_workspace] Creating mock workspace."
            )
            return await self.outbound_workspace_repository.insert_outbound_workspace(
                organization_id=organization_id,
                name=fetch_org.display_name,
                vendor=OutboundVendor.INFRAFORGE,
                external_id=f"mock_id for :{fetch_org.display_name}",
                is_mock_record=True,
            )

        infraforge_response = await self.infraforge_client.create_workspace(
            ExternalCreateWorkspaceRequest(
                name=fetch_org.display_name,
                attachUniqueIp=True,  # use new IP for new org
            )
        )
        return await self.outbound_workspace_repository.insert_outbound_workspace(
            organization_id=organization_id,
            name=fetch_org.display_name,
            vendor=OutboundVendor.INFRAFORGE,
            external_id=infraforge_response.id,
            is_mock_record=False,
        )

    async def list_domains(
        self,
        organization_id: UUID,
    ) -> list[DomainResponse]:
        db_domains = await self.outbound_domain_repository.find_many(
            {"organization_id": organization_id}
        )
        return [DomainResponse.from_db_model(domain) for domain in db_domains]

    async def validate_domain_request_and_get_purchasable_domains(
        self,
        request: BuyDomainRequest,
        user_id: UUID,
        organization_id: UUID,
        workspace: OutboundWorkspace,
    ) -> list[str]:
        existing_domains = (
            await self.outbound_domain_repository.get_all_domains()  # all domains present in the db
        )
        existing_domain_names = {
            domain.domain for domain in existing_domains
        }  # normalize domain names for comparison
        logger.bind(existing_domain_names=existing_domain_names).info(
            "[buy_domains] Existing domains."
        )
        new_domains = [
            domain.lower()
            for domain in request.domains
            if domain.lower() not in existing_domain_names
        ]
        logger.bind(new_domains=new_domains).info(
            "[buy_domains] New domains to purchase."
        )
        if not new_domains:
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code=ErrorCode.DOMAIN_ALREADY_EXISTS,
                    details="All requested domains have already been purchased and are not available.",
                    conflicted_existing_object=StdObjectIdentifiers.domain.identifier,
                    reference_id=str(workspace.id),
                )
            )
        await self.quota_policy_service.ensure_quota_policy_exists(  # Make sure the Reevo plan included domain policy exists
            organization_id=organization_id,
            user_id=user_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=QuotaConsumingResource.DOMAIN,
            entity_id=organization_id,
            quota_limit=settings.quota_per_org_max_domains,
            period=QuotaPeriod.ANNUAL,
        )

        # Create the quota summary to use the remaining plan included domains
        domain_quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
        # quota summary created for domains
        # Ensure that the quota policy exists for overall total domains that the customer organization can have
        logger.bind(domain_quota_summary=domain_quota_summary).info(
            "[buy_domains] Domain quota summary."
        )
        if domain_quota_summary.total_remaining <= 0:
            raise PaymentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.QUOTA_LIMIT_EXCEEDED,
                    details=f"Your desired purchase did not go through since it would exceed your organization's quota of {domain_quota_summary.total_limit}. Please contact support for further assistance.",
                ),
            )
        # Ensure that the quota policy exists for domains that Reevo purchases for the customer included in the quota policy
        await self.quota_policy_service.ensure_quota_policy_exists(
            organization_id=organization_id,
            user_id=user_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            entity_id=organization_id,
            quota_limit=settings.quota_per_org_plan_included_domains,
            period=QuotaPeriod.ANNUAL,
        )
        if request.is_mock_record:
            purchasable_domains = new_domains
        else:
            purchasable_domains = []
            for new_domain in new_domains:  # Make sure that we can only buy domains that are available (not owned by other organizations too)
                available_domain = (
                    await self.infraforge_client.check_domain_availability(
                        domain=new_domain
                    )
                )  # make sure that we only add domains that we can add
                if available_domain and available_domain.available:
                    purchasable_domains.append(new_domain)

        if (
            not purchasable_domains
        ):  # There aren't any domains that are new or aren't bought by someone else
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code=ErrorCode.DOMAIN_NOT_AVAILABLE,
                    details="There are no requested domains that are available.",
                    conflicted_existing_object=StdObjectIdentifiers.domain.identifier,
                    reference_id=str(workspace.id),
                )
            )
        return purchasable_domains

    async def _process_domain_purchase(
        self,
        infraforge_response: ExternalBuyDomainsResponse,
        request: BuyDomainRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> OutboundDomain | None:
        if not infraforge_response.domains:
            return None

        domain_from_purchase: ExternalGetDomainsResponse = infraforge_response.domains[
            0
        ]
        full_domain_name = domain_from_purchase.sld + "." + domain_from_purchase.tld
        outbound_workspace = await self.get_or_create_workspace(
            organization_id=organization_id,
            user_id=user_id,
            is_mock_record=request.is_mock_record,
        )

        transaction = AccountsReceivable.INVOICED_TO_CUSTOMER
        # I've moved this here so that we can configure the transaction type of the domain
        # before we update the OutboundDomain so that we don't have unnecessary extra queries
        plan_included_domain_covered_quota_policy_summary = (
            await self.quota_service.get_quota_summary_per_resource(
                organization_id=organization_id,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
                period=QuotaPeriod.ANNUAL,
            )
        )
        """
        This represents what happens after purchasing a domain. We check if the org has any Reevo plan included domains left.
        If it does not OR the domain costs between plan_included_domain_max_cost_per_org and max_accounts_receivable_per_domain_purchase,
        we create an invoice slack notification. Otherwise, this should just increase usage since we've covered the 2 cases where
        invoices would be created. This is after purchase, if we want to block $100+ purchases, it has to happen on FE or on find_domains
        since this is a blind purchase.
        """

        if plan_included_domain_covered_quota_policy_summary.total_remaining <= 0 or (
            settings.max_cost_per_plan_included_domain
            < infraforge_response.invoice.total
            <= settings.max_cost_per_invoiced_domain
        ):
            # If the price is between $20 and $100 OR we've already covered 1 domain, this transaction is INVOICE type
            logger.bind(organization_id=organization_id).info(
                "[buy_domains] No remaining quota for Reevo plan included domains, sending invoice to customer."
            )
            await self._send_domain_purchase_invoice_slack_message(
                infraforge_response=infraforge_response,
                full_domain_name=full_domain_name,
                organization_id=organization_id,
            )

        # Case in which the reevo plan included domain quota has remaining quota
        else:
            if not request.is_mock_record:
                transaction = AccountsReceivable.INCLUDED_IN_PLAN
            logger.bind(organization_id=organization_id).info(
                "[buy_domains] Invoice total is less than maximum Reevo coverage price, increasing quota usage."
            )
            await self.increment_plan_included_domain_quota_usage(
                organization_id=organization_id, is_mock_record=request.is_mock_record
            )

        logger.bind(
            organization_id=organization_id,
            domain=request.domains[0],
            external_id=infraforge_response.setup.id,
            is_mock_record=request.is_mock_record,
            transaction=transaction,
        ).info("Updating domain values in database.")

        outbound_domain = OutboundDomain(
            organization_id=organization_id,
            domain=full_domain_name,
            external_id=domain_from_purchase.id,
            created_by_user_id=user_id,
            status=OutboundDomainStatus.ACTIVE,
            workspace_id=outbound_workspace.id,
            forward_to_domain=infraforge_response.setup.forwardToDomain,
            is_mock_record=request.is_mock_record,
            invoice=infraforge_response.invoice,
            transaction_type=transaction,
        )

        db_domain = await self.outbound_domain_repository.upsert_outbound_domain(
            outbound_domain=outbound_domain,
        )
        if not db_domain:
            raise DatabaseError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DATABASE_ERROR,
                    details="Failed to create domain in database",
                )
            )

        await self.increment_domain_quota_usage(organization_id)
        return db_domain

    async def increment_plan_included_domain_quota_usage(
        self,
        organization_id: UUID,
        is_mock_record: bool,
    ) -> None:
        # Don't increase plan-included usage if it is a mock domain
        if is_mock_record:
            return
        try:
            plan_domain_quota_usage = await self.quota_service.increase_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
                usage=1,
                timestamp=zoned_utc_now(),
            )
            logger.bind(plan_domain_quota_usage=plan_domain_quota_usage).debug(
                "[buy_domains] Increased quota usage for plan included domains"
            )
        except Exception as e:
            logger.bind(
                organization_id=organization_id,
            ).error("Failed to increase plan-included-cost-domain usage.", exc_info=e)

    async def increment_domain_quota_usage(
        self,
        organization_id: UUID,
    ) -> None:
        # If the domain purchase goes through, we should increment the usage by 1.
        domain_usage = await self.quota_service.increase_usage(
            organization_id=organization_id,
            entity_id=organization_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=QuotaConsumingResource.DOMAIN,
            usage=1,
            timestamp=zoned_utc_now(),
        )
        logger.bind(domain_usage=domain_usage).info(
            f"[buy_domains] Increased domain quota usage for organization id ({organization_id})"
        )

    async def purchase_and_process_domains(
        self,
        request: BuyDomainRequest,
        user_id: UUID,
        organization_id: UUID,
        purchasable_domains: list[str],
    ) -> OutboundDomain | None:
        purchase_domains_response = await self._purchase_domains(
            request=request,
            user_id=user_id,
            organization_id=organization_id,
            purchasable_domains=purchasable_domains,
        )
        return await self._process_domain_purchase(
            infraforge_response=purchase_domains_response,
            request=request,
            user_id=user_id,
            organization_id=organization_id,
        )

    async def _purchase_domains(
        self,
        request: BuyDomainRequest,
        user_id: UUID,
        organization_id: UUID,
        purchasable_domains: list[str],
    ) -> ExternalBuyDomainsResponse:
        outbound_workspace = await self.get_or_create_workspace(
            organization_id=organization_id,
            user_id=user_id,
            is_mock_record=request.is_mock_record,
        )
        infraforge_request = ExternalBuyDomainsRequest(
            contactDetails=ContactDetails.get_reevo_default_contact().to_infraforge_model(  # since the customer is buying through us, default with reevo details
                primary_domain=request.forward_to_domain
            ),
            domains=purchasable_domains,
            workspaceId=outbound_workspace.external_id,
        )

        if request.is_mock_record:
            logger.bind(infraforge_request=infraforge_request).info(
                "[buy_domains] Mock infraforge request."
            )
            invoice = OneTimeDomainPurchaseInvoice(
                amountPaid=1400,
                creditsApplied=0,
                id="mock_id",
                paidAt=str(zoned_utc_now()),
                subTotal=1400,
                total=1400,
            )
            domains = []
            for domain in purchasable_domains:
                domains.append(
                    ExternalGetDomainsResponse(
                        id="mock_" + domain,
                        createdAt=str(zoned_utc_now()),
                        updatedAt=str(zoned_utc_now()),
                        setupId=f"mock_setup_id_for_{domain}",
                        workspaceId=outbound_workspace.external_id,
                        sld=tldextract.extract(domain).domain,
                        status=InfraforgeDomainStatus.ACTIVE,
                        tld=tldextract.extract(domain).suffix,
                    )
                )
            setup = SetupResponse.from_mock_response(
                forward_to_domain=request.forward_to_domain,
            )
            infraforge_response = ExternalBuyDomainsResponse(
                domains=domains,
                setup=setup,
                invoice=invoice,
            )
        else:
            logger.bind(infraforge_request=infraforge_request).info(
                "[buy_domains] Infraforge request."
            )
            infraforge_response = await self.infraforge_client.buy_domains(
                infraforge_request
            )
        await self._send_domain_purchase_slack_alert(
            infraforge_response=infraforge_response,
            full_domain_name=purchasable_domains[0],
            organization_id=organization_id,
        )
        return infraforge_response

    async def get_domain_if_not_available(
        self, domain_name: str
    ) -> ExternalBuyDomainsResponse:
        # TODO: @benson implement this once approved
        raise NotImplementedError("Not implemented")

    async def perform_domain_health_checks(
        self,
        outbound_domain: OutboundDomain,
    ) -> OutboundDomainV2:
        formatted_domain = outbound_domain.domain + "."
        if outbound_domain.is_mock_record:
            domain_health = DomainHealth(
                domain_id=outbound_domain.id,
                mx_records=not_none(
                    EntityDnsRecord.list_from_request_field(
                        [
                            "10 mx1.reevo-internal.mocked",
                            "20 mx2.reevo-internal.mocked",
                        ]
                    )
                ),
                spf_records=not_none(
                    EntityDnsRecord.list_from_request_field(
                        ["v=spf1 include:_spf.reevo-internal.mocked ~all"]
                    )
                ),  # using .list_from_request_field since they're all lists for consistency
                dkim_record=not_none(
                    EntityDnsRecord.list_from_request_field(
                        ["v=DKIM1; k=rsa; p=ThisIsATotallyMockedDkimPublicKey..."]
                    )
                ),
                dmarc_record=not_none(
                    EntityDnsRecord.list_from_request_field(
                        [
                            "v=DMARC1; p=reject; rua=mailto:<EMAIL>"
                        ]
                    )
                ),
                is_mock_record=outbound_domain.is_mock_record,
            )
            domain_health_inserted = (
                await self.domain_health_repository.upsert_domain_health(
                    domain_health=domain_health,
                )
            )
            if not domain_health:
                raise DatabaseError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.DATABASE_ERROR,
                        details=f"Failed to create domain health record in database for {outbound_domain.id}",
                    )
                )
        else:
            dns_records = await check_dns(formatted_domain)
            domain_health = DomainHealth(
                domain_id=outbound_domain.id,
                mx_records=dns_records.mx_records,
                dmarc_record=dns_records.dmarc_record,
                dkim_record=dns_records.dkim_record,
                spf_records=dns_records.spf_records,
                is_mock_record=outbound_domain.is_mock_record,
            )
            domain_health_inserted = (
                await self.domain_health_repository.upsert_domain_health(
                    domain_health=domain_health,
                )
            )
        if not domain_health_inserted:
            raise DatabaseError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DATABASE_ERROR,
                    details=f"Failed to create domain health record in database for {outbound_domain.id}",
                )
            )
        db_domain_health = DomainConfigurationModel.from_db_model(
            domain_health=domain_health_inserted,
            domain_forwarding=bool(outbound_domain.forward_to_domain),
        )
        if dns_records.is_complete:
            evaluated_domain_health = OutboundDomainHealth.HEALTHY
        else:
            evaluated_domain_health = OutboundDomainHealth.PENDING

        return OutboundDomainV2.from_db_model(
            domain=outbound_domain,
            domain_health=evaluated_domain_health,
            dns_configuration=db_domain_health,
        )

    async def buy_domains(
        self, request: BuyDomainRequest, user_id: UUID, organization_id: UUID
    ) -> list[OutboundDomainV2]:
        # if not is_prod_env():  # if is dev or local, should be mock only now that we know it works and have working real domains on zvtestabc
        #     request.is_mock_record = True
        logger.bind(
            organization_id=organization_id, user_id=user_id, request=request
        ).info("[buy_domains] Buy domains request")
        workspace = await self.get_or_create_workspace(
            organization_id=organization_id,
            is_mock_record=request.is_mock_record,
            user_id=user_id,
        )
        if workspace.is_mock_record and not request.is_mock_record:
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code=ErrorCode.MOCK_WORKSPACE_CANNOT_BUY_REAL_DOMAIN,
                    details="Cannot attempt to purchase a real domain when a mock workspace exists for current organization.",
                    reference_id=str(workspace.id),
                    conflicted_existing_object=StdObjectIdentifiers.workspace.identifier,
                )
            )

        purchasable_domains = (
            await self.validate_domain_request_and_get_purchasable_domains(
                request=request,
                user_id=user_id,
                organization_id=organization_id,
                workspace=workspace,
            )
        )
        # only supports one domain to purchase at a time, validation function ensures this is not none
        domain_to_purchase = not_none(purchasable_domains[0])

        if request.is_mock_record:
            outbound_domain = await self.purchase_and_process_domains(
                request=request,
                user_id=user_id,
                organization_id=organization_id,
                purchasable_domains=purchasable_domains,
            )
            if not outbound_domain:
                return []
            return [
                OutboundDomainV2.from_db_model(
                    domain=outbound_domain,
                    domain_health=OutboundDomainHealth.HEALTHY,
                    dns_configuration=DomainConfigurationModel.from_db_model(
                        domain_health=DomainHealth(
                            domain_id=outbound_domain.id,
                            mx_records=[
                                EntityDnsRecord(record="mx1.domain.com"),
                            ],
                            dmarc_record=[
                                EntityDnsRecord(
                                    record="v=DMARC1; p=reject; rua=mailto:<EMAIL>"
                                )
                            ],
                            dkim_record=[
                                EntityDnsRecord(
                                    record="v=DKIM1; k=rsa; p=ThisIsATotallyMockedDkimPublicKey..."
                                )
                            ],
                            spf_records=[
                                EntityDnsRecord(
                                    record="v=spf1 include:_spf.reevo-internal.mocked ~all"
                                )
                            ],
                        ),
                        domain_forwarding=bool(outbound_domain.forward_to_domain),
                    ),
                )
            ]

        outbound_domain = OutboundDomain(
            organization_id=organization_id,
            domain=domain_to_purchase,
            external_id="",
            created_by_user_id=user_id,
            status=OutboundDomainStatus.ATTEMPTING_TO_PURCHASE,
            workspace_id=workspace.id,
            is_mock_record=request.is_mock_record,
        )
        await self.outbound_domain_repository.insert(outbound_domain)

        client = await get_temporal_client()
        await client.start_workflow(
            "DomainPurchaseWorkflow",
            args=[
                DomainPurchaseWorkflowInput(
                    request=request,
                    user_id=user_id,
                    organization_id=organization_id,
                    purchasable_domains=purchasable_domains,
                )
            ],
            id=f"domain_purchase_workflow_{'_'.join(purchasable_domains)}",
            task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
        )
        await self.domain_health_check_schedule_service.create_domain_health_check_per_domain_schedule(
            domain_id=outbound_domain.id,
            organization_id=outbound_domain.organization_id,
            domain_name=outbound_domain.domain,
        )
        return [
            OutboundDomainV2.from_db_model(
                domain=outbound_domain,
                domain_health=OutboundDomainHealth.PENDING,
                dns_configuration=None,
            )
        ]

    async def find_domains(  # noqa: C901, PLR0912, PLR0915
        self,
        request: FindDomainRequest,
        user_id: UUID,
        organization_id: UUID,
    ) -> list[FindDomainResponse]:
        results = []
        # extract SLD and TLD from the request.
        # e.g. reevo.ai's SLD is "reevo" an the TLD is "ai".
        extracted = tldextract.extract(request.query)
        # normalize sld and tld by forcing lowercase
        # subdomains don't have to be cleaned since they're just a condition for error checking
        sld = (extracted.subdomain + extracted.domain).lower()

        # clean all of the tld by removing the period to keep things consistent and append only before we need to use it
        initial_tld = extracted.suffix.lower().removeprefix(".")

        check_find_request(sld=sld, tld=initial_tld, query=request.query)
        if not sld:
            if not contains_special_or_whitespace(
                request.query
            ):  # if we ever get to this point, it's because tldextract failed to extract the sld and tld, so request must be in a weird format or is a tld normally
                logger.bind(sld=sld, initial_tld=initial_tld).info(
                    f"[find_domains]Special sld or tld provided: {request.query} is widely recognized as a tld or is a faulty input",
                )
                sld = request.query
                initial_tld = "com"
            else:
                logger.bind(sld=sld, initial_tld=initial_tld).info(
                    f"[find_domains]Special sld or tld provided: {request.query} looks like it contains special characters or something is wrong",
                )
                sld = re.sub(r"[^a-zA-Z0-9]", "", request.query)
                initial_tld = "com"
        if (
            len(sld) > settings.domain_sld_max_len
            or len(sld) < settings.domain_sld_min_len
        ):
            raise InvalidArgumentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.INVALID_REQUEST,
                    details=f"Invalid request: {request.query} does not fit the length requirements (3-63 characters)",
                )
            )
        initial_request = ""
        all_domains = await self.outbound_domain_repository.get_all_domains()
        all_domains_names = {domain.domain.lower() for domain in all_domains}

        """
        Specifies the maximum number of domains included in an organization's subscription plan.
        Domains beyond this count will be invoiced to the organization.
        Typically should already exist, but this ensures existence in the for correct modal configuration.
        """
        await self.quota_policy_service.ensure_quota_policy_exists(
            organization_id=organization_id,
            user_id=user_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            entity_id=organization_id,
            quota_limit=settings.quota_per_org_plan_included_domains,
            period=QuotaPeriod.ANNUAL,
        )
        plan_included_domain_quota_policy_summary = (
            await self.quota_service.get_quota_summary_per_resource(
                organization_id=organization_id,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
                period=QuotaPeriod.ANNUAL,
            )
        )
        if sld and initial_tld:
            logger.bind(query=request.query, sld=sld, initial_tld=initial_tld).info(
                "[find_domains] Checking domain availability"
            )
            if f"{sld}.{initial_tld}" in all_domains_names:
                logger.bind(query=request.query).info(
                    "[find_domains] Domain already exists in our database, so let's just check alternatives"
                )
                results.append(
                    FindDomainResponse(
                        domain=f"{sld}.{initial_tld}",
                        available=False,
                        price_cents=0,
                        billing_cycle=DomainBillingCycle.ANNUALLY,
                    )
                )
            else:
                # this is the case in where we have a whole domain like "testing.com" and we need to check if it's available since it's not in our database
                response = await self.infraforge_client.check_domain_availability(
                    f"{sld}.{initial_tld}".lower()
                )
                if not response:
                    logger.bind(query=request.query).info(
                        "External service error checking domain availability"
                    )
                    return []
                initial_request = request.query
                # We are now including domains that are above the previous $20
                initial_domain = FindDomainResponse.from_check_availability_response(
                    response
                )
                if (
                    settings.enable_new_domain_payments
                    or str(organization_id)
                    in settings.enable_new_domain_payments_org_ids
                ):
                    initial_domain.payment_method = self.find_correct_payment_method(
                        domain_price_cents=initial_domain.price_cents,
                        total_included_domains_remaining=plan_included_domain_quota_policy_summary.total_remaining,
                    )
                elif (
                    initial_domain.price_cents
                    > settings.max_cost_per_plan_included_domain
                ):  # original logic
                    logger.bind(query=request.query).info(
                        f"[find_domains] Initial TLD of {initial_tld} is too expensive, changing to less expensive TLDs and making unavailable no matter what"
                    )
                    initial_domain.available = False
                results.append(initial_domain)

        # check for alternative domains using different TLDs.
        # e.g. ".ai" -> ".com", ".org", etc...
        # TODO: @Benson remove organization_id after settings.enable_new_domain_payments_org_ids is removed
        new_tlds = await self.find_new_tlds(
            sld=sld,
            initial_request=initial_request,
            all_domains_names=all_domains_names,
            total_included_domains_remaining=plan_included_domain_quota_policy_summary.total_remaining,
            organization_id=organization_id,
        )

        # check for alternative domains using different SLDs.
        # e.g. "reevo" -> "teamreevo", "tryreevo", etc...
        if initial_tld not in [
            "com",
            "org",
            "net",
        ]:
            initial_tld = "com"

        # TODO: @Benson remove organization_id after settings.enable_new_domain_payments_org_ids is removed
        alt_domains = await self.generate_alternative_domains_with_prefixes(
            sld=sld,
            initial_tld=initial_tld,
            initial_request=initial_request,
            all_domains_names=all_domains_names,
            total_included_domains_remaining=plan_included_domain_quota_policy_summary.total_remaining,
            organization_id=organization_id,
        )

        # the following logic is used to return results in the follow preference order to match the Figma design:
        # - return 2 new TLD results if available
        # - return 3 alternative domains if available

        # generate the output now, how should i layer it?
        new_tlds_at_once = (
            2  # linting doesn't allow magic numbers so I have to do it like this
        )

        while len(results) < request.suggest_alternative and (new_tlds or alt_domains):
            remaining_slots = request.suggest_alternative - len(results)

            if new_tlds and remaining_slots >= new_tlds_at_once:
                # Add up to 2 new TLD results if we have space
                for _ in range(min(2, remaining_slots)):
                    if new_tlds:
                        results.append(new_tlds.pop(0))

            remaining_slots = request.suggest_alternative - len(results)
            if remaining_slots > 0 and alt_domains:
                # Add up to 3 alternative domains if we have space
                for _ in range(min(3, remaining_slots)):
                    if alt_domains:
                        results.append(alt_domains.pop(0))

        return results

    async def find_new_tlds(
        self,
        sld: str,
        initial_request: str,
        all_domains_names: set[str],
        organization_id: UUID,
        total_included_domains_remaining: int = 0,
    ) -> list[FindDomainResponse]:
        # First filter domains that are potentially available using DNS check
        tld_ranking = [
            "com",
            "org",
            "net",
        ]
        available_domains = []
        for tld in tld_ranking:
            new_domain = f"{sld}.{tld}"
            if (
                await is_domain_available(new_domain)
                and new_domain != initial_request
                and new_domain not in all_domains_names
            ):
                available_domains.append(new_domain)

        if not available_domains:
            return []

        # Run availability checks in parallel
        logger.bind(available_domains=available_domains).info(
            "[find_new_tlds] Available domains."
        )
        availability_tasks = [
            self.infraforge_client.check_domain_availability(domain)
            for domain in available_domains
        ]
        responses = await asyncio.gather(*availability_tasks)

        # Process results
        new_tlds = []
        for response in responses:
            if response:
                new_response = FindDomainResponse.from_check_availability_response(
                    not_none(response)
                )
                if (
                    settings.enable_new_domain_payments
                    or str(organization_id)
                    in settings.enable_new_domain_payments_org_ids
                ):
                    #  Assign the correct payment method to the new response
                    new_response.payment_method = self.find_correct_payment_method(
                        domain_price_cents=new_response.price_cents,
                        total_included_domains_remaining=total_included_domains_remaining,
                    )
                    if new_response.available:
                        new_tlds.append(new_response)
                elif (
                    new_response.price_cents
                    > settings.max_cost_per_plan_included_domain
                    and new_response.available
                ):  # original logic
                    new_tlds.append(new_response)

        return new_tlds

    async def generate_alternative_domains_with_prefixes(
        self,
        sld: str,
        initial_tld: str,
        initial_request: str,
        all_domains_names: set[str],
        organization_id: UUID,
        total_included_domains_remaining: int = 0,
    ) -> list[FindDomainResponse]:
        domain_prefixes = [
            "discover",
            "explore",
            "learn",
            "connect",
            "start",
            "about",
            "welcome",
            "intro",
            "begin",
            "try",
            "find",
            "join",
            "visit",
            "know",
            "select",
            "get",
            "go",
            "use",
            "hello",
            "hi",
            "meet",
            "choose",
            "pick",
        ]  # TODO: test with 5 for now, do batches if it's still slow because of overloading (try 5 then 5)

        domain_prefixes_subset = random.sample(
            domain_prefixes, 4
        )  # take 4 random prefixes from the list (lowered from 8 for concurrency issues)
        # Ensure the initial_tld doesn't start with a period if we're going to add one
        clean_tld = initial_tld.removeprefix(".")

        # Generate domains with prefixes
        prefixed_domains = [
            f"{prefix}{sld}.{clean_tld}" for prefix in domain_prefixes_subset
        ]

        # Filter out the initial request if it exists in the list
        available_prefixed_domains = {
            domain
            for domain in prefixed_domains
            if await is_domain_available(domain)
            and len(domain) <= settings.domain_sld_max_len
            and domain.lower() not in all_domains_names
        }  # set comprehension, only add domains that are available with dns checks
        available_prefixed_domains.discard(
            initial_request
        )  # remove raises error on non-existent element, discard does nothing if the element is not in the set, removes the initial request from the list which is a duplicate

        logger.bind(available_prefixed_domains=available_prefixed_domains).info(
            "[generate_alternative_domains_with_prefixes] Checking availability for prefixed domains."
        )

        # Use TaskGroup for parallel execution
        async with asyncio.TaskGroup() as tg:
            vendor_availability_check = [  # create a list of tasks to run in parallel for each domain
                tg.create_task(self.infraforge_client.check_domain_availability(domain))
                for domain in available_prefixed_domains
            ]

        # Check for alternative domains, within the alloted budget
        if (
            settings.enable_new_domain_payments
            or str(organization_id) in settings.enable_new_domain_payments_org_ids
        ):
            return_domains = []
            for response in vendor_availability_check:
                if (available_domains_response := response.result()) is not None:
                    domain = FindDomainResponse.from_check_availability_response(
                        available_domains_response
                    )
                    domain.payment_method = self.find_correct_payment_method(
                        domain_price_cents=domain.price_cents,
                        total_included_domains_remaining=total_included_domains_remaining,
                    )
                    return_domains.append(domain)
            return return_domains
        return [
            FindDomainResponse.from_check_availability_response(
                available_domains_response
            )
            for response in vendor_availability_check
            if (available_domains_response := response.result()) is not None
            and available_domains_response.available
            and FindDomainResponse.from_check_availability_response(
                available_domains_response
            ).price_cents
            <= settings.max_cost_per_plan_included_domain
        ]  #:= assigns response to the result of the task and checks if the domain is available and the price is less than the maximum price in 1 call

    async def archive_domain(
        self,
        request: ArchiveDomainRequest,
        outbound_domain_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> OutboundDomainV2:  # TODO: fill in actual archiving behavior
        # filler for now
        domain = await self.outbound_domain_repository.get_domain_by_id_and_org_id(
            outbound_domain_id, organization_id=organization_id, exclude_archived=False
        )
        if not domain:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DOMAIN_NOT_FOUND,
                    details="Domain not found for the specified domain id and organization",
                )
            )
        if domain.status != OutboundDomainStatus.ACTIVE:
            logger.bind(domain=domain).info(
                "[archive_domain] Domain is not active and cannot be archived"
            )
            return OutboundDomainV2.from_db_model(domain)

        if (
            settings.enable_domain_health_check_per_domain
        ):  # on archive, delete the domain health check schedule
            logger.bind(domain=domain).info(
                "[archive_domain] Deleting domain health check schedule"
            )
            await self.domain_health_check_schedule_service.delete_domain_health_check_per_domain_schedule(
                domain_id=outbound_domain_id,
                organization_id=organization_id,
                domain_name=domain.domain,
            )
        logger.bind(domain=domain).info("[archive_domain] Finding email accounts")
        email_accounts_list = (
            await self.email_account_service_v2.find_email_accounts_by_domain_id(
                organization_id=organization_id, outbound_domain_id=outbound_domain_id
            )
        )
        logger.bind(email_accounts_list=email_accounts_list).info(
            "[archive_domain] Found email accounts, beginning archiving process"
        )
        if email_accounts_list:
            user_auth_context = UserAuthContext(
                organization_id=organization_id, user_id=user_id
            )

            email_accounts_to_archive = {
                email_account.id: request.sequence_handling
                for email_account in email_accounts_list
            }
            await self.email_account_service_v2.archive_email_accounts(
                email_accounts_to_archive=email_accounts_to_archive,
                user_auth_context=user_auth_context,
                archive_domain=True,
            )

        domain = await self.outbound_domain_repository.update_outbound_domain_with_organization_id(
            outbound_domain_id=outbound_domain_id,
            organization_id=organization_id,
            column_to_update={
                "status": OutboundDomainStatus.INACTIVE,
                "updated_at": zoned_utc_now(),
                "archived_at": zoned_utc_now(),
            },
        )
        if not domain:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DOMAIN_NOT_FOUND,
                    details="Domain not found after updating status",
                )
            )
        return OutboundDomainV2.from_db_model(domain, domain_health=None)

    async def unarchive_domain(
        self, outbound_domain_id: UUID, user_id: UUID, organization_id: UUID
    ) -> OutboundDomainV2:
        domain = await self.outbound_domain_repository.get_domain_by_id_and_org_id(
            outbound_domain_id, organization_id=organization_id, exclude_archived=False
        )
        if not domain:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DOMAIN_NOT_FOUND,
                    details=f"Domain {outbound_domain_id} not found while attempting to unarchive",
                )
            )

        if domain.status != OutboundDomainStatus.INACTIVE:
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code=ErrorCode.DOMAIN_NOT_INACTIVE,
                    details="Domain is not inactive and cannot be unarchived",
                    conflicted_existing_object=StdObjectIdentifiers.domain.identifier,
                )
            )

        now = datetime.now(tz=UTC)
        updated_domain = await self.outbound_domain_repository.update_outbound_domain_with_organization_id(
            outbound_domain_id=domain.id,
            organization_id=organization_id,
            column_to_update={
                "status": OutboundDomainStatus.ACTIVE,
                "updated_at": now,
                "archived_at": None,
            },
        )
        if not updated_domain:
            raise ServiceError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.DOMAIN_NOT_FOUND,
                    details=f"Domain {outbound_domain_id} failed to unarchive properly",
                )
            )
        if (
            settings.enable_domain_health_check_per_domain
        ):  # on unarchive, re-create the domain health check schedule
            await self.domain_health_check_schedule_service.create_domain_health_check_per_domain_schedule(
                domain_id=outbound_domain_id,
                organization_id=organization_id,
                domain_name=domain.domain,
            )
        # No extra handling for mailboxes or sequences since those are handled with more granularity (basically, cannot unarchive those from domain).
        return OutboundDomainV2.from_db_model(updated_domain)

    def find_correct_payment_method(
        self, domain_price_cents: int, total_included_domains_remaining: int
    ) -> DomainPaymentMethod:
        """
        Assigns the correct payment method to a domain based on the price and the total plan-included domains remaining,
        used by front-end to determine correct modal to display. Additionally, the domain policy is checked at the purchasing stage
        not here, as we only block purchases but not the ability to find domains.
        """
        if domain_price_cents > settings.max_cost_per_invoiced_domain:
            logger.bind(domain_price_cents=domain_price_cents).info(
                "[find_correct_payment_method] This domain is too expensive, please talk to support to purchase this domain"
            )
            return DomainPaymentMethod.CONTACT_SUPPORT
        elif (
            settings.max_cost_per_plan_included_domain
            <= domain_price_cents
            <= settings.max_cost_per_invoiced_domain
        ) or total_included_domains_remaining <= 0:
            logger.bind(
                domain_price_cents=domain_price_cents,
                total_included_domains_remaining=total_included_domains_remaining,
            ).info(
                "[find_correct_payment_method] Domain price is over our free limit or we are out of included plan quota, changing to ACCOUNTS_RECEIVABLE"
            )
            return DomainPaymentMethod.ACCOUNTS_RECEIVABLE

        logger.bind(
            domain_price_cents=domain_price_cents,
            total_included_domains_remaining=total_included_domains_remaining,
        ).info(
            "[find_correct_payment_method] Domain price is within our free limit and we have included plan quota remaining, changing to INCLUDED_IN_PLAN"
        )
        return DomainPaymentMethod.INCLUDED_IN_PLAN

    async def _send_domain_purchase_invoice_slack_message(
        self,
        infraforge_response: ExternalBuyDomainsResponse,
        full_domain_name: str,
        organization_id: UUID,
    ) -> None:
        logger.bind(
            organization_id=organization_id,
            full_domain_name=full_domain_name,
            invoice_total=infraforge_response.invoice.total,
            invoice_id=infraforge_response.invoice.id,
        ).info("Sending domain invoice to customer.")
        try:
            organization = await self.organization_service_v2.get_organization_by_id(
                organization_id
            )
            await self.slack_client.send_message(
                env=settings.environment,
                channel=SlackChannels.DOMAIN_PURCHASE_INVOICE,
                text=f"Domain Purchase Invoice: {full_domain_name} for {organization.display_name}. \n Total: {format_cents_to_usd(infraforge_response.invoice.total)} \n Invoice ID: {infraforge_response.invoice.id}",
            )
        except Exception as e:
            logger.bind(
                invoice_total=infraforge_response.invoice.total,
                invoice_id=infraforge_response.invoice.id,
                full_domain_name=full_domain_name,
                organization_id=organization_id,
            ).error(
                "Error sending message to Slack.",
                exc_info=e,
            )

    # this slack alert is sent for every SUCCESSFUL purchase
    async def _send_domain_purchase_slack_alert(
        self,
        infraforge_response: ExternalBuyDomainsResponse,
        full_domain_name: str,
        organization_id: UUID,
    ) -> None:
        if not (
            settings.enable_slack_domain_purchase_alert
            or str(organization_id)
            in settings.enable_slack_domain_purchase_alert_org_ids
        ):
            logger.bind(organization_id=organization_id).info(
                "FF is disabled for slack domain purchase alert notifications."
            )
            return
        logger.bind(
            organization_id=organization_id,
            full_domain_name=full_domain_name,
            infraforge_invoice=infraforge_response.invoice,
            infraforge_domains=infraforge_response.domains,
        ).info("Sending domain purchase alert internally.")
        try:
            organization = await self.organization_service_v2.get_organization_by_id(
                organization_id
            )
            # Things are not formatted super well, but this is just to be aware when a domain is purchased, not really for external eyes
            await self.slack_alert_client.send_message(
                env=settings.environment,
                channel=SlackChannels.DOMAIN_PURCHASE_SUCCESS_ALERT,
                text=f"Domain Purchase Alert: {full_domain_name} for {organization.display_name}. Entire invoice response: {infraforge_response.invoice}",
            )
        except Exception as e:
            logger.bind(
                organization_id=organization_id,
                infraforge_invoice=infraforge_response.invoice,
                infraforge_domains=infraforge_response.domains,
            ).error("Error sending message to Slack.", exc_info=e)

    async def get_or_create_domain_and_mailbox_quotas(
        self, organization_id: UUID, user_id: UUID
    ) -> dict[UUID, OutboundEmailQuota]:
        """
        str represents the org's id in str form
        """
        # creates quota policy and backfills for domain
        org_level_num_domain_quota_object = (
            await self.get_or_create_org_level_num_domain_quota_with_backfill(
                organization_id=organization_id, user_id=user_id
            )
        )

        org_level_num_mailbox_quota_object = (
            await self.get_or_create_org_level_num_mailbox_quota_with_backfill(
                organization_id=organization_id, user_id=user_id
            )
        )

        org_level_plan_included_domain_quota_object = (
            await self.get_or_create_org_level_plan_included_domain_quota_with_backfill(
                organization_id=organization_id, user_id=user_id
            )
        )

        org_level_plan_included_mailbox_quota_object = await self.get_or_create_org_level_plan_included_mailbox_quota_with_backfill(
            organization_id=organization_id, user_id=user_id
        )

        org_quota_object = OutboundEmailQuota(
            org_level_num_domain_quota_object=org_level_num_domain_quota_object,
            org_level_num_mailbox_quota_object=org_level_num_mailbox_quota_object,
            org_level_plan_included_domain_quota_object=org_level_plan_included_domain_quota_object,
            org_level_plan_included_mailbox_quota_object=org_level_plan_included_mailbox_quota_object,
        )

        return {organization_id: org_quota_object}

    async def get_or_create_org_level_num_domain_quota_with_backfill(
        self, organization_id: UUID, user_id: UUID
    ) -> QuotaUsageWithActual:
        await self.quota_policy_service.ensure_quota_policy_exists(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.DOMAIN,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            entity_id=organization_id,
            quota_limit=settings.quota_per_org_max_domains,
            period=QuotaPeriod.ANNUAL,
        )
        # Get the quota summary for the organization.
        logger.info("get the quota summary for the organization")
        quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
        logger.bind(quota_summary=quota_summary, organization_id=organization_id).info(
            "quota summary for domains in organization"
        )
        number_of_domains = await self.outbound_domain_repository.get_number_of_outbound_domains_by_org_id(
            organization_id=organization_id,
        )
        logger.bind(num_domains=number_of_domains).info(
            "number of domains in the organization"
        )
        delta = number_of_domains - quota_summary.total_used
        logger.bind(delta=delta).info(
            "delta between number of domains in the organization and the quota summary"
        )
        if delta == 0:
            logger.info(
                "number of domains and quota usage are in sync, no need to query the quota summary again"
            )
            return QuotaUsageWithActual(delta=delta, quota_summary=quota_summary)

        # Backfill quota usage if number of domains and the quota usage are out of sync
        logger.bind(organization_id=organization_id, delta=delta).info(
            "backfilling quota usage for domains in the organization"
        )
        if delta > 0:
            await self.quota_service.increase_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.DOMAIN,
                usage=delta,
                timestamp=zoned_utc_now(),
            )

        else:
            logger.bind(organization_id=organization_id, delta=delta).info(
                "number of domains in the organization is less than the quota usage, going to decrease the quota usage"
            )
            await self.quota_service.decrease_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.DOMAIN,
                usage=abs(delta),
                timestamp=zoned_utc_now(),
            )
        updated_quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
        logger.bind(
            updated_quota_summary=updated_quota_summary, organization_id=organization_id
        ).info("updated quota summary for domains in the organization")
        return QuotaUsageWithActual(delta=delta, quota_summary=updated_quota_summary)

    async def get_or_create_org_level_num_mailbox_quota_with_backfill(
        self, organization_id: UUID, user_id: UUID
    ) -> QuotaUsageWithActual:
        await self.quota_policy_service.ensure_quota_policy_exists(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.MAILBOX,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            entity_id=organization_id,
            quota_limit=settings.quota_per_org_max_mailboxes,
            period=QuotaPeriod.ANNUAL,
        )
        # Get the quota summary for the organization.
        logger.info("get the quota summary for the organization")
        quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
        logger.bind(quota_summary=quota_summary, organization_id=organization_id).info(
            "quota summary for mailboxes in the organization"
        )
        number_of_mailboxes = await self.email_account_repo.get_number_of_outbound_accounts_by_organization_id(
            organization_id=organization_id,
        )
        logger.bind(num_mailboxes=number_of_mailboxes).info(
            "number of mailboxes in the organization"
        )
        delta = number_of_mailboxes - quota_summary.total_used
        logger.bind(delta=delta).info(
            "delta between number of mailboxes in the organization and the quota summary"
        )
        if delta == 0:
            logger.info(
                "number of organization mailboxes and quota usage are in sync, no need to query the quota summary again"
            )
            return QuotaUsageWithActual(delta=delta, quota_summary=quota_summary)

        # Backfill quota usage if number of mailboxes and the quota usage are out of sync
        logger.bind(organization_id=organization_id, delta=delta).info(
            "backfilling quota usage for mailboxes in the organization"
        )
        if delta > 0:
            await self.quota_service.increase_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.MAILBOX,
                usage=delta,
                timestamp=zoned_utc_now(),
            )
        else:
            await self.quota_service.decrease_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.MAILBOX,
                usage=abs(delta),
                timestamp=zoned_utc_now(),
            )

        updated_quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
        logger.bind(
            updated_quota_summary=updated_quota_summary, organization_id=organization_id
        ).info("updated quota summary for mailboxes in the organization")
        return QuotaUsageWithActual(delta=delta, quota_summary=updated_quota_summary)

    async def get_or_create_org_level_plan_included_domain_quota_with_backfill(
        self, organization_id: UUID, user_id: UUID
    ) -> QuotaUsageWithActual:
        await self.quota_policy_service.ensure_quota_policy_exists(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            entity_id=organization_id,
            quota_limit=settings.quota_per_org_plan_included_domains,
            period=QuotaPeriod.ANNUAL,
        )
        # Get the quota summary for the organization.
        logger.info("get the quota summary for the organization")
        quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
        logger.bind(quota_summary=quota_summary, organization_id=organization_id).info(
            "quota summary for plan included domains in the organization"
        )
        number_of_plan_included_domains = await self.outbound_domain_repository.get_number_of_plan_included_outbound_domains_by_org_id(
            organization_id=organization_id,
        )

        logger.bind(num_plan_included_domains=number_of_plan_included_domains).info(
            "number of plan included domains in the organization"
        )
        delta = number_of_plan_included_domains - quota_summary.total_used
        logger.bind(delta=delta).info(
            "delta between number of plan included domains in the organization and the quota summary"
        )

        if delta == 0:
            logger.info(
                "number of plan included domains and quota usage are in sync, no need to query the quota summary again"
            )
            return QuotaUsageWithActual(
                delta=delta,
                quota_summary=quota_summary,
            )

        # Backfill quota usage if number of plan included domains and the quota usage are out of sync
        logger.bind(organization_id=organization_id, delta=delta).info(
            "backfilling quota usage for plan included domains in the organization"
        )
        if delta > 0:
            await self.quota_service.increase_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
                usage=delta,
                timestamp=zoned_utc_now(),
            )
        else:
            await self.quota_service.decrease_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
                usage=abs(delta),
                timestamp=zoned_utc_now(),
            )

        updated_quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_DOMAIN,
            period=QuotaPeriod.ANNUAL,
        )
        logger.bind(
            updated_quota_summary=updated_quota_summary, organization_id=organization_id
        ).info("updated quota summary for plan included domains in the organization")
        return QuotaUsageWithActual(
            delta=delta,
            quota_summary=updated_quota_summary,
        )

    async def get_or_create_org_level_plan_included_mailbox_quota_with_backfill(
        self, organization_id: UUID, user_id: UUID
    ) -> QuotaUsageWithActual:
        await self.quota_policy_service.ensure_quota_policy_exists(
            organization_id=organization_id,
            user_id=user_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            entity_id=organization_id,
            quota_limit=settings.quota_per_org_plan_included_mailboxes,
            period=QuotaPeriod.ANNUAL,
        )
        # Get the quota summary for the organization.
        logger.info("get the quota summary for the organization")
        quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )

        logger.bind(quota_summary=quota_summary, organization_id=organization_id).info(
            "quota summary for plan included mailboxes in the organization"
        )
        number_of_plan_included_mailboxes = await self.email_account_repo.get_number_of_plan_included_outbound_accounts_by_organization_id(
            organization_id=organization_id,
        )
        logger.bind(num_plan_included_mailboxes=number_of_plan_included_mailboxes).info(
            "number of plan included mailboxes in the organization"
        )
        delta = number_of_plan_included_mailboxes - quota_summary.total_used
        logger.bind(delta=delta).info(
            "delta between number of plan included mailboxes in the organization and the quota summary"
        )
        if delta == 0:
            logger.info(
                "number of plan included mailboxes and quota usage are in sync, no need to query the quota summary again"
            )
            return QuotaUsageWithActual(
                delta=delta,
                quota_summary=quota_summary,
            )

        # Backfill quota usage if number of plan included mailboxes and the quota usage are out of sync
        logger.bind(organization_id=organization_id, delta=delta).info(
            "backfilling quota usage for plan included mailboxes in the organization"
        )

        if delta > 0:
            await self.quota_service.increase_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
                usage=delta,
                timestamp=zoned_utc_now(),
            )

        else:
            await self.quota_service.decrease_usage(
                organization_id=organization_id,
                entity_id=organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
                usage=abs(delta),
                timestamp=zoned_utc_now(),
            )

        updated_quota_summary = await self.quota_service.get_quota_summary_per_resource(
            organization_id=organization_id,
            resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
            period=QuotaPeriod.ANNUAL,
        )
        logger.bind(
            updated_quota_summary=updated_quota_summary, organization_id=organization_id
        ).info("updated quota summary for plan included mailboxes in the organization")
        return QuotaUsageWithActual(
            delta=delta,
            quota_summary=updated_quota_summary,
        )

    async def get_or_create_domain_and_mailbox_quotas_all_orgs(
        self,
    ) -> dict[UUID, OutboundEmailQuota]:
        org_to_active_admins = (
            await self.user_repository.get_active_org_ids_with_first_admin_user()
        )
        results = {}
        for org_id, admin_user_id in org_to_active_admins.items():
            try:
                org_to_quota = await self.get_or_create_domain_and_mailbox_quotas(
                    org_id, admin_user_id
                )
                results.update(org_to_quota)
            except Exception as e:
                logger.bind(organization_id=org_id).error(
                    "Quota creation failed during all org backfill", exc_info=e
                )
        return results

    async def signal_email_account_create_workflows(
        self,
        email_accounts: list[EmailAccount],
    ) -> None:
        """
        Signal to email account lifecycle workflows that a domain is active

        Args:
            email_accounts: The email accounts to signal
            domain_id: The domain ID that is now active
        """
        client = await get_temporal_client()

        for email_account in email_accounts:
            workflow_id = f"{EMAIL_ACCOUNT_LIFECYCLE_ACTIVATE_WORKFLOW_ID_PREFIX}_{email_account.id}"

            # Get the workflow handle and send the signal,
            try:
                handle = client.get_workflow_handle(workflow_id)
                await handle.signal(SIGNAL_DOMAIN_ACTIVE)
            except Exception as e:
                # Log error but continue
                logger.error(
                    f"Failed to signal domain_active to workflow {workflow_id}: {e!s}",
                )


def contains_special_or_whitespace(s: str) -> bool:
    # checks if string s contains any non-alphanumeric characters or whitespace (except for hyphens)
    return bool(SPECIAL_OR_WHITESPACE_PATTERN.search(s))


def check_find_request(sld: str, tld: str, query: str) -> None:
    logger.bind(sld=sld, tld=tld).info(
        "[find_domains] Extracted SLD and TLD from request."
    )
    if (
        contains_special_or_whitespace(sld)
        or contains_special_or_whitespace(tld)
        or sld.startswith("xn--")
    ):
        raise InvalidArgumentError(
            additional_error_details=ErrorDetails(
                code=ErrorCode.INVALID_REQUEST,
                details=f"Invalid request: {query} contains special characters or punycode",
            )
        )


class SingletonOutboundDomainService(Singleton, OutboundDomainService):
    pass


def get_outbound_domain_service_general(
    engine: DatabaseEngine,
) -> OutboundDomainService:
    if SingletonOutboundDomainService.has_instance():
        return SingletonOutboundDomainService.get_singleton_instance()
    return SingletonOutboundDomainService(engine=engine)
