from typing import Annotated, override
from uuid import UUID

from fastapi import Depends, Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.common.domain_service import AllowedUsers, DomainQueryService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.email.account.service_v2 import (
    EmailAccountServiceV2,
    get_email_account_service_v2_by_db_engine,
)
from salestech_be.core.email.account.types import EmailAccountV2
from salestech_be.core.quota.service.quota_policy_service import (
    QuotaPolicyService,
    get_quota_policy_service_from_engine,
)
from salestech_be.db.dao.email_account import EmailAccountRepository
from salestech_be.db.dao.email_account_warm_up_campaign_repository import (
    EmailAccountWarmUpCampaignRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.settings import settings
from salestech_be.util.validation import not_none


class EmailAccountQueryService(DomainQueryService[EmailAccountV2]):
    def __init__(
        self,
        email_account_service: EmailAccountServiceV2,
        quota_policy_service: QuotaPolicyService,
        email_account_repository: Annotated[EmailAccountRepository, Depends()],
        email_account_warm_up_campaign_repository: Annotated[
            EmailAccountWarmUpCampaignRepository, Depends()
        ],
    ):
        self.email_account_service = email_account_service
        self.quota_policy_service = quota_policy_service
        self.email_account_repository = email_account_repository
        self.email_account_warm_up_campaign_repository = (
            email_account_warm_up_campaign_repository
        )

    async def list_email_accounts(
        self,
        user_auth_context: UserAuthContext,
        only_include_email_account_ids: UnsetAware[set[UUID]] = UNSET,
        include_archived: bool = False,
    ) -> list[EmailAccountV2]:
        bypass_permission_check = (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        )

        # Determine filtering parameters
        email_account_ids = (
            list(only_include_email_account_ids)
            if specified(only_include_email_account_ids)
            else None
        )
        owner_user_id = None if bypass_permission_check else user_auth_context.user_id

        # Get email accounts using unified method
        db_email_accounts = await self.email_account_repository.find_email_accounts_with_permission_filter(
            organization_id=user_auth_context.organization_id,
            email_account_ids=email_account_ids,
            owner_user_id=owner_user_id,
            include_archived=include_archived,
        )

        quota_policies = await self.quota_policy_service.list_quota_policies_by_entity_ids_and_type_resource(
            entity_id_list=[
                db_email_account.id for db_email_account in db_email_accounts
            ],
            entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
            resource=QuotaConsumingResource.EMAIL,
            organization_id=user_auth_context.organization_id,
        )

        return [
            await self.email_account_service.email_account_v2_from_db_model(
                db_email_account=db_email_account,
                daily_quota=not_none(
                    next(
                        (
                            p.quota_limit
                            for p in quota_policies
                            if p.entity_id == db_email_account.id
                            and p.period == QuotaPeriod.DAILY
                        ),
                        0,
                    )
                ),
                organization_id=db_email_account.organization_id,
                user_id=db_email_account.owner_user_id,
            )
            for db_email_account in db_email_accounts
        ]

    async def find_signature_email_account_associations_by_signature_ids(
        self,
        user_auth_context: UserAuthContext,
        signature_ids: list[UUID],
    ) -> tuple[dict[UUID, set[UUID]], set[UUID]]:
        return await self.email_account_repository.find_signature_email_account_associations_by_signature_ids(
            signature_ids=signature_ids,
            organization_id=user_auth_context.organization_id,
        )

    # only admin and owner can view email account
    @override
    async def get_allowed_users_from_entity(
        self, domain_object: EmailAccountV2
    ) -> AllowedUsers:
        return AllowedUsers(
            owner_user_id=domain_object.owner_user_id,
            participant_user_ids=[],
        )

    @override
    async def is_entity_viewable_by_user(
        self, user_auth_context: UserAuthContext, domain_object: EmailAccountV2
    ) -> bool:
        if (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        ):
            return True

        allowed_users = await self.get_allowed_users_from_entity(domain_object)
        return allowed_users.is_owner_or_participant(user_id=user_auth_context.user_id)


class SingletonEmailAccountQueryService(Singleton, EmailAccountQueryService):
    pass


def get_email_account_query_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> EmailAccountQueryService:
    if SingletonEmailAccountQueryService.has_instance():
        return SingletonEmailAccountQueryService.get_singleton_instance()
    return EmailAccountQueryService(
        email_account_service=get_email_account_service_v2_by_db_engine(
            db_engine=db_engine,
        ),
        quota_policy_service=get_quota_policy_service_from_engine(engine=db_engine),
        email_account_repository=EmailAccountRepository(engine=db_engine),
        email_account_warm_up_campaign_repository=EmailAccountWarmUpCampaignRepository(
            engine=db_engine
        ),
    )


def get_email_account_query_service(request: Request) -> EmailAccountQueryService:
    return get_email_account_query_service_by_db_engine(
        db_engine=get_db_engine(request)
    )
