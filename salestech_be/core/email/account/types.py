from typing import Annotated, Any
from uuid import UUID

from pydantic import BaseModel, EmailStr

from salestech_be.common.schema_manager.std_object_field_identifier import (
    EmailAccountField,
    EmailAccountWarmUpCampaignField,
    OrganizationUserField,
    SequenceField,
    SignatureField,
    StdObjectIdentifiers,
)
from salestech_be.common.schema_manager.std_object_relationship import (
    EmailAccountRelationship,
)
from salestech_be.common.type.metadata.field.field_indexable_config import (
    UniqueIndexableConfig,
)
from salestech_be.common.type.metadata.field.field_type_property import (
    BooleanCheckboxFieldProperty,
    DefaultEnumFieldProperty,
    EmailFieldProperty,
    Numeric<PERSON>ieldProperty,
    Text<PERSON>ieldProperty,
    TimestampFieldProperty,
    UUIDFieldProperty,
)
from salestech_be.common.type.metadata.schema import OutboundRelationship
from salestech_be.core.common.types import (
    DomainModel,
    FieldMetadata,
)
from salestech_be.db.models.email_account import (
    EmailAccount,
    EmailAccountType,
    EmailAccountUseOverride,
)
from salestech_be.db.models.email_account_warm_up import (
    MailboxWarmUpService,
    MailboxWarmUpSpeed,
    MailboxWarmUpStatus,
)
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class DefaultPoolStatus(NameValueStrEnum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    QUEUED = "QUEUED"


class WarmupDefaults:
    RESPONSE_RATE: int = 10
    RAMPUP_SPEED: MailboxWarmUpSpeed = MailboxWarmUpSpeed.FAST
    WITH_RAMPUP: bool = True
    WARMUP_PROVIDER: MailboxWarmUpService = MailboxWarmUpService.MAILIVERY
    ENDPOINT: str = "smtp"
    REQUIRED_WARMUP_DAYS: int = 14


class CreateWarmUpRequest(BaseModel):
    email_account_id: UUID
    email_per_day: int
    response_rate: int = WarmupDefaults.RESPONSE_RATE
    with_rampup: bool = WarmupDefaults.WITH_RAMPUP
    rampup_speed: MailboxWarmUpSpeed = WarmupDefaults.RAMPUP_SPEED
    endpoint: str = WarmupDefaults.ENDPOINT


class WarmupConfig(BaseModel):
    warmup_limit: int | None = None
    warmup_status: MailboxWarmUpStatus | None = None


class EmailAccountLifecycleActivateWorkflowType(NameValueStrEnum):
    CREATE = "CREATE"
    UNARCHIVE = "UNARCHIVE"


class EmailAccountDeactivatePatchInput(BaseModel):
    user_id: UUID
    organization_id: UUID
    warmup_configs: dict[UUID, WarmupConfig]


class EmailAccountLifecycleActivateWorkflowInput(BaseModel):
    organization_id: UUID
    user_id: UUID
    workflow_type: EmailAccountLifecycleActivateWorkflowType
    # if create, lifecycle_operation_data is a CreateWarmUpRequest
    # if unarchive, lifecycle_operation_data is a dict of email_account_id -> WarmupConfig
    lifecycle_operation_data: CreateWarmUpRequest | dict[UUID, WarmupConfig]

    def model_post_init(self, __context: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().model_post_init(__context)
        if (
            self.workflow_type == EmailAccountLifecycleActivateWorkflowType.CREATE
            and not isinstance(self.lifecycle_operation_data, CreateWarmUpRequest)
        ):
            raise ValueError("lifecycle_operation_data must be a CreateWarmUpRequest")
        elif (
            self.workflow_type == EmailAccountLifecycleActivateWorkflowType.UNARCHIVE
            and not isinstance(self.lifecycle_operation_data, dict)
        ):
            raise ValueError("lifecycle_operation_data must be a dict")


class MailboxStatus(NameValueStrEnum):
    READY = "READY"
    ARCHIVED = "ARCHIVED"
    WARMING_UP = "WARMING_UP"
    PENDING = "PENDING"
    NOT_APPLICABLE = "NOT_APPLICABLE"


class EmailAccountWarmUpCampaignV2(DomainModel):
    """
    Email Account Warm Up Campaign Domain Model.

    We don't expose this class through endpoints yet, but is added to make cdc processing easier to set up.
    """

    object_id = StdObjectIdentifiers.email_account_warm_up_campaign.identifier
    field_name_provider = EmailAccountWarmUpCampaignField
    object_display_name = "EmailAccountWarmUpCampaign"

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    organization_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    email_account_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Email Account ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    status: Annotated[
        MailboxWarmUpStatus | None,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=MailboxWarmUpStatus,
                field_display_name="Satus",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None


class EmailAccountV2(DomainModel):
    """
    Email Account Domain Model.
    """

    object_id = StdObjectIdentifiers.email_account.identifier
    field_name_provider = EmailAccountField
    object_display_name = "EmailAccount"

    outbound_relationships = (
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=EmailAccountRelationship.email_account__to__owner_user,
            relationship_name="Owner User",
            self_object_identifier=StdObjectIdentifiers.email_account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                EmailAccountField.owner_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=EmailAccountRelationship.email_account__to__created_by_user,
            relationship_name="Created By User",
            self_object_identifier=StdObjectIdentifiers.email_account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                EmailAccountField.created_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=EmailAccountRelationship.email_account__to__updated_by_user,
            relationship_name="Updated By User",
            self_object_identifier=StdObjectIdentifiers.email_account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                EmailAccountField.updated_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=EmailAccountRelationship.email_account__to__deleted_by_user,
            relationship_name="Deleted By User",
            self_object_identifier=StdObjectIdentifiers.email_account.identifier,
            related_object_identifier=StdObjectIdentifiers.user.identifier,
            self_cardinality=OutboundRelationship.Cardinality.MANY,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(
                EmailAccountField.deleted_by_user_id.identifier,
            ),
            ordered_related_field_identifiers=(OrganizationUserField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=EmailAccountRelationship.email_account__to__sequence,
            relationship_name="Sequence",
            self_object_identifier=StdObjectIdentifiers.email_account.identifier,
            related_object_identifier=StdObjectIdentifiers.sequence.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.MANY,
            ordered_self_field_identifiers=(EmailAccountField.id.identifier,),
            ordered_related_field_identifiers=(SequenceField.id.identifier,),
        ),
        OutboundRelationship(
            relation_type=OutboundRelationship.RelationType.LOOKUP,
            id=EmailAccountRelationship.email_account__to__signature,
            relationship_name="Signature",
            self_object_identifier=StdObjectIdentifiers.email_account.identifier,
            related_object_identifier=StdObjectIdentifiers.signature.identifier,
            self_cardinality=OutboundRelationship.Cardinality.ONE,
            related_object_cardinality=OutboundRelationship.Cardinality.ONE,
            ordered_self_field_identifiers=(EmailAccountField.id.identifier,),
            ordered_related_field_identifiers=(SignatureField.id.identifier,),
        ),
    )

    id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                index_config=UniqueIndexableConfig(is_indexed=True, is_unique=True),
                field_display_name="ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    organization_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Organization ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    first_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="First Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    last_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Last Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    display_name: Annotated[
        str | None,
        FieldMetadata(
            type_property=TextFieldProperty(
                field_display_name="Display Name",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    email: Annotated[
        str,
        FieldMetadata(
            type_property=EmailFieldProperty(
                field_display_name="Email",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    reply_to_email: Annotated[
        EmailStr | None,
        FieldMetadata(
            type_property=EmailFieldProperty(
                field_display_name="Reply To Email",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ] = None

    signature_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Signature ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    email_account_health_score: Annotated[
        int | None,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Health Score",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    status: Annotated[
        MailboxStatus,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=MailboxStatus,
                field_display_name="Mailbox Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    warmup_limit: Annotated[
        int | None,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Warmup Limit",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    active: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Active",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ] = None

    seconds_delay_between_emails: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Seconds Delay Between Emails",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    type: Annotated[
        EmailAccountType,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=EmailAccountType,
                field_display_name="Type",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    owner_user_id: Annotated[
        UUID,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Owner User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ]

    outbound_domain_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Outbound Domain ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    is_in_default_pool: Annotated[
        bool | None,
        FieldMetadata(
            type_property=BooleanCheckboxFieldProperty(
                field_display_name="Add to Default Pool",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ] = None

    default_pool_status: Annotated[
        DefaultPoolStatus | None,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=DefaultPoolStatus,
                field_display_name="Default Pool Status",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ] = None

    daily_quota: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Daily Quota",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ]

    current_day_usage: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="Daily Usage",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    all_time_usage: Annotated[
        int,
        FieldMetadata(
            type_property=NumericFieldProperty(
                field_display_name="All Time Usage",
                is_ui_displayable=True,
                is_ui_editable=False,
            ),
        ),
    ]

    created_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Created At",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    created_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Created By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    updated_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Updated At",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    updated_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Updated By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    archived_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Archived At",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    archived_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Archived By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    deleted_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Deleted At",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    warmup_started_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Warmup Start At Date",
            ),
        ),
    ] = None

    warmup_completion_scheduled_at: Annotated[
        ZoneRequiredDateTime | None,
        FieldMetadata(
            type_property=TimestampFieldProperty(
                field_display_name="Warmup Completion Scheduled At",
            ),
        ),
    ] = None

    deleted_by_user_id: Annotated[
        UUID | None,
        FieldMetadata(
            type_property=UUIDFieldProperty(
                field_display_name="Deleted By User ID",
                is_ui_displayable=False,
                is_ui_editable=False,
            ),
        ),
    ] = None

    use_override: Annotated[
        EmailAccountUseOverride | None,
        FieldMetadata(
            type_property=DefaultEnumFieldProperty(
                enum_class=EmailAccountUseOverride,
                field_display_name="Use Override",
                is_ui_displayable=True,
                is_ui_editable=True,
            ),
        ),
    ] = None

    @classmethod
    def from_db_model(
        cls,
        db_email_account: EmailAccount,
        daily_quota: int,
        current_day_usage: int,
        all_time_usage: int,
        status: MailboxStatus,
        warmup_limit: int | None,
        email_account_health_score: int | None,
        is_in_default_pool: bool,
        default_pool_status: DefaultPoolStatus,
        warmup_started_at: ZoneRequiredDateTime | None,
        warmup_completion_scheduled_at: ZoneRequiredDateTime | None,
    ) -> "EmailAccountV2":
        return cls(
            id=db_email_account.id,
            organization_id=db_email_account.organization_id,
            first_name=db_email_account.first_name,
            last_name=db_email_account.last_name,
            display_name=db_email_account.display_name,
            email=db_email_account.email,
            reply_to_email=db_email_account.reply_to_email,
            signature_id=db_email_account.signature_id,
            email_account_health_score=email_account_health_score,
            is_in_default_pool=is_in_default_pool,
            default_pool_status=default_pool_status,
            warmup_limit=warmup_limit,
            status=status,
            active=db_email_account.active,
            seconds_delay_between_emails=db_email_account.seconds_delay_between_emails,
            type=db_email_account.type,
            owner_user_id=db_email_account.owner_user_id,
            outbound_domain_id=db_email_account.outbound_domain_id,
            created_at=db_email_account.created_at,
            created_by_user_id=db_email_account.created_by_user_id,
            updated_at=db_email_account.updated_at,
            updated_by_user_id=db_email_account.updated_by_user_id,
            archived_at=db_email_account.archived_at,
            archived_by_user_id=db_email_account.archived_by_user_id,
            deleted_at=db_email_account.deleted_at,
            deleted_by_user_id=db_email_account.deleted_by_user_id,
            all_time_usage=all_time_usage,
            current_day_usage=current_day_usage,
            daily_quota=daily_quota,
            use_override=db_email_account.use_override,
            warmup_started_at=warmup_started_at,
            warmup_completion_scheduled_at=warmup_completion_scheduled_at,
        )
