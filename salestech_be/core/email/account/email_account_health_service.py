from datetime import timed<PERSON><PERSON>
from typing import ClassVar
from uuid import UUID, uuid4

from pydantic import BaseModel, ConfigDict

from salestech_be.common.singleton import Singleton
from salestech_be.core.email.warmup.service import (
    MailiveryWarmupService,
    get_mailivery_warmup_service_by_db_engine,
)
from salestech_be.db.dao.email_account import (
    EmailAccountHealthRepository,
    EmailAccountRepository,
)
from salestech_be.db.dao.email_event_repository import EmailEventRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.email_account import (
    EmailAccount,
    EmailAccountHealth,
    EmailAccountHealthHistory,
    EmailAccountType,
)
from salestech_be.db.models.sequence import EmailEventType
from salestech_be.integrations.mailivery.async_mailivery_client import (
    AsyncMailiveryClient,
)
from salestech_be.integrations.mailivery.type import DailyMetrics
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


class EmailAccountHealthScoreParams(BaseModel):
    # Make the entire model frozen/immutable
    model_config = ConfigDict(frozen=True)

    # Weights for email events that affect health score
    # Positive weights add up to 100, negative weights reduce the score
    # The ratio between positive weights is maintained as (OPENED:LINK_CLICKED:REPLIED = 3:1:1)
    EMAIL_EVENT_TYPE_WEIGHTS: ClassVar[dict[EmailEventType, int]] = {
        EmailEventType.BOUNCE_DETECTED: -50,  # Negative impact for bounces
        EmailEventType.OPENED: 70,  # 70% of positive weight
        EmailEventType.LINK_CLICKED: 0,  # 0% of positive weight (not used for now)
        EmailEventType.REPLIED: 0,  # 0% of positive weight (not used for now)
        EmailEventType.UNSUBSCRIBED: -5,  # Negative impact for unsubscribes
    }
    # Weight as a percentage (0-100) of how much the warmup score contributes to the final health score
    WARM_UP_CAMPAIGN_HEALTH_SCORE_WEIGHT: ClassVar[int] = 30
    WINDOW_SIZE_DAYS: ClassVar[int] = 14


class EmailAccountHealthScoreData(BaseModel):
    composite_health_score: int
    warmup_health_score: int
    warmup_health_score_weight: int
    deliverability_health_score: int
    deliverability_health_score_weight: int
    mailivery_metrics: list[DailyMetrics]
    email_event_metrics: dict[EmailEventType, int]


class EmailAccountHealthService:
    def __init__(
        self,
        email_account_health_repository: EmailAccountHealthRepository,
        email_event_repository: EmailEventRepository,
        email_account_repository: EmailAccountRepository,
        email_account_warm_up_service: MailiveryWarmupService,
        mailivery_client: AsyncMailiveryClient,
    ):
        self.email_account_health_repository = email_account_health_repository
        self.email_event_repository = email_event_repository
        self.email_account_repository = email_account_repository
        self.email_account_warm_up_service = email_account_warm_up_service
        self.mailivery_client = mailivery_client

    async def sync_email_account_health(self, organization_id: UUID) -> None:
        outbound_email_accounts = (
            await self.email_account_repository._find_by_column_values(  # noqa: SLF001
                EmailAccount,
                organization_id=organization_id,
                type=EmailAccountType.OUTBOUND,
            )
        )
        if not outbound_email_accounts:
            logger.bind(organization_id=organization_id).info(
                "No email accounts found for organization"
            )
            return
        for outbound_email_account in outbound_email_accounts:
            health_score_data = await self.calculate_health_score(
                outbound_email_account.id, organization_id
            )
            if health_score_data:
                await self.update_email_account_health(
                    outbound_email_account.id, organization_id, health_score_data
                )

    async def calculate_health_score(
        self, email_account_id: UUID, organization_id: UUID
    ) -> EmailAccountHealthScoreData | None:
        """Calculate email account health score (0-100 scale).

        Combines email engagement metrics and warmup campaign inbox placement rate.
        Returns None if account not found or less than 3 days old.
        """

        email_account = (
            await self.email_account_repository.find_by_tenanted_primary_key(
                EmailAccount, id=email_account_id, organization_id=organization_id
            )
        )
        if not email_account:
            return None
        if email_account.created_at > zoned_utc_now() - timedelta(days=3):
            return None

        health_score: float = 0
        email_event_counts = await self.email_event_repository.get_email_event_count_by_types_and_email_account_id(
            organization_id=organization_id,
            start_time=zoned_utc_now()
            - timedelta(days=EmailAccountHealthScoreParams.WINDOW_SIZE_DAYS),
            end_time=zoned_utc_now(),
            event_types=[
                *EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS.keys(),
                EmailEventType.SEND_ATTEMPTED,
            ],
            email_account_id=email_account_id,
        )

        emails_sent = email_event_counts[EmailEventType.SEND_ATTEMPTED]
        for (
            email_event_type,
            weight,
        ) in EmailAccountHealthScoreParams.EMAIL_EVENT_TYPE_WEIGHTS.items():
            component_score = email_event_counts[email_event_type] / max(1, emails_sent)
            health_score += component_score * weight

        warmup_health_score_data = await self.calculate_warmup_health_score(
            email_account_id, organization_id
        )
        if not warmup_health_score_data:
            return None
        warmup_health_score, mailivery_metrics = warmup_health_score_data

        # Normalize the deliverability score to a 0-100 scale
        normalized_deliverability_score = max(0, min(100, health_score))

        if emails_sent > 0:
            # Apply weights to component score and warmup score
            deliverability_weight = 1 - (
                EmailAccountHealthScoreParams.WARM_UP_CAMPAIGN_HEALTH_SCORE_WEIGHT / 100
            )
            warmup_weight = (
                EmailAccountHealthScoreParams.WARM_UP_CAMPAIGN_HEALTH_SCORE_WEIGHT / 100
            )
        else:
            deliverability_weight = 0
            warmup_weight = 1
        # Combine the scores using weighted average (both scores are on 0-100 scale)
        final_score = (normalized_deliverability_score * deliverability_weight) + (
            warmup_health_score * warmup_weight
        )

        # Ensure the score is within 0-100 range
        final_score = max(0, min(100, final_score))

        return EmailAccountHealthScoreData(
            composite_health_score=round(final_score),
            warmup_health_score=warmup_health_score,
            warmup_health_score_weight=round(
                warmup_weight * 100
            ),  # Convert to percentage
            deliverability_health_score=round(normalized_deliverability_score),
            deliverability_health_score_weight=round(
                deliverability_weight * 100
            ),  # Convert to percentage
            mailivery_metrics=mailivery_metrics,
            email_event_metrics=email_event_counts,
        )

    async def calculate_warmup_health_score(
        self, email_account_id: UUID, organization_id: UUID
    ) -> tuple[int, list[DailyMetrics]] | None:
        """Calculate warmup health score based on inbox placement rate (0-100 scale).

        Returns the percentage of warmup emails that landed in the inbox.
        """

        warmup_campaign = await self.email_account_warm_up_service.get_warmup_campaign_by_email_account_id(
            email_account_id, organization_id
        )
        if not warmup_campaign:
            logger.bind(
                email_account_id=email_account_id, organization_id=organization_id
            ).info("No warmup campaign found for email account")
            return None

        external_id = warmup_campaign.external_id
        if not external_id:
            logger.bind(
                email_account_id=email_account_id,
                organization_id=organization_id,
                warmup_campaign_id=warmup_campaign.id,
            ).info("Warmup campaign has no external ID")
            return None

        try:
            # Get metrics from Mailivery
            mailivery_metrics_response = await self.mailivery_client.get_metrics(
                external_id
            )

            # Parse the metrics data
            metrics_list = mailivery_metrics_response.metrics

            # Get dates for the last WINDOW_SIZE_DAYS days
            today = zoned_utc_now().date()
            start_date = today - timedelta(
                days=EmailAccountHealthScoreParams.WINDOW_SIZE_DAYS
            )

            # Filter metrics for the window period
            recent_metrics = [
                metrics
                for metrics in metrics_list
                if metrics.event_date
                and metrics.event_date >= start_date
                and metrics.event_date <= today
            ]

            if not recent_metrics:
                logger.bind(
                    email_account_id=email_account_id, external_id=external_id
                ).info("No recent metrics found for the last 14 days")
                return None

            # Calculate a health score based on inbox placement rate
            total_sent = 0
            total_in_inbox = 0

            for daily_metrics in recent_metrics:
                # Get warmup emails sent
                warmup_total = daily_metrics.warm_up.total

                # Get inbox placement metrics
                inbox_placement = (
                    daily_metrics.warm_up.landed_in.inbox
                    if daily_metrics.warm_up.landed_in.inbox is not None
                    else 0
                )

                total_sent += warmup_total
                total_in_inbox += inbox_placement

            # Calculate inbox placement rate (as a percentage)
            if total_sent <= 0:
                return None

            inbox_rate = (total_in_inbox / total_sent) * 100 if total_sent > 0 else 0

            # Convert to a score from 0-100
            warmup_health_score = round(inbox_rate)

            logger.bind(
                email_account_id=email_account_id,
                external_id=external_id,
                total_sent=total_sent,
                total_in_inbox=total_in_inbox,
                inbox_rate=inbox_rate,
                warmup_health_score=warmup_health_score,
            ).debug("Calculated warmup health score")

            return warmup_health_score, recent_metrics

        except Exception as e:
            logger.bind(
                email_account_id=email_account_id, external_id=external_id, error=str(e)
            ).error("Failed to calculate warmup health score")
            return None

    async def update_email_account_health(
        self,
        email_account_id: UUID,
        organization_id: UUID,
        health_score_data: EmailAccountHealthScoreData,
    ) -> None:
        existing_health = (
            await self.email_account_health_repository.get_email_account_health(
                email_account_id, organization_id
            )
        )

        # Evict the existing health record and push to history
        if existing_health:
            history_record = EmailAccountHealthHistory(
                id=uuid4(),
                organization_id=organization_id,
                email_account_id=existing_health.email_account_id,
                health_score=existing_health.health_score,
                deliverability_health_score=existing_health.deliverability_health_score,
                warmup_health_score=existing_health.warmup_health_score,
                warmup_health_score_weight=existing_health.warmup_health_score_weight,
                deliverability_health_score_weight=existing_health.deliverability_health_score_weight,
                mailivery_metrics=existing_health.mailivery_metrics,
                email_event_metrics=existing_health.email_event_metrics,
                recorded_at=existing_health.created_at,
                created_at=zoned_utc_now(),
            )
            await self.email_account_health_repository.insert(history_record)

            # Update the health record with existing id and new health score
            new_health = EmailAccountHealth(
                id=existing_health.id,
                email_account_id=email_account_id,
                organization_id=organization_id,
                health_score=health_score_data.composite_health_score,
                deliverability_health_score=health_score_data.deliverability_health_score,
                warmup_health_score=health_score_data.warmup_health_score,
                warmup_health_score_weight=health_score_data.warmup_health_score_weight,
                deliverability_health_score_weight=health_score_data.deliverability_health_score_weight,
                mailivery_metrics=health_score_data.mailivery_metrics,
                email_event_metrics=health_score_data.email_event_metrics,
                created_at=zoned_utc_now(),
            )

            await self.email_account_health_repository.upsert_pk(new_health)
        else:
            # Create a new health record
            new_health = EmailAccountHealth(
                id=uuid4(),
                email_account_id=email_account_id,
                organization_id=organization_id,
                health_score=health_score_data.composite_health_score,
                deliverability_health_score=health_score_data.deliverability_health_score,
                warmup_health_score=health_score_data.warmup_health_score,
                warmup_health_score_weight=health_score_data.warmup_health_score_weight,
                deliverability_health_score_weight=health_score_data.deliverability_health_score_weight,
                mailivery_metrics=health_score_data.mailivery_metrics,
                email_event_metrics=health_score_data.email_event_metrics,
                created_at=zoned_utc_now(),
            )
            await self.email_account_health_repository.insert(new_health)

    async def get_email_account_health_score(
        self, email_account_id: UUID, organization_id: UUID
    ) -> int | None:
        health_score = (
            await self.email_account_health_repository.get_email_account_health(
                email_account_id, organization_id
            )
        )
        return health_score.health_score if health_score else None


class SingletonEmailAccountHealthService(Singleton, EmailAccountHealthService):
    pass


def get_email_account_health_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> EmailAccountHealthService:
    if SingletonEmailAccountHealthService.has_instance():
        return SingletonEmailAccountHealthService.get_singleton_instance()
    return EmailAccountHealthService(
        email_account_health_repository=EmailAccountHealthRepository(engine=db_engine),
        email_event_repository=EmailEventRepository(engine=db_engine),
        email_account_repository=EmailAccountRepository(engine=db_engine),
        email_account_warm_up_service=get_mailivery_warmup_service_by_db_engine(
            db_engine
        ),
        mailivery_client=AsyncMailiveryClient(),
    )
