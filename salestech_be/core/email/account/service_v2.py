import uuid
from contextlib import suppress
from datetime import UTC, datetime, timedelta
from random import randint
from typing import Any, cast, override
from uuid import UUID, uuid4

import grpc
import temporalio
from temporalio.client import (
    Client,
    RPCError,
    Schedule,
    ScheduleActionStartWorkflow,
    ScheduleIntervalSpec,
    ScheduleSpec,
)
from temporalio.common import RetryPolicy, WorkflowIDReusePolicy

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    ConflictResourceError,
    ErrorDetails,
    PaymentError,
    ResourceNotFoundError,
    ServiceError,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.metadata.common import StandardObjectIdentifier
from salestech_be.common.type.patch_request import (
    BasePatchRequest,
    specified,
)
from salestech_be.common.util import format_cents_to_usd
from salestech_be.core.common.accounts_receivable import AccountsReceivable
from salestech_be.core.common.domain_service import AllowedUsers, DomainService
from salestech_be.core.common.types import UserAuth<PERSON>ontext
from salestech_be.core.email.account.email_account_health_service import (
    get_email_account_health_service_by_db_engine,
)
from salestech_be.core.email.account.schema import (
    CreateEmailAccountRequest,
    PatchEmailAccountRequestV2,
)
from salestech_be.core.email.account.types import (
    CreateWarmUpRequest,
    DefaultPoolStatus,
    EmailAccountDeactivatePatchInput,
    EmailAccountLifecycleActivateWorkflowInput,
    EmailAccountLifecycleActivateWorkflowType,
    EmailAccountV2,
    MailboxStatus,
    WarmupConfig,
    WarmupDefaults,
)
from salestech_be.core.email.pool.service import (
    get_email_account_pool_service_general,
)
from salestech_be.core.email.type.email import EmailAccountArchiveSequenceHandling
from salestech_be.core.email.warmup.service import (
    get_mailivery_warmup_service_by_db_engine,
)
from salestech_be.core.organization.service.organization_service_v2 import (
    get_organization_service_v2_from_engine,
)
from salestech_be.core.quota.service.quota_policy_service import (
    get_quota_policy_service_from_engine,
)
from salestech_be.core.quota.service.quota_service import get_quota_service_by_db_engine
from salestech_be.core.quota.type.quota_policy_type import (
    QuotaPolicy,
    UpdateQuotaPolicyRequest,
)
from salestech_be.db.dao.email_account import (
    EmailAccountRepository,
)
from salestech_be.db.dao.email_account_warm_up_campaign_repository import (
    EmailAccountWarmUpCampaignRepository,
)
from salestech_be.db.dao.outbound_repository import (
    OutboundDomainRepository,
    OutboundWorkspaceRepository,
)
from salestech_be.db.dao.user_repository import UserRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.email_account import (
    EmailAccount,
    EmailAccountType,
    EmailAccountUpdate,
    EmailProvider,
)
from salestech_be.db.models.email_account_warm_up import (
    EmailAccountWarmUpCampaign,
    MailboxWarmUpService,
    MailboxWarmUpSpeed,
    MailboxWarmUpSpeedDays,
    MailboxWarmUpStatus,
)
from salestech_be.db.models.outbound import (
    OutboundDomain,
    OutboundDomainStatus,
    OutboundVendor,
    OutboundWorkspace,
)
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
    QuotaPeriod,
)
from salestech_be.integrations.infraforge.async_infraforge_client import (
    AsyncInfraForgeClient,
)
from salestech_be.integrations.infraforge.type import (
    DomainPayload,
    ExternalBuyMailboxesRequest,
    ExternalBuyMailboxesResponse,
    GetMailboxesResponse,
    InfraforgeMailboxStatus,
    MailboxPayload,
    MailboxPurchaseInvoice,
)
from salestech_be.integrations.slack.slack_client import (
    SlackChannels,
    SlackClient,
    SlackWebhookUrls,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import TemporalTaskQueue
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.encryptions import fernet_encryption_manager
from salestech_be.settings import settings
from salestech_be.temporal.workflows.email.email_account_lifecycle_constants import (
    EMAIL_ACCOUNT_LIFECYCLE_ACTIVATE_WORKFLOW_ID_PREFIX,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    SIGNAL_CANCEL,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.temporal.workflows.email.email_account_remove import (
    EmailAccountRemoveInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EmailAccountRemoveWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.temporal.workflows.email.email_health_check_workflow import (
    EmailHealthCheckWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EmailHealthCheckWorkflowInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.email.common.imap_sync_schedule_service import (
    get_imap_sync_schedule_service_by_db_engine,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class EmailAccountServiceV2(DomainService[EmailAccountV2]):
    def __init__(self, engine: DatabaseEngine):
        self.email_account_pool_service = get_email_account_pool_service_general(
            engine=engine
        )
        self.quota_service = get_quota_service_by_db_engine(db_engine=engine)
        self.quota_policy_service = get_quota_policy_service_from_engine(engine=engine)
        self.email_account_repo = EmailAccountRepository(engine=engine)
        self.email_account_warm_up_campaign_repo = EmailAccountWarmUpCampaignRepository(
            engine=engine
        )
        self.outbound_domain_repo = OutboundDomainRepository(engine=engine)
        self.outbound_workspace_repo = OutboundWorkspaceRepository(engine=engine)
        self.user_repo = UserRepository(engine=engine)
        self.async_infraforge_client = AsyncInfraForgeClient()
        self.imap_sync_schedule_service = get_imap_sync_schedule_service_by_db_engine(
            db_engine=engine
        )
        self.email_account_health_service = (
            get_email_account_health_service_by_db_engine(db_engine=engine)
        )
        self.email_account_warmup_service = get_mailivery_warmup_service_by_db_engine(
            db_engine=engine
        )

        # Both invoices can be sent to the same channel regardless of name
        self.slack_client = SlackClient(
            webhook_url=SlackWebhookUrls.DOMAIN_PURCHASE_INVOICE
        )
        self.organization_service_v2 = get_organization_service_v2_from_engine(
            db_engine=engine
        )

    async def create_and_warmup_email_account(  # noqa: PLR0915, C901
        self,
        create_email_account_request: CreateEmailAccountRequest,
        user_auth_context: UserAuthContext,
    ) -> EmailAccountV2:
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            request=create_email_account_request,
        ).info("Create and warmup email account request")
        # check if email account already exists in organization
        existing_account = await self.email_account_repo._find_unique_by_column_values(  # noqa: SLF001
            EmailAccount,
            organization_id=user_auth_context.organization_id,
            email=create_email_account_request.email,
        )
        if existing_account:
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code=ErrorCode.EMAIL_ACCOUNT_ALREADY_EXISTS_WITH_DOMAIN_NAME,
                    details="Email account already exists",
                    conflicted_existing_object=StandardObjectIdentifier(
                        object_name="email_account"
                    ),
                )
            )
        # TODO: @neel — remove mock flag once testing is done
        if not (
            create_email_account_request.mock_config_params
            and create_email_account_request.mock_config_params.ignore_quota
        ):
            # Specifies the maximum number of mailboxes an organization can have. The aggregate of all user mailboxes should never exceed this value.
            mailbox_quota_policy = (
                await self.quota_policy_service.ensure_quota_policy_exists(
                    organization_id=user_auth_context.organization_id,
                    user_id=user_auth_context.user_id,
                    entity_type=QuotaConsumerEntityType.ORGANIZATION,
                    resource=QuotaConsumingResource.MAILBOX,
                    entity_id=user_auth_context.organization_id,
                    quota_limit=settings.quota_per_org_max_mailboxes,
                    period=QuotaPeriod.ANNUAL,
                )
            )

            # Specifies the maximum number of mailboxes a user can have.
            mailbox_quota_per_user_policy = (
                await self.quota_policy_service.ensure_quota_policy_exists(
                    organization_id=user_auth_context.organization_id,
                    user_id=create_email_account_request.owner_user_id,
                    entity_type=QuotaConsumerEntityType.USER,
                    resource=QuotaConsumingResource.MAILBOX,
                    entity_id=create_email_account_request.owner_user_id,
                    quota_limit=settings.quota_per_user_max_mailboxes,
                    period=QuotaPeriod.ANNUAL,
                )
            )

            # Specifies the maximum number of mailboxes included in an organization's subscription plan.
            # Mailboxes beyond this count will be invoiced to the organization.
            included_mailbox_quota_policy = (  # noqa: F841
                await self.quota_policy_service.ensure_quota_policy_exists(
                    organization_id=user_auth_context.organization_id,
                    user_id=user_auth_context.user_id,
                    entity_type=QuotaConsumerEntityType.ORGANIZATION,
                    resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
                    entity_id=user_auth_context.organization_id,
                    quota_limit=settings.quota_per_org_plan_included_mailboxes,
                    period=QuotaPeriod.ANNUAL,
                )
            )

            # Grab the total used and total limit for the organization
            mailbox_quota_summary = (
                await self.quota_service.get_quota_summary_per_resource(
                    organization_id=user_auth_context.organization_id,
                    resource=QuotaConsumingResource.MAILBOX,
                    period=mailbox_quota_policy.period,
                )
            )
            logger.bind(
                organization_id=user_auth_context.organization_id,
                mailbox_quota_summary=mailbox_quota_summary,
            ).info(
                "[create_and_warmup_email_account] Mailbox quota summary for the organization."
            )

            # Check if the organization has reached the mailbox quota limit
            if mailbox_quota_summary.total_remaining <= 0:
                raise PaymentError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.QUOTA_LIMIT_EXCEEDED,
                        details=f"Organization's mailbox quota of {mailbox_quota_policy.quota_limit} has been reached. No more mailboxes can be purchased. Please contact support for further assistance.",
                    ),
                )

            mailbox_quota_summary_per_user = (
                await self.quota_service.get_quota_summary_per_resource(
                    organization_id=user_auth_context.organization_id,
                    user_id=create_email_account_request.owner_user_id,
                    resource=QuotaConsumingResource.MAILBOX,
                    period=mailbox_quota_per_user_policy.period,
                )
            )
            logger.bind(
                organization_id=user_auth_context.organization_id,
                mailbox_quota_summary_per_user=mailbox_quota_summary_per_user,
            ).info(
                "[create_and_warmup_email_account] Mailbox quota summary for the user."
            )

            if mailbox_quota_summary_per_user.total_remaining <= 0:
                raise PaymentError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.QUOTA_LIMIT_EXCEEDED,
                        details=f"User's mailbox quota of {mailbox_quota_per_user_policy.quota_limit} has been reached. No more mailboxes can be purchased. Please contact support for further assistance.",
                    ),
                )

        # purchase mailbox
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            email=create_email_account_request.email,
        ).info("[create_and_warmup_email_account] Purchasing mailbox.")
        mailbox_purchase_response = await self._buy_mailbox(
            create_email_account_request, user_auth_context
        )

        mailbox_purchase_invoice = mailbox_purchase_response.invoice
        mailbox_details = mailbox_purchase_response.mailboxes[0]
        is_mock_record = create_email_account_request.mock_config_params is not None

        # get reevo plan included mailbox quota summary
        # I've moved the summary to right after the purchase so that when we persist the mailbox,
        # the mailbox should include the transaction type
        transaction = AccountsReceivable.INVOICED_TO_CUSTOMER
        included_mailbox_quota_summary = (
            await self.quota_service.get_quota_summary_per_resource(
                organization_id=user_auth_context.organization_id,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
                period=QuotaPeriod.ANNUAL,
            )
        )

        # check if reevo plan included mailbox quota has any remaining quota, and send the slack notification
        # for the invoice if not.
        if included_mailbox_quota_summary.total_remaining <= 0:
            logger.bind(organization_id=user_auth_context.organization_id).info(
                "[create_and_warmup_email_account] Reevo plan included mailbox quota has no remaining quota, invoice notification is being sent."
            )
            """
            Currently, the clients are only able to purchase 1 mailbox at a time so we can just use the first mailbox in the response.
            Additionally, we should be okay to send the invoice to the domain purchase channel since the channel is probably sufficient for Infraforge purchases.
            """
            await self._send_mailbox_purchase_invoice_slack_message(
                infraforge_response=mailbox_purchase_response,
                mailbox_name=mailbox_details.email,
                organization_id=user_auth_context.organization_id,
            )
        # Case in which the reevo plan included mailbox quota has remaining quota
        # Also make sure that mock mailboxes (if they're still made) don't count towards the plan-included limit
        else:
            # increase usage for organization for reevo plan included mailbox quota if it is not mock
            if not is_mock_record:
                transaction = AccountsReceivable.INCLUDED_IN_PLAN
            await self.increment_plan_included_mailbox_quota_usage(
                user_auth_context=user_auth_context, is_mock_record=is_mock_record
            )

        # create email account
        logger.bind(
            organization_id=user_auth_context.organization_id,
            email=mailbox_details.email,
            external_id=str(mailbox_details.id),
            is_mock_record=is_mock_record,
            transaction=transaction,
        ).info("Creating email account in database.")
        email_account = await self.email_account_repo.create_email_account(
            EmailAccount(
                id=uuid.uuid4(),
                owner_user_id=create_email_account_request.owner_user_id,
                email=mailbox_details.email,
                type=EmailAccountType.OUTBOUND,
                outbound_domain_id=create_email_account_request.outbound_domain_id,
                active=True,
                external_id=str(mailbox_details.id),
                vendor=EmailProvider.INFRAFORGE,
                reply_to_email=create_email_account_request.reply_to_email,
                signature_id=create_email_account_request.signature_id,
                first_name=mailbox_details.firstName,
                last_name=mailbox_details.lastName,
                is_default=False,
                seconds_delay_between_emails=create_email_account_request.seconds_delay_between_emails,
                organization_id=user_auth_context.organization_id,
                created_at=zoned_utc_now(),
                created_by_user_id=user_auth_context.user_id,
                invoice=mailbox_purchase_invoice,
                is_mock_record=is_mock_record,
                transaction_type=transaction,
            )
        )

        # increase usage for organization
        mailbox_usage_per_organization = await self.quota_service.increase_usage(
            organization_id=user_auth_context.organization_id,
            entity_id=user_auth_context.organization_id,
            entity_type=QuotaConsumerEntityType.ORGANIZATION,
            resource=QuotaConsumingResource.MAILBOX,
            usage=1,
            timestamp=zoned_utc_now(),
        )
        logger.bind(
            mailbox_usage_per_organization=mailbox_usage_per_organization,
        ).info(
            f"[create_and_warmup_email_account] Mailbox usage for the organization id ({user_auth_context.organization_id})."
        )

        # increase usage for user
        mailbox_usage_per_user = await self.quota_service.increase_usage(
            organization_id=user_auth_context.organization_id,
            entity_id=create_email_account_request.owner_user_id,
            entity_type=QuotaConsumerEntityType.USER,
            resource=QuotaConsumingResource.MAILBOX,
            usage=1,
            timestamp=zoned_utc_now(),
        )
        logger.bind(
            mailbox_usage_per_user=mailbox_usage_per_user,
            organization_id=user_auth_context.organization_id,
        ).info(
            f"[create_and_warmup_email_account] Mailbox usage for the user id ({create_email_account_request.owner_user_id})."
        )

        # create quota policy for email account daily quota
        quota_policy = await self.quota_policy_service.upsert_quota_policy(
            quota_limit=create_email_account_request.daily_quota,
            period=QuotaPeriod.DAILY,
            resource=QuotaConsumingResource.EMAIL,
            entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
            entity_id=email_account.id,
            applied_sub_entity_types=None,
            user_id=email_account.owner_user_id,
            organization_id=user_auth_context.organization_id,
        )

        # add to default pool if flag is set
        if create_email_account_request.is_in_default_pool:
            logger.bind(
                organization_id=user_auth_context.organization_id,
                email_account_id=email_account.id,
            ).info(
                "[create_and_warmup_email_account] Adding email account to default pool."
            )
            default_email_account_pool = await self.email_account_pool_service.get_or_create_default_email_account_pool(
                user_id=email_account.owner_user_id,
                organization_id=email_account.organization_id,
            )

            await self.email_account_pool_service.add_email_account_to_pool(
                user_id=email_account.owner_user_id,
                organization_id=email_account.organization_id,
                email_account_pool_id=default_email_account_pool.id,
                email_account_ids=[email_account.id],
                is_mock_record=is_mock_record,
            )

        # add to other pools if flag is set
        if create_email_account_request.email_account_pool_ids:
            logger.bind(
                organization_id=email_account.organization_id,
                email_account_id=email_account.id,
                pool_ids=create_email_account_request.email_account_pool_ids,
            ).info(
                "[create_and_warmup_email_account] Adding email account to specified pools."
            )
            for email_pool_id in create_email_account_request.email_account_pool_ids:
                await self.email_account_pool_service.add_email_account_to_pool(
                    user_id=email_account.owner_user_id,
                    organization_id=email_account.organization_id,
                    email_account_pool_id=email_pool_id,
                    email_account_ids=[email_account.id],
                    is_mock_record=is_mock_record,
                )

        # don't start warmup if mock purchase, create mock campaign instead
        if (
            create_email_account_request.mock_config_params
            and create_email_account_request.mock_config_params.mock_purchase
        ):
            logger.bind(
                organization_id=user_auth_context.organization_id,
                email_account_id=email_account.id,
            ).info("[create_and_warmup_email_account] Creating mock warmup campaign.")
            warmup_campaign = EmailAccountWarmUpCampaign(
                id=uuid.uuid4(),
                domain_id=create_email_account_request.outbound_domain_id,
                email_per_day=create_email_account_request.warmup_limit,
                response_rate=WarmupDefaults.RESPONSE_RATE,
                email_account_id=email_account.id,
                requested_by_user_id=user_auth_context.user_id,
                requested_at=zoned_utc_now(),
                warm_up_service=MailboxWarmUpService.MAILIVERY,
                external_id=str(uuid4()),
                status=MailboxWarmUpStatus.IN_PROGRESS,
                with_rampup=False,
                rampup_speed=MailboxWarmUpSpeed.FAST,
                organization_id=user_auth_context.organization_id,
                created_at=zoned_utc_now(),
                created_by_user_id=user_auth_context.user_id,
                is_mock_record=True,
                last_started_at=zoned_utc_now(),
            )
            await self.email_account_warm_up_campaign_repo.insert(warmup_campaign)

            return await self.email_account_v2_from_db_model(
                db_email_account=email_account,
                daily_quota=quota_policy.quota_limit,
                organization_id=email_account.organization_id,
                user_id=email_account.owner_user_id,
            )

        logger.bind(
            organization_id=user_auth_context.organization_id,
            email_account_id=email_account.id,
        ).info(
            "[create_and_warmup_email_account] Starting async workflows for email account."
        )
        await self.start_email_account_async_workflows(
            email_account_id=email_account.id,
            user_auth_context=user_auth_context,
            create_email_account_request=create_email_account_request,
        )

        # create imap sync schedule
        await self.imap_sync_schedule_service.create_imap_sync_schedule(
            email_account_id=email_account.id,
            organization_id=user_auth_context.organization_id,
        )
        return await self.email_account_v2_from_db_model(
            db_email_account=email_account,
            daily_quota=quota_policy.quota_limit,
            organization_id=email_account.organization_id,
            user_id=email_account.owner_user_id,
        )

    async def start_email_account_async_workflows(
        self,
        email_account_id: UUID,
        user_auth_context: UserAuthContext,
        create_email_account_request: CreateEmailAccountRequest,
    ) -> None:
        create_warmup_request = CreateWarmUpRequest(
            email_account_id=email_account_id,
            email_per_day=create_email_account_request.warmup_limit,
        )

        client = await get_temporal_client()
        wf_id = await self.email_account_wf_id(
            organization_id=user_auth_context.organization_id,
            email_account_ids=[email_account_id],
            prefix=EMAIL_ACCOUNT_LIFECYCLE_ACTIVATE_WORKFLOW_ID_PREFIX,
            domain_level=False,
        )
        await client.start_workflow(
            "EmailAccountLifecycleActivateWorkflow",
            args=[
                EmailAccountLifecycleActivateWorkflowInput(
                    workflow_type=EmailAccountLifecycleActivateWorkflowType.CREATE,
                    lifecycle_operation_data=create_warmup_request,
                    user_id=user_auth_context.user_id,
                    organization_id=user_auth_context.organization_id,
                ),
            ],
            id=wf_id,
            task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
        )
        await self._create_email_account_health_check_schedule(
            client=client,
            organization_id=user_auth_context.organization_id,
        )

    def _filter_changed_fields(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        current_model: EmailAccount,
        fields_to_update: dict[str, Any],
    ) -> dict[str, Any]:
        """Filter fields that are different from current model values."""
        filtered_fields = {}
        for field, value in fields_to_update.items():
            current_value = getattr(current_model, field, None)
            if current_value != value:
                filtered_fields[field] = value
        return filtered_fields

    async def _handle_email_account_owner_change(
        self,
        email_account: EmailAccount,
        patch_email_account_request: PatchEmailAccountRequestV2,
        new_owner_user_id: UUID,
        user_auth_context: UserAuthContext,
    ) -> None:
        email_account_id = email_account.id
        original_owner_user_id = email_account.owner_user_id
        (
            original_owner_user_mailbox_quota_policy,
            transferred_user_mailbox_quota_policy,
        ) = await self.get_or_create_mailbox_policy_for_update(
            request=patch_email_account_request,
            user_auth_context=user_auth_context,
            original_owner_user_id=original_owner_user_id,
        )
        transferred_user_mailbox_quota_summary = (
            await self.quota_service.get_quota_summary_per_resource(
                organization_id=user_auth_context.organization_id,
                user_id=new_owner_user_id,
                resource=QuotaConsumingResource.MAILBOX,
                period=QuotaPeriod.ANNUAL,
            )
        )
        logger.bind(
            organization_id=user_auth_context.organization_id,
            mailbox_quota_summary_per_user=transferred_user_mailbox_quota_summary,
        ).info("[update_email_account] Transferred user mailbox quota summary.")
        if transferred_user_mailbox_quota_summary.total_remaining <= 0:
            raise PaymentError(
                additional_error_details=ErrorDetails(
                    code=ErrorCode.QUOTA_LIMIT_EXCEEDED,
                    details=f"User's mailbox quota of {transferred_user_mailbox_quota_policy.quota_limit} has been reached. No more mailboxes can be transferred to this user. Please contact support for further assistance.",
                ),
            )
        await self.quota_service.increase_usage(
            organization_id=user_auth_context.organization_id,
            entity_id=new_owner_user_id,
            entity_type=QuotaConsumerEntityType.USER,
            resource=QuotaConsumingResource.MAILBOX,
            usage=1,
            timestamp=zoned_utc_now(),
        )
        await self.quota_service.decrease_usage(
            organization_id=user_auth_context.organization_id,
            entity_id=original_owner_user_id,
            entity_type=QuotaConsumerEntityType.USER,
            resource=QuotaConsumingResource.MAILBOX,
            usage=1,
            timestamp=zoned_utc_now(),
        )

        await self.email_account_pool_service.remove_email_accounts_from_all_pools(
            email_account_ids=[email_account_id],
            user_id=user_auth_context.user_id,
            organization_id=user_auth_context.organization_id,
        )
        new_owner_default_email_account_pool = await self.email_account_pool_service.get_or_create_default_email_account_pool(
            user_id=new_owner_user_id,
            organization_id=user_auth_context.organization_id,
        )
        await self.email_account_pool_service.add_email_account_to_pool(
            user_id=new_owner_user_id,
            organization_id=user_auth_context.organization_id,
            email_account_pool_id=new_owner_default_email_account_pool.id,
            email_account_ids=[email_account_id],
            is_mock_record=email_account.is_mock_record,
        )

    async def _handle_in_default_pool_change(
        self,
        email_account: EmailAccount,
        patch_email_account_request: PatchEmailAccountRequestV2,
        user_auth_context: UserAuthContext,
    ) -> None:
        email_account_id = email_account.id
        default_email_account_pool_membership = await self.email_account_pool_service.get_default_email_account_pool_membership(
            email_account_id=email_account_id,
            user_id=email_account.owner_user_id,
            organization_id=email_account.organization_id,
        )
        # Current state
        is_in_default_pool = default_email_account_pool_membership is not None
        # Only take action if the requested state is different from current state
        if patch_email_account_request.is_in_default_pool == is_in_default_pool:
            return
        # if add to default pool is true and email account is not in default pool, add to default pool
        if patch_email_account_request.is_in_default_pool:
            logger.bind(
                organization_id=user_auth_context.organization_id,
                email_account_id=email_account_id,
            ).info("[update_email_account] Adding email account to default pool.")
            default_email_account_pool = await self.email_account_pool_service.get_or_create_default_email_account_pool(
                user_id=email_account.owner_user_id,
                organization_id=email_account.organization_id,
            )

            await self.email_account_pool_service.add_email_account_to_pool(
                user_id=email_account.owner_user_id,
                organization_id=email_account.organization_id,
                email_account_pool_id=default_email_account_pool.id,
                email_account_ids=[email_account_id],
                is_mock_record=email_account.is_mock_record,
            )
        # if is in default pool is false and email account is in default pool, remove from default pool
        else:
            await self.email_account_pool_service.remove_email_account_from_pool(
                organization_id=email_account.organization_id,
                email_account_pool_id=not_none(
                    default_email_account_pool_membership
                ).email_account_pool_id,
                email_account_ids=[email_account_id],
            )

            client = await get_temporal_client()
            await client.start_workflow(
                EmailAccountRemoveWorkflow.run,
                args=[
                    EmailAccountRemoveInput(
                        organization_id=user_auth_context.organization_id,
                        user_id=user_auth_context.user_id,
                        email_accounts={
                            email_account_id: EmailAccountArchiveSequenceHandling(
                                str(patch_email_account_request.sequence_handling)
                            )
                        },
                    ),
                ],
                id=f"email_account_remove_workflow_{email_account_id}",
                task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
            )

    async def _handle_warmup_limit_change(
        self,
        email_account: EmailAccount,
        new_warmup_limit: int,
        user_auth_context: UserAuthContext,
    ) -> None:
        email_account_id = email_account.id
        # Get current warmup campaign to check if value has changed
        warmup_campaign = await self.email_account_warm_up_campaign_repo.find_campaign_by_email_account_id_organization_id_and_warm_up_service(
            email_account_id=email_account_id,
            organization_id=user_auth_context.organization_id,
            warm_up_service=MailboxWarmUpService.MAILIVERY,
        )
        if not warmup_campaign:
            raise ServiceError(
                f"Warmup campaign not found for email account {email_account_id}"
            )

        if warmup_campaign.email_per_day == new_warmup_limit:
            return
        # if warmup limit is different, patch the warmup limit
        warmup_configs = {
            email_account_id: WarmupConfig(
                warmup_limit=new_warmup_limit,
            )
        }
        client = await get_temporal_client()

        wf_id = await self.email_account_wf_id(
            organization_id=user_auth_context.organization_id,
            email_account_ids=[email_account_id],
            prefix="email_account_lifecycle_deactivate_patch_workflow",
            domain_level=False,
        )
        await client.start_workflow(
            "EmailAccountLifecycleDeactivatePatchWorkflow",
            args=[
                EmailAccountDeactivatePatchInput(
                    user_id=user_auth_context.user_id,
                    organization_id=user_auth_context.organization_id,
                    warmup_configs=warmup_configs,
                )
            ],
            id=wf_id,
            task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
            id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
        )

    async def update_email_account(
        self,
        email_account_id: UUID,
        patch_email_account_request: PatchEmailAccountRequestV2,
        user_auth_context: UserAuthContext,
    ) -> EmailAccountV2:
        logger.bind(
            organization_id=user_auth_context.organization_id,
            email_account_id=email_account_id,
            user_id=user_auth_context.user_id,
            request=patch_email_account_request,
        ).info("Update email account request")

        if not (
            email_account := await self.email_account_repo.find_by_tenanted_primary_key(
                EmailAccount,
                organization_id=user_auth_context.organization_id,
                id=email_account_id,
            )
        ):
            raise ResourceNotFoundError("Email account not found")

        email_accounts_fields_to_update = patch_email_account_request.model_dump(
            include={
                "first_name",
                "last_name",
                "reply_to_email",
                "signature_html",
                "signature_id",
                "owner_user_id",
                "use_override",
                "seconds_delay_between_emails",
            }
        )
        if (
            specified(patch_email_account_request.owner_user_id)
            and patch_email_account_request.owner_user_id != email_account.owner_user_id
        ):
            await self._handle_email_account_owner_change(
                email_account=email_account,
                patch_email_account_request=patch_email_account_request,
                new_owner_user_id=patch_email_account_request.owner_user_id,
                user_auth_context=user_auth_context,
            )

        # Only keep fields that have changed
        email_accounts_fields_to_update = self._filter_changed_fields(
            current_model=email_account,
            fields_to_update=email_accounts_fields_to_update,
        )

        if email_accounts_fields_to_update:
            email_accounts_fields_to_update["updated_at"] = zoned_utc_now()
            email_accounts_fields_to_update["updated_by_user_id"] = (
                user_auth_context.user_id
            )

            logger.bind(
                organization_id=user_auth_context.organization_id,
                email_account_id=email_account_id,
                fields_to_update=email_accounts_fields_to_update,
            ).info("[update_email_account] Updating email account fields.")
            updated_email_account = not_none(
                await self.email_account_repo.update_email_account(
                    email_account_id=email_account_id,
                    column_to_update=email_accounts_fields_to_update,
                )
            )
        else:
            updated_email_account = email_account

        if specified(patch_email_account_request.is_in_default_pool):
            await self._handle_in_default_pool_change(
                email_account=email_account,
                patch_email_account_request=patch_email_account_request,
                user_auth_context=user_auth_context,
            )

        if specified(patch_email_account_request.email_account_pool_ids):
            logger.bind(
                organization_id=user_auth_context.organization_id,
                email_account_id=email_account_id,
                pool_ids=patch_email_account_request.email_account_pool_ids,
            ).info("[update_email_account] Updating email account pool memberships.")
            await self.email_account_pool_service.update_email_account_pool_memberships_by_email_account_id_and_pool_ids(
                user_auth_context=user_auth_context,
                email_account_id=email_account_id,
                email_account_pool_ids=patch_email_account_request.email_account_pool_ids,
            )

        if (
            specified(patch_email_account_request.warmup_limit)
            and not email_account.is_mock_record
        ):
            await self._handle_warmup_limit_change(
                email_account=email_account,
                new_warmup_limit=patch_email_account_request.warmup_limit,
                user_auth_context=user_auth_context,
            )

        quota_policies = await self.quota_policy_service.list_quota_policies_by_entity_ids_and_type_resource(
            entity_id_list=[email_account.id],
            resource=QuotaConsumingResource.EMAIL,
            entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
            organization_id=user_auth_context.organization_id,
        )
        if len(quota_policies) != 1:
            raise ServiceError(
                f"Email Account {email_account.id} has invalid quota policy."
            )
        quota_policy = quota_policies[0]
        if (
            specified(patch_email_account_request.daily_quota)
            and patch_email_account_request.daily_quota != quota_policy.quota_limit
        ):
            logger.bind(
                organization_id=user_auth_context.organization_id,
                email_account_id=email_account_id,
                current_quota=quota_policy.quota_limit,
                new_quota=patch_email_account_request.daily_quota,
            ).info("[update_email_account] Updating daily quota policy.")
            quota_policy = await self.quota_policy_service.update_quota_policy(
                user_id=user_auth_context.user_id,
                organization_id=user_auth_context.organization_id,
                policy_id=quota_policy.id,
                api_request=UpdateQuotaPolicyRequest(
                    quota_limit=patch_email_account_request.daily_quota
                ),
            )

        return await self.email_account_v2_from_db_model(
            db_email_account=updated_email_account,
            daily_quota=quota_policy.quota_limit,
            organization_id=email_account.organization_id,
            user_id=email_account.owner_user_id,
        )

    async def email_account_wf_id(
        self,
        organization_id: UUID,
        email_account_ids: list[UUID],
        prefix: str,
        domain_level: bool = False,
    ) -> str:
        if not domain_level:
            # this should only be one email account for now
            return f"{prefix}_{'_'.join(map(str, email_account_ids))}"
        else:
            if not email_account_ids:
                raise ServiceError("No email account ids provided")
            email_account = await self.email_account_repo.find_by_tenanted_primary_key(
                table_model=EmailAccount,
                organization_id=organization_id,
                id=email_account_ids[0],
            )
            if not email_account:
                raise ServiceError("Email account not found")

            outbound_domain_id = email_account.outbound_domain_id
            if not outbound_domain_id:
                raise ServiceError("Outbound domain not found")
            outbound_domain = (
                await self.outbound_domain_repo.find_by_tenanted_primary_key(
                    table_model=OutboundDomain,
                    organization_id=organization_id,
                    id=outbound_domain_id,
                )
            )
            if not outbound_domain:
                raise ServiceError("Outbound domain not found")
            return f"{prefix}_{outbound_domain.domain}"

    async def archive_email_accounts(
        self,
        email_accounts_to_archive: dict[UUID, EmailAccountArchiveSequenceHandling],
        user_auth_context: UserAuthContext,
        archive_domain: bool = False,
    ) -> None:
        logger.bind(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            email_accounts_to_archive=email_accounts_to_archive,
        ).info("Archive email account request")

        if not email_accounts_to_archive:
            return

        email_accounts = await self.email_account_repo._find_by_column_values(  # noqa: SLF001
            EmailAccount,
            organization_id=user_auth_context.organization_id,
            id=list(email_accounts_to_archive.keys()),
        )

        email_account_ids = [email_account.id for email_account in email_accounts]
        if not email_account_ids:
            raise ResourceNotFoundError("Email accounts not found")

        logger.bind(
            organization_id=user_auth_context.organization_id,
            email_account_ids=email_account_ids,
        ).info("[archive_email_account] Removing email account from all pools.")

        await self.email_account_pool_service.remove_email_accounts_from_all_pools(
            email_account_ids=email_account_ids,
            user_id=user_auth_context.user_id,
            organization_id=user_auth_context.organization_id,
        )

        # Delete IMAP sync schedules for each email account
        for email_account_id in email_account_ids:
            try:
                await self.imap_sync_schedule_service.delete_imap_sync_schedule(
                    organization_id=user_auth_context.organization_id,
                    email_account_id=email_account_id,
                )
                logger.bind(
                    organization_id=user_auth_context.organization_id,
                    email_account_id=email_account_id,
                ).info("[archive_email_account] Deleted IMAP sync schedule.")
            except Exception as e:
                logger.bind(
                    organization_id=user_auth_context.organization_id,
                    email_account_id=email_account_id,
                    error=str(e),
                ).warning(
                    "[archive_email_account] Failed to delete IMAP sync schedule."
                )

        pause_warmup_configs = {
            email_account.id: WarmupConfig(
                warmup_limit=None,
                warmup_status=MailboxWarmUpStatus.PAUSED,
            )
            for email_account in email_accounts
            if not email_account.is_mock_record
        }

        client = await get_temporal_client()
        await self._cancel_existing_activate_email_account_workflow(
            client, pause_warmup_configs
        )
        wf_id = await self.email_account_wf_id(
            organization_id=user_auth_context.organization_id,
            email_account_ids=email_account_ids,
            prefix="email_account_lifecycle_deactivate_patch_workflow",
            domain_level=archive_domain,
        )
        await client.start_workflow(
            "EmailAccountLifecycleDeactivatePatchWorkflow",
            args=[
                EmailAccountDeactivatePatchInput(
                    warmup_configs=pause_warmup_configs,
                    user_id=user_auth_context.user_id,
                    organization_id=user_auth_context.organization_id,
                )
            ],
            id=wf_id,
            task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
            id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
        )
        await client.start_workflow(
            EmailAccountRemoveWorkflow.run,
            args=[
                EmailAccountRemoveInput(
                    organization_id=user_auth_context.organization_id,
                    user_id=user_auth_context.user_id,
                    email_accounts=email_accounts_to_archive,
                )
            ],
            id=f"email_account_remove_workflow_{'_'.join(map(str, email_account_ids))}",
            task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
        )

        await self.email_account_repo.archive_email_accounts_by_ids(
            email_account_ids=email_account_ids,
            user_auth_context=user_auth_context,
        )

    async def unarchive_email_accounts(
        self,
        email_account_ids: list[UUID],
        user_auth_context: UserAuthContext,
    ) -> None:
        logger.bind(
            organization_id=user_auth_context.organization_id,
            email_account_ids=email_account_ids,
            user_id=user_auth_context.user_id,
        ).info("Unarchive email account request")

        if not email_account_ids:
            return

        email_accounts = (
            await self.email_account_repo.find_archived_email_accounts_by_ids(
                organization_id=user_auth_context.organization_id,
                email_account_ids=email_account_ids,
            )
        )
        if not email_accounts:
            raise ResourceNotFoundError("Archived email accounts not found")

        logger.bind(
            organization_id=user_auth_context.organization_id,
            email_account_ids=email_account_ids,
        ).info("[unarchive_email_account] Updating email account to active state.")

        await self.email_account_repo.unarchive_email_accounts_by_ids(
            email_account_ids=email_account_ids,
            user_auth_context=user_auth_context,
        )

        # Create IMAP sync schedules for each unarchived email account
        for email_account_id in email_account_ids:
            try:
                await self.imap_sync_schedule_service.create_imap_sync_schedule(
                    organization_id=user_auth_context.organization_id,
                    email_account_id=email_account_id,
                )
                logger.bind(
                    organization_id=user_auth_context.organization_id,
                    email_account_id=email_account_id,
                ).info("[unarchive_email_account] Created IMAP sync schedule.")
            except Exception as e:
                logger.bind(
                    organization_id=user_auth_context.organization_id,
                    email_account_id=email_account_id,
                    error=str(e),
                ).warning(
                    "[unarchive_email_account] Failed to create IMAP sync schedule."
                )

        # resume warmup if it's not a mock record
        resume_warmup_configs = {
            email_account.id: WarmupConfig(
                warmup_status=MailboxWarmUpStatus.IN_PROGRESS,
            )
            for email_account in email_accounts
            if not email_account.is_mock_record
        }
        logger.bind(
            organization_id=user_auth_context.organization_id,
            email_account_ids=email_account_ids,
        ).info("[unarchive_email_account] Starting workflow to resume warmup.")

        client = await get_temporal_client()
        wf_id = await self.email_account_wf_id(
            organization_id=user_auth_context.organization_id,
            email_account_ids=email_account_ids,
            prefix=EMAIL_ACCOUNT_LIFECYCLE_ACTIVATE_WORKFLOW_ID_PREFIX,
            domain_level=False,
        )
        await client.start_workflow(
            "EmailAccountLifecycleActivateWorkflow",
            args=[
                EmailAccountLifecycleActivateWorkflowInput(
                    workflow_type=EmailAccountLifecycleActivateWorkflowType.UNARCHIVE,
                    lifecycle_operation_data=resume_warmup_configs,
                    user_id=user_auth_context.user_id,
                    organization_id=user_auth_context.organization_id,
                ),
            ],
            id=wf_id,
            task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
            id_reuse_policy=WorkflowIDReusePolicy.TERMINATE_IF_RUNNING,
        )

    async def _buy_mailbox(
        self,
        create_email_account_request: CreateEmailAccountRequest,
        user_auth_context: UserAuthContext,
    ) -> ExternalBuyMailboxesResponse:
        outbound_domain: OutboundDomain | None = None

        # create a mock outbound domain and workspace if mock purchase
        if (
            create_email_account_request.mock_config_params
            and create_email_account_request.mock_config_params.mock_domain_name
        ):
            outbound_workspace = await self.outbound_workspace_repo.get_outbound_workspace_by_organization_id(
                organization_id=user_auth_context.organization_id
            )
            if not outbound_workspace:
                outbound_workspace = OutboundWorkspace(
                    id=uuid4(),
                    name=f"Test Workspace {user_auth_context.user_id}",
                    vendor=OutboundVendor.INFRAFORGE,
                    organization_id=user_auth_context.organization_id,
                    external_id=str(uuid4()),
                    is_mock_record=True,
                )
                outbound_workspace = await self.outbound_workspace_repo.insert(
                    outbound_workspace
                )

            existing_test_outbound_domain = await self.outbound_domain_repo._find_unique_by_column_values(  # noqa: SLF001
                OutboundDomain,
                organization_id=user_auth_context.organization_id,
                domain=create_email_account_request.mock_config_params.mock_domain_name,
                is_mock_record=True,
            )
            if existing_test_outbound_domain:
                outbound_domain = existing_test_outbound_domain
            else:
                outbound_domain = OutboundDomain(
                    id=uuid4(),
                    organization_id=user_auth_context.organization_id,
                    domain=create_email_account_request.mock_config_params.mock_domain_name,
                    external_id=str(uuid4()),
                    status=OutboundDomainStatus.ACTIVE,
                    workspace_id=outbound_workspace.id,
                    vendor=OutboundVendor.INFRAFORGE,
                    created_by_user_id=user_auth_context.user_id,
                    is_mock_record=True,
                )
                outbound_domain = await self.outbound_domain_repo.insert(
                    outbound_domain
                )
        else:
            outbound_domain = (
                await self.outbound_domain_repo.find_by_tenanted_primary_key(
                    OutboundDomain,
                    organization_id=user_auth_context.organization_id,
                    id=create_email_account_request.outbound_domain_id,
                )
            )

            # check if domain exists
            if not outbound_domain:
                raise ResourceNotFoundError(
                    additional_error_details=ErrorDetails(
                        code=ErrorCode.RESOURCE_NOT_FOUND,
                        details="Domain not found",
                    )
                )
            if outbound_domain.status not in {
                OutboundDomainStatus.ACTIVE,
                OutboundDomainStatus.PENDING,
            }:
                raise ServiceError(
                    f"Cannot purchase mailbox for domain {outbound_domain.domain} with status {outbound_domain.status}"
                )
        # mock purchase if flag is set
        if (
            create_email_account_request.mock_config_params
            and create_email_account_request.mock_config_params.mock_purchase
        ):
            amount_paid = randint(1, 1000)  # noqa: S311
            return ExternalBuyMailboxesResponse(
                invoice=MailboxPurchaseInvoice(
                    id=str(uuid4()),
                    paid_at=str(zoned_utc_now()),
                    sub_total=randint(1, amount_paid),  # noqa: S311
                    amount_paid=amount_paid,
                    total=amount_paid,
                    credits_applied=randint(1, amount_paid),  # noqa: S311
                ),
                mailboxes=[
                    GetMailboxesResponse(
                        email=create_email_account_request.email,
                        firstName=create_email_account_request.first_name,
                        lastName=create_email_account_request.last_name,
                        forwardingEmail=None,
                        id=str(uuid4()),
                        signature=None,
                        status=InfraforgeMailboxStatus.ACTIVE,
                    )
                ],
            )
        purchase_mailbox_request = ExternalBuyMailboxesRequest(
            domains=[
                DomainPayload(
                    domain=outbound_domain.domain,
                    mailboxes=[
                        MailboxPayload(
                            email=create_email_account_request.email,
                            firstName=create_email_account_request.first_name,
                            lastName=create_email_account_request.last_name,
                        )
                    ],
                )
            ],
        )

        return await self.async_infraforge_client.buy_mailboxes(
            purchase_mailbox_request
        )

    async def get_or_create_mailbox_policy_for_update(
        self,
        request: PatchEmailAccountRequestV2,
        user_auth_context: UserAuthContext,
        original_owner_user_id: UUID,
    ) -> tuple[QuotaPolicy, QuotaPolicy]:
        if not specified(
            request.owner_user_id
        ):  # generally this check is not needed since we already checked outside of this function, but it fails
            # the mypy check so we check it again to see that it's not unset
            raise ValueError("owner_user_id must be set")
        # we don't need to make an organization policy since we're not making a new mailbox
        # create a policy for the new owner user if it doesn't exist
        transferred_user_mailbox_quota_policy = await self.quota_policy_service.get_quota_policy_by_entity_id_and_type_resource(
            entity_id=request.owner_user_id,
            entity_type=QuotaConsumerEntityType.USER,
            resource=QuotaConsumingResource.MAILBOX,
            organization_id=user_auth_context.organization_id,
        )
        if not transferred_user_mailbox_quota_policy:
            transferred_user_mailbox_quota_policy = (
                await self.quota_policy_service.upsert_quota_policy(
                    organization_id=user_auth_context.organization_id,
                    entity_id=request.owner_user_id,
                    resource=QuotaConsumingResource.MAILBOX,
                    entity_type=QuotaConsumerEntityType.USER,
                    applied_sub_entity_types=None,
                    quota_limit=settings.quota_per_user_max_mailboxes,
                    period=QuotaPeriod.ANNUAL,
                    user_id=request.owner_user_id,
                )
            )
        # make sure that the old user has a quota policy
        original_owner_user_mailbox_quota_policy = await self.quota_policy_service.get_quota_policy_by_entity_id_and_type_resource(
            organization_id=user_auth_context.organization_id,
            entity_id=original_owner_user_id,
            resource=QuotaConsumingResource.MAILBOX,
            entity_type=QuotaConsumerEntityType.USER,
        )
        if not original_owner_user_mailbox_quota_policy:
            original_owner_user_mailbox_quota_policy = (
                await self.quota_policy_service.upsert_quota_policy(
                    organization_id=user_auth_context.organization_id,
                    entity_id=original_owner_user_id,
                    resource=QuotaConsumingResource.MAILBOX,
                    entity_type=QuotaConsumerEntityType.USER,
                    applied_sub_entity_types=None,
                    quota_limit=settings.quota_per_user_max_mailboxes,
                    period=QuotaPeriod.ANNUAL,
                    user_id=original_owner_user_id,
                )
            )
        return (
            original_owner_user_mailbox_quota_policy,
            transferred_user_mailbox_quota_policy,
        )

    async def get_and_persist_mailbox_credentials(
        self,
        email_account_id: UUID,
        organization_id: UUID,
    ) -> EmailAccount:
        email_account = await self.email_account_repo.find_by_tenanted_primary_key(
            EmailAccount,
            organization_id=organization_id,
            id=email_account_id,
        )
        if email_account is None or email_account.external_id is None:
            raise ValueError("Email account not found")

        workspace = await self.outbound_workspace_repo.get_outbound_workspace_by_organization_id(
            organization_id=organization_id
        )

        if not workspace:
            raise ResourceNotFoundError("Workspace not found")

        all_mailboxes = await self.async_infraforge_client.get_mailboxes(
            workspace_id=workspace.external_id
        )
        if not all_mailboxes:
            raise ResourceNotFoundError("Mailbox not found")

        mailbox = next(
            (
                mailbox
                for mailbox in all_mailboxes.data
                if mailbox.email == email_account.email
            ),
            None,
        )
        if not mailbox:
            raise ResourceNotFoundError("Mailbox not found")

        credentials = mailbox.credentials

        # Before the following mailbox credentials are persisted, .get_secret_value()
        # must be called in order to grab the actual string values and encrypted.
        # At no point should the passwords be stored in plaintext.

        encrypted_imap_password = fernet_encryption_manager.encrypt(
            credentials.imapPassword.get_secret_value()
        )

        encrypted_smtp_password = fernet_encryption_manager.encrypt(
            credentials.smtpPassword.get_secret_value()
        )

        email_account_update = EmailAccountUpdate(
            imap_host=credentials.imapHost,
            imap_port=credentials.imapPort,
            imap_username=credentials.imapUsername,
            imap_password=encrypted_imap_password,
            smtp_host=credentials.smtpHost,
            smtp_port=credentials.smtpPort,
            smtp_username=credentials.smtpUsername,
            smtp_password=encrypted_smtp_password,
            updated_at=zoned_utc_now(),
        )

        updated_email_account = await self.email_account_repo.update_email_account(
            email_account_id=email_account_id,
            column_to_update=email_account_update.flatten_specified_values(),
        )

        if not updated_email_account:
            raise ValueError("Failed to update email account")

        return updated_email_account

    async def find_email_account_by_ids(
        self,
        email_account_ids: list[UUID],
        organization_id: UUID,
        include_archived: bool = False,
    ) -> list[EmailAccount]:
        if not email_account_ids:
            return []
        return await self.email_account_repo.find_accounts_by_ids(
            organization_id=organization_id,
            email_account_ids=email_account_ids,
            include_archived=include_archived,
        )

    async def find_email_accounts_by_organization_id(
        self,
        organization_id: UUID,
        include_archived: bool = False,
    ) -> list[EmailAccount]:
        return await self.email_account_repo.find_accounts_by_organization_id(
            organization_id=organization_id,
            include_archived=include_archived,
        )

    async def find_email_accounts_by_domain_id(
        self, organization_id: UUID, outbound_domain_id: UUID
    ) -> list[EmailAccount]:
        return await self.email_account_repo.find_accounts_by_domain_id(
            organization_id=organization_id, outbound_domain_id=outbound_domain_id
        )

    async def map_warmup_status_by_email_account_ids(
        self,
        email_account_ids: list[UUID],
        organization_id: UUID,
    ) -> dict[UUID, MailboxWarmUpStatus]:
        if not email_account_ids:
            return {}
        campaigns = await self.email_account_warm_up_campaign_repo.list_campaigns_by_email_account_ids(
            email_account_ids=set(email_account_ids),
            organization_id=organization_id,
        )
        warmup_status_by_email_account_id: dict[UUID, MailboxWarmUpStatus] = {}
        for c in campaigns:
            warmup_status_by_email_account_id[c.email_account_id] = c.status
        return warmup_status_by_email_account_id

    async def email_account_v2_from_db_model(
        self,
        db_email_account: EmailAccount,
        daily_quota: int,
        organization_id: UUID,
        user_id: UUID,
    ) -> EmailAccountV2:
        is_in_default_pool = False
        default_pool_status = DefaultPoolStatus.INACTIVE
        total_daily_usage = 0
        all_time_usage = 0

        user_auth_context = UserAuthContext(
            user_id=user_id,
            organization_id=organization_id,
        )

        user = await self.user_repo.get_by_id(user_id=user_id)
        if user:
            total_daily_usage = await self.quota_service.get_aggregate_user_usage_in_current_quota_period(
                user_auth_context=user_auth_context,
                entity_id=db_email_account.id,
                entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
                resource=QuotaConsumingResource.EMAIL,
                quota_period=QuotaPeriod.DAILY,
            )
            all_time_usage = (
                await self.quota_service.get_aggregate_user_usage_in_period(
                    organization_id=organization_id,
                    entity_id=db_email_account.id,
                    entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
                    resource=QuotaConsumingResource.EMAIL,
                    period_start=datetime.min.replace(tzinfo=UTC),
                    period_end=zoned_utc_now(),
                )
            )

        default_email_account_pool_membership = await self.email_account_pool_service.get_default_email_account_pool_membership(
            email_account_id=db_email_account.id,
            user_id=db_email_account.owner_user_id,
            organization_id=db_email_account.organization_id,
        )

        is_in_default_pool = default_email_account_pool_membership is not None

        warmup_campaign = await self.email_account_warmup_service.get_warmup_campaign_by_email_account_id(
            email_account_id=db_email_account.id,
            organization_id=organization_id,
        )
        warmup_status = warmup_campaign.status if warmup_campaign else None
        override_warmup = db_email_account.use_override is not None
        warmup_limit = warmup_campaign.email_per_day if warmup_campaign else None
        warmup_started_at = warmup_campaign.last_started_at if warmup_campaign else None
        # get the duration of the warmup period from the warmup speed
        warmup_completion_scheduled_at = (
            warmup_started_at
            + timedelta(
                days=MailboxWarmUpSpeedDays.get_days(warmup_campaign.rampup_speed)
            )
            if (warmup_started_at and warmup_campaign)
            else None
        )

        email_account_health_score = (
            await self.email_account_health_service.get_email_account_health_score(
                email_account_id=db_email_account.id,
                organization_id=organization_id,
            )
        )

        mailbox_status: MailboxStatus = MailboxStatus.PENDING
        if not db_email_account.active:
            mailbox_status = MailboxStatus.ARCHIVED
        elif db_email_account.type == EmailAccountType.CONNECTED:
            mailbox_status = MailboxStatus.NOT_APPLICABLE
        elif warmup_status == MailboxWarmUpStatus.IN_PROGRESS:
            mailbox_status = MailboxStatus.WARMING_UP
        elif (
            warmup_status == MailboxWarmUpStatus.COMPLETED
            or db_email_account.type == EmailAccountType.REGULAR
        ):
            mailbox_status = MailboxStatus.READY

        if is_in_default_pool and (
            mailbox_status == MailboxStatus.READY or override_warmup
        ):
            default_pool_status = DefaultPoolStatus.ACTIVE
        elif is_in_default_pool and mailbox_status == MailboxStatus.WARMING_UP:
            default_pool_status = DefaultPoolStatus.QUEUED
        else:
            default_pool_status = DefaultPoolStatus.INACTIVE

        return EmailAccountV2.from_db_model(
            db_email_account=db_email_account,
            current_day_usage=total_daily_usage,
            all_time_usage=all_time_usage,
            daily_quota=daily_quota,
            status=mailbox_status,
            warmup_limit=warmup_limit,
            email_account_health_score=email_account_health_score,
            is_in_default_pool=is_in_default_pool,
            default_pool_status=default_pool_status,
            warmup_started_at=warmup_started_at,
            warmup_completion_scheduled_at=warmup_completion_scheduled_at,
        )

    async def get_email_account_by_id(
        self,
        email_account_id: UUID,
        organization_id: UUID,
        include_archived: bool = True,
    ) -> EmailAccount | None:
        accounts = await self.email_account_repo.find_accounts_by_ids(
            organization_id=organization_id,
            email_account_ids=[email_account_id],
            include_archived=include_archived,
        )
        return accounts[0] if accounts else None

    async def get_email_accounts_by_owner_user_id(
        self,
        owner_user_id: UUID,
        organization_id: UUID,
        include_archived: bool = True,
    ) -> list[EmailAccount]:
        return await self.email_account_repo.find_accounts_by_owner_user_id(
            organization_id=organization_id,
            owner_user_id=owner_user_id,
            include_archived=include_archived,
        )

    async def get_email_account_v2(
        self,
        email_account_id: UUID,
        organization_id: UUID,
        user_id: UUID,
    ) -> EmailAccountV2:
        email_account = await self.get_email_account_by_id(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )
        if not email_account:
            raise ResourceNotFoundError("Email account not found")

        quota_policies = await self.quota_policy_service.list_quota_policies_by_entity_ids_and_type_resource(
            entity_id_list=[email_account_id],
            entity_type=QuotaConsumerEntityType.EMAIL_ACCOUNT,
            resource=QuotaConsumingResource.EMAIL,
            organization_id=organization_id,
        )
        daily_quota = next(
            (
                policy.quota_limit
                for policy in quota_policies
                if policy.resource == QuotaConsumingResource.EMAIL
                and policy.period == QuotaPeriod.DAILY
            ),
        )

        return await self.email_account_v2_from_db_model(
            db_email_account=email_account,
            daily_quota=daily_quota,
            organization_id=organization_id,
            user_id=user_id,
        )

    async def _create_email_account_health_check_schedule(
        self, client: Client, organization_id: UUID
    ) -> str:
        schedule_id = await self._get_email_account_health_check_schedule_id(
            organization_id
        )
        try:
            handle = client.get_schedule_handle(
                schedule_id
            )  # check to see if duplicate schedule exists
            await handle.describe()
            logger.bind(organization_id=organization_id, schedule_id=schedule_id).info(
                "Email account health check schedule already exists, skipping creation"
            )
            return schedule_id
        except temporalio.service.RPCError as e:
            if e.grpc_status.code == grpc.StatusCode.NOT_FOUND.value[0]:
                logger.bind(
                    organization_id=organization_id, schedule_id=schedule_id
                ).info("Creating new email account health check schedule")
        # Create the schedule but not the initial run because we've already checked while purchasing
        logger.bind(
            organization_id=organization_id,
            schedule_id=schedule_id,
        ).info("Email account health check schedule creation started")
        # Calculate offset to the next hour by including all remaining minutes, seconds, and microseconds
        offset = timedelta(hours=zoned_utc_now().hour)

        await client.create_schedule(
            id=schedule_id,
            schedule=Schedule(
                action=ScheduleActionStartWorkflow(
                    workflow=EmailHealthCheckWorkflow.run,
                    args=[
                        EmailHealthCheckWorkflowInput(
                            organization_id=organization_id,
                        )
                    ],
                    id=schedule_id,
                    task_queue=TemporalTaskQueue.EMAIL_TASK_QUEUE,
                    execution_timeout=timedelta(hours=1),
                    retry_policy=RetryPolicy(
                        initial_interval=timedelta(minutes=2),
                        backoff_coefficient=2,
                        maximum_interval=timedelta(minutes=20),
                        maximum_attempts=3,
                        non_retryable_error_types=[
                            "ValueError",
                            "ResourceNotFoundError",
                        ],
                    ),
                ),
                spec=ScheduleSpec(
                    intervals=[
                        ScheduleIntervalSpec(
                            every=timedelta(days=1),
                            offset=offset,
                        )
                    ],
                    jitter=timedelta(seconds=60),
                ),
            ),
            # We don't want to trigger the initial run, just create the schedule and run a day from now
            trigger_immediately=False,
        )
        return schedule_id

    async def increment_plan_included_mailbox_quota_usage(
        self, user_auth_context: UserAuthContext, is_mock_record: bool
    ) -> None:
        if is_mock_record:
            return
        try:
            included_mailbox_usage = await self.quota_service.increase_usage(
                organization_id=user_auth_context.organization_id,
                entity_id=user_auth_context.organization_id,
                entity_type=QuotaConsumerEntityType.ORGANIZATION,
                resource=QuotaConsumingResource.PLAN_INCLUDED_COST_MAILBOX,
                usage=1,
                timestamp=zoned_utc_now(),
            )
            logger.bind(
                included_mailbox_usage=included_mailbox_usage,
            ).debug(
                f"Included mailbox usage for the organization id ({user_auth_context.organization_id})."
            )
        except Exception as e:
            logger.bind(
                user_auth_context=user_auth_context,
            ).error("Failed to increase plan-included-cost-mailbox usage.", exc_info=e)

    async def _send_mailbox_purchase_invoice_slack_message(
        self,
        infraforge_response: ExternalBuyMailboxesResponse,
        mailbox_name: str,
        organization_id: UUID,
    ) -> None:
        # I've created our own invoice total because we don't have them in the response from Infraforge for every purchase
        # We only get billed when we buy more mailbox slots, so instead we'll create an invoice
        # Going off the highest value we can pay per month for a mailbox, which is $4.00 per mailbox
        invoice = MailboxPurchaseInvoice(
            amount_paid=400,
            credits_applied=0,
            id=mailbox_name + "_invoice",
            paid_at=str(zoned_utc_now()),
            sub_total=400,
            total=400,
        )
        logger.bind(
            organization_id=organization_id,
            mailbox_name=mailbox_name,
            invoice_id=invoice.id,
            invoice_total=invoice.total,
        ).info("Sending mailbox invoice to customer.")

        try:
            organization = await self.organization_service_v2.get_organization_by_id(
                organization_id
            )
            await self.slack_client.send_message(
                env=settings.environment,
                channel=SlackChannels.DOMAIN_PURCHASE_INVOICE,
                text=f"Mailbox Purchase Invoice: {mailbox_name} for {organization.display_name}. \n Total: {format_cents_to_usd(invoice.total)} \n Invoice ID: {invoice.id}",
            )
        except Exception as e:
            logger.bind(
                invoice_total=invoice.total,
                invoice_id=invoice.id,
                mailbox_name=mailbox_name,
                organization_id=organization_id,
            ).error(
                "[send_mailbox_purchase_invoice_slack_message] Error sending message to Slack.",
                exc_info=e,
            )

    async def get_outbound_domain_from_email_account_id(
        self,
        email_account_id: UUID,
        organization_id: UUID,
    ) -> OutboundDomain | None:
        email_account = await self.get_email_account_by_id(
            email_account_id,
            organization_id=organization_id,
        )
        if not email_account or not email_account.outbound_domain_id:
            return None
        return await self.outbound_domain_repo.get_domain_by_id_and_org_id(
            email_account.outbound_domain_id, email_account.organization_id
        )

    async def _cancel_existing_activate_email_account_workflow(
        self, temporal_client: Client, warmup_configs: dict[UUID, WarmupConfig]
    ) -> None:
        """
        Cancels existing activate email account workflows for the given email accounts if running.
        """
        for email_account_id, warmup_config in warmup_configs.items():
            if warmup_config.warmup_status == MailboxWarmUpStatus.PAUSED:
                # this only works if we don't support bulk unarchive, we which is not the case yet
                wf_id = f"{EMAIL_ACCOUNT_LIFECYCLE_ACTIVATE_WORKFLOW_ID_PREFIX}_{email_account_id}"
                handle = temporal_client.get_workflow_handle(wf_id)
                with suppress(RPCError):
                    await handle.signal(SIGNAL_CANCEL)

    async def _get_email_account_health_check_schedule_id(
        self, organization_id: UUID
    ) -> str:
        return f"email_account_health_check_{organization_id}"

    async def create_entity(
        self, organization_id: UUID, user_id: UUID, request: CreateEmailAccountRequest
    ) -> EmailAccountV2:
        return await self.create_and_warmup_email_account(
            create_email_account_request=request,
            user_auth_context=UserAuthContext(
                user_id=user_id,
                organization_id=organization_id,
            ),
        )

    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> EmailAccountV2 | None:
        return await self.get_email_account_v2(
            email_account_id=entity_id,
            organization_id=organization_id,
            user_id=user_id,
        )

    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: EmailAccountV2,
        request: BasePatchRequest,
    ) -> EmailAccountV2:
        patch_email_account_request = cast(PatchEmailAccountRequestV2, request)
        return await self.update_email_account(
            email_account_id=entity.id,
            patch_email_account_request=patch_email_account_request,
            user_auth_context=UserAuthContext(
                user_id=user_id,
                organization_id=organization_id,
            ),
        )

    async def remove_entity(
        self, organization_id: UUID, user_id: UUID, entity_id: UUID
    ) -> DeleteEntityResponse:
        raise NotImplementedError("Email account deletion is not supported")

    async def authed_archive_email_accounts(
        self,
        email_accounts_to_archive: dict[UUID, EmailAccountArchiveSequenceHandling],
        user_auth_context: UserAuthContext,
        archive_domain: bool = False,
    ) -> None:
        if (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        ):
            return await self.archive_email_accounts(
                email_accounts_to_archive=email_accounts_to_archive,
                user_auth_context=user_auth_context,
                archive_domain=archive_domain,
            )

        for email_account_id in email_accounts_to_archive:
            await self.error_if_no_entity_access(
                user_auth_context=user_auth_context,
                entity_id=email_account_id,
                access_check_function=self.can_access_entity_for_patch,
            )
        return await self.archive_email_accounts(
            email_accounts_to_archive=email_accounts_to_archive,
            user_auth_context=user_auth_context,
            archive_domain=archive_domain,
        )

    async def authed_unarchive_email_accounts(
        self,
        email_account_ids: list[UUID],
        user_auth_context: UserAuthContext,
    ) -> None:
        if (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        ):
            return await self.unarchive_email_accounts(
                email_account_ids=email_account_ids,
                user_auth_context=user_auth_context,
            )

        for email_account_id in email_account_ids:
            await self.error_if_no_entity_access(
                user_auth_context=user_auth_context,
                entity_id=email_account_id,
                access_check_function=self.can_access_entity_for_patch,
            )
        return await self.unarchive_email_accounts(
            email_account_ids=email_account_ids,
            user_auth_context=user_auth_context,
        )

    # No participant list for email accounts
    @override
    async def get_allowed_users_from_entity(
        self, domain_entity: EmailAccountV2
    ) -> AllowedUsers:
        """
        Returns AllowedUsers for an EmailAccountV2 object.
        """
        model_as_dict = domain_entity.model_dump(include={"owner_user_id"})
        owner_user_id = UUID(str(model_as_dict.get("owner_user_id")))
        return AllowedUsers(owner_user_id=owner_user_id, participant_user_ids=[])

    # temporary flag for rollout
    @override
    async def can_access_entity_for_patch(
        self,
        user_auth_context: UserAuthContext,
        entity: EmailAccountV2,
        patch_request: BasePatchRequest | None = None,
    ) -> bool:
        if (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        ):
            return True
        return await super().can_access_entity_for_patch(
            user_auth_context=user_auth_context,
            entity=entity,
            patch_request=patch_request,
        )

    @override
    async def can_access_entity_for_delete(
        self, user_auth_context: UserAuthContext, entity: EmailAccountV2
    ) -> bool:
        if (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        ):
            return True
        return await super().can_access_entity_for_delete(
            user_auth_context=user_auth_context, entity=entity
        )

    # only admin and owner can read email accounts
    @override
    async def can_access_entity_for_read(
        self, user_auth_context: UserAuthContext, entity: EmailAccountV2
    ) -> bool:
        if (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        ):
            return True
        return await super().can_access_entity_for_read_participants_or_owner(
            user_auth_context=user_auth_context, entity=entity
        )

    @override
    async def can_create_entity(self, user_auth_context: UserAuthContext) -> bool:
        if (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        ):
            return True
        return await super().can_create_entity(user_auth_context=user_auth_context)


class SingletonEmailAccountServiceV2(Singleton, EmailAccountServiceV2):
    pass


def get_email_account_service_v2_by_db_engine(
    db_engine: DatabaseEngine,
) -> EmailAccountServiceV2:
    if SingletonEmailAccountServiceV2.has_instance():
        return SingletonEmailAccountServiceV2.get_singleton_instance()
    return SingletonEmailAccountServiceV2(
        engine=db_engine,
    )
