from typing import Annotated, Any
from uuid import UUID

from fastapi import Depends

from salestech_be.common.error_code import ErrorCode
from salestech_be.common.exception import (
    ConflictResourceError,
)
from salestech_be.common.exception.exception import (
    ConflictErrorDetails,
    ResourceNotFoundError,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.singleton import Singleton
from salestech_be.core.email.account.email_account_ext import (
    EmailAccountServiceExt,
    get_email_account_service_ext,
    get_email_account_service_ext_by_db_engine,
)
from salestech_be.core.email.account.types import CreateWarmUpRequest, WarmupConfig
from salestech_be.core.email.warmup.schema import (
    CampaignResponse,
)
from salestech_be.db.dao.email_account_warm_up_campaign_repository import (
    EmailAccountWarmUpCampaignRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.email_account import EmailAccount
from salestech_be.db.models.email_account_warm_up import (
    EmailAccountWarmUpCampaign,
    MailboxWarmUpService,
    MailboxWarmUpStatus,
)
from salestech_be.integrations.mailivery.async_mailivery_client import (
    AsyncMailiveryClient,
)
from salestech_be.integrations.mailivery.type import (
    Campaign,
    CreateCampaignRequest,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger()


class MailiveryWarmupService:
    def __init__(
        self,
        email_account_warm_up_campaign_repository: Annotated[
            EmailAccountWarmUpCampaignRepository, Depends()
        ],
        email_account_service_ext: Annotated[
            EmailAccountServiceExt, Depends(get_email_account_service_ext)
        ],
    ) -> None:
        self.email_account_warm_up_campaign_repository = (
            email_account_warm_up_campaign_repository
        )
        self.email_account_service_ext = email_account_service_ext
        self.mailivery_client = AsyncMailiveryClient()

    async def create_warm_up_job(
        self,
        organization_id: UUID,
        user_id: UUID,
        request: CreateWarmUpRequest,
    ) -> CampaignResponse:
        existing_campaign = await self.email_account_warm_up_campaign_repository.find_campaign_by_email_account_id(
            request.email_account_id
        )
        if existing_campaign:
            logger.error(f"Campaign already exist={existing_campaign[0].id}")
            raise ConflictResourceError(
                additional_error_details=ConflictErrorDetails(
                    code=ErrorCode.CAMPAIGN_ALREADY_EXISTS,
                    details="Campaign already exist, can only create one campaign per mailbox.",
                    reference_id=str(existing_campaign[0].id),
                    conflicted_existing_object=StdObjectIdentifiers.campaign.identifier,
                )
            )
        decrypted_email_account = (
            await self.email_account_service_ext.get_decrypted_email_account_by_id(
                email_account_id=request.email_account_id,
            )
        )
        if not decrypted_email_account:
            raise ResourceNotFoundError(
                f"Unable to find email account {request.email_account_id}"
            )

        mailivery_campaign = await self._create_campaign_on_mailivery(
            create_warmup_request=request,
            db_email_account=decrypted_email_account,
        )
        external_id = mailivery_campaign.id
        outbound_domain_id = decrypted_email_account.outbound_domain_id
        if not outbound_domain_id:
            raise ResourceNotFoundError(
                f"Unable to find outbound domain for email account {request.email_account_id}"
            )

        db_campaign = (
            await self.email_account_warm_up_campaign_repository.create_campaign(
                domain_id=outbound_domain_id,
                organization_id=organization_id,
                user_id=user_id,
                email_per_day=request.email_per_day,
                response_rate=request.response_rate,
                email_account_id=request.email_account_id,
                external_id=external_id,
                with_rampup=request.with_rampup,
                rampup_speed=request.rampup_speed,
                status=MailboxWarmUpStatus.IN_PROGRESS,
            )
        )
        return CampaignResponse.from_db_model(db_campaign)

    async def _create_campaign_on_mailivery(
        self,
        create_warmup_request: CreateWarmUpRequest,
        db_email_account: EmailAccount,
    ) -> Campaign:
        create_campaign_request = self.create_campaign_request_from_warmup_request_and_decrypted_email_account(
            create_warmup_request, db_email_account
        )
        return await self.mailivery_client.create_campaign(create_campaign_request)

    async def update_campaign(  # noqa: C901
        self,
        user_id: UUID,
        organization_id: UUID,
        # email_account_id -> WarmupConfig, this allows us to update multiple email accounts at once
        warmup_configs: dict[UUID, WarmupConfig],
    ) -> None:
        # early return if no update
        if not warmup_configs:
            logger.bind(warmup_configs=warmup_configs).warning(
                "[update_campaign] skip warmup campaign update"
            )
            return
        for email_account_id, warmup_config in warmup_configs.items():
            warmup_limit = warmup_config.warmup_limit
            warmup_status = warmup_config.warmup_status

            if not (
                warmup_campaign
                := await self.email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service(
                    email_account_id=email_account_id,
                    organization_id=organization_id,
                    warm_up_service=MailboxWarmUpService.MAILIVERY,
                )
            ):
                logger.bind(email_account_id=email_account_id).warning(
                    "Warmup campaign for email account not exist."
                )
                continue

            if warmup_limit and warmup_limit != warmup_campaign.email_per_day:
                if not warmup_campaign.is_mock_record:
                    await self.mailivery_client.update_email_per_day(
                        campaign_id=not_none(warmup_campaign.external_id),
                        email_per_day=warmup_limit,
                    )
                await self.email_account_warm_up_campaign_repository.update_campaign(
                    user_id=user_id,
                    campaign_id=warmup_campaign.id,
                    column_to_update={"email_per_day": warmup_limit},
                )

            if warmup_status and warmup_status != warmup_campaign.status:
                # Only make external API calls for non-mock records
                if not warmup_campaign.is_mock_record:
                    match warmup_status:
                        case MailboxWarmUpStatus.PAUSED:
                            await self.mailivery_client.pause_warmup(
                                campaign_id=not_none(warmup_campaign.external_id),
                            )

                        case MailboxWarmUpStatus.IN_PROGRESS:
                            await self.mailivery_client.start_warmup(
                                campaign_id=not_none(warmup_campaign.external_id),
                            )

                # Update database for both mock and real records
                column_to_update: dict[str, Any] = {"status": warmup_status}  # type: ignore[explicit-any] # TODO: fix-any-annotation
                if warmup_status == MailboxWarmUpStatus.IN_PROGRESS:
                    column_to_update["last_started_at"] = zoned_utc_now()
                await self.email_account_warm_up_campaign_repository.update_campaign(
                    user_id=user_id,
                    campaign_id=warmup_campaign.id,
                    column_to_update=column_to_update,
                )

    async def get_warmup_campaign_by_email_account_id(
        self, email_account_id: UUID, organization_id: UUID
    ) -> EmailAccountWarmUpCampaign | None:
        return await self.email_account_warm_up_campaign_repository.find_campaign_by_email_account_id_organization_id_and_warm_up_service(
            email_account_id=email_account_id,
            organization_id=organization_id,
            warm_up_service=MailboxWarmUpService.MAILIVERY,
        )

    def create_campaign_request_from_warmup_request_and_decrypted_email_account(
        self, create_warmup_request: CreateWarmUpRequest, db_email_account: EmailAccount
    ) -> CreateCampaignRequest:
        return CreateCampaignRequest(
            email=db_email_account.email,
            email_per_day=create_warmup_request.email_per_day,
            response_rate=create_warmup_request.response_rate,
            with_rampup=create_warmup_request.with_rampup,
            rampup_speed=create_warmup_request.rampup_speed,
            first_name=db_email_account.first_name,
            last_name=db_email_account.last_name,
            imap_email=db_email_account.email,
            imap_host=db_email_account.imap_host,
            imap_port=db_email_account.imap_port,
            imap_username=db_email_account.imap_username,
            imap_password=db_email_account.imap_password,
            smtp_host=db_email_account.smtp_host,
            smtp_port=db_email_account.smtp_port,
            smtp_username=db_email_account.smtp_username,
            smtp_password=db_email_account.smtp_password,
        )

    async def update_warmup_campaign_status_by_email_account_ids(
        self,
        email_account_ids: list[UUID],
        organization_id: UUID,
        status: MailboxWarmUpStatus,
    ) -> list[EmailAccountWarmUpCampaign]:
        return await self.email_account_warm_up_campaign_repository.update_campaign_status_by_email_account_ids(
            email_account_ids=email_account_ids,
            organization_id=organization_id,
            status=status,
        )


class SingletonMailiveryWarmupService(Singleton, MailiveryWarmupService):
    pass


def get_mailivery_warmup_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> MailiveryWarmupService:
    return SingletonMailiveryWarmupService(
        email_account_warm_up_campaign_repository=EmailAccountWarmUpCampaignRepository(
            engine=db_engine
        ),
        email_account_service_ext=get_email_account_service_ext_by_db_engine(db_engine),
    )
