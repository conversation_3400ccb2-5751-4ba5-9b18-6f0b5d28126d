from typing import override
from uuid import UUID

from fastapi import Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.common.domain_service import AllowedUsers, DomainQueryService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.user.signature.types_v2 import SignatureV2
from salestech_be.db.dao.signature_repository import SignatureRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.settings import settings


class SignatureQueryService(DomainQueryService[SignatureV2]):
    def __init__(
        self,
        signature_repository: SignatureRepository,
    ):
        self.signature_repository = signature_repository

    async def list_signatures(
        self,
        user_auth_context: UserAuthContext,
        only_include_signature_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> list[SignatureV2]:
        if specified(only_include_signature_ids):
            db_signatures = await self.signature_repository.find_signatures_by_ids(
                organization_id=user_auth_context.organization_id,
                signature_ids=list(only_include_signature_ids),
            )
        else:
            db_signatures = (
                await self.signature_repository.find_signatures_by_organization_id(
                    organization_id=user_auth_context.organization_id,
                )
            )
        return [
            SignatureV2.from_db_model(db_signature) for db_signature in db_signatures
        ]

    # only admin and owner can view signature
    @override
    async def get_allowed_users_from_entity(
        self, domain_object: SignatureV2
    ) -> AllowedUsers:
        return AllowedUsers(
            owner_user_id=domain_object.owner_user_id,
            participant_user_ids=[],
        )

    @override
    async def is_entity_viewable_by_user(
        self, user_auth_context: UserAuthContext, domain_object: SignatureV2
    ) -> bool:
        if (
            user_auth_context.is_admin
            or user_auth_context.is_super_admin
            or (
                not settings.enable_mailbox_perms
                and str(user_auth_context.organization_id)
                not in settings.enable_mailbox_perms_org_ids
            )
        ):
            return True

        allowed_users = await self.get_allowed_users_from_entity(domain_object)
        return allowed_users.is_owner_or_participant(user_id=user_auth_context.user_id)


class SingletonSignatureQueryService(Singleton, SignatureQueryService):
    pass


def get_signature_query_service_by_db_engine(
    db_engine: DatabaseEngine,
) -> SignatureQueryService:
    return SingletonSignatureQueryService(
        signature_repository=SignatureRepository(engine=db_engine),
    )


def get_signature_query_service(
    request: Request,
) -> SignatureQueryService:
    return get_signature_query_service_by_db_engine(db_engine=get_db_engine(request))
