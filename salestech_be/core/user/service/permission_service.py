import itertools
import uuid
from typing import Annotated
from uuid import UUID

from fastapi import Depends, Request

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.user.types import (
    PermissionResource,
    PermissionSetDTO,
    PermissionSetGroupDTO,
    PermissionSpecialOperations,
    PermissionVerb,
    UserPermissionSetGroupMembershipDTO,
)
from salestech_be.db.dao.job_role_user_association_repository import (
    JobRoleUserAssociationRepository,
)
from salestech_be.db.dao.permission_set_group_permission_set_association_repository import (
    PermissionSetGroupPermissionSetAssociationRepository,
)
from salestech_be.db.dao.permission_set_group_repository import (
    PermissionSetGroupRepository,
)
from salestech_be.db.dao.permission_set_group_user_association_repository import (
    PermissionSetGroupUserAssociationRepository,
)
from salestech_be.db.dao.permission_set_repository import PermissionSetRepository
from salestech_be.db.dao.permission_set_user_association_repository import (
    PermissionSetUserAssociationRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.permission_set import PermissionSet, PermissionSetStatus
from salestech_be.db.models.permission_set_group import (
    PermissionSetGroup,
    PermissionSetGroupStatus,
)
from salestech_be.db.models.permission_set_group_permission_set_association import (
    PermissionSetGroupPermissionSetAssociation,
    PermissionSetGroupPermissionSetAssociationStatus,
)
from salestech_be.db.models.permission_set_group_user_association import (
    PermissionSetGroupUserAssociation,
    PermissionSetGroupUserAssociationStatus,
)
from salestech_be.db.models.permission_set_user_association import (
    PermissionSetUserAssociation,
    PermissionSetUserAssociationStatus,
)
from salestech_be.db.models.user import UserRole
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger(__name__)


class PermissionService:
    DEFAULT_SUPER_USER_PERMISSION_SET_NAME = "Default Super User Permission Set"
    DEFAULT_ADMIN_PERMISSION_SET_NAME = "Default Admin Permission Set"
    DEFAULT_USER_PERMISSION_SET_NAME = "Default User Permission Set"

    DEFAULT_ADMIN_PERMISSION_SET_GROUP_NAME = "Default Admin Permission Set Group"
    DEFAULT_USER_PERMISSION_SET_GROUP_NAME = "Default User Permission Set Group"

    def __init__(
        self,
        permission_set_repository: Annotated[PermissionSetRepository, Depends()],
        permission_set_group_repository: Annotated[
            PermissionSetGroupRepository, Depends()
        ],
        permission_set_user_association_repository: Annotated[
            PermissionSetUserAssociationRepository, Depends()
        ],
        permission_set_group_user_association_repository: Annotated[
            PermissionSetGroupUserAssociationRepository, Depends()
        ],
        permission_set_group_permission_set_association_repository: Annotated[
            PermissionSetGroupPermissionSetAssociationRepository, Depends()
        ],
        job_role_user_association_repository: Annotated[
            JobRoleUserAssociationRepository, Depends()
        ],
    ):
        # TODO: (hao) too many repos? maybe just use one generic perm repo, use table
        # model at query time
        super().__init__()
        self.permission_set_repository = permission_set_repository
        self.permission_set_group_repository = permission_set_group_repository
        self.permission_set_user_association_repository = (
            permission_set_user_association_repository
        )
        self.permission_set_group_user_association_repository = (
            permission_set_group_user_association_repository
        )
        self.permission_set_group_permission_set_association_repository = (
            permission_set_group_permission_set_association_repository
        )
        self.job_role_user_association_repository = job_role_user_association_repository

    async def get_or_create_default_permission_set_groups_for_org(
        self, created_by_user_id: UUID, organization_id: UUID
    ) -> dict[UserRole, PermissionSetGroupDTO]:
        """
        This method is used to create default permission sets groups for an
        organization's ROLEs. The calling method is responsible for assigning
        the appropriate permission set group(s) to the user based on their role.

        Returns a dictionary of user roles to their associated permission set groups.
        """

        # Create the default super user permission set if it doesn't exist.
        # Note: this is never assigned to a user. It is reserved for Reevo internal.
        default_super_user_perm_set = await self.get_permission_set_by_name(
            PermissionService.DEFAULT_SUPER_USER_PERMISSION_SET_NAME,
            organization_id=organization_id,
        )
        if not default_super_user_perm_set:
            # there is a unique constraint on permission_set(organization_id, name) so we do not need to worry about duplicates.
            logger.info(
                f"Creating default super user permission set for organization {organization_id}"
            )
            default_super_user_perm_set = (
                await self.create_default_super_user_permission_set(
                    organization_id=organization_id,
                    created_by_user_id=created_by_user_id,
                )
            )

        # Create the default admin permission set if it doesn't exist.
        default_admin_perm_set = await self.get_permission_set_by_name(
            PermissionService.DEFAULT_ADMIN_PERMISSION_SET_NAME,
            organization_id=organization_id,
        )
        if not default_admin_perm_set:
            # there is a unique constraint on permission_set(organization_id, name) so we do not need to worry about duplicates.
            logger.info(
                f"Creating default admin permission set for organization {organization_id}"
            )
            default_admin_perm_set = await self.create_default_admin_permission_set(
                organization_id=organization_id, created_by_user_id=created_by_user_id
            )

        # Create the default admin permission set group if it doesn't exist.
        default_admin_perm_group = await self.get_permission_set_group_by_name(
            name=PermissionService.DEFAULT_ADMIN_PERMISSION_SET_GROUP_NAME,
            organization_id=organization_id,
        )
        if not default_admin_perm_group:
            # there is a unique constraint on permission_set_group(organization_id, name) so we do not need to worry about duplicates.
            logger.info(
                f"Creating default admin permission set group for organization {organization_id}"
            )
            default_admin_perm_group = await self.create_permission_set_group(
                name=PermissionService.DEFAULT_ADMIN_PERMISSION_SET_GROUP_NAME,
                description=PermissionService.DEFAULT_ADMIN_PERMISSION_SET_GROUP_NAME,
                organization_id=organization_id,
                created_by_user_id=created_by_user_id,
            )
            # assign default admin permission set to default admin permission set group.
            # there is a unique constraint on permission_set_group_user_association(organization_id, user_id, permission_set_group_id) so we do not need to worry about duplicates.
            logger.info(
                f"Assigning default admin permission set {default_admin_perm_set.id} to default admin permission set group {default_admin_perm_group.id}"
            )
            await self.assign_permission_set_group(
                permission_set_group_id=default_admin_perm_group.id,
                permission_set_ids=[default_admin_perm_set.id],
                organization_id=organization_id,
                created_by_user_id=created_by_user_id,
            )

        # Create the default user permission set if it doesn't exist.
        default_user_perm_set = await self.get_permission_set_by_name(
            PermissionService.DEFAULT_USER_PERMISSION_SET_NAME,
            organization_id=organization_id,
        )
        if default_user_perm_set:
            logger.bind(
                organization_id=organization_id,
                permission_set_id=default_user_perm_set.id,
            ).info("Updating default user permission set for organization")
            default_user_perm_set = await self.update_default_user_permission_set(
                organization_id=organization_id,
                default_user_perm_set=not_none(default_user_perm_set),
            )
        else:
            # there is a unique constraint on permission_set(organization_id, name) so we do not need to worry about duplicates.
            logger.bind(
                organization_id=organization_id,
                created_by_user_id=created_by_user_id,
            ).info("Creating default user permission set for organization")
            default_user_perm_set = await self.create_default_user_permission_set(
                organization_id=organization_id, created_by_user_id=created_by_user_id
            )

        # Create the default permission set group for (non-admin) users if it doesn't exist.
        default_user_perm_group = await self.get_permission_set_group_by_name(
            name=PermissionService.DEFAULT_USER_PERMISSION_SET_GROUP_NAME,
            organization_id=organization_id,
        )
        if not default_user_perm_group:
            logger.bind(
                organization_id=organization_id,
            ).info("Creating default user permission set group for organization")
            default_user_perm_group = await self.create_permission_set_group(
                name=PermissionService.DEFAULT_USER_PERMISSION_SET_GROUP_NAME,
                description=PermissionService.DEFAULT_USER_PERMISSION_SET_GROUP_NAME,
                organization_id=organization_id,
                created_by_user_id=created_by_user_id,
            )

            # assign default user permission set to default user permission set group.
            # there is a unique constraint on permission_set_group_user_association(organization_id, user_id, permission_set_group_id) so we do not need to worry about duplicates.
            logger.bind(
                organization_id=organization_id,
                permission_set_group_id=default_user_perm_group.id,
                permission_set_id=default_user_perm_set.id,
            ).info(
                "Assigning default user permission set to default user permission set group"
            )
            await self.assign_permission_set_group(
                permission_set_group_id=default_user_perm_group.id,
                permission_set_ids=[default_user_perm_set.id],
                organization_id=organization_id,
                created_by_user_id=created_by_user_id,
            )

        # Note: never return UserRole.SUPER_USER as it is reserved for Reevo internal.
        return {
            UserRole.ADMIN: default_admin_perm_group,
            UserRole.USER: default_user_perm_group,
        }

    async def get_permission_set_by_id(
        self, permission_set_id: UUID, organization_id: UUID
    ) -> PermissionSetDTO | None:
        permission_set_db_object = (
            await self.permission_set_repository.find_by_tenanted_primary_key(
                table_model=PermissionSet,
                organization_id=organization_id,
                id=permission_set_id,
            )
        )
        return (
            PermissionSetDTO.map_from_db_obj(permission_set_db_object)
            if permission_set_db_object
            else None
        )

    async def get_permission_set_by_name(
        self, name: str, organization_id: UUID
    ) -> PermissionSetDTO | None:
        permission_set_db_object = await self.permission_set_repository.find_by_name(
            name=name, organization_id=organization_id
        )
        return (
            PermissionSetDTO.map_from_db_obj(permission_set_db_object)
            if permission_set_db_object
            else None
        )

    async def get_permission_set_group_by_id(
        self, permission_set_group_id: UUID, organization_id: UUID
    ) -> PermissionSetGroupDTO | None:
        permission_set_group_db_object = (
            await self.permission_set_group_repository.find_by_tenanted_primary_key(
                table_model=PermissionSetGroup,
                organization_id=organization_id,
                id=permission_set_group_id,
            )
        )

        permission_set_db_objects = []
        if permission_set_group_db_object:
            # find its attached permission sets
            permission_set_db_objects = await self.permission_set_group_repository.find_all_permission_sets_for_group_id(
                organization_id=organization_id,
                permission_set_group_id=permission_set_group_id,
            )

        return (
            PermissionSetGroupDTO.map_from_db_obj(
                permission_set_group_db_object, permission_set_db_objects
            )
            if permission_set_group_db_object
            else None
        )

    async def get_permission_set_group_by_name(
        self, name: str, organization_id: UUID
    ) -> PermissionSetGroupDTO | None:
        permission_set_group_db_object = (
            await self.permission_set_group_repository.find_by_name(
                name=name, organization_id=organization_id
            )
        )
        if permission_set_group_db_object:
            return await self.get_permission_set_group_by_id(
                permission_set_group_id=permission_set_group_db_object.id,
                organization_id=organization_id,
            )
        return None

    async def create_default_super_user_permission_set(
        self, organization_id: UUID, created_by_user_id: UUID
    ) -> PermissionSetDTO:
        permission_set_db_object = await self.permission_set_repository.insert(
            PermissionSet(
                id=uuid.uuid4(),
                name=PermissionService.DEFAULT_SUPER_USER_PERMISSION_SET_NAME,
                description=PermissionService.DEFAULT_SUPER_USER_PERMISSION_SET_NAME,
                allowed_operations=[
                    PermissionSpecialOperations.SUPER_ADMIN_ALL.value,
                ],
                organization_id=organization_id,
                status=PermissionSetStatus.ACTIVE,
                created_at=zoned_utc_now(),
                created_by_user_id=created_by_user_id,
            )
        )
        return PermissionSetDTO.map_from_db_obj(permission_set_db_object)

    async def create_default_admin_permission_set(
        self, organization_id: UUID, created_by_user_id: UUID
    ) -> PermissionSetDTO:
        permission_set_db_object = await self.permission_set_repository.insert(
            PermissionSet(
                id=uuid.uuid4(),
                name=PermissionService.DEFAULT_ADMIN_PERMISSION_SET_NAME,
                description=PermissionService.DEFAULT_ADMIN_PERMISSION_SET_NAME,
                allowed_operations=[PermissionSpecialOperations.ADMIN_ALL.value],
                organization_id=organization_id,
                status=PermissionSetStatus.ACTIVE,
                created_at=zoned_utc_now(),
                created_by_user_id=created_by_user_id,
            )
        )
        return PermissionSetDTO.map_from_db_obj(permission_set_db_object)

    def get_latest_default_user_allowed_operations(self) -> set[str]:
        # --- Generic permission permutations starts here ---

        # Exclude EMAIL_ACCOUNT for now since create:email_account is reserved for ADMIN only. Add support for EMAIL_ACCOUNT after the generic block.
        permission_resources = {resource.value for resource in PermissionResource} - {
            PermissionResource.EMAIL_ACCOUNT.value
        }

        permission_verbs = {
            verb.value
            for verb in {
                PermissionVerb.CREATE,
                PermissionVerb.READ,
                PermissionVerb.UPDATE,
                PermissionVerb.DELETE,
            }
        }

        # e.g. create:account, read:account, etc...
        generic_allowed_operations = {
            f"{verb}:{resource}"
            for verb, resource in itertools.product(
                permission_verbs, permission_resources
            )
        }

        # --- Specific permission permutations starts here ---

        # create:email_account is reserved for ADMIN only, hence only "RUD" verbs are allowed for USERs.
        email_account_allowed_operations = {
            f"{verb.value}:{PermissionResource.EMAIL_ACCOUNT.value}"
            for verb in {
                # Do NOT add the CREATE verb here, since it is reserved for ADMIN only.
                PermissionVerb.READ,
                PermissionVerb.UPDATE,
                PermissionVerb.DELETE,
            }
        }

        # --- Combine the generic-, and specific- allowed operations ---
        return generic_allowed_operations | email_account_allowed_operations

    async def create_default_user_permission_set(
        self, organization_id: UUID, created_by_user_id: UUID
    ) -> PermissionSetDTO:
        """
        UserService.backfill_permissions() should update the default PSGs. Ensure you have test coverage against modifications here.
        """
        allowed_operations = list(self.get_latest_default_user_allowed_operations())

        permission_set_db_object = await self.permission_set_repository.insert(
            PermissionSet(
                id=uuid.uuid4(),
                name=PermissionService.DEFAULT_USER_PERMISSION_SET_NAME,
                description=PermissionService.DEFAULT_USER_PERMISSION_SET_NAME,
                allowed_operations=allowed_operations,
                organization_id=organization_id,
                status=PermissionSetStatus.ACTIVE,
                created_at=zoned_utc_now(),
                created_by_user_id=created_by_user_id,
            )
        )
        return PermissionSetDTO.map_from_db_obj(permission_set_db_object)

    async def create_permission_set(
        self,
        name: str,
        description: str | None,
        allowed_operations: list[str],
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> PermissionSetDTO:
        permission_set_db_object = await self.permission_set_repository.insert(
            PermissionSet(
                id=uuid.uuid4(),
                name=name,
                description=description,
                allowed_operations=allowed_operations,
                organization_id=organization_id,
                status=PermissionSetStatus.ACTIVE,
                created_at=zoned_utc_now(),
                created_by_user_id=created_by_user_id,
            )
        )
        return PermissionSetDTO.map_from_db_obj(permission_set_db_object)

    async def create_permission_set_group(
        self,
        name: str,
        description: str | None,
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> PermissionSetGroupDTO:
        permission_set_group_db_object = (
            await self.permission_set_group_repository.insert(
                PermissionSetGroup(
                    id=uuid.uuid4(),
                    name=name,
                    description=description,
                    organization_id=organization_id,
                    status=PermissionSetGroupStatus.ACTIVE,
                    created_at=zoned_utc_now(),
                    created_by_user_id=created_by_user_id,
                )
            )
        )
        # initial creation of the permission set group
        # there would be no permission sets in the group yet
        return PermissionSetGroupDTO.map_from_db_obj(
            permission_set_group_db_object, permission_set_db_objs=[]
        )

    async def update_default_user_permission_set(
        self, organization_id: UUID, default_user_perm_set: PermissionSetDTO
    ) -> PermissionSetDTO:
        current_allowed_operations = set(
            not_none(default_user_perm_set.allowed_operations)
        )
        latest_allowed_operations = self.get_latest_default_user_allowed_operations()

        if current_allowed_operations == latest_allowed_operations:
            return default_user_perm_set

        # update the permission set group
        return PermissionSetDTO.map_from_db_obj(
            not_none(
                await self.permission_set_group_repository.update_by_tenanted_primary_key(
                    table_model=PermissionSet,
                    primary_key_to_value={"id": default_user_perm_set.id},
                    organization_id=organization_id,
                    exclude_deleted_or_archived=False,
                    column_to_update={
                        "allowed_operations": list(latest_allowed_operations),
                    },
                )
            )
        )

    async def assign_permission_set_group(
        self,
        permission_set_group_id: UUID,
        permission_set_ids: list[UUID],
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> PermissionSetGroupDTO:
        # TODO: (hao) validations...

        # create a link for each permission set for the group
        for permission_set_id in permission_set_ids:
            await (
                self.permission_set_group_permission_set_association_repository.insert(
                    PermissionSetGroupPermissionSetAssociation(
                        id=uuid.uuid4(),
                        permission_set_group_id=permission_set_group_id,
                        permission_set_id=permission_set_id,
                        status=PermissionSetGroupPermissionSetAssociationStatus.ACTIVE,
                        organization_id=organization_id,
                        created_at=zoned_utc_now(),
                        created_by_user_id=created_by_user_id,
                    )
                )
            )

        # return the DTO object, with the get method
        # which include the permission sets in the group
        return not_none(
            await self.get_permission_set_group_by_id(
                permission_set_group_id=permission_set_group_id,
                organization_id=organization_id,
            )
        )

    async def assign_permission_set_to_users(
        self,
        to_user_ids: list[UUID],
        permission_set_id: UUID,
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> None:
        # link permission set to each of the users
        for to_user_id in to_user_ids:
            await self.permission_set_user_association_repository.insert_or_none(
                PermissionSetUserAssociation(
                    id=uuid.uuid4(),
                    user_id=to_user_id,
                    permission_set_id=permission_set_id,
                    status=PermissionSetUserAssociationStatus.ACTIVE,
                    organization_id=organization_id,
                    created_at=zoned_utc_now(),
                    created_by_user_id=created_by_user_id,
                ),
                # if uniq constraint violation, do nothing, instead of failing the whole request
                on_conflict_do_nothing_target_columns=[
                    "user_id",
                    "organization_id",
                    "permission_set_id",
                ],
            )

        # TODO: (hao) return some DTO object?

    async def assign_permission_set_group_to_users(
        self,
        to_user_ids: list[UUID],
        permission_set_group_id: UUID,
        organization_id: UUID,
        created_by_user_id: UUID,
    ) -> None:
        # link permission set group to each of the users
        for to_user_id in to_user_ids:
            logger.info(
                f"Assigning permission set group {permission_set_group_id} to user {to_user_id}"
            )
            await self.permission_set_group_user_association_repository.insert_or_none(
                PermissionSetGroupUserAssociation(
                    id=uuid.uuid4(),
                    user_id=to_user_id,
                    permission_set_group_id=permission_set_group_id,
                    status=PermissionSetGroupUserAssociationStatus.ACTIVE,
                    organization_id=organization_id,
                    created_at=zoned_utc_now(),
                    created_by_user_id=created_by_user_id,
                ),
                # if uniq constraint violation, do nothing, instead of failing the whole request
                on_conflict_do_nothing_target_columns=[
                    "user_id",
                    "organization_id",
                    "permission_set_group_id",
                ],
                # supplements on_conflict_do_nothing_target_columns, because table has a partial/conditional index on column status
                on_conflict_do_nothing_conditional_columns={
                    "status": PermissionSetGroupUserAssociationStatus.ACTIVE
                },
            )

        # TODO: (hao) return some DTO object?

    async def update_permission_groups_for_user(
        self,
        user_id: UUID,
        organization_id: UUID,
        permission_set_group_names: list[str],
    ) -> UserPermissionSetGroupMembershipDTO:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            permission_set_group_names=permission_set_group_names,
        ).info("Updating permission groups for user")

        current_user_groups = await self.permission_set_group_user_association_repository.find_active_permission_set_group_associations_for_user_id(
            user_id=user_id, organization_id=organization_id
        )
        current_user_groups_by_group_id = {
            group.id: group for group in current_user_groups
        }

        group_associations_to_set = []
        group_associations_group_ids = set()
        for permission_set_group_name in permission_set_group_names:
            permission_set_group = await self.get_permission_set_group_by_name(
                name=permission_set_group_name,
                organization_id=organization_id,
            )
            if not permission_set_group:
                logger.bind(permission_set_group_name=permission_set_group_name).error(
                    "Permission set group not found"
                )
                raise InvalidArgumentError(
                    f"Permission set group {permission_set_group_name} not found"
                )
            group_associations_group_ids.add(permission_set_group.id)

            if permission_set_group.id not in current_user_groups_by_group_id:
                group_associations_to_set.append(
                    PermissionSetGroupUserAssociation(
                        id=uuid.uuid4(),
                        user_id=user_id,
                        permission_set_group_id=permission_set_group.id,
                        status=PermissionSetGroupUserAssociationStatus.ACTIVE,
                        organization_id=organization_id,
                        created_at=zoned_utc_now(),
                        created_by_user_id=user_id,
                    )
                )

        group_id_associations_to_remove = []
        for current_group in current_user_groups:
            if current_group.id not in group_associations_group_ids:
                group_id_associations_to_remove.append(current_group.id)

        if group_associations_to_set or group_id_associations_to_remove:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                group_associations_to_set=[
                    a.permission_set_group_id for a in group_associations_to_set
                ],
                group_id_associations_to_remove=group_id_associations_to_remove,
            ).info("Changing user groups")
            await self.permission_set_group_user_association_repository.update_group_associations(
                organization_id=organization_id,
                user_id=user_id,
                group_associations_to_add=group_associations_to_set,
                group_id_associations_to_remove=group_id_associations_to_remove,
            )

        latest_user_groups = await self.permission_set_group_user_association_repository.find_active_permission_set_group_associations_for_user_id(
            user_id=user_id, organization_id=organization_id
        )

        return UserPermissionSetGroupMembershipDTO.map_from_db_obj(
            user_id=user_id,
            organization_id=organization_id,
            permission_set_groups=latest_user_groups,
        )

    async def get_user_permission_claims(
        self,
        user_id: UUID,
        organization_id: UUID,
    ) -> list[str]:
        # get all permission sets for the user from direct user association
        perm_set_from_user_association = await self.permission_set_user_association_repository.find_active_permission_sets_for_user_id(
            user_id=user_id, organization_id=organization_id
        )

        # get all permission sets for the user from group user association
        perm_set_from_group_association = await self.permission_set_group_user_association_repository.find_active_permission_sets_for_user_id(
            user_id=user_id, organization_id=organization_id
        )

        # merge them
        perm_set_claims = []
        for permission_set in perm_set_from_user_association:
            if permission_set.allowed_operations:
                # if not None, then add to the claims
                perm_set_claims.extend(permission_set.allowed_operations)

        for permission_set in perm_set_from_group_association:
            if permission_set.allowed_operations:
                # if not None, then add to the claims
                perm_set_claims.extend(permission_set.allowed_operations)

        # dedup before returning
        return sorted(set(perm_set_claims))

    async def get_user_permission_set_group_associations(
        self,
        user_id: UUID,
        organization_id: UUID,
    ) -> dict[str, UUID]:
        """
        Used to determine which Permissions Set Group(s) a user is associated with.
        Useful for backfilling missing Permissions Set Group associations.

        Returns a dictionary of Permission Set Group names to their IDs.
        """
        # get all permission sets for the user from group user association
        psg_associations = await self.permission_set_group_user_association_repository.find_active_permission_set_group_associations_for_user_id(
            user_id=user_id, organization_id=organization_id
        )
        return {
            permission_set_group.name: permission_set_group.id
            for permission_set_group in psg_associations
            if permission_set_group.name is not None
        }


class SingletonPermissionService(Singleton, PermissionService):
    pass


def get_permission_service_by_db_engine(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> PermissionService:
    if SingletonPermissionService.has_instance():
        return SingletonPermissionService.get_singleton_instance()
    return SingletonPermissionService(
        permission_set_repository=PermissionSetRepository(engine=db_engine),
        permission_set_group_repository=PermissionSetGroupRepository(engine=db_engine),
        permission_set_user_association_repository=PermissionSetUserAssociationRepository(
            engine=db_engine
        ),
        permission_set_group_user_association_repository=PermissionSetGroupUserAssociationRepository(
            engine=db_engine
        ),
        permission_set_group_permission_set_association_repository=PermissionSetGroupPermissionSetAssociationRepository(
            engine=db_engine
        ),
        job_role_user_association_repository=JobRoleUserAssociationRepository(
            engine=db_engine
        ),
    )


def get_permission_service(
    request: Request,
) -> PermissionService:
    return get_permission_service_by_db_engine(get_db_engine(request))
