from typing import Annotated, assert_never, override
from uuid import UUID

from fastapi import Depends, Request

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.db.dao.sequence_repository import (
    SequenceRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.sequence import (
    SequenceV2 as DbSequenceV2,
)
from salestech_be.db.models.sequence import (
    SequenceVisibility,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


class SequenceQueryService(DomainQueryService[SequenceV2]):
    def __init__(
        self,
        sequence_repository: Annotated[SequenceRepository, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
    ) -> None:
        super().__init__(feature_flag_service=feature_flag_service)

        self.sequence_repository = sequence_repository

    @override
    async def is_editable(
        self, user_auth_context: UserAuthContext | None, domain_object: SequenceV2
    ) -> bool:
        if not user_auth_context:
            return True

        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        match domain_object.visibility:
            case SequenceVisibility.TEAM_EDITABLE:
                return True
            case SequenceVisibility.PRIVATE | SequenceVisibility.TEAM_VIEWABLE:
                allowed_users = await super().get_allowed_users_from_entity(
                    domain_object
                )
                return allowed_users.is_owner_or_participant(user_auth_context.user_id)
            case _ as never:
                assert_never(never)

    @override
    async def is_entity_viewable_by_user(
        self, user_auth_context: UserAuthContext, domain_object: SequenceV2
    ) -> bool:
        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        match domain_object.visibility:
            case SequenceVisibility.TEAM_VIEWABLE | SequenceVisibility.TEAM_EDITABLE:
                return True
            case SequenceVisibility.PRIVATE:
                allowed_users = await super().get_allowed_users_from_entity(
                    domain_object
                )
                return allowed_users.is_owner_or_participant(user_auth_context.user_id)
            case _ as never:
                assert_never(never)

    async def get_sequence_by_id(
        self,
        sequence_id: UUID,
        organization_id: UUID,
    ) -> SequenceV2:
        sequence_v2_list = await self.list_sequences(
            organization_id=organization_id, only_include_sequence_ids={sequence_id}
        )
        if not sequence_v2_list:
            raise ResourceNotFoundError(f"Sequence {sequence_id} not found")

        return sequence_v2_list[0]

    # @log_timing(logger=logger)
    async def list_sequences(
        self,
        organization_id: UUID,
        only_include_sequence_ids: UnsetAware[set[UUID]] = UNSET,
        include_deleted: bool = False,
    ) -> list[SequenceV2]:
        if specified(only_include_sequence_ids) and not only_include_sequence_ids:
            return []

        # Fetch sequences from repository
        if specified(only_include_sequence_ids):
            sequences = await self.sequence_repository.find_sequences_by_ids_and_organization_id(
                sequence_ids=list(only_include_sequence_ids),
                organization_id=organization_id,
                include_deleted=include_deleted,
            )
        else:
            sequences = await self.sequence_repository.get_sequence_by_organization_v2(
                organization_id=organization_id,
            )
        sequence_ids = [seq.id for seq in sequences]
        sequence_stats = await self.sequence_repository.get_sequence_stats(
            organization_id=organization_id,
            sequence_ids=sequence_ids,
        )
        return [
            SequenceV2.from_db_model(
                seq,
                stats=next(
                    (stat for stat in sequence_stats if stat.sequence_id == seq.id),
                    None,
                ),
            )
            for seq in sequences
        ]

    async def map_sequences_by_id(
        self,
        organization_id: UUID,
        only_include_sequence_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> dict[UUID, SequenceV2]:
        sequences = await self.list_sequences(
            organization_id=organization_id,
            only_include_sequence_ids=only_include_sequence_ids,
        )
        return {seq.id: seq for seq in sequences}

    async def populate_sequence(self, db_sequence: DbSequenceV2) -> SequenceV2:
        stats = await self.sequence_repository.get_sequence_stats(
            organization_id=db_sequence.organization_id,
            sequence_ids=[db_sequence.id],
        )
        return SequenceV2.from_db_model(
            db_sequence=db_sequence, stats=next(iter(stats), None)
        )

    async def list_sequence_blueprints(
        self,
        organization_id: UUID,
    ) -> list[SequenceV2]:
        # Sequence blueprints are by definition visible to all in the org, so no need to
        # check permissions or filter.
        sequences = (
            await self.sequence_repository.find_blueprint_sequences_by_organization_id(
                organization_id=organization_id,
            )
        )
        return [SequenceV2.from_db_model(seq) for seq in sequences]


class SingletonSequenceQueryService(Singleton, SequenceQueryService):
    pass


def get_sequence_query_service(request: Request) -> SequenceQueryService:
    db_engine = get_db_engine(request)
    return get_sequence_query_service_by_db(db_engine=db_engine)


def get_sequence_query_service_by_db(db_engine: DatabaseEngine) -> SequenceQueryService:
    if SingletonSequenceQueryService.has_instance():
        return SingletonSequenceQueryService.get_singleton_instance()
    return SingletonSequenceQueryService(
        sequence_repository=SequenceRepository(engine=db_engine),
        feature_flag_service=get_feature_flag_service(),
    )
