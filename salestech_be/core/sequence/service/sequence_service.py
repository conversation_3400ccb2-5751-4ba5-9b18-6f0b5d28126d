from __future__ import annotations

from typing import cast, override
from uuid import UUID, uuid4

from fastapi import Request
from pydantic_extra_types.timezone_name import TimeZoneName

from salestech_be.common.exception import (
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.exception.exception import ForbiddenError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, BasePatchRequest, specified
from salestech_be.core.activity.service.activity_service import (
    ActivityService,
    get_activity_service_general,
)
from salestech_be.core.activity.types import ActivityRequest
from salestech_be.core.common.domain_service import DomainService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.email.unsubscription_group.unsubscription_group_ext import (
    UnsuscriptionGroupServiceExt,
    get_unsubscription_ext_service_by_db_engine,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
    get_sequence_enrollment_query_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_permission_service import (
    SequencePermissionService,
    get_sequence_permission_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_query_service import (
    SequenceQueryService,
    get_sequence_query_service_by_db,
)
from salestech_be.core.sequence.service.sequence_step_service import (
    SequenceStepService,
    get_sequence_step_service_by_db_engine,
)
from salestech_be.core.sequence.type.sequence_blueprint import (
    DEFAULT_SEQUENCE_BLUEPRINT_TIMEZONE,
    get_sequence_blueprints_with_timezone,
)
from salestech_be.core.sequence.type.sequence_step_types import SequenceStepV2
from salestech_be.core.sequence.type.sequence_v2 import (
    SequenceV2,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.db.dao.sequence_repository import (
    SequenceRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.activity import (
    ActivityPriority,
    ActivityReferenceIdType,
    ActivityStatus,
    ActivitySubType,
    ActivityType,
)
from salestech_be.db.models.sequence import (
    FlowControlAction,
    SequenceEnrollmentStatus,
    SequenceFlowControlConfig,
    SequenceStatus,
    SequenceStepVariantStatus,
    SequenceV2Update,
)
from salestech_be.db.models.sequence import (
    SequenceParticipant as DbSequenceParticipant,
)
from salestech_be.db.models.sequence import (
    SequenceScheduleTime as DbSequenceScheduleTime,
)
from salestech_be.db.models.sequence import (
    SequenceV2 as DbSequenceV2,
)
from salestech_be.db.models.sequence import (
    SequenceV2Schedule as DbSequenceV2Schedule,
)
from salestech_be.db.models.unsubscription_group import UnsubscriptionGroup
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.sequence.schema import (
    CloneSequenceRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateSequenceRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchSequenceRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    ScheduleConfig,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class SequenceService(DomainService[SequenceV2]):
    def __init__(
        self,
        feature_flag_service: FeatureFlagService,
        sequence_permission_service: SequencePermissionService,
        sequence_repository: SequenceRepository,
        sequence_query_service: SequenceQueryService,
        sequence_step_service: SequenceStepService,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
        unsubscribe_group_service_ext: UnsuscriptionGroupServiceExt,
        activity_service: ActivityService,
        user_service: UserService,
    ):
        super().__init__(
            feature_flag_service=feature_flag_service,
        )
        self.feature_flag_service = feature_flag_service
        self.sequence_permission_service = sequence_permission_service
        self.sequence_repository = sequence_repository
        self.sequence_query_service = sequence_query_service
        self.sequence_step_service = sequence_step_service
        self.sequence_enrollment_query_service = sequence_enrollment_query_service
        self.unsubscribe_group_service_ext = unsubscribe_group_service_ext
        self.activity_service = activity_service
        self.user_service = user_service

    @override
    async def can_access_entity_for_read(
        self, user_auth_context: UserAuthContext, entity: SequenceV2
    ) -> bool:
        return await self.sequence_permission_service.can_access_sequence_for_read(
            user_auth_context=user_auth_context,
            entity=entity,
        )

    @override
    async def can_access_entity_for_patch(
        self,
        user_auth_context: UserAuthContext,
        entity: SequenceV2,
        patch_request: BasePatchRequest | None = None,
    ) -> bool:
        return await self.sequence_permission_service.can_access_sequence_for_update(
            user_auth_context=user_auth_context,
            entity=entity,
        )

    @override
    async def can_access_entity_for_delete(
        self,
        user_auth_context: UserAuthContext,
        entity: SequenceV2,
    ) -> bool:
        return await self.sequence_permission_service.can_access_sequence_for_delete(
            user_auth_context=user_auth_context,
            entity=entity,
        )

    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> SequenceV2 | None:
        db_sequence = await self.sequence_repository.find_by_tenanted_primary_key(
            table_model=DbSequenceV2,
            organization_id=organization_id,
            id=entity_id,
        )
        return SequenceV2.from_db_model(db_sequence) if db_sequence else None

    async def _get_sequence_or_error(
        self, sequence_id: UUID, organization_id: UUID
    ) -> DbSequenceV2:
        db_sequence = await self.sequence_repository.find_by_tenanted_primary_key(
            table_model=DbSequenceV2,
            organization_id=organization_id,
            id=sequence_id,
        )

        if not db_sequence:
            raise ResourceNotFoundError(
                f"Sequence with id {sequence_id} does not exist"
            )

        return db_sequence

    def _db_sequence_update_from_patch_request(
        self,
        user_id: UUID,
        patch_request: PatchSequenceRequest,
    ) -> SequenceV2Update:
        return SequenceV2Update(
            name=patch_request.name,
            description=patch_request.description,
            visibility=patch_request.visibility,
            participants=patch_request.participants,
            owner_user_id=patch_request.owner_user_id,
            schedule=self._convert_schedule_config_to_v2_schedule(
                patch_request.schedule
            )
            if specified(patch_request.schedule) and patch_request.schedule
            else UNSET,
            updated_at=zoned_utc_now(),
            updated_by_user_id=user_id,
            unsubscription_group_id=patch_request.unsubscription_group_id,
            enable_pixel_tracking=patch_request.enable_pixel_tracking,
        )

    def _convert_schedule_config_to_v2_schedule(
        self,
        schedule_config: ScheduleConfig,
    ) -> DbSequenceV2Schedule:
        if not schedule_config.schedule_times:
            raise InvalidArgumentError("Schedule times are required")

        for schedule_time in schedule_config.schedule_times:
            if schedule_time.end_time <= schedule_time.start_time:
                raise InvalidArgumentError(
                    f"End time must be greater than start time ({schedule_time.start_time}, {schedule_time.end_time})"
                )

        return DbSequenceV2Schedule(
            timezone=TimeZoneName(schedule_config.timezone),
            skip_holidays=schedule_config.skip_holidays,
            schedule_times=[
                DbSequenceScheduleTime(
                    day_of_the_week=schedule_time.day_of_week,
                    start_time=schedule_time.start_time,
                    end_time=schedule_time.end_time,
                    is_active=True,
                )
                for schedule_time in schedule_config.schedule_times
            ],
        )

    async def onboard_sequence_blueprints(
        self,
        organization_id: UUID,
        user_id: UUID,
        is_recreate: bool = False,
    ) -> list[tuple[SequenceV2, list[SequenceStepV2]]]:
        if not await self.feature_flag_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="enable_new_sequence_onboarding",
                user_id=user_id,
                organization_id=organization_id,
            )
        ):
            # if not settings.enable_new_sequence_onboarding:
            logger.bind(user_id=user_id, organization_id=organization_id).info(
                "Onboarding sequence blueprints"
            )
            return []

        if is_recreate:
            # Delete existing blueprints
            logger.bind(user_id=user_id, organization_id=organization_id).info(
                "Archiving existing sequence blueprints"
            )
            await self.archive_sequence_blueprints(organization_id=organization_id)

        logger.bind(user_id=user_id, organization_id=organization_id).info(
            "Onboarding sequence blueprints"
        )

        # Get existing blueprints
        existing_blueprints = (
            await self.sequence_repository.find_blueprint_sequences_by_organization_id(
                organization_id=organization_id
            )
        )

        try:
            timezone = await self.user_service.get_user_timezone_or_default(
                user_auth_context=UserAuthContext(
                    user_id=user_id,
                    organization_id=organization_id,
                ),
                default_timezone=DEFAULT_SEQUENCE_BLUEPRINT_TIMEZONE,
            )
        except Exception as e:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                exc_info=e,
            ).warning("Error getting user timezone for sequence blueprint backfill")
            timezone = DEFAULT_SEQUENCE_BLUEPRINT_TIMEZONE

        sequence_blueprints = get_sequence_blueprints_with_timezone(timezone)

        # Create sequences that don't exist
        result = []
        for blueprint in sequence_blueprints:
            db_sequence = next(
                (
                    bp
                    for bp in existing_blueprints
                    if bp.name == blueprint.sequence_template.name
                ),
                None,
            )
            if db_sequence:
                sequence = SequenceV2.from_db_model(db_sequence)
            else:
                logger.bind(
                    user_id=user_id,
                    organization_id=organization_id,
                    blueprint_name=blueprint.sequence_template.name,
                ).info("Creating blueprint sequence")

                # Create the sequence
                sequence = await self._create_sequence(
                    organization_id=organization_id,
                    user_id=user_id,
                    request=blueprint.sequence_template,
                    is_blueprint=True,
                )

            if sequence:
                # Get existing steps for this sequence
                existing_steps = (
                    await self.sequence_step_service.list_sorted_sequence_steps_v2(
                        sequence_id=sequence.id,
                        organization_id=organization_id,
                    )
                )
                existing_step_names = {step.name: step for step in existing_steps}
                # Create only steps that don't exist
                result_steps_for_sequence = []
                for step_template in blueprint.step_templates:
                    if step_template.name in existing_step_names:
                        result_steps_for_sequence.append(
                            existing_step_names[step_template.name]
                        )
                    else:
                        logger.bind(
                            user_id=user_id,
                            organization_id=organization_id,
                            blueprint_name=blueprint.sequence_template.name,
                            step_name=step_template.name,
                        ).info("Creating blueprint sequence step")

                        # Update the sequence_id to match the sequence
                        step_template.sequence_id = sequence.id
                        result_steps_for_sequence.append(
                            await self.sequence_step_service.create_step(
                                user_id=user_id,
                                organization_id=organization_id,
                                api_request=step_template,
                            )
                        )

                result.append((sequence, result_steps_for_sequence))

        return result

    async def archive_sequence_blueprints(
        self,
        organization_id: UUID,
    ) -> list[SequenceV2]:
        """
        Archives all sequence blueprints for an organization and returns the archived sequences.
        """
        blueprint_sequences = (
            await self.sequence_repository.find_blueprint_sequences_by_organization_id(
                organization_id=organization_id
            )
        )
        archived_sequences = []
        for blueprint in blueprint_sequences:
            steps = await self.sequence_step_service.list_sorted_sequence_steps_v2(
                sequence_id=blueprint.id,
                organization_id=organization_id,
            )
            archived = await self.sequence_repository.archive_blueprint_sequence(
                organization_id=organization_id,
                blueprint_id=blueprint.id,
                step_ids=[step.id for step in steps],
            )
            archived_sequences.append(SequenceV2.from_db_model(archived))
        return archived_sequences

    async def create_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        request: CreateSequenceRequest,
    ) -> SequenceV2:
        logger.bind(
            user_id=user_id, organization_id=organization_id, request=request
        ).info("Create sequence request")
        return await self._create_sequence(
            organization_id=organization_id,
            user_id=user_id,
            request=request,
            is_blueprint=False,
        )

    async def _create_sequence(
        self,
        organization_id: UUID,
        user_id: UUID,
        request: CreateSequenceRequest,
        is_blueprint: bool,
    ) -> SequenceV2:
        if request.unsubscription_group_id:
            await self.unsubscribe_group_service_ext.validate_unsub_group_id(
                unsubscription_group_id=request.unsubscription_group_id,
                organization_id=organization_id,
            )

        now = zoned_utc_now()
        db_sequence = await self.sequence_repository.insert(
            DbSequenceV2(
                id=uuid4(),
                organization_id=organization_id,
                name=request.name,
                description=request.description,
                owner_user_id=user_id,
                participants=[
                    DbSequenceParticipant(
                        user_id=request_participant.user_id,
                        role=request_participant.role,
                    )
                    for request_participant in request.participants
                ]
                if request.participants
                else None,
                visibility=request.visibility,
                status=SequenceStatus.DRAFT,
                schedule=self._convert_schedule_config_to_v2_schedule(request.schedule),
                cloned_from_sequence_id=None,  # Clone is separate path
                created_at=now,
                created_by_user_id=user_id,
                updated_at=now,
                is_blueprint=is_blueprint,
                unsubscription_group_id=request.unsubscription_group_id,
                flow_control_config=SequenceFlowControlConfig(  # Currently fixed/not configurable
                    meeting_booked=FlowControlAction.TERMINATE,
                    email_unsubscribed=FlowControlAction.TERMINATE,
                ),
                enable_pixel_tracking=request.enable_pixel_tracking,
            )
        )

        # No stats for new sequence
        logger.bind(organization_id=organization_id, sequence_id=db_sequence.id).info(
            "New sequence created"
        )
        try:
            await self.activity_service.insert_activity(
                organization_id=organization_id,
                insert_activity_request=ActivityRequest(
                    type=ActivityType.SEQUENCE,
                    sub_type=ActivitySubType.SEQUENCE_CREATED,
                    priority=ActivityPriority.LOW,
                    status=ActivityStatus.READ,
                    owner_user_id=user_id,
                    account_id=None,
                    sequence_id=db_sequence.id,
                    reference_id=str(db_sequence.id),
                    reference_id_type=ActivityReferenceIdType.SEQUENCE_ID,
                    display_name=db_sequence.name,
                    sub_references=None,
                    created_at=now,
                    created_by_user_id=user_id,
                    metadata=None,
                ),
            )
        except Exception as e:
            logger.bind(
                organization_id=organization_id, sequence_id=db_sequence.id, exc_info=e
            ).error("Error inserting activity for sequence creation")

        return SequenceV2.from_db_model(db_sequence=db_sequence, stats=None)

    async def authed_clone_sequence(
        self,
        user_auth_context: UserAuthContext,
        sequence_id: UUID,
        request: CloneSequenceRequest,
    ) -> SequenceV2:
        user_id = user_auth_context.user_id
        organization_id = user_auth_context.organization_id
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            sequence_id=sequence_id,
            request=request,
        ).info("Clone sequence request")

        existing_sequence = await self._get_sequence_or_error(
            sequence_id=sequence_id, organization_id=organization_id
        )

        if not await self.can_access_entity_for_read(
            user_auth_context=user_auth_context,
            entity=SequenceV2.from_db_model(existing_sequence),
        ):
            raise ForbiddenError("You do not have permission to clone this sequence")

        # For cloned sequence record, we only update name.  Though request offers all fields that creation does,
        # current use case is for name only to be set upon clone.  Status is explicitly set to DRAFT as cloned
        # one is new.
        now = zoned_utc_now()
        cloned_sequence: DbSequenceV2 = DbSequenceV2(
            id=uuid4(),
            organization_id=organization_id,
            name=request.name or existing_sequence.name,
            description=existing_sequence.description,
            owner_user_id=user_id,
            participants=None,
            visibility=existing_sequence.visibility,
            schedule=existing_sequence.schedule,
            status=SequenceStatus.DRAFT,
            cloned_from_sequence_id=sequence_id,
            created_at=now,
            created_by_user_id=user_id,
            updated_at=now,
            enable_pixel_tracking=existing_sequence.enable_pixel_tracking,
        )
        steps_with_variants = await self.sequence_step_service.prepare_clone_steps_v2(
            user_id=user_id,
            organization_id=organization_id,
            existing_sequence_id=sequence_id,
            new_sequence_id=cloned_sequence.id,
        )
        new_sequence = (
            await self.sequence_repository.transactional_create_sequence_resources_v2(
                sequence=cloned_sequence,
                steps_with_variants=steps_with_variants,
            )
        )
        logger.bind(organization_id=organization_id, sequence_id=new_sequence.id).info(
            "Cloned sequence created"
        )
        return SequenceV2.from_db_model(db_sequence=new_sequence, stats=None)

    async def _activate_sequence_variants_if_all_inactive(
        self,
        db_sequence: DbSequenceV2,
        organization_id: UUID,
        user_id: UUID,
    ) -> None:
        # Get all steps
        steps = await self.sequence_step_service.list_sorted_sequence_steps_v2(
            sequence_id=db_sequence.id,
            organization_id=organization_id,
        )

        # Check if all variants across all steps are inactive using a generator expression
        all_variants_inactive = all(
            variant.status == SequenceStepVariantStatus.INACTIVE
            for step in steps
            for variant in step.variants
        )

        # Only activate variants if all were inactive
        if all_variants_inactive:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                sequence_id=db_sequence.id,
            ).info("All variants were inactive, activating all variants")

            for step in steps:
                for variant in step.variants:
                    await self.sequence_step_service.activate_variant(
                        variant_id=variant.id,
                        user_id=user_id,
                        organization_id=organization_id,
                    )
        else:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                sequence_id=db_sequence.id,
            ).info("Some variants were already active, skipping variant activation")

    def _validate_sequence_eligible_for_activation(
        self, user_id: UUID, organization_id: UUID, db_sequence: DbSequenceV2
    ) -> None:
        if db_sequence.is_blueprint:
            raise InvalidArgumentError("Blueprint sequences cannot be activated")
        elif db_sequence.status not in [
            SequenceStatus.DRAFT,
            SequenceStatus.INACTIVE,
            SequenceStatus.ACTIVE,
        ]:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                sequence_id=db_sequence.id,
                status=db_sequence.status,
            ).warning("Sequence not eligible for activation")
            raise InvalidArgumentError("Sequence not eligible for activation")

    def _validate_steps_eligible_for_activation(
        self,
        db_sequence: DbSequenceV2,
        steps: list[SequenceStepV2],
    ) -> None:
        if not db_sequence.last_activated_at:
            # If not activated before, we will auto enable variants.  Check for missing subjects before we do.
            invalid_step = next(
                (
                    step
                    for step in steps
                    if any(
                        variant.content
                        and not SequenceStepService.is_variant_content_eligible_for_activate(
                            variant.content, variant.reply_to_previous_thread
                        )
                        for variant in step.variants
                    )
                ),
                None,
            )
            if invalid_step:
                raise InvalidArgumentError(
                    f"Email step '{invalid_step.name}' is missing a subject"
                )

    async def authed_activate_sequence(
        self,
        user_auth_context: UserAuthContext,
        sequence_id: UUID,
        activate_all_sequence_variants: bool,
    ) -> SequenceV2:
        user_id = user_auth_context.user_id
        organization_id = user_auth_context.organization_id
        logger.bind(
            user_id=user_id, organization_id=organization_id, sequence_id=sequence_id
        ).info("Activate sequence request")

        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=sequence_id,
            access_check_function=self.can_access_entity_for_patch,
        )

        db_sequence = await self._get_sequence_or_error(
            sequence_id=sequence_id, organization_id=organization_id
        )
        self._validate_sequence_eligible_for_activation(
            user_id=user_id, organization_id=organization_id, db_sequence=db_sequence
        )
        if db_sequence.status == SequenceStatus.ACTIVE:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                sequence_id=db_sequence.id,
            ).info("Sequence is already active")
            return SequenceV2.from_db_model(db_sequence)

        # Get all steps
        steps = await self.sequence_step_service.list_sorted_sequence_steps_v2(
            sequence_id=sequence_id,
            organization_id=organization_id,
        )

        if activate_all_sequence_variants:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                sequence_id=sequence_id,
            ).info(
                "While activating sequence, checking for auto activation of variants"
            )
            self._validate_steps_eligible_for_activation(
                db_sequence=db_sequence,
                steps=steps,
            )
            await self._activate_sequence_variants_if_all_inactive(
                db_sequence=db_sequence,
                organization_id=organization_id,
                user_id=user_id,
            )

        now = zoned_utc_now()
        columns_to_update = {
            "status": SequenceStatus.ACTIVE,
            "updated_at": now,
            "updated_by_user_id": user_id,
            "last_activated_at": now,
        }
        if not db_sequence.unsubscription_group_id:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                sequence_id=sequence_id,
            ).info("Assume default unsubscription group for sequence")
            # Gets or creates default for the organization
            group: UnsubscriptionGroup = await self.unsubscribe_group_service_ext.get_default_unsub_group_for_org(
                organization_id=organization_id
            )
            columns_to_update["unsubscription_group_id"] = group.id
        updated_sequence = not_none(
            await self.sequence_repository.update_by_tenanted_primary_key(
                table_model=DbSequenceV2,
                organization_id=organization_id,
                primary_key_to_value={"id": sequence_id},
                column_to_update=columns_to_update,
            )
        )
        logger.bind(
            organization_id=organization_id,
            sequence_id=updated_sequence.id,
            unsub_group_id=updated_sequence.unsubscription_group_id,
        ).info("Sequence activated")

        try:
            await self.activity_service.insert_activity(
                organization_id=organization_id,
                insert_activity_request=ActivityRequest(
                    type=ActivityType.SEQUENCE,
                    sub_type=ActivitySubType.SEQUENCE_ACTIVATED,
                    priority=ActivityPriority.LOW,
                    status=ActivityStatus.READ,
                    owner_user_id=user_id,
                    account_id=None,
                    sequence_id=db_sequence.id,
                    reference_id=str(db_sequence.id),
                    reference_id_type=ActivityReferenceIdType.SEQUENCE_ID,
                    display_name=db_sequence.name,
                    sub_references=None,
                    created_at=now,
                    created_by_user_id=user_id,
                    metadata=None,
                ),
            )
        except Exception as e:
            logger.bind(
                organization_id=organization_id, sequence_id=db_sequence.id, exc_info=e
            ).error("Error inserting activity for sequence activation")

        return SequenceV2.from_db_model(db_sequence=updated_sequence, stats=None)

    async def authed_deactivate_sequence(
        self,
        user_auth_context: UserAuthContext,
        sequence_id: UUID,
    ) -> SequenceV2:
        user_id = user_auth_context.user_id
        organization_id = user_auth_context.organization_id
        logger.bind(
            user_id=user_id, organization_id=organization_id, sequence_id=sequence_id
        ).info("Deactivate sequence request")

        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=sequence_id,
            access_check_function=self.can_access_entity_for_patch,
        )

        db_sequence = await self._get_sequence_or_error(
            sequence_id=sequence_id, organization_id=organization_id
        )
        if db_sequence.status == SequenceStatus.INACTIVE:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                sequence_id=sequence_id,
            ).info("Sequence is already inactive")
            return SequenceV2.from_db_model(db_sequence)
        elif db_sequence.status != SequenceStatus.ACTIVE:
            logger.bind(
                user_id=user_id,
                organization_id=organization_id,
                sequence_id=sequence_id,
                status=db_sequence.status,
            ).warning("Sequence not eligible for deactivation")
            raise InvalidArgumentError("Sequence not eligible for deactivation")

        updated_sequence = not_none(
            await self.sequence_repository.update_by_tenanted_primary_key(
                table_model=DbSequenceV2,
                organization_id=organization_id,
                primary_key_to_value={"id": sequence_id},
                column_to_update={
                    "status": SequenceStatus.INACTIVE,
                    "updated_at": zoned_utc_now(),
                    "updated_by_user_id": user_id,
                },
            )
        )
        logger.bind(
            organization_id=organization_id, sequence_id=updated_sequence.id
        ).info("Sequence deactivated")

        try:
            await self.activity_service.insert_activity(
                organization_id=organization_id,
                insert_activity_request=ActivityRequest(
                    type=ActivityType.SEQUENCE,
                    sub_type=ActivitySubType.SEQUENCE_DEACTIVATED,
                    priority=ActivityPriority.LOW,
                    status=ActivityStatus.READ,
                    owner_user_id=user_id,
                    account_id=None,
                    sequence_id=db_sequence.id,
                    reference_id=str(db_sequence.id),
                    reference_id_type=ActivityReferenceIdType.SEQUENCE_ID,
                    display_name=db_sequence.name,
                    sub_references=None,
                    created_at=zoned_utc_now(),
                    created_by_user_id=user_id,
                    metadata=None,
                ),
            )
        except Exception as e:
            logger.bind(
                organization_id=organization_id, sequence_id=db_sequence.id, exc_info=e
            ).error("Error inserting activity for sequence deactivation")

        return await self.sequence_query_service.populate_sequence(
            db_sequence=updated_sequence
        )

    async def remove_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity_id: UUID,
    ) -> DeleteEntityResponse:
        logger.bind(
            user_id=user_id, organization_id=organization_id, sequence_id=entity_id
        ).info("Delete sequence request")

        sequence, _ = await self.sequence_repository.update_sequence_and_enrollments(
            sequence_id=entity_id,
            organization_id=organization_id,
            updated_by_user_id=user_id,
            updated_at=zoned_utc_now(),
            new_sequence_status=SequenceStatus.TERMINATED,
            new_enrollment_status=SequenceEnrollmentStatus.INACTIVE,
        )
        logger.bind(organization_id=organization_id, sequence_id=sequence.id).info(
            "Sequence deleted"
        )
        return DeleteEntityResponse(
            id=entity_id,
            deleted_by_user_id=user_id,
            deleted_at=sequence.deleted_at,
        )

    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: SequenceV2,
        request: BasePatchRequest,
    ) -> SequenceV2:
        logger.bind(
            user_id=user_id,
            organization_id=organization_id,
            sequence_id=entity.id,
            patch_request=request.model_dump(exclude_unset=True),
        ).info("Patch sequence request")

        db_sequence = await self._get_sequence_or_error(
            sequence_id=entity.id, organization_id=organization_id
        )
        if db_sequence.is_blueprint:
            raise InvalidArgumentError("Blueprint sequences cannot be updated")

        api_request = cast(PatchSequenceRequest, request)
        db_sequence_update = self._db_sequence_update_from_patch_request(
            patch_request=api_request, user_id=user_id
        )

        if specified(db_sequence_update.unsubscription_group_id):
            await self.unsubscribe_group_service_ext.validate_unsub_group_id(
                unsubscription_group_id=not_none(
                    db_sequence_update.unsubscription_group_id
                ),
                organization_id=organization_id,
            )

        updated_sequence = not_none(
            await self.sequence_repository.update_by_tenanted_primary_key(
                table_model=DbSequenceV2,
                organization_id=organization_id,
                primary_key_to_value={"id": entity.id},
                column_to_update=db_sequence_update.model_dump(exclude_unset=True),
            )
        )
        logger.bind(
            organization_id=organization_id, sequence_id=updated_sequence.id
        ).info("Sequence updated")
        return await self.sequence_query_service.populate_sequence(
            db_sequence=updated_sequence
        )


class SingletonSequenceService(Singleton, SequenceService):
    pass


def get_sequence_service_by_db_engine(
    engine: DatabaseEngine,
) -> SequenceService:
    if SingletonSequenceService.has_instance():
        return SingletonSequenceService.get_singleton_instance()
    return SingletonSequenceService(
        feature_flag_service=get_feature_flag_service(),
        sequence_permission_service=get_sequence_permission_service_by_db_engine(
            engine=engine
        ),
        sequence_repository=SequenceRepository(
            engine=engine,
        ),
        sequence_query_service=get_sequence_query_service_by_db(
            db_engine=engine,
        ),
        sequence_step_service=get_sequence_step_service_by_db_engine(engine=engine),
        sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
            db_engine=engine,
        ),
        unsubscribe_group_service_ext=get_unsubscription_ext_service_by_db_engine(
            engine=engine,
        ),
        activity_service=get_activity_service_general(db_engine=engine),
        user_service=get_user_service_general(db_engine=engine),
    )


def get_sequence_service(request: Request) -> SequenceService:
    db_engine = get_db_engine(request)
    return get_sequence_service_by_db_engine(engine=db_engine)
