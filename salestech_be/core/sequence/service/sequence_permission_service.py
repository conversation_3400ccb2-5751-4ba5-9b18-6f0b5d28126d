from typing import assert_never
from uuid import UUID

from fastapi import Request

from salestech_be.common.exception.exception import InvalidArgumentError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.core.common.domain_service import _ReeService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.sequence.type.sequence_enrollment_types import SequenceEnrollment
from salestech_be.core.sequence.type.sequence_step_types import SequenceStepV2
from salestech_be.core.sequence.type.sequence_v2 import SequenceV2
from salestech_be.db.dao.sequence_enrollment_repo import (
    SequenceEnrollmentRepository,
    SequenceEnrollmentRun,
)
from salestech_be.db.dao.sequence_repository import (
    SequenceRepository,
    SequenceStepRepository,
    SequenceStepVariantRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.sequence import (
    SequenceStepV2 as DbSequenceStepV2,
)
from salestech_be.db.models.sequence import (
    SequenceStepVariant as DbSequenceStepVariant,
)
from salestech_be.db.models.sequence import (
    SequenceVisibility,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

logger = get_logger(__name__)


class SequencePermissionService(_ReeService[SequenceV2]):
    """
    Service that handles permissions for Sequences, and Sequence Steps.

    This implements some of the same interfaces from DomainService, but this class is not intended to be inherited from.

    We need more flexibility here to accommodate Sequences, Steps/Variants, and Enrollments thereof.
    """

    def __init__(
        self,
        sequence_repository: SequenceRepository,
        sequence_step_repository: SequenceStepRepository,
        sequence_step_variant_repository: SequenceStepVariantRepository,
        sequence_enrollment_repository: SequenceEnrollmentRepository,
    ):
        self.sequence_repository = sequence_repository
        self.sequence_step_repository = sequence_step_repository
        self.sequence_step_variant_repository = sequence_step_variant_repository
        self.sequence_enrollment_repository = sequence_enrollment_repository

    async def can_access_sequence_for_read(
        self, user_auth_context: UserAuthContext, entity: SequenceV2
    ) -> bool:
        """
        Admins always have access to read Sequences.

        All users in the organization have access to read Team-Viewable, or Team-Editable Sequences.

        Only the owner, or participants of a Private Sequence have access to read them.

        This implements the same interface as DomainService.can_access_entity_for_read().
        """
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        match entity.visibility:
            case SequenceVisibility.TEAM_VIEWABLE | SequenceVisibility.TEAM_EDITABLE:
                return True
            case SequenceVisibility.PRIVATE:
                allowed_users = await super().get_allowed_users_from_entity(entity)
                return allowed_users.is_owner_or_participant(user_auth_context.user_id)
            case _ as never:
                assert_never(never)

    async def can_access_sequence_by_id_for_read(
        self,
        user_auth_context: UserAuthContext,
        sequence_id: UUID,
    ) -> bool:
        return (
            await self.can_access_sequence_by_ids_for_read(
                user_auth_context=user_auth_context,
                sequence_ids=[sequence_id],
            )
        ).get(sequence_id, False)

    async def can_access_sequence_by_ids_for_read(
        self,
        user_auth_context: UserAuthContext,
        sequence_ids: list[UUID],
    ) -> dict[UUID, bool]:
        """
        Returns a mapping of sequence ID(s) to booleans, indicating whether each sequence is accessible.
        """
        if len(sequence_ids) == 0:
            raise InvalidArgumentError("No Sequence ID(s) were specified")

        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return dict.fromkeys(sequence_ids, True)

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return dict.fromkeys(sequence_ids, True)

        deduped_sequence_ids = list(set(sequence_ids))
        db_sequences = (
            await self.sequence_repository.find_sequences_by_ids_and_organization_id(
                sequence_ids=deduped_sequence_ids,
                organization_id=user_auth_context.organization_id,
            )
        )

        # We expect all specified sequence IDs to exist.
        if (num_db_sequences := len(db_sequences)) < (
            num_sequence_ids := len(deduped_sequence_ids)
        ):
            raise InvalidArgumentError(
                f"{num_sequence_ids - num_db_sequences} specified Sequence(s) does not exist",
            )

        # map sequence IDs to their access status.
        return {
            db_sequence.id: await self.can_access_sequence_for_read(
                user_auth_context=user_auth_context,
                entity=SequenceV2.from_db_model(db_sequence),
            )
            for db_sequence in db_sequences
        }

    async def can_access_sequence_for_update(
        self, user_auth_context: UserAuthContext, entity: SequenceV2
    ) -> bool:
        """
        Admins always have access to update Sequences.

        All users in the organization have access to update Sequences that are Team-Editable.

        Only the owner, or participants of a Team-Viewable, or Private Sequences have access to update them.

        This implements the same interface as DomainService.can_access_entity_for_patch().
        """
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        match entity.visibility:
            case SequenceVisibility.TEAM_EDITABLE:
                return True
            case SequenceVisibility.TEAM_VIEWABLE | SequenceVisibility.PRIVATE:
                allowed_users = await super().get_allowed_users_from_entity(entity)
                return allowed_users.is_owner_or_participant(user_auth_context.user_id)
            case _ as never:
                assert_never(never)

    async def can_access_sequence_by_ids_for_update(
        self, user_auth_context: UserAuthContext, sequence_ids: list[UUID]
    ) -> bool:
        """
        Admins always have access to update Sequences.
        """
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        if not (
            db_sequences
            := await self.sequence_repository.find_sequences_by_ids_and_organization_id(
                sequence_ids=sequence_ids,
                organization_id=user_auth_context.organization_id,
            )
        ):
            raise InvalidArgumentError(
                "One or more specified Sequences do not exist",
            )

        for db_sequence in db_sequences:
            if not await self.can_access_sequence_for_update(
                user_auth_context=user_auth_context,
                entity=SequenceV2.from_db_model(db_sequence),
            ):
                return False
        return True

    async def can_access_sequence_for_delete(
        self,
        user_auth_context: UserAuthContext,
        entity: SequenceV2,
    ) -> bool:
        """
        Only an admin, or the owner of a Sequence can delete it.

        TEAM_EDITABLE cannot be deleted by participants.
        """
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        return entity.owner_user_id == user_auth_context.user_id

    async def can_access_sequence_step_for_update(
        self, user_auth_context: UserAuthContext, entity: SequenceStepV2
    ) -> bool:
        return await self.can_access_sequence_by_ids_for_update(
            user_auth_context=user_auth_context,
            sequence_ids=[entity.sequence_id],
        )

    async def can_access_sequence_step_by_id_for_update(
        self, user_auth_context: UserAuthContext, sequence_step_id: UUID
    ) -> bool:
        """
        Sequence Steps inherit permissions from their parent Sequence.

        This implements the same interface as DomainService.can_access_entity_for_patch().
        """
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        if not (
            sequence_step := (
                await self.sequence_step_repository.find_by_tenanted_primary_key(
                    table_model=DbSequenceStepV2,
                    id=sequence_step_id,
                    organization_id=user_auth_context.organization_id,
                )
            )
        ):
            raise InvalidArgumentError(
                f"Sequence step with id {sequence_step_id} does not exist",
            )

        return await self.can_access_sequence_by_ids_for_update(
            user_auth_context=user_auth_context,
            sequence_ids=[sequence_step.sequence_id],
        )

    async def can_access_sequence_step_variant_by_id_for_update(
        self, user_auth_context: UserAuthContext, step_variant_id: UUID
    ) -> bool:
        """
        Sequence Step Variants inherit permissions from their parent Sequence Step.

        This implements the same interface as DomainService.can_access_entity_for_patch().
        """
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        if not (
            sequence_step := (
                await self.sequence_step_variant_repository.find_by_tenanted_primary_key(
                    table_model=DbSequenceStepVariant,
                    id=step_variant_id,
                    organization_id=user_auth_context.organization_id,
                )
            )
        ):
            raise InvalidArgumentError(
                f"Sequence step variant with id {step_variant_id} does not exist",
            )

        return await self.can_access_sequence_step_by_id_for_update(
            user_auth_context=user_auth_context,
            sequence_step_id=sequence_step.sequence_step_id,
        )

    async def can_access_sequence_enrollment_for_read(
        self, user_auth_context: UserAuthContext, entity: SequenceEnrollment
    ) -> bool:
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        return (
            await self.can_access_sequence_by_ids_for_read(
                user_auth_context=user_auth_context,
                sequence_ids=[entity.sequence_id],
            )
        ).get(entity.sequence_id, False)

    async def can_access_sequence_enrollment_by_ids_for_update(
        self, user_auth_context: UserAuthContext, enrollment_ids: list[UUID]
    ) -> bool:
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        if not (
            enrollments
            := await self.sequence_enrollment_repository.find_sequence_enrollments_by_ids(
                sequence_enrollment_ids=set(enrollment_ids),
                organization_id=user_auth_context.organization_id,
            )
        ):
            raise InvalidArgumentError(
                "One or more specified Sequence enrollments not found"
            )

        return await self.can_access_sequence_by_ids_for_update(
            user_auth_context=user_auth_context,
            sequence_ids=[enrollment.sequence_id for enrollment in enrollments],
        )

    async def can_access_sequence_enrollment_run_by_id_for_read(
        self, user_auth_context: UserAuthContext, enrollment_run_id: UUID
    ) -> bool:
        if (
            not settings.enable_sequence_perms
            and str(user_auth_context.organization_id)
            not in settings.enable_sequence_perms_org_ids
        ):
            return True

        if user_auth_context.is_admin or user_auth_context.is_super_admin:
            return True

        enrollment_run = (
            await self.sequence_enrollment_repository.find_by_tenanted_primary_key(
                table_model=SequenceEnrollmentRun,
                organization_id=user_auth_context.organization_id,
                id=enrollment_run_id,
            )
        )

        if not enrollment_run:
            raise InvalidArgumentError(
                "One or more specified Sequence enrollment runs not found"
            )

        return (
            await self.can_access_sequence_by_ids_for_read(
                user_auth_context=user_auth_context,
                sequence_ids=[enrollment_run.sequence_id],
            )
        ).get(enrollment_run.sequence_id, False)


class SingletonSequencePermissionService(Singleton, SequencePermissionService):
    pass


def get_sequence_permission_service_by_db_engine(
    engine: DatabaseEngine,
) -> SequencePermissionService:
    return SingletonSequencePermissionService(
        sequence_repository=SequenceRepository(engine=engine),
        sequence_step_repository=SequenceStepRepository(engine=engine),
        sequence_step_variant_repository=SequenceStepVariantRepository(engine=engine),
        sequence_enrollment_repository=SequenceEnrollmentRepository(engine=engine),
    )


def get_sequence_permission_service(request: Request) -> SequencePermissionService:
    db_engine = get_db_engine(request)
    return get_sequence_permission_service_by_db_engine(db_engine)
