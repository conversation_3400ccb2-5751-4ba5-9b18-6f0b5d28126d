from typing import Annotated, override
from uuid import UUID

from fastapi import Depends, Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.query_util.domain_fetch_hints import DomainFetchHints
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.common.domain_fetching_hint import to_sql_selection_spec
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.sequence.service.sequence_permission_service import (
    SequencePermissionService,
    get_sequence_permission_service_by_db_engine,
)
from salestech_be.core.sequence.service.sequence_step_execution_fetch_config import (
    sequence_step_execution_domain_db_mapping,
)
from salestech_be.core.sequence.type.sequence_step_execution_types import (
    SequenceStepExecution,
)
from salestech_be.db.dao.sequence_repository import (
    SequenceExecutionRepository,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.message import MessageStatus
from salestech_be.db.models.sequence import (
    EmailEventType,
)
from salestech_be.db.models.sequence import (
    SequenceStepExecution as DbSequenceStepExecution,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


class SequenceStepExecutionQueryService(DomainQueryService[SequenceStepExecution]):
    def __init__(
        self,
        sequence_step_execution_repository: Annotated[
            SequenceExecutionRepository, Depends()
        ],
        sequence_permission_service: Annotated[SequencePermissionService, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
    ) -> None:
        super().__init__(feature_flag_service=feature_flag_service)

        self.sequence_step_execution_repository = sequence_step_execution_repository
        self.sequence_permission_service = sequence_permission_service

    async def can_edit_parent_sequence(
        self,
        user_auth_context: UserAuthContext,
        sequence_id: UUID,
    ) -> bool:
        return await self.sequence_permission_service.can_access_sequence_by_ids_for_update(
            user_auth_context=user_auth_context,
            sequence_ids=[sequence_id],
        )

    @override
    async def filter_viewable_records(
        self,
        records: list[SequenceStepExecution],
        user_auth_context: UserAuthContext,
    ) -> list[SequenceStepExecution]:
        logger.bind(num_records=len(records)).info("number of sequence step executions")

        # this list may contain duplicates, but that's fine. it will be de-duped later.
        sequence_ids = [record.sequence_id for record in records]
        if len(sequence_ids) == 0:
            return records

        # check which sequences the user has access to. this will de-dup the sequence IDs.
        sequence_access_dict = (
            await self.sequence_permission_service.can_access_sequence_by_ids_for_read(
                user_auth_context=user_auth_context,
                sequence_ids=sequence_ids,
            )
        )

        logger.bind(num_unique_sequences=len(sequence_access_dict)).info(
            "number of unique sequences"
        )

        # filter the records to only include those that the user has access to.
        filtered_records = [
            record for record in records if sequence_access_dict[record.sequence_id]
        ]
        logger.bind(num_filtered_records=len(filtered_records)).info(
            "filtered viewable records"
        )
        return filtered_records

    @override
    async def is_entity_viewable_by_user(
        self, user_auth_context: UserAuthContext, domain_object: SequenceStepExecution
    ) -> bool:
        if user_auth_context.is_admin:
            return True
        # SequenceStepExecution doesn't have visibility or owner_user_id fields
        # So we'll always return True for now
        return True

    @override
    async def is_editable(
        self,
        user_auth_context: UserAuthContext | None,
        domain_object: SequenceStepExecution,
    ) -> bool:
        if not user_auth_context:
            return True

        return await self.sequence_permission_service.can_access_sequence_by_ids_for_update(
            user_auth_context=user_auth_context,
            sequence_ids=[domain_object.sequence_id],
        )

    async def list_sequence_step_executions(
        self,
        organization_id: UUID,
        sequence_id: UUID,
        only_include_sequence_step_execution_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> list[SequenceStepExecution]:
        if (
            specified(only_include_sequence_step_execution_ids)
            and not only_include_sequence_step_execution_ids
        ):
            return []

        # Fetch sequences from repository
        if specified(only_include_sequence_step_execution_ids):
            sequence_step_executions = await self.sequence_step_execution_repository.find_sequence_step_executions_by_ids_and_organization_id(
                sequence_step_execution_ids=list(
                    only_include_sequence_step_execution_ids
                ),
                organization_id=organization_id,
                sequence_id=sequence_id,
            )
        else:
            sequence_step_executions = await self.sequence_step_execution_repository.get_sequence_step_executions_by_organization_id(
                organization_id=organization_id,
                sequence_id=sequence_id,
            )
        return [
            SequenceStepExecution.from_db_model(seq) for seq in sequence_step_executions
        ]

    async def list_sequence_step_executions_with_domain_fetch_hints(
        self,
        organization_id: UUID,
        sequence_id: UUID,
        only_include_sequence_step_execution_ids: UnsetAware[set[UUID]] = UNSET,
        domain_fetch_hints: DomainFetchHints | None = None,
    ) -> list[SequenceStepExecution]:
        logger.bind(domain_fetch_hints=domain_fetch_hints).info(
            "Using list_sequence_step_executions_with_domain_fetch_hints"
        )

        # Early return if empty set provided
        if (
            specified(only_include_sequence_step_execution_ids)
            and not only_include_sequence_step_execution_ids
        ):
            return []

        # Convert domain fetch hints to SQL selection spec
        sql_selection_spec = to_sql_selection_spec(
            domain_fetch_hints=domain_fetch_hints if domain_fetch_hints else None,
            domain_to_table_model_field_mapping=sequence_step_execution_domain_db_mapping,
        )

        logger.bind(sql_selection_spec=sql_selection_spec).info(
            "SQL selection spec for sequence step executions"
        )

        # Fetch sequence step executions using selection spec
        db_sequence_step_executions = (
            await self.sequence_step_execution_repository.list_by_selection_spec(
                table_model=DbSequenceStepExecution,
                organization_id=organization_id,
                selection_spec=sql_selection_spec,
            )
        )

        # Convert database models to domain models
        return [
            SequenceStepExecution.from_db_model(db_execution)
            for db_execution in db_sequence_step_executions
        ]

    async def get_global_message_ids_by_sequence_step_execution_id_and_email_event_type(
        self,
        organization_id: UUID,
        sequence_step_execution_ids: list[UUID],
        step_ids: list[UUID] | None = None,
        messages_statuses_include: list[MessageStatus] | None = None,
        messages_statuses_exclude: list[MessageStatus] | None = None,
        email_event_types_include: list[EmailEventType] | None = None,
    ) -> list[UUID]:
        return await self.sequence_step_execution_repository.get_global_message_ids_by_sequence_step_execution_id_and_email_event_type(
            organization_id=organization_id,
            sequence_step_execution_ids=sequence_step_execution_ids,
            step_ids=step_ids,
            messages_statuses_include=messages_statuses_include,
            messages_statuses_exclude=messages_statuses_exclude,
            email_event_types_include=email_event_types_include,
        )


class SingletonSequenceStepExecutionQueryService(
    Singleton, SequenceStepExecutionQueryService
):
    pass


def get_sequence_step_execution_query_service(
    request: Request,
) -> SequenceStepExecutionQueryService:
    db_engine = get_db_engine(request)
    return get_sequence_step_execution_query_service_by_db(db_engine=db_engine)


def get_sequence_step_execution_query_service_by_db(
    db_engine: DatabaseEngine,
) -> SequenceStepExecutionQueryService:
    if SingletonSequenceStepExecutionQueryService.has_instance():
        return SingletonSequenceStepExecutionQueryService.get_singleton_instance()
    return SingletonSequenceStepExecutionQueryService(
        sequence_step_execution_repository=SequenceExecutionRepository(
            engine=db_engine
        ),
        sequence_permission_service=get_sequence_permission_service_by_db_engine(
            engine=db_engine
        ),
        feature_flag_service=get_feature_flag_service(),
    )
