import asyncio
import uuid
from collections import defaultdict
from datetime import <PERSON><PERSON><PERSON>
from typing import Annotated
from uuid import UUID

from asyncpg import UniqueViolationError
from fastapi import Depends, status
from sqlalchemy.exc import IntegrityError
from starlette.requests import Request

from salestech_be.common.exception import IllegalStateError, InvalidArgumentError
from salestech_be.common.exception.exception import (
    ExternalServiceError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.stats.metric import custom_metric
from salestech_be.core.files.service.file_service import (
    PREVIEW_FILE_NAME,
    FileService,
    file_service_from_engine,
    get_file_service,
)
from salestech_be.core.meeting.constants import (
    BOT_RESTRICTED_UPDATE_WITHIN_START_MINUTES,
    BOT_WELCOME_MESSAGE_WITHIN_SECONDS,
    DEFAULT_BOT_EVERYONE_LEFT_SECONDS,
    DEFAULT_BOT_WAITING_ROOM_SECONDS,
    MEETING_BOT_METADATA_CREATED_AT_KEY,
    MEETING_BOT_METADATA_MEETING__ID_KEY,
    REALTIME_WEBHOOK_EVENT_TOKEN_PARAM,
    REALTIME_WEBHOOK_MEETING_PARAM,
)
from salestech_be.core.meeting.meeting_service_mapper import _MeetingServiceMapper
from salestech_be.core.meeting.service_type import (
    MeetingBotFSM,
    MeetingBotMetrics,
)
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.meeting import (
    BotProvider,
    BotStatusSubCode,
    MeetingBot,
    MeetingBotStatus,
    MeetingBotUpdate,
    MeetingProvider,
    RecallMeetingParticipants,
    RecallRecording,
    RecallRecordingData,
    TranscriptProvider,
)
from salestech_be.db.models.transcript import Transcript
from salestech_be.integrations.recallai.client import (
    RecallAIClientV1,
)
from salestech_be.integrations.recallai.type import (
    AssemblyAiChunkedTranscriptOptions,
    AutomaticLeaveSettings,
    AwsTranscribeTranscriptOptions,
    BotDetectionSettings,
    BotDetectionUsingParticipantNames,
    CreateRecallBotRequest,
    DeepgramTranscriptOptions,
    GladiaTranscriptOptions,
    GoogleMeetOptions,
    RealtimeMedia,
    RealtimeTranscription,
    RecallTranscriptProvider,
    RecordingModeOptions,
    SendChatMessageRequest,
    SpeechmaticsTranscriptOptions,
    StartRecordingOn,
    TranscriptionOptions,
    UpdateRecallBotRequest,
    VideoScreenShare,
)
from salestech_be.integrations.s3.s3_bucket_manager import (
    S3BucketManager,
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import get_latest_db_object, not_none
from salestech_be.web.api.meeting.schema import (
    RecallBotEventData,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    RecallRealtimeCallData,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)

MEDIA_FILE_NAME = "video.mp4"


class RecallTranscriptOptionsFactory:
    @classmethod
    def generate_options(
        cls, provider: RecallTranscriptProvider
    ) -> TranscriptionOptions:
        transcript_options = TranscriptionOptions(provider=provider)
        match provider:
            case RecallTranscriptProvider.ASSEMBLY_AI_ASYNC_CHUNKED:
                transcript_options.assembly_ai_chunked = (
                    AssemblyAiChunkedTranscriptOptions(
                        language_code="en_us", chunk_maximum=2
                    )
                )
            case RecallTranscriptProvider.AWS_TRANSCRIBE:
                transcript_options.aws_transcribe = AwsTranscribeTranscriptOptions(
                    language_code="en-US",
                )
            case RecallTranscriptProvider.DEEPGRAM:
                transcript_options.deepgram = DeepgramTranscriptOptions(
                    model="nova-2", language="en-US"
                )
            case RecallTranscriptProvider.GLADIA:
                transcript_options.gladia = GladiaTranscriptOptions(
                    language_behavior="manual", language="english"
                )
            case RecallTranscriptProvider.SPEECHMATICS:
                transcript_options.speechmatics = SpeechmaticsTranscriptOptions(
                    language="en",
                    enable_partials=settings.recall_realtime_partial_results,
                    max_delay=3,
                )

        return transcript_options


class MeetingBotService:
    def __init__(
        self,
        meeting_repository: Annotated[MeetingRepository, Depends()],
        meeting_s3_manager: Annotated[S3BucketManager, Depends()],
        recallai_client_v1: Annotated[RecallAIClientV1, Depends()],
        file_service: Annotated[FileService, Depends(get_file_service)],
    ) -> None:
        self.meeting_repository = meeting_repository
        self.meeting_s3_manager = meeting_s3_manager
        self.recallai_client_v1 = recallai_client_v1
        self.file_service = file_service
        self.meeting_bot_fsm = MeetingBotFSM()

    async def bot_joining_event(self, data: RecallBotEventData) -> MeetingBot | None:
        # Joining: we update bot status
        return await self._update_bot_status_for_event(
            data=data,
            expected_bot_status=MeetingBotStatus.SCHEDULED,
            new_bot_status=MeetingBotStatus.JOINING,
        )

    async def bot_in_waiting_room_event(
        self, data: RecallBotEventData
    ) -> MeetingBot | None:
        return await self._update_bot_status_for_event(
            data=data,
            expected_bot_status=MeetingBotStatus.JOINING,
            new_bot_status=MeetingBotStatus.WAITING,
        )

    async def bot_in_call_recording_event(
        self,
        data: RecallBotEventData,
    ) -> MeetingBot | None:
        bot_id = data.bot_id
        recording_id = data.status.recording_id
        if not recording_id:
            logger.bind(bot_id=bot_id).error(
                "Recording ID not found in webhook payload",
            )
            return None

        meeting_bot = await self.meeting_repository.get_meeting_bot_by_external_id(
            external_meeting_bot_id=data.bot_id,
            provider=BotProvider.RECALLAI,
        )
        if not meeting_bot:
            logger.bind(external_meeting_bot_id=data.bot_id).warning(
                "No meeting bot found for external id"
            )
            return None

        status_event = data.get_db_status_event()
        updated_meeting_bot: MeetingBot | None
        if meeting_bot.status in [MeetingBotStatus.WAITING, MeetingBotStatus.JOINING]:
            # First time we got to recording - update bot status to recording.
            # Meeting status is unchanged (active at this time).
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                meeting_bot_status=meeting_bot.status,
            ).info("Updating status and recording history")
            updated_meeting_bot = await self.meeting_repository.append_bot_status_history_and_update_status(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                bot_provider=BotProvider.RECALLAI,
                status_event=status_event,
                status=MeetingBotStatus.IN_CALL_RECORDING,
            )
        else:
            # Otherwise, we got to recording from pause/resume.  We updated meeting bot
            # status when those were triggered, so here we only record external history.
            updated_meeting_bot = (
                await self.meeting_repository.append_bot_status_history(
                    external_meeting_bot_id=data.bot_id,
                    bot_provider=BotProvider.RECALLAI,
                    status_event=status_event,
                )
            )

        if meeting_bot.external_recording_id != recording_id:
            # Also set the recording ID if we don't have it
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            ).info("Recording meeting bot recording id")
            updated_meeting_bot = (
                await self.meeting_repository.update_recording_id_by_bot_id(
                    external_meeting_bot_id=bot_id,
                    recording_id=recording_id,
                )
            )

        return updated_meeting_bot

    async def bot_recording_permission_denied(
        self, data: RecallBotEventData
    ) -> MeetingBot | None:
        return await self._update_bot_status_for_event(
            data=data,
            expected_bot_status=MeetingBotStatus.JOINING,
            new_bot_status=MeetingBotStatus.IN_CALL_RECORDING_DENIED,
        )

    async def bot_call_ended_event(self, data: RecallBotEventData) -> MeetingBot | None:
        # Update status if possible, record history, and record participants
        meeting_bot = await self._update_bot_status_for_event(
            data=data,
            expected_bot_status=MeetingBotStatus.IN_CALL_RECORDING,
            new_bot_status=MeetingBotStatus.EXITED,
        )
        if not meeting_bot:
            logger.bind(external_meeting_bot_id=data.bot_id).warning(
                "No meeting bot found for external id"
            )
            return None

        bot_response = await self.recallai_client_v1.get_bot(data.bot_id)
        logger.bind(
            meeting_bot_id=meeting_bot.id,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
        ).info("Recording meeting bot participants")
        updated_meeting_bot = not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=MeetingBot,
                organization_id=meeting_bot.organization_id,
                primary_key_to_value={"id": meeting_bot.id},
                column_to_update={
                    "meeting_participant": RecallMeetingParticipants(
                        meeting_participants=bot_response.meeting_participants,
                    ),
                    "updated_at": zoned_utc_now(),
                },
            )
        )
        if updated_meeting_bot.status_history and any(
            history.sub_code
            == BotStatusSubCode.CALL_ENDED_BY_PLATFORM_WAITING_ROOM_TIMEOUT
            for history in updated_meeting_bot.status_history.status_history
        ):
            custom_metric.increment(MeetingBotMetrics.ENDED_WITHOUT_ACCESS)
            logger.bind(
                external_meeting_bot_id=data.bot_id,
                meeting_id=updated_meeting_bot.meeting_id,
                organization_id=updated_meeting_bot.organization_id,
                participants=updated_meeting_bot.meeting_participant,
            ).warning("Meeting bot exited without gaining access to meeting")
        return updated_meeting_bot

    async def bot_done_event(self, data: RecallBotEventData) -> MeetingBot | None:
        bot_id = data.bot_id
        meeting_bot = await self.meeting_repository.get_meeting_bot_by_external_id(
            external_meeting_bot_id=data.bot_id,
            provider=BotProvider.RECALLAI,
        )
        if not meeting_bot:
            logger.bind(external_meeting_bot_id=data.bot_id).warning(
                "No meeting bot found for external id"
            )
            return None

        await self.meeting_repository.append_bot_status_history(
            external_meeting_bot_id=bot_id,
            bot_provider=BotProvider.RECALLAI,
            status_event=data.get_db_status_event(),
        )

        bot_response = await self.recallai_client_v1.get_bot(bot_id)

        time_now = zoned_utc_now()
        return not_none(
            await self.meeting_repository.update_meeting_bot_recording_data(
                external_meeting_bot_id=bot_id,
                organization_id=meeting_bot.organization_id,
                external_media_url=str(bot_response.video_url)
                if bot_response.video_url
                else None,
                external_media_retention_ended_at=bot_response.media_retention_end,
                recording_metadata=RecallRecordingData(
                    recordings=[
                        RecallRecording(
                            id=r.id,
                            started_at=r.started_at,
                            completed_at=r.completed_at,
                        )
                        for r in bot_response.recordings
                    ]
                )
                if bot_response.recordings
                else None,
                screen_share_ranges=_MeetingServiceMapper.map_bot_participants_screen_event_to_screen_share_ranges(
                    bot_participants=bot_response.meeting_participants,
                    recording_started_at=bot_response.recordings[0].started_at,
                )
                if bot_response.meeting_participants
                and bot_response.recordings
                and bot_response.recordings[0]
                and bot_response.recordings[0].started_at
                else None,
                updated_at=time_now,
                completed_at=time_now,
            )
        )

    async def bot_analysis_done_event(
        self, data: RecallBotEventData
    ) -> MeetingBot | None:
        return await self.meeting_repository.append_bot_status_history(
            external_meeting_bot_id=data.bot_id,
            bot_provider=BotProvider.RECALLAI,
            status_event=data.get_db_status_event(),
        )

    async def copy_external_bot_media_to_s3(
        self,
        external_meeting_bot_id: str,
        organization_id: UUID,
        external_media_url: str,
        s3_key_prefix: str,
        s3_title: str,
    ) -> MeetingBot:
        media_s3_key = f"{s3_key_prefix}/{s3_title}"
        await self.meeting_s3_manager.copy_video_url_to_s3(
            external_media_url, media_s3_key
        )

        return not_none(
            await self.meeting_repository.update_meeting_bot_by_external_id(
                external_meeting_bot_id=external_meeting_bot_id,
                organization_id=organization_id,
                meeting_bot_update=MeetingBotUpdate(
                    media_url=media_s3_key,
                ),
            )
        )

    async def persist_external_media_data(
        self,
        external_media_url: str,
        s3_key_prefix: str,
        external_meeting_bot_id: str,
        organization_id: UUID,
    ) -> MeetingBot:
        # Keep existing return signature of create_and_upload_video_thumbnail_sprite
        (
            sprite_file_s3_path,
            vtt_file_s3_path,
        ) = await self.file_service.create_and_upload_video_thumbnail_sprite(
            external_media_url=external_media_url, s3_key_prefix=s3_key_prefix
        )

        # Construct preview path using same pattern as in FileService
        preview_file_s3_path = f"{s3_key_prefix}/{PREVIEW_FILE_NAME}"

        return not_none(
            await self.meeting_repository.update_meeting_bot_by_external_id(
                external_meeting_bot_id=external_meeting_bot_id,
                organization_id=organization_id,
                meeting_bot_update=MeetingBotUpdate(
                    media_vtt_url=vtt_file_s3_path,
                    media_sprite_url=sprite_file_s3_path,
                    media_preview_thumbnail_url=preview_file_s3_path,  # Store the preview URL
                ),
            )
        )

    @staticmethod
    def _generate_toast_message(full_name: str, status_message: str) -> str:
        """
        Generate a toast message for Google Meet's notification limits.

        The full message format is: "Welcome {name}! FYI I am {status_message}."
        Google Meet has a 52 character limit for toast notifications.
        """
        toast_limit = 52
        template = "Welcome {}! FYI I am {}."

        # -4 for the {} placeholders, +3 to reserve space for potential "..."
        max_name_length = toast_limit - (len(template) - 4) - len(status_message) - 3
        max_name_length = max(max_name_length, 1)

        if len(full_name) <= max_name_length:
            return template.format(full_name, status_message)

        # Truncate at word boundaries
        words = full_name.split()
        if not words or len(words[0]) > max_name_length:
            # Truncate directly if first word too long
            truncated_name = full_name[:max_name_length] + "..."
        else:
            # Build from complete words that fit
            result: list[str] = []
            length = 0
            for word in words:
                space_needed = 1 if result else 0
                if length + space_needed + len(word) <= max_name_length:
                    result.append(word)
                    length += space_needed + len(word)
                else:
                    break

            truncated_name = " ".join(result)
            if len(result) < len(words):  # We truncated some words
                truncated_name += "..."

        return template.format(truncated_name, status_message)

    async def bot_realtime_participant_join_event(
        self,
        data: RecallRealtimeCallData,
        meeting_id: UUID,
        realtime_event_token: UUID | None,
    ) -> None:
        meeting_bot = (
            await self.meeting_repository.get_meeting_bot_by_external_id_and_meeting_id(
                external_meeting_bot_id=data.bot_id,
                meeting_id=meeting_id,
            )
        )
        if not meeting_bot:
            logger.bind(external_meeting_bot_id=data.bot_id).warning(
                "No meeting bot found for external id"
            )
            return
        if meeting_bot.realtime_event_token != realtime_event_token:
            logger.bind(
                meeting_bot_value=meeting_bot.realtime_event_token,
                query_parameter_value=realtime_event_token,
            ).warning("Realtime event token mismatch")
            return

        if meeting_bot.status == MeetingBotStatus.IN_CALL_RECORDING:
            status_message = "recording"
        elif meeting_bot.status in [
            MeetingBotStatus.IN_CALL_PAUSED,
            MeetingBotStatus.IN_CALL_RECORDING_DENIED,
        ]:
            status_message = "not recording"
        else:
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                meeting_bot_status=meeting_bot.status,
            ).info("Meeting bot status requires no notification")
            return

        logger.info("Finding name for who joined")
        bot_response = await self.recallai_client_v1.get_bot(bot_id=data.bot_id)

        matched_participants = [
            p for p in bot_response.meeting_participants if p.id == data.participant_id
        ]
        if not matched_participants:
            logger.bind(external_participant_id=data.participant_id).info(
                "Could not resolve participant"
            )
            return
        elif matched_participants[0].is_host:
            logger.bind(external_participant_id=data.participant_id).info(
                "Participant is host, skipping join notification"
            )
            return

        # Generate toast message with name truncated to fit Google Meet's limits
        full_name = matched_participants[0].name
        toast_message = self._generate_toast_message(full_name, status_message)

        if settings.enable_bot_welcome_message_timing_check:
            first_event_time = (
                min(event.created_at for event in matched_participants[0].events)
                if matched_participants[0].events
                else None
            )
            if first_event_time and first_event_time < zoned_utc_now() - timedelta(
                seconds=BOT_WELCOME_MESSAGE_WITHIN_SECONDS
            ):
                logger.bind(
                    external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                    status_message=status_message,
                ).info("Skipping message due to recent event")
                return

        logger.bind(
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            status_message=status_message,
        ).info("Sending message")
        await self.recallai_client_v1.send_message(
            bot_id=meeting_bot.external_meeting_bot_id,
            request=SendChatMessageRequest(message=toast_message),
        )

    async def _update_bot_status_for_event(
        self,
        data: RecallBotEventData,
        expected_bot_status: MeetingBotStatus,
        new_bot_status: MeetingBotStatus,
    ) -> MeetingBot | None:
        meeting_bot = await self.meeting_repository.get_meeting_bot_by_external_id(
            external_meeting_bot_id=data.bot_id,
            provider=BotProvider.RECALLAI,
        )
        if not meeting_bot:
            logger.bind(external_meeting_bot_id=data.bot_id).warning(
                "No meeting bot found for external id"
            )
            return None
        if meeting_bot.status != expected_bot_status:
            if not self.meeting_bot_fsm.is_valid_progression(
                current_state=meeting_bot.status, end_state=new_bot_status
            ):
                logger.bind(
                    meeting_bot_id=meeting_bot.id,
                    meeting_bot_status=meeting_bot.status,
                    new_status=new_bot_status,
                    external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                ).warning(
                    "Unsupported meeting bot state transition, fallback to recording history only"
                )
                return await self.meeting_repository.append_bot_status_history(
                    external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                    bot_provider=BotProvider.RECALLAI,
                    status_event=data.get_db_status_event(),
                )
            else:
                # Not the state we expected, but one that is possible from our FSM:
                # likely webhook ordering issue.
                logger.bind(
                    meeting_bot_id=meeting_bot.id,
                    meeting_bot_status=meeting_bot.status,
                    new_status=new_bot_status,
                    external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                ).warning("Unexpected order for meeting bot state transition")

        logger.bind(
            external=data.status.code,
            new_status=new_bot_status,
            old_status=meeting_bot.status,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
        ).info("Updating bot status")
        return (
            await self.meeting_repository.append_bot_status_history_and_update_status(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                bot_provider=BotProvider.RECALLAI,
                status_event=data.get_db_status_event(),
                status=new_bot_status,
            )
        )

    async def get_latest_meeting_bot_by_meeting_id(
        self,
        meeting_id: UUID,
        organization_id: UUID,
        bot_provider: BotProvider = BotProvider.RECALLAI,
    ) -> MeetingBot | None:
        meeting_bots = await self.meeting_repository.find_meeting_bots_by_meeting(
            meeting_id=meeting_id, organization_id=organization_id
        )
        return self.filter_latest_meeting_bot(
            meeting_bots=meeting_bots, bot_provider=bot_provider
        )

    async def get_active_meeting_bot_by_meeting_id(
        self,
        meeting_id: UUID,
        organization_id: UUID,
        bot_provider: BotProvider = BotProvider.RECALLAI,
    ) -> MeetingBot | None:
        meeting_bots = await self.meeting_repository.find_meeting_bots_by_meeting(
            meeting_id=meeting_id, organization_id=organization_id
        )
        for meeting_bot in meeting_bots:
            # There is one non-terminal bot active per provider at a time
            # Find the first one
            if (
                meeting_bot.provider == bot_provider
                and not MeetingBotStatus.is_terminal(meeting_bot.status)
            ):
                return meeting_bot
        return None

    async def get_all_meeting_bots_by_meeting_id(
        self,
        meeting_id: UUID,
        organization_id: UUID,
    ) -> list[MeetingBot]:
        return await self.meeting_repository.find_meeting_bots_by_meeting(
            meeting_id=meeting_id, organization_id=organization_id
        )

    @staticmethod
    def filter_latest_meeting_bot(
        meeting_bots: list[MeetingBot],
        bot_provider: BotProvider = BotProvider.RECALLAI,
    ) -> MeetingBot | None:
        # For cases where we are not looking for the active/in-use bot, we find the
        # most recent one.  This assumes the most recently added bot was used for the
        # meeting. Not handled is the case where this is not true.
        if not meeting_bots:
            return None

        provider_bots = [
            meeting_bot
            for meeting_bot in meeting_bots
            if meeting_bot.provider == bot_provider
        ]
        return get_latest_db_object(provider_bots) if provider_bots else None

    async def update_bot_settings_in_provider(
        self, meeting_bot_id: UUID, organization_id: UUID
    ) -> bool:
        logger.bind(
            meeting_bot_id=meeting_bot_id, organization_id=organization_id
        ).info("Updating bot settings in provider")

        meeting_bot = await self.meeting_repository.find_by_tenanted_primary_key(
            MeetingBot,
            organization_id=organization_id,
            id=meeting_bot_id,
        )
        if not meeting_bot:
            logger.bind(meeting_bot_id=meeting_bot_id).warning("No meeting bot found")
            return False

        # Currently we only propagate automatic leave settings to Recall for this flow
        request = UpdateRecallBotRequest(
            automatic_leave=self._create_automatic_leave_settings(),
        )

        try:
            await self.recallai_client_v1.update_bot(
                bot_id=meeting_bot.external_meeting_bot_id, request=request
            )
            return True
        except Exception as e:
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                organization_id=organization_id,
                meeting_bot_id=meeting_bot.id,
            ).error("Failed to update bot settings", exc_info=e)
            return False

    async def change_name_for_scheduled_bots(
        self,
        organization_id: UUID,
        user_id: UUID,
        new_bot_name: str,
    ) -> list[MeetingBot]:
        logger.bind(
            organization_id=organization_id, user_id=user_id, new_bot_name=new_bot_name
        ).info("Changing name for scheduled bots")
        # Go through bots, handle earliest scheduled first
        meeting_bots = sorted(
            await self.meeting_repository.find_future_meeting_bots_by_meeting_user_id(
                organization_id=organization_id, user_id=user_id
            ),
            key=lambda bot: bot.scheduled_at or bot.created_at,
        )
        updated_meeting_bots = []
        for meeting_bot in meeting_bots:
            if meeting_bot.status != MeetingBotStatus.SCHEDULED:
                logger.bind(
                    meeting_bot_id=meeting_bot.id, meeting_bot_status=meeting_bot.status
                ).info("Skipping non-scheduled bot")
                continue
            if meeting_bot.name == new_bot_name:
                logger.bind(
                    meeting_bot_id=meeting_bot.id, meeting_bot_name=meeting_bot.name
                ).info("Skipping bot that already has the correct name")
                continue

            request = UpdateRecallBotRequest(
                bot_name=new_bot_name,
            )
            logger.bind(request=request).info("Updating meeting bot")
            await self.recallai_client_v1.update_bot(
                meeting_bot.external_meeting_bot_id, request
            )

            updated_meeting_bot = not_none(
                await self.meeting_repository.update_by_tenanted_primary_key(
                    MeetingBot,
                    organization_id=organization_id,
                    primary_key_to_value={
                        "id": meeting_bot.id,
                    },
                    column_to_update={
                        "name": new_bot_name,
                        "updated_at": zoned_utc_now(),
                    },
                )
            )

            updated_meeting_bots.append(updated_meeting_bot)

        logger.bind(updated_bot_count=len(updated_meeting_bots)).info(
            "Updated scheduled bots for name change"
        )
        return updated_meeting_bots

    async def create_scheduled_meeting_bot(
        self,
        meeting_id: UUID,
        meeting_url: str,
        bot_name: str,
        meeting_provider: MeetingProvider,
        scheduled_at: ZoneRequiredDateTime,
        organization_id: UUID,
    ) -> MeetingBot:
        (result_meeting_bot, _) = await self._bootstrap_recall_bot(
            meeting_url=meeting_url,
            bot_name=bot_name,
            meeting_provider=meeting_provider,
            join_at=scheduled_at,
            organization_id=organization_id,
            meeting_id=meeting_id,
        )
        return result_meeting_bot

    async def update_scheduled_meeting_bot(
        self,
        meeting_bot: MeetingBot,
        meeting_id: UUID,
        meeting_url: str,
        bot_name: str,
        meeting_provider: MeetingProvider,
        scheduled_at: ZoneRequiredDateTime,
        organization_id: UUID,
    ) -> MeetingBot:
        if meeting_bot.status == MeetingBotStatus.SCHEDULED and scheduled_at > (
            zoned_utc_now()
            + timedelta(minutes=BOT_RESTRICTED_UPDATE_WITHIN_START_MINUTES)
        ):
            # Scheduled, and our new join at time we would assume is at least 5 minutes
            # out: update existing scheduled bot.  If updating within 5 minutes of start
            # recall will error out.
            logger.bind(
                meeting_id=meeting_id,
                meeting_bot_id=meeting_bot.id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                scheduled_at=meeting_bot.scheduled_at,
            ).info("Updating scheduled meeting bot")
            result_meeting_bot = await self._update_recall_bot(
                meeting_url=meeting_url,
                bot_name=bot_name,
                meeting_provider=meeting_provider,
                join_at=scheduled_at,
                external_bot_id=meeting_bot.external_meeting_bot_id,
                organization_id=organization_id,
                meeting_bot_id=meeting_bot.id,
            )
        else:
            logger.bind(
                meeting_id=meeting_id,
                join_at=scheduled_at,
                meeting_bot_id=meeting_bot.id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            ).info(
                "Swapping meeting bot due to time change for active, used, or near active bot"
            )
            if meeting_bot.status != MeetingBotStatus.EXITED:
                # End current bot
                await self.remove_bot_from_meeting(
                    meeting_bot=meeting_bot,
                    is_consent_denied=False,
                    chat_notification_message=None,
                )

            # Schedule a new one
            (result_meeting_bot, _) = await self._bootstrap_recall_bot(
                meeting_url=meeting_url,
                bot_name=bot_name,
                meeting_provider=meeting_provider,
                join_at=scheduled_at,
                organization_id=organization_id,
                meeting_id=meeting_id,
            )

        return result_meeting_bot

    def _create_automatic_leave_settings(self) -> AutomaticLeaveSettings:
        """Creates standard automatic leave settings for Recall bots."""
        return AutomaticLeaveSettings(
            everyone_left_timeout=DEFAULT_BOT_EVERYONE_LEFT_SECONDS,
            waiting_room_timeout=DEFAULT_BOT_WAITING_ROOM_SECONDS,
            bot_detection=BotDetectionSettings(
                using_participant_names=BotDetectionUsingParticipantNames(
                    timeout=30, activate_after=30, matches=["Notetaker", "Recorder"]
                )
            ),
        )

    async def _bootstrap_recall_bot(
        self,
        meeting_url: str,
        bot_name: str,
        meeting_provider: MeetingProvider,
        join_at: ZoneRequiredDateTime,
        organization_id: UUID,
        meeting_id: UUID,
    ) -> tuple[MeetingBot, Transcript]:
        logger.bind(
            meeting_id=meeting_id,
            join_at=join_at,
            bot_name=bot_name,
            meeting_url=meeting_url,
            meeting_provider=meeting_provider,
        ).info("Creating new bot")
        if not MeetingProvider.supports_video_conferencing(meeting_provider):
            raise InvalidArgumentError(
                f"Meeting provider {meeting_provider} is not supported"
            )

        realtime_event_token = uuid.uuid4()
        webhook_query_params = f"?{REALTIME_WEBHOOK_MEETING_PARAM}={meeting_id}&{REALTIME_WEBHOOK_EVENT_TOKEN_PARAM}={realtime_event_token}"
        request = CreateRecallBotRequest(
            meeting_url=meeting_url,
            bot_name=bot_name,
            join_at=join_at,
            automatic_leave=self._create_automatic_leave_settings(),
            metadata={
                MEETING_BOT_METADATA_MEETING__ID_KEY: str(meeting_id),
                MEETING_BOT_METADATA_CREATED_AT_KEY: str(zoned_utc_now()),
            },
        )
        if (
            meeting_provider == MeetingProvider.GMEET
            and settings.enable_google_meet_authenticated_bot
        ):
            request.google_meet = GoogleMeetOptions(
                login_required=True,
                google_login_group_id=settings.recall_google_login_group_id,
            )

        if settings.recall_realtime_transcript_enabled:
            request.transcription_options = (
                RecallTranscriptOptionsFactory.generate_options(
                    provider=RecallTranscriptProvider.get(
                        settings.recall_realtime_transcript_provider
                    )
                )
            )

            request.real_time_transcription = RealtimeTranscription(
                destination_url=settings.recall_realtime_transcript_webhook_url
                + webhook_query_params,
                partial_results=settings.recall_realtime_partial_results,
                enhanced_diarization=False,
            )

        if settings.enable_recall_call_events:
            request.real_time_media = RealtimeMedia(
                webhook_call_events_destination_url=settings.recall_realtime_transcript_webhook_url
                + webhook_query_params
            )

        if settings.enable_recall_recording_mode_options:
            request.recording_mode_options = RecordingModeOptions(
                start_recording_on=StartRecordingOn.CALL_JOIN,
                participant_video_when_screenshare=VideoScreenShare.BESIDE,
            )

        logger.bind(
            organization_id=organization_id,
            meeting_id=meeting_id,
            scheduled_at=join_at,
            realtime_event_token=realtime_event_token,
        ).info("Creating meeting bot and transcript")
        transcript_provider = settings.recall_default_transcript_provider
        try:
            (
                meeting_bot,
                transcript,
            ) = await self.meeting_repository.create_meeting_bot_transcript_initial_rows(
                meeting_id=meeting_id,
                external_bot_id="",
                bot_provider=BotProvider.RECALLAI,
                meeting_bot_name=bot_name,
                transcript_provider=TranscriptProvider(transcript_provider),
                organization_id=organization_id,
                scheduled_at=join_at,
                realtime_event_token=realtime_event_token,
                meeting_url=meeting_url,
            )
        except (UniqueViolationError, IntegrityError):
            # If we get a unique constraint violation, try to fetch existing records as we could be retrying
            logger.bind(
                meeting_id=meeting_id,
                organization_id=organization_id,
            ).info("Unique constraint violation, fetching existing records")

            meeting_bots = (
                await self.meeting_repository.find_meeting_bots_by_meeting_url_and_time(
                    organization_id=organization_id,
                    meeting_id=meeting_id,
                    meeting_url=meeting_url,
                    scheduled_at=join_at,
                )
            )
            if not meeting_bots:
                raise
            meeting_bot = meeting_bots[0]
            if meeting_bot.status != MeetingBotStatus.PENDING:
                logger.bind(
                    meeting_id=meeting_id,
                    meeting_bot_id=meeting_bot.id,
                    meeting_bot_status=meeting_bot.status,
                ).warning("Meeting bot is not pending")
                raise
            # Fetch existing transcript by reference ID
            existing_transcript = (
                await self.meeting_repository.find_transcript_for_meeting_bot(
                    organization_id=organization_id,
                    meeting_bot_id=meeting_bot.id,
                )
            )
            if not existing_transcript:
                raise
            transcript = existing_transcript

        bot_id = await self.recallai_client_v1.create_bot(
            request=request,
            idempotency_key=f"{organization_id}-{meeting_url}-{meeting_bot.id}-{join_at}",
        )
        logger.bind(meeting_id=meeting_id, external_meeting_bot_id=bot_id).info(
            "Created recall bot"
        )

        updated_meeting_bot = not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=MeetingBot,
                organization_id=organization_id,
                primary_key_to_value={"id": meeting_bot.id},
                column_to_update={
                    "external_meeting_bot_id": bot_id,
                    "status": MeetingBotStatus.SCHEDULED,
                    "updated_at": zoned_utc_now(),
                },
            )
        )
        return updated_meeting_bot, transcript

    async def _update_recall_bot(
        self,
        meeting_url: str,
        bot_name: str,
        meeting_provider: MeetingProvider,
        join_at: ZoneRequiredDateTime,
        external_bot_id: str,
        organization_id: UUID,
        meeting_bot_id: UUID,
    ) -> MeetingBot:
        # TODO make this more patch like
        if meeting_provider not in (
            MeetingProvider.GMEET,
            MeetingProvider.ZOOM,
            MeetingProvider.TEAMS,
        ):
            raise InvalidArgumentError(
                f"Meeting provider {meeting_provider} is not supported"
            )
        request = UpdateRecallBotRequest(
            meeting_url=meeting_url,
            bot_name=bot_name,
            join_at=join_at,
        )
        logger.bind(request=request).info("Updating meeting bot")
        bot_id = await self.recallai_client_v1.update_bot(external_bot_id, request)

        meeting_bot = await self.meeting_repository.update_by_tenanted_primary_key(
            MeetingBot,
            organization_id=organization_id,
            primary_key_to_value={
                "id": meeting_bot_id,
            },
            column_to_update={
                "external_meeting_bot_id": bot_id,
                "scheduled_at": join_at,
                "name": bot_name,
                "updated_at": zoned_utc_now(),
            },
        )
        if not meeting_bot:
            raise IllegalStateError(f"Meeting bot not found with id={meeting_bot_id}")
        return meeting_bot

    async def _remove_scheduled_bot_from_meeting(
        self, meeting_bot: MeetingBot, raise_provider_error: bool
    ) -> bool:
        logger.bind(
            meeting_id=meeting_bot.meeting_id,
            meeting_bot_id=meeting_bot.id,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
        ).info("Deleting scheduled bot")
        try:
            await self.recallai_client_v1.delete_bot(
                bot_id=meeting_bot.external_meeting_bot_id
            )
        except ExternalServiceError as e:
            if e.return_http_code == status.HTTP_404_NOT_FOUND:
                logger.bind(
                    external_meeting_bot_id=meeting_bot.external_meeting_bot_id
                ).warning("No bot found in recall, likely already deleted")
                return True
            else:
                raise e
        except Exception as e:
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).error(f"Unable to remove bot from call: {e}", exc_info=e)
            if raise_provider_error:
                # In consent flow, we don't error out as we must return meeting url
                # to user.  But for the cancel flow, allow error.
                raise e
            else:
                return False
        return True

    async def remove_bot_from_meeting(
        self,
        meeting_bot: MeetingBot,
        is_consent_denied: bool = False,
        chat_notification_message: str | None = None,
    ) -> MeetingBot:
        logger.bind(
            meeting_id=meeting_bot.meeting_id,
            meeting_bot_id=meeting_bot.id,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            is_consent_denied=is_consent_denied,
            meeting_bot_status=meeting_bot.status,
        ).info("Removing bot from meeting")

        new_bot_status: MeetingBotStatus = meeting_bot.status
        if meeting_bot.status in [
            MeetingBotStatus.WAITING,
            MeetingBotStatus.JOINING,
            MeetingBotStatus.IN_CALL_PAUSED,
            MeetingBotStatus.IN_CALL_RECORDING,
            MeetingBotStatus.IN_CALL_RECORDING_DENIED,
        ]:
            removed = await self._remove_bot_from_meeting_with_message(
                meeting_bot=meeting_bot,
                chat_notification_message=chat_notification_message,
                raise_provider_error=(not is_consent_denied),
            )
            if removed:
                new_bot_status = MeetingBotStatus.EXITED
        elif meeting_bot.status == MeetingBotStatus.SCHEDULED:
            logger.info("Attempting to remove scheduled bot")
            removed = await self._remove_scheduled_bot_from_meeting(
                meeting_bot=meeting_bot, raise_provider_error=(not is_consent_denied)
            )
            if removed:
                new_bot_status = MeetingBotStatus.CANCELED
        else:
            logger.bind(meeting_bot_status=meeting_bot.status).warning(
                "Bot status requires no action"
            )

        if new_bot_status == meeting_bot.status:
            logger.bind(
                meeting_bot_id=meeting_bot.meeting_id,
                meeting_bot_status=meeting_bot.status,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                meeting_id=meeting_bot.meeting_id,
            ).info("Meeting bot not updated")
            return meeting_bot

        time_now = zoned_utc_now()
        column_to_update = {
            "updated_at": time_now,
            "status": new_bot_status,
        }
        if is_consent_denied:
            column_to_update["removed_at"] = time_now
            column_to_update["consent_declined_at"] = time_now
        if meeting_bot.status == MeetingBotStatus.SCHEDULED:
            column_to_update["deleted_at"] = time_now

        return not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=MeetingBot,
                organization_id=meeting_bot.organization_id,
                primary_key_to_value={"id": meeting_bot.id},
                column_to_update=column_to_update,
            )
        )

    async def _remove_bot_from_meeting_with_message(
        self,
        meeting_bot: MeetingBot,
        chat_notification_message: str | None,
        raise_provider_error: bool,
    ) -> bool:
        if chat_notification_message:
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                meeting_id=meeting_bot.meeting_id,
                external_meeting_bot=meeting_bot.external_meeting_bot_id,
            ).info("Sending bot termination message")
            try:
                await self.recallai_client_v1.send_message(
                    bot_id=meeting_bot.external_meeting_bot_id,
                    request=SendChatMessageRequest(message=chat_notification_message),
                )
            except Exception as e:
                logger.bind(
                    external_meeting_bot_id=meeting_bot.external_meeting_bot_id
                ).error(f"Unable to send bot termination message: {e}", exc_info=e)

            # If leaving right away, meeting platforms may obscure message/replace it right away with exit notification.
            # Non-block wait to give participants chance to read message.
            await asyncio.sleep(3)
        else:
            logger.info("No chat notification to send")

        logger.bind(
            meeting_bot_id=meeting_bot.id,
            meeting_id=meeting_bot.meeting_id,
            external_meeting_bot=meeting_bot.external_meeting_bot_id,
        ).info("Attempting to remove bot from call")
        try:
            await self.recallai_client_v1.leave_call(
                bot_id=meeting_bot.external_meeting_bot_id
            )
        except Exception as e:
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).error(f"Unable to remove bot from call: {e}", exc_info=e)
            if raise_provider_error:
                # In consent flow, we don't error out as we must return meeting url
                # to user.  But for the cancel flow, allow error.
                raise e
            else:
                return False

        return True

    async def _stop_bot_recording(
        self, meeting_bot: MeetingBot, raise_provider_error: bool
    ) -> bool:
        logger.bind(
            meeting_bot_id=meeting_bot.id,
            meeting_id=meeting_bot.meeting_id,
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            meeting_bot_status=meeting_bot.status,
        ).info("Stopping bot recording")
        try:
            await self.recallai_client_v1.stop_recording(
                bot_id=meeting_bot.external_meeting_bot_id
            )
            return True
        except Exception as e:
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).error(f"Unable to stop bot recording for call: {e}", exc_info=e)
            if raise_provider_error:
                raise e
            else:
                return False

    async def pause_bot_recording_with_message(
        self, meeting_bot: MeetingBot, chat_message: str, raise_provider_error: bool
    ) -> bool:
        try:
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                meeting_id=meeting_bot.meeting_id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                meeting_bot_status=meeting_bot.status,
            ).info("Pausing bot recording")
            await self.recallai_client_v1.pause_recording(
                bot_id=meeting_bot.external_meeting_bot_id
            )
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).info("Sending chat message")
            await self.recallai_client_v1.send_message(
                bot_id=meeting_bot.external_meeting_bot_id,
                request=SendChatMessageRequest(message=chat_message),
            )
            return True
        except Exception as e:
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).warning(f"Error invoking bot api: {e}", exc_info=e)
            if raise_provider_error:
                raise e
            else:
                return False

    async def resume_bot_recording_with_message(
        self, meeting_bot: MeetingBot, chat_message: str
    ) -> None:
        try:
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                meeting_id=meeting_bot.meeting_id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                meeting_bot_status=meeting_bot.status,
            ).info("Pausing bot recording")
            await self.recallai_client_v1.resume_recording(
                bot_id=meeting_bot.external_meeting_bot_id
            )
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).info("Sending chat message")
            await self.recallai_client_v1.send_message(
                bot_id=meeting_bot.external_meeting_bot_id,
                request=SendChatMessageRequest(message=chat_message),
            )
        except Exception as e:
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).warning(f"Error invoking bot api: {e}", exc_info=e)
            raise e

    async def start_bot_recording_with_message(
        self, meeting_bot: MeetingBot, chat_message: str
    ) -> None:
        try:
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                meeting_id=meeting_bot.meeting_id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                meeting_bot_status=meeting_bot.status,
            ).info("Pausing bot recording")
            await self.recallai_client_v1.start_recording(
                bot_id=meeting_bot.external_meeting_bot_id
            )
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).info("Sending chat message")
            await self.recallai_client_v1.send_message(
                bot_id=meeting_bot.external_meeting_bot_id,
                request=SendChatMessageRequest(message=chat_message),
            )
        except Exception as e:
            logger.bind(
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id
            ).warning(f"Error invoking bot api: {e}", exc_info=e)
            raise e

    async def halt_bot_recording_for_consent(
        self, meeting_bot: MeetingBot
    ) -> MeetingBot:
        """
        Stops recording for a bot due to declined consent.  Errors from bot provider
        are swallowed as this is for the consent flow, where we will have to return
        the meeting URL in order for the participant to join the meeting.
        """
        logger.bind(
            external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            meeting_id=meeting_bot.meeting_id,
            meeting_bot_id=meeting_bot.id,
        ).info("Pausing bot recording for call")

        result_bot_status = meeting_bot.status
        if meeting_bot.status == MeetingBotStatus.IN_CALL_RECORDING:
            result = await self.pause_bot_recording_with_message(
                meeting_bot=meeting_bot,
                chat_message="Not all participants consent to recording, so I will stop recording now.",
                raise_provider_error=False,
            )
            if result:
                result_bot_status = MeetingBotStatus.IN_CALL_PAUSED
        elif meeting_bot.status in [MeetingBotStatus.WAITING, MeetingBotStatus.JOINING]:
            # To prevent recording prior to bot joining meeting (where we configure auto
            # record upon joining), explicitly stop it.  Pausing in this state has no
            # effect, we must stop.
            result = await self._stop_bot_recording(
                meeting_bot=meeting_bot, raise_provider_error=False
            )
            if result:
                result_bot_status = MeetingBotStatus.IN_CALL_NOT_RECORDING
        else:
            logger.bind(meeting_bot_status=meeting_bot.status).warning(
                "Bot status requires no change for consent rejection"
            )

        if result_bot_status != meeting_bot.status:
            logger.bind(
                status=result_bot_status,
                meeting_bot_id=meeting_bot.id,
                meeting_id=meeting_bot.meeting_id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            ).info("Updating Meeting Bot status")
            time_now = zoned_utc_now()
            return not_none(
                await self.meeting_repository.update_by_tenanted_primary_key(
                    table_model=MeetingBot,
                    organization_id=meeting_bot.organization_id,
                    primary_key_to_value={"id": meeting_bot.id},
                    column_to_update={
                        "status": result_bot_status,
                        "updated_at": time_now,
                        "consent_declined_at": time_now,
                    },
                )
            )
        else:
            logger.bind(
                status=meeting_bot.status,
                meeting_bot_id=meeting_bot.id,
                meeting_id=meeting_bot.meeting_id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            ).info("Meeting bot update not required")
            return meeting_bot

    async def get_meeting_id_to_bot_dict(
        self, *, organization_id: UUID, meeting_ids: list[UUID]
    ) -> dict[UUID, list[MeetingBot]]:
        meeting_id_to_bot: dict[UUID, list[MeetingBot]] = defaultdict(list)
        meeting_bots = (
            await self.meeting_repository.find_meeting_bots_by_organization_id(
                organization_id=organization_id,
                meeting_ids=meeting_ids,
            )
        )
        for bot in meeting_bots:
            meeting_id_to_bot[bot.meeting_id].append(bot)
        return meeting_id_to_bot

    async def refresh_external_media_url(self, meeting_bot: MeetingBot) -> MeetingBot:
        """
        Gets the external media url for a meeting bot.  May refresh url from recall, which will invalidate previous ones.
        """
        recall_bot = await self.recallai_client_v1.get_bot(
            meeting_bot.external_meeting_bot_id
        )
        return not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=MeetingBot,
                organization_id=meeting_bot.organization_id,
                primary_key_to_value={"id": meeting_bot.id},
                column_to_update={
                    "media_url": str(recall_bot.video_url),
                    "updated_at": zoned_utc_now(),
                },
            )
        )


class SingletonMeetingBotService(Singleton, MeetingBotService):
    pass


def meeting_bot_service_general(db_engine: DatabaseEngine) -> MeetingBotService:
    return SingletonMeetingBotService(
        meeting_repository=MeetingRepository(engine=db_engine),
        meeting_s3_manager=get_s3_bucket_manager_by_bucket_name(
            settings.meeting_bucket_name,
        ),
        recallai_client_v1=RecallAIClientV1(
            recall_v1_api_key=settings.recall_v1_api_key,
            recall_v1_base_url=settings.recall_v1_base_url,
        ),
        file_service=file_service_from_engine(engine=db_engine),
    )


def meeting_bot_service_factory(request: Request) -> MeetingBotService:
    return meeting_bot_service_general(db_engine=get_db_engine(request))
