import uuid
from datetime import timed<PERSON><PERSON>
from typing import (
    Any,
    cast,
    override,
)
from uuid import UUID

from fastapi import Request
from temporalio.exceptions import WorkflowAlreadyStartedError

from salestech_be.common.exception import (
    ConflictResourceError,
    IllegalStateError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.exception.exception import ForbiddenError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.query_util.legacy.filter_schema import StandardValueFilter
from salestech_be.common.singleton import Singleton
from salestech_be.common.stats.metric import custom_metric
from salestech_be.common.type.patch_request import (
    UNSET,
    BasePatchRequest,
    specified,
    specified_or_default,
)
from salestech_be.core.activity.service.activity_service import (
    ActivityService,
    get_activity_service_general,
)
from salestech_be.core.common.domain_service import AllowedUsers, DomainService
from salestech_be.core.common.types import UserAuthContext
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.service.contact_resolve_service import (
    ContactResolveService,
    get_contact_resolve_service,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
    get_custom_object_service,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    get_domain_crm_association_service,
)
from salestech_be.core.domain_crm_association.types import (
    CreateMeetingCrmAssociation,
)
from salestech_be.core.extraction_prompt.type.shared_type import Feature
from salestech_be.core.job.service.job_service import (
    JobService,
    job_service_from_engine,
)
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.dto.meeting_insight_dto import InsightDTO
from salestech_be.core.meeting.meeting_ai_rec_service import (
    MeetingAIRecService,
    PatchMeetingSalesActionsRequest,
    get_meeting_ai_rec_service,
)
from salestech_be.core.meeting.meeting_bot_service import (
    MeetingBotService,
    meeting_bot_service_general,
)
from salestech_be.core.meeting.meeting_crm_association import (
    _MeetingCrmAssociationMapper,
)
from salestech_be.core.meeting.meeting_insight_service import (
    MeetingInsightService,
    meeting_insight_service_factory_general,
)
from salestech_be.core.meeting.meeting_service_mapper import _MeetingServiceMapper
from salestech_be.core.meeting.meeting_stats_service import (
    MeetingStatsService,
    get_meeting_stats_service,
)
from salestech_be.core.meeting.service.meeting_query_service import (
    MeetingQueryService,
    get_meeting_query_service,
)
from salestech_be.core.meeting.service.meeting_reference_type_strategy import (
    _ReferenceTypeStrategyFactory,
    get_meeting_reference_type_strategy_factory_db_engine,
)
from salestech_be.core.meeting.service_type import (
    ContactSummary,
    MeetingEventFieldUpdateRequest,
    MeetingFSM,
    MeetingMetrics,
    RecallStatusCode,
)
from salestech_be.core.meeting.types.meeting_types_v2 import (
    MeetingV2,
    SubServiceMeetingPermission,
)
from salestech_be.core.meeting.utils import (
    calculate_bot_join_time,
    get_meeting_bot_name,
)
from salestech_be.core.opportunity_stage_criteria.criteria_types import (
    CriteriaExtractionSourceObjectId,
    CriteriaExtractionSourceObjectType,
)
from salestech_be.core.organization.service.organization_service import (
    OrganizationService,
)
from salestech_be.core.pipeline.service.pipeline_query_service import (
    PipelineQueryService,
    get_pipeline_query_service,
)
from salestech_be.core.sequence.service.sequence_enrollment_query_service import (
    SequenceEnrollmentQueryService,
    get_sequence_enrollment_query_service_by_db_engine,
)
from salestech_be.core.task.service.task_v2_service import (
    TaskV2Service,
    get_task_v2_service_general,
)
from salestech_be.core.transcript.transcript_service import (
    TranscriptService,
    transcript_service_from_engine,
)
from salestech_be.core.transcript.types import (
    AssemblyCreateTranscriptRequest,
    AssemblyProcessTranscriptRequest,
    RecallCompleteTranscriptRequest,
    RecallCreateTranscriptRequest,
    RecallLoadTranscriptRequest,
    RecallProcessTranscriptRequest,
    TranscriptContainer,
    UpdateTranscriptProviderRequest,
)
from salestech_be.core.transcript.utils import s3_file_key_prefix
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.core.user.types import UserDTO
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dao.user_calendar_repository import UserCalendarRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.activity import (
    ActivitySubType,
)
from salestech_be.db.models.meeting import (
    BotProvider,
    BotStatusEvent,
    BotStatusHistory,
    Meeting,
    MeetingAttendee,
    MeetingBot,
    MeetingBotStatus,
    MeetingCancelReason,
    MeetingInvitee,
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingStats,
    MeetingStatus,
    MeetingUpdate,
    RecallMeetingParticipants,
    RecallRecording,
    RecallRecordingData,
    TranscriptProvider,
)
from salestech_be.db.models.task import (
    TaskPriority,
    TaskSourceType,
    TaskStatus,
    TaskType,
)
from salestech_be.db.models.transcript import Transcript, TranscriptReferenceIdType
from salestech_be.integrations.s3.s3_bucket_manager import (
    S3BucketManager,
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import (
    MEETING_TASK_QUEUE,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.temporal.workflows.meeting.clear_meeting_bots import (
    ClearFutureMeetingBotsInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    ClearFutureMeetingBotsWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.temporal.workflows.meeting.meeting_agent import (
    MeetingAgentWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.temporal.workflows.meeting.meeting_recording import (
    MeetingRecordingWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    MeetingRecordingWorkflowData,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import (
    not_none,
)
from salestech_be.web.api.common.container import (
    DeleteEntityResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.meeting.schema import (
    AttachBotRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    Attendee,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateMeetingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    EndMeetingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    ImportBotRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    ImportMeetingFromExternalRecordingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    Invitee,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    MeetingEventConferencing,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    MeetingReferenceId,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchMeetingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    RecallBotEventData,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.task.schema import (
    CreateTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)


class MeetingService(DomainService[MeetingV2]):
    """
    Service for Meeting FSM.
    """

    def __init__(
        self,
        meeting_repository: MeetingRepository,
        meeting_s3_manager: S3BucketManager,
        call_recording_s3: S3BucketManager,
        meeting_bot_service: MeetingBotService,
        meeting_insight_service: MeetingInsightService,
        user_service: UserService,
        task_v2_service: TaskV2Service,
        activity_service: ActivityService,
        transcript_service: TranscriptService,
        contact_query_service: ContactQueryService,
        contact_resolve_service: ContactResolveService,
        user_calendar_repository: UserCalendarRepository,
        custom_object_service: CustomObjectService,
        meeting_query_service: MeetingQueryService,
        meeting_stats_service: MeetingStatsService,
        job_service: JobService,
        reference_type_strategy_factory: _ReferenceTypeStrategyFactory,
        pipeline_query_service: PipelineQueryService,
        sequence_enrollment_query_service: SequenceEnrollmentQueryService,
        domain_crm_association_service: DomainCRMAssociationService,
        meeting_ai_rec_service: MeetingAIRecService,
    ) -> None:
        self.meeting_repository = meeting_repository
        self.meeting_s3_manager = meeting_s3_manager
        self.call_recording_s3 = call_recording_s3
        self.meeting_bot_service = meeting_bot_service
        self.meeting_insight_service = meeting_insight_service
        self.user_service = user_service
        self.task_v2_service = task_v2_service
        self.activity_service = activity_service
        self.transcript_service = transcript_service
        self.contact_query_service = contact_query_service
        self.contact_resolve_service = contact_resolve_service
        self.user_calendar_repository = user_calendar_repository
        self.custom_object_service = custom_object_service
        self.meeting_query_service = meeting_query_service
        self.meeting_stats_service = meeting_stats_service
        self.job_service = job_service
        self.meeting_fsm = MeetingFSM()
        self.reference_type_strategy_factory = reference_type_strategy_factory
        self.pipeline_query_service = pipeline_query_service
        self.sequence_enrollment_query_service = sequence_enrollment_query_service
        self.domain_crm_association_service = domain_crm_association_service
        self.meeting_ai_rec_service = meeting_ai_rec_service

    """
    Meeting Event handlers

    Functions that handle signals from outside parties (e.g. meeting bots) and
    dispatches updates as needed.  Meeting service is the coordinator among all involved
    services, so even though events may be specific to other domains such as bots, the
    events flow through here.  For example we may have an event that requires meeting
    modifications, bot updates, and well as transcript activities - all of which are
    dispatched from this service.
    """

    async def list_meetings_by_ids_untenanted(
        self, meeting_ids: list[UUID], exclude_deleted_or_archived: bool = True
    ) -> list[Meeting]:
        return await self.meeting_repository.list_meetings_by_ids_untenanted(
            meeting_ids, exclude_deleted_or_archived=exclude_deleted_or_archived
        )

    async def bot_joining_event(self, event: str, data: RecallBotEventData) -> None:
        # Meeting status does not change, just take care of bot update
        logger.bind(event=event, external_bot_id=data.bot_id).info("Bot joining event")
        await self.meeting_bot_service.bot_joining_event(
            data=data,
        )

    async def bot_in_waiting_room_event(
        self, event: str, data: RecallBotEventData
    ) -> MeetingDto | None:
        # Update bot status to waiting, meeting is now considered active
        logger.bind(event=event, data=data.model_dump_json()).info(
            "In waiting room bot event",
        )
        updated_meeting_bot = await self.meeting_bot_service.bot_in_waiting_room_event(
            data
        )
        if not updated_meeting_bot:
            return None

        updated_meeting = not_none(
            await self._update_meeting_status_for_bot_event(
                external_bot_id=data.bot_id,
                expected_meeting_status=MeetingStatus.SCHEDULED,
                new_meeting_status=MeetingStatus.ACTIVE,
                field_update_request=MeetingEventFieldUpdateRequest(
                    started_at=zoned_utc_now()
                ),
            )
        )

        return self._populate_meeting_dto_for_webhook_response(
            meeting=updated_meeting, meeting_bot=updated_meeting_bot
        )

    async def bot_in_call_recording_event(
        self,
        event: str,
        data: RecallBotEventData,
    ) -> MeetingDto | None:
        # Meeting status does not change, but bot status may
        logger.bind(event=event, data=data.model_dump_json()).info(
            "In call recording bot event",
        )

        updated_meeting_bot = (
            await self.meeting_bot_service.bot_in_call_recording_event(data=data)
        )
        if not updated_meeting_bot:
            return None

        meeting = not_none(
            await self.meeting_repository.find_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=updated_meeting_bot.organization_id,
                id=updated_meeting_bot.meeting_id,
            )
        )
        return self._populate_meeting_dto_for_webhook_response(
            meeting=meeting, meeting_bot=updated_meeting_bot
        )

    async def bot_recording_permission_denied(
        self, event: str, data: RecallBotEventData
    ) -> MeetingDto | None:
        # Update bot state to reflect denied permission.  Meeting is unchanged (still
        # considered active).
        logger.bind(event=event, data=data.model_dump_json()).info(
            "Recording permission denied bot event",
        )
        updated_meeting_bot = (
            await self.meeting_bot_service.bot_recording_permission_denied(data=data)
        )
        if not updated_meeting_bot:
            return None

        meeting = not_none(
            await self.meeting_repository.find_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=updated_meeting_bot.organization_id,
                id=updated_meeting_bot.meeting_id,
            )
        )
        return self._populate_meeting_dto_for_webhook_response(
            meeting=meeting, meeting_bot=updated_meeting_bot
        )

    async def bot_call_ended_event(
        self, event: str, data: RecallBotEventData
    ) -> MeetingDto | None:
        # Update bot state to reflect exited status, and Meeting as it is no longer
        # considered active.  We also record who attended the meeting in both the bot
        # and meeting models.
        # TODO handle case where bot is kicked out vs. call ended regularly
        logger.bind(event=event, data=data.model_dump_json()).info(
            "Call ended bot event",
        )

        meeting_bot = await self.meeting_bot_service.bot_call_ended_event(
            data=data,
        )
        if not meeting_bot:
            return None

        meeting = await self.meeting_repository.get_meeting_for_external_bot_id(
            external_meeting_bot_id=data.bot_id, bot_provider=BotProvider.RECALLAI
        )
        if not meeting:
            logger.bind(external_bot_id=data.bot_id).error(
                "No meeting found for external bot id"
            )
            return None

        meeting_attendees = None

        contact_ids_to_summary, user_ids_to_dto = await self._get_contact_and_user_maps(
            meeting_invitees=meeting.invitees, organization_id=meeting.organization_id
        )
        try:
            meeting_attendees = _MeetingServiceMapper.map_bot_participants_to_meeting_attendees(
                invitees=meeting.invitees,
                bot_participants=meeting_bot.meeting_participant.meeting_participants
                if meeting_bot.meeting_participant
                else [],
                contact_ids_to_summary=contact_ids_to_summary,
                user_ids_to_dto=user_ids_to_dto,
            )
            logger.bind(
                meeting_id=meeting.id, organization_id=meeting.organization_id
            ).info(
                f"Determined meeting participants.  Invitees: {meeting.invitees}.  Attendees: {meeting.attendees}.  Bot participant: {meeting_bot.meeting_participant}"
            )
        except Exception as e:
            logger.bind(
                meeting_id=meeting.id, organization_id=meeting.organization_id
            ).error("Failed to determine meeting participants", exc_info=e)

        try:
            updated_meeting_dto, _ = await self._end_active_meeting(
                meeting=meeting,
                meeting_bot=meeting_bot,
                organization_id=meeting.organization_id,
                attendees=meeting_attendees,
            )
        except IllegalStateError:
            logger.bind(
                organization_id=meeting.organization_id, meeting_id=meeting.id
            ).info("Unable to update meeting due to bot ending")
            return None

        logger.bind(
            meeting_id=updated_meeting_dto.meeting_id,
            organization_id=updated_meeting_dto.organization_id,
            previoius_status=meeting.status,
        ).info("Updated meeting to ended state")
        return updated_meeting_dto

    async def bot_done_event(
        self, event: str, data: RecallBotEventData
    ) -> MeetingDto | None:
        # After updating bot state and history, if media and transcript are available,
        # trigger analysis.
        logger.bind(event=event, data=data.model_dump_json()).info(
            "Handling Done status event",
        )
        meeting_bot = await self.meeting_bot_service.bot_done_event(data)
        if not meeting_bot:
            return None

        if meeting_bot.external_media_url is None:
            logger.bind(external_bot_id=data.bot_id).info(
                "Media URL not found for external bot id",
            )
            return None

        organization_id = meeting_bot.organization_id
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            Meeting,
            organization_id=organization_id,
            id=meeting_bot.meeting_id,
        )

        if meeting is None:
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                organization_id=meeting_bot.organization_id,
            ).error(
                "Meeting not found for MeetingBot",
            )
            return None

        transcript = await self.transcript_service.get_transcript_record(
            organization_id=organization_id,
            request=RecallLoadTranscriptRequest(
                # transcriber_provider may not match transcript.provider from DB
                transcript_provider=TranscriptProvider.ASSEMBLYAI,
                meeting_bot_id=meeting_bot.id,
            ),
        )

        if not transcript:
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                organization_id=meeting_bot.organization_id,
            ).error("No transcript found for bot")
            return None

        updated_meeting, _ = await self.analyze_from_bot_media(
            organization_id=organization_id,
            meeting=meeting,
            meeting_bot=meeting_bot,
            transcript=transcript,
        )
        if not updated_meeting:
            logger.bind(
                meeting_id=meeting.id, organization_id=meeting_bot.organization_id
            ).error("Meeting not analyzed")
            return None

        return self._populate_meeting_dto_for_webhook_response(
            meeting=updated_meeting, meeting_bot=meeting_bot
        )

    async def _get_meeting_custom_words(self, meeting: Meeting) -> list[str] | None:
        if not settings.enable_transcript_recall_custom_words:
            return None

        try:
            primary_account_name_by_contact_id = await self.contact_query_service.find_primary_account_company_name_for_contact_ids(
                contact_ids={
                    invitee.contact_id
                    for invitee in meeting.invitees_or_empty_list()
                    if invitee.contact_id
                },
                organization_id=meeting.organization_id,
            )
            # TODO add account level fields
            valid_names = set()
            for company_name in primary_account_name_by_contact_id.values():
                if custom_word := OrganizationService.extract_custom_word_value(
                    company_name
                ):
                    valid_names.add(custom_word)

            return list(valid_names)

        except Exception as e:
            logger.bind(
                meeting_id=meeting.id, organization_id=meeting.organization_id
            ).error("Failed to get meeting custom words", exc_info=e)
            return None

    async def analyze_from_bot_media(
        self,
        organization_id: UUID,
        meeting: Meeting,
        meeting_bot: MeetingBot,
        transcript: Transcript,
    ) -> tuple[Meeting, Transcript] | tuple[None, None]:
        logger.bind(
            organization_id=organization_id,
            meeting_id=meeting.id,
            external_bot_id=meeting_bot.external_meeting_bot_id,
        ).info("Triggering analysis for meeting")
        desired_provider = await self.transcript_service.get_desired_provider(
            organization_id
        )
        if transcript.provider != desired_provider:
            # Transcript records are created ahead of time and may be out-of-date
            logger.bind(
                organization_id=organization_id,
                meeting_id=meeting.id,
                external_bot_id=meeting_bot.external_meeting_bot_id,
                transcript_id=transcript.id,
                old_provider=transcript.provider,
                new_provider=desired_provider,
            ).info(
                f"Persisted transcript provider out-of-date and will be updated from {transcript.provider} to {desired_provider}"
            )
            transcript = await self.transcript_service.update_transcript_provider(
                organization_id=organization_id,
                request=UpdateTranscriptProviderRequest(
                    transcript_id=transcript.id, new_provider=desired_provider
                ),
            )

        match transcript.provider:
            case (
                TranscriptProvider.ASSEMBLYAI
                | TranscriptProvider.GLADIA
                | TranscriptProvider.SPEECHMATICS
            ):
                recording = (
                    meeting_bot.recording_metadata.recordings[0]
                    if meeting_bot.recording_metadata
                    and meeting_bot.recording_metadata.recordings
                    else None
                )

                result_transcript = (
                    await self.transcript_service.initiate_transcription(
                        organization_id=organization_id,
                        request=RecallCreateTranscriptRequest(
                            transcript_provider=transcript.provider,
                            recording_started_at=recording.started_at
                            if recording
                            else None,
                            recording_ended_at=recording.completed_at
                            if recording
                            else None,
                            speakers_expected=len(meeting.invitees)
                            if meeting.invitees
                            else None,
                            bot_id=str(meeting_bot.external_meeting_bot_id),
                            transcript_id=transcript.id,
                            meeting_custom_words=await self._get_meeting_custom_words(
                                meeting
                            ),
                        ),
                    )
                )
            case _:
                logger.bind(
                    organization_id=organization_id,
                    meeting_id=meeting.id,
                    meeting_bot_id=meeting_bot.id,
                ).error(f"Transcript provider {transcript.provider} not supported")
                return None, None

        logger.bind(meeting_id=meeting.id, organization_id=organization_id).info(
            "Updating meeting to analyzing status"
        )
        updated_meeting = not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=organization_id,
                primary_key_to_value={"id": meeting.id},
                column_to_update={
                    "status": MeetingStatus.ANALYZING,
                    "updated_at": zoned_utc_now(),
                },
            )
        )
        self._emit_state_change_metric(
            pre_update_meeting=meeting, updated_meeting=updated_meeting
        )
        prefix = s3_file_key_prefix(
            organization_id=organization_id,
            transcript_provider=transcript.provider,
            transcript_id=transcript.id,
        )
        temporal_client = await get_temporal_client()
        s3_title = S3BucketManager.convert_title_and_date_to_s3_file_name(
            title=updated_meeting.title,
            date=updated_meeting.starts_at,
        )
        await temporal_client.start_workflow(
            MeetingRecordingWorkflow.run,
            args=[
                MeetingRecordingWorkflowData(
                    external_media_url=not_none(meeting_bot.external_media_url),
                    s3_key_prefix=prefix,
                    s3_title=s3_title,
                    external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                    organization_id=organization_id,
                ),
            ],
            id=f"meeting_recording_{meeting_bot.id}",
            task_queue=MEETING_TASK_QUEUE,
        )
        return updated_meeting, result_transcript

    async def bot_analysis_done_event(
        self, event: str, data: RecallBotEventData
    ) -> MeetingDto | None:
        # Complete transcript
        # After this we are eligible to switch meeting to completed (and handle
        # insights), but this is triggered from the call site in meeting_webhook_service
        # to avoid circular dependency.
        logger.bind(event=event, data=data.model_dump_json()).info(
            "Handling Analysis Done event",
        )

        # Use transcript_service to save the raw, now completed/available transcript
        meeting_bot = await self.meeting_bot_service.bot_analysis_done_event(data)
        if not meeting_bot:
            logger.bind(external_meeting_bot_id=data.bot_id).error(
                "No bot found for event"
            )
            return None

        transcript = await self.transcript_service.complete_transcription(
            request=RecallCompleteTranscriptRequest(
                transcript_provider=TranscriptProvider.ASSEMBLYAI,
                meeting_bot_id=meeting_bot.id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            )
        )

        if not transcript:
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                organization_id=meeting_bot.organization_id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            ).error("No completed raw transcript for meeting bot")
            return None

        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=meeting_bot.organization_id,
            id=meeting_bot.meeting_id,
        )
        if not meeting:
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                organization_id=meeting_bot.organization_id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
            ).error("No meeting found")
            return None

        return self._populate_meeting_dto_for_webhook_response(
            meeting=meeting, meeting_bot=meeting_bot
        )

    @staticmethod
    async def bot_analysis_failed_event(
        event: str,
        data: RecallBotEventData,
    ) -> None:
        # TODO: Need to track analysis failed status inside the Transcript table
        logger.bind(event=event, data=data.model_dump_json()).error(
            "Bot Analysis Failed status event",
        )

    async def bot_default_event_handler(
        self, event: str, data: RecallBotEventData
    ) -> None:
        logger.bind(event=event, data=data.model_dump_json()).info(
            "Default event handler",
        )
        await self.meeting_repository.append_bot_status_history(
            external_meeting_bot_id=data.bot_id,
            bot_provider=BotProvider.RECALLAI,
            status_event=data.get_db_status_event(),
        )

    async def _update_meeting_status_for_bot_event(
        self,
        external_bot_id: str,
        expected_meeting_status: MeetingStatus,
        new_meeting_status: MeetingStatus,
        field_update_request: MeetingEventFieldUpdateRequest,
    ) -> Meeting | None:
        meeting = await self.meeting_repository.get_meeting_for_external_bot_id(
            external_meeting_bot_id=external_bot_id, bot_provider=BotProvider.RECALLAI
        )
        if not meeting:
            logger.bind(external_bot_id=external_bot_id).error(
                "No bot found for external bot id"
            )
            return None

        result_meeting = meeting
        if (
            meeting.status != expected_meeting_status
            and not self.meeting_fsm.is_valid_progression(
                current_state=meeting.status, end_state=new_meeting_status
            )
        ):
            logger.bind(
                organization_id=meeting.organization_id,
                meeting_id=meeting.id,
                meeting_status=meeting.status,
                expected_meeting_status=expected_meeting_status,
                new_meeting_status=new_meeting_status,
            ).warning("Unsupported meeting state transition, skipping update")
        else:
            if meeting.status != expected_meeting_status:
                logger.bind(
                    organization_id=meeting.organization_id,
                    meeting_id=meeting.id,
                    meeting_status=meeting.status,
                    expected_meeting_status=expected_meeting_status,
                    new_meeting_status=new_meeting_status,
                ).warning("Unexpected order for meeting bot state transition")
            else:
                logger.bind(
                    organization_id=meeting.organization_id,
                    meeting_id=meeting.id,
                    meeting_status=meeting.status,
                    expected_meeting_status=expected_meeting_status,
                    new_meeting_status=new_meeting_status,
                ).info("Updating meeting status")

            fields_to_update = {
                k: v for k, v in field_update_request.dict().items() if v != UNSET
            }
            fields_to_update["status"] = new_meeting_status
            fields_to_update["updated_at"] = zoned_utc_now()
            result_meeting = not_none(
                await self.meeting_repository.update_by_tenanted_primary_key(
                    table_model=Meeting,
                    organization_id=meeting.organization_id,
                    primary_key_to_value={"id": meeting.id},
                    column_to_update=fields_to_update,
                )
            )
            self._emit_state_change_metric(
                pre_update_meeting=meeting, updated_meeting=result_meeting
            )
        return result_meeting

    async def complete_analyze_meeting_by_reference_id(
        self,
        reference_id: str,
        reference_id_type: MeetingReferenceIdType,
        organization_id: UUID,
    ) -> tuple[list[InsightDTO], Meeting]:
        logger.bind(
            reference_id=reference_id,
            reference_id_type=reference_id_type,
            organization_id=organization_id,
        ).info("Completing analyzing meeting by reference id")
        meeting = await self._lookup_meeting_by_reference_id_and_type_or_error(
            reference_id=reference_id,
            reference_id_type=reference_id_type,
            organization_id=organization_id,
        )
        return await self._complete_analyze_meeting(
            meeting=meeting, organization_id=organization_id
        )

    async def complete_analyze_meeting(
        self, meeting_id: UUID, organization_id: UUID
    ) -> tuple[list[InsightDTO], Meeting]:
        logger.bind(
            meeting_id=meeting_id,
            organization_id=organization_id,
        ).info("Completing analyzing meeting")
        meeting_dto = await self.get_meeting(
            meeting_id=meeting_id, organization_id=organization_id
        )
        return await self._complete_analyze_meeting(
            meeting=meeting_dto.meeting, organization_id=organization_id
        )

    async def get_meeting_transcript(
        self, meeting: Meeting, organization_id: UUID
    ) -> tuple[Transcript, TranscriptContainer]:
        return await self.transcript_service.get_processed_transcript(
            organization_id=organization_id,
            request=await self.reference_type_strategy_factory.get_instance(
                reference_id_type=meeting.reference_id_type
            ).get_load_transcript_request(meeting=meeting),
        )

    async def _complete_analyze_meeting(
        self, meeting: Meeting, organization_id: UUID
    ) -> tuple[list[InsightDTO], Meeting]:
        """
        Analyze the transcript for the given meeting_id and organization_id.
        This function should always return a TranscriptConfig for each ExtractionSection
        even if the extraction is empty.

        This involves external calls for processing a raw transcript and analyzing it
        to extract insights.  Call sites should invoke this via task and not
        synchronously.
        """

        # Form or get existing processed transcript
        (
            transcript,
            transcript_container,
        ) = await self.transcript_service.process_completed_transcript(
            organization_id=organization_id,
            request=await self.reference_type_strategy_factory.get_instance(
                meeting.reference_id_type
            ).get_process_transcript_request(meeting=meeting),
        )
        if not transcript or not transcript_container:
            raise IllegalStateError("No processed transcript for meeting")
        else:
            logger.bind(
                organization_id=organization_id,
                transcript_id=transcript.id,
                transcript_reference_id=transcript.reference_id,
                transcript_reference_id_type=transcript.reference_id_type,
            ).info("Found transcript for completing meeting")

        # Use processed transcript to generate stats if not already generated
        if not meeting.meeting_stats_id:
            try:
                meeting_stats = await self.meeting_stats_service.generate_meeting_stats(
                    meeting=meeting,
                    transcript_container=transcript_container,
                    organization_id=organization_id,
                )
                updated_meeting = not_none(
                    await self.meeting_repository.update_by_tenanted_primary_key(
                        Meeting,
                        organization_id=organization_id,
                        primary_key_to_value={"id": meeting.id},
                        column_to_update=MeetingUpdate(
                            meeting_stats_id=meeting_stats.id
                        ),
                    )
                )
                logger.bind(
                    meeting_id=updated_meeting.id, meeting_stats_id=meeting_stats.id
                ).info("Updated meeting with stats")
            except Exception as e:
                logger.bind(
                    organization_id=organization_id,
                    transcript_id=transcript.id,
                    meeting_id=meeting.id,
                ).error("Failed to generate meeting stats", exc_info=e)

        # Update sales action types and roles for meeting
        is_sales_action_role_classification_enabled = (
            settings.enable_sales_action_role_classification
            and (
                str(organization_id)
                in settings.enable_sales_action_role_classification_org_ids
                or settings.enable_sales_action_role_classification_org_ids == []
            )
        )
        if meeting.pipeline_id and is_sales_action_role_classification_enabled:
            try:
                await self.start_sales_action_role_classification_workflow(
                    organization_id=organization_id,
                    pipeline_id=meeting.pipeline_id,
                    source_object=CriteriaExtractionSourceObjectId(
                        object_type=CriteriaExtractionSourceObjectType.MEETING,
                        object_id=meeting.id,
                    ),
                )
            except WorkflowAlreadyStartedError:
                logger.bind(
                    organization_id=organization_id,
                    pipeline_id=meeting.pipeline_id,
                    meeting_id=meeting.id,
                ).info("Workflow already started, ignoring for retry")

        # Use processed transcript to get insights
        insight_dtos = (
            await self.meeting_insight_service.extract_insights_from_transcript(
                organization_id=organization_id,
                meeting=meeting,
                transcript_container=transcript_container,
            )
        )

        # Transition meeting to completed
        logger.bind(meeting_id=meeting.id).info("Updating meeting to completed status")
        now = zoned_utc_now()
        updated_meeting = not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=organization_id,
                primary_key_to_value={"id": meeting.id},
                column_to_update={
                    "status": MeetingStatus.COMPLETED,
                    "updated_at": now,
                    "completed_at": now,
                },
            )
        )
        self._emit_state_change_metric(
            pre_update_meeting=meeting, updated_meeting=updated_meeting
        )
        if updated_meeting.ended_at:
            custom_metric.timing(
                metric_name=MeetingMetrics.COMPLETED_TIMING_METRIC_NAME,
                value=(now - updated_meeting.ended_at).seconds,
            )
        else:
            logger.bind(
                meeting_id=updated_meeting.id,
                organization_id=organization_id,
            ).warning(
                "Meeting ended at is missing, cannot emit completed timing metric"
            )

        return insight_dtos, updated_meeting

    async def get_meeting_stats_by_meeting_id(
        self, meeting_id: UUID, organization_id: UUID
    ) -> MeetingStats:
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting, organization_id=organization_id, id=meeting_id
        )
        if not meeting:
            raise ResourceNotFoundError(f"No meeting found with id {meeting_id}")

        if not meeting.meeting_stats_id:
            raise ResourceNotFoundError(
                f"No meeting stats found for meeting {meeting_id}"
            )

        return await self.meeting_repository.find_by_tenanted_primary_key_or_fail(
            MeetingStats, organization_id=organization_id, id=meeting.meeting_stats_id
        )

    async def authed_get_meeting_stats_by_meeting_id(
        self,
        user_auth_context: UserAuthContext,
        meeting_id: UUID,
    ) -> MeetingStats:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=meeting_id,
            access_check_function=self.can_access_entity_for_read,
        )

        return await self.get_meeting_stats_by_meeting_id(
            meeting_id=meeting_id, organization_id=user_auth_context.organization_id
        )

    async def process_transcript(
        self, meeting_id: UUID, organization_id: UUID
    ) -> tuple[Transcript, TranscriptContainer] | tuple[None, None]:
        logger.bind(meeting_id=meeting_id).debug("Getting transcript for meeting")
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting, organization_id=organization_id, id=meeting_id
        )
        if not meeting:
            raise ResourceNotFoundError("No meeting found")

        if meeting.reference_id_type == MeetingReferenceIdType.VOICE_V2:
            return await self.transcript_service.process_completed_transcript(
                organization_id=organization_id,
                request=AssemblyProcessTranscriptRequest(
                    transcript_provider=TranscriptProvider.ASSEMBLYAI_DIRECT,
                    reference_id=meeting.reference_id,
                    reference_id_type=TranscriptReferenceIdType.VOICE_V2,
                    reprocess_raw_transcript=True,
                ),
            )

        # Recall only for this path
        meeting_bot = (
            await self.meeting_bot_service.get_latest_meeting_bot_by_meeting_id(
                meeting_id=meeting_id, organization_id=organization_id
            )
        )
        if not meeting_bot:
            raise ResourceNotFoundError("No meeting bot found")

        return await self.transcript_service.process_completed_transcript(
            organization_id=organization_id,
            request=RecallProcessTranscriptRequest(
                transcript_provider=TranscriptProvider.ASSEMBLYAI,
                meeting_bot_id=meeting_bot.id,
                external_meeting_bot_id=meeting_bot.external_meeting_bot_id,
                reprocess_raw_transcript=True,
            ),
        )

    async def authed_get_transcript_by_meeting_id(
        self, user_auth_context: UserAuthContext, meeting_id: UUID
    ) -> tuple[Transcript, TranscriptContainer, Meeting]:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=meeting_id,
            access_check_function=self.can_access_entity_for_read,
        )
        return await self.get_transcript_by_meeting_id(
            meeting_id=meeting_id, organization_id=user_auth_context.organization_id
        )

    async def get_transcript_by_meeting_id(
        self, meeting_id: UUID, organization_id: UUID
    ) -> tuple[Transcript, TranscriptContainer, Meeting]:
        logger.bind(meeting_id=meeting_id).debug("Getting transcript for meeting")
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting, organization_id=organization_id, id=meeting_id
        )
        if not meeting:
            raise ResourceNotFoundError("No meeting found")

        meeting_row = not_none(meeting)
        (
            transcript_row,
            transcript_container,
        ) = await self.transcript_service.get_processed_transcript(
            organization_id=organization_id,
            request=await self.reference_type_strategy_factory.get_instance(
                reference_id_type=meeting_row.reference_id_type
            ).get_load_transcript_request(meeting=meeting_row),
        )
        return transcript_row, transcript_container, meeting_row

    async def analyze_ended_meeting(
        self,
        reference_id: MeetingReferenceId,
        reference_id_type: MeetingReferenceIdType,
        organization_id: UUID,
        media_url: str,
    ) -> MeetingDto:
        logger.bind(
            reference_id=reference_id,
            reference_id_type=reference_id_type,
            organization_id=organization_id,
        ).info("Analyzing ended meeting")
        meeting = await self._lookup_meeting_by_reference_id_and_type_or_error(
            reference_id=reference_id,
            reference_id_type=reference_id_type,
            organization_id=organization_id,
        )
        if meeting.status != MeetingStatus.ENDED:
            logger.bind(
                meeting_id=meeting.id,
                meeting_status=meeting.status,
                organization_id=organization_id,
            ).warning("Meeting in unexpected status for analyzing")
            raise IllegalStateError(
                "Unable to analyze meeting - invalid current state for analysis"
            )

        if reference_id_type == MeetingReferenceIdType.VOICE:
            await self.transcript_service.initiate_transcription(
                organization_id=organization_id,
                request=AssemblyCreateTranscriptRequest(
                    transcript_provider=TranscriptProvider.ASSEMBLYAI_DIRECT,
                    audio_url=media_url,
                    reference_id=str(reference_id),
                    reference_id_type=TranscriptReferenceIdType.VOICE,
                    meeting_custom_words=None,
                ),
            )
        elif reference_id_type == MeetingReferenceIdType.VOICE_V2:
            await self.transcript_service.initiate_transcription(
                organization_id=organization_id,
                request=AssemblyCreateTranscriptRequest(
                    transcript_provider=TranscriptProvider.ASSEMBLYAI_DIRECT,
                    audio_url=media_url,
                    reference_id=str(reference_id),
                    reference_id_type=TranscriptReferenceIdType.VOICE_V2,
                    meeting_custom_words=await self._get_meeting_custom_words(meeting),
                ),
            )
        elif reference_id_type == MeetingReferenceIdType.EXTERNAL_RECORDING:
            await self.transcript_service.initiate_transcription(
                organization_id=organization_id,
                request=AssemblyCreateTranscriptRequest(
                    transcript_provider=TranscriptProvider.ASSEMBLYAI_DIRECT,
                    audio_url=media_url,
                    reference_id=str(reference_id),
                    reference_id_type=TranscriptReferenceIdType.FILE,
                    meeting_custom_words=None,
                ),
            )
        else:
            # For calendar events, this is triggered through webhooks and handled
            # outside this function.
            logger.bind(
                reference_id=reference_id,
                reference_id_type=reference_id_type,
                organization_id=organization_id,
            ).error("Unsupported case for analyzing ended meeting")
            raise InvalidArgumentError("Unsupported meeting type for analysis")
        column_to_update = {
            "status": MeetingStatus.ANALYZING,
            "updated_at": zoned_utc_now(),
        }
        if reference_id_type == MeetingReferenceIdType.VOICE_V2:
            metadata = meeting.metadata or {}
            voice_v2 = metadata.get("voice_v2", {})
            voice_v2["is_recorded"] = True
            metadata.update({"voice_v2": voice_v2})
            column_to_update["metadata"] = metadata
        logger.bind(
            meeting_id=meeting.id,
            meeting_status=meeting.status,
            organization_id=organization_id,
        ).info("Updating meeting to analyzing status")
        updated_meeting = not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=organization_id,
                primary_key_to_value={"id": meeting.id},
                column_to_update=column_to_update,
            )
        )
        self._emit_state_change_metric(
            pre_update_meeting=meeting, updated_meeting=updated_meeting
        )

        return await self._populate_meeting_dto_for_client_response(
            organization_id=organization_id,
            meeting=updated_meeting,
            meeting_bot=None,
        )

    async def _resolve_sequence_id(
        self,
        organization_id: UUID,
        contacts: list[ContactSummary],
        request_sequence_id: UUID | None,
    ) -> UUID | None:
        if request_sequence_id:
            return request_sequence_id

        if not contacts:
            return None

        # Get contact IDs from the contact summaries
        contact_ids = {contact.id for contact in contacts}

        # Get all enrollments for these contacts, and create flattened list
        contact_enrollments = (
            await self.sequence_enrollment_query_service.get_contacts_enrollments(
                contact_ids=contact_ids,
                organization_id=organization_id,
            )
        )
        all_enrollments = [
            enrollment
            for enrollments in contact_enrollments.values()
            for enrollment in enrollments
        ]
        if not all_enrollments:
            return None

        most_recent_enrollment = max(all_enrollments, key=lambda x: x.enrolled_at)
        return most_recent_enrollment.sequence_id

    async def _resolve_account_id(
        self,
        organization_id: UUID,
        request_account_id: UUID | None,
        contacts: list[ContactSummary],
        invitees: list[Invitee] | None = None,
    ) -> UUID | None:
        if request_account_id:
            logger.bind(request_account_id=request_account_id).info(
                "Using account id from request"
            )
            return request_account_id

        # Prioritize using contextual information
        invitee_account_ids: set[UUID] = set()
        contact_email_pairs: list[tuple[UUID | None, str | None]] = []
        if invitees:
            for invitee in invitees:
                if invitee.account_id:
                    invitee_account_ids.add(invitee.account_id)
                    continue
                if invitee.contact_id:
                    contact_email_pairs.append((invitee.contact_id, invitee.email))
        else:
            contact_email_pairs = [(contact.id, None) for contact in contacts]
        resolved_account_id_map = await self.contact_resolve_service.resolve_relevant_account_by_contact_and_email_pairs(
            organization_id=organization_id,
            contact_email_pairs=contact_email_pairs,
        )
        account_ids = list(set(resolved_account_id_map.values()) | invitee_account_ids)
        if not account_ids:
            logger.info("No account ids found for contacts")
            return None
        elif len(account_ids) > 1:
            logger.bind(account_ids=account_ids).warning("Unable to resolve account id")
            return None
        else:
            return account_ids[0]

    async def _resolve_pipeline_ids(
        self,
        organization_id: UUID,
        invitees: list[Invitee],
        request_pipeline_id: UUID | None,
        request_pipeline_select_list_value_id: UUID | None,
    ) -> tuple[UUID, UUID] | tuple[None, None]:
        logger.bind(
            request_pipeline_id=request_pipeline_id,
            request_pipeline_select_list_value_id=request_pipeline_select_list_value_id,
            invitees=invitees,
        ).info("Resolving pipeline fields")

        if request_pipeline_id:
            # May raises not found error
            resolved_pipeline = await self.pipeline_query_service.get_pipeline_by_id(
                pipeline_id=request_pipeline_id,
                organization_id=organization_id,
            )
            return resolved_pipeline.id, resolved_pipeline.stage_id

        # No pipeline parameters specified: attempt to resolve based on invitee contact ids
        contact_ids = [i.contact_id for i in invitees if i.contact_id]
        pipeline_ids: set[tuple[UUID, UUID]] = set()
        for contact_id in contact_ids:
            pipelines = (
                await self.contact_query_service.list_active_pipelines_by_contact_id(
                    organization_id=organization_id,
                    contact_id=contact_id,
                )
            )
            for pipeline in pipelines:
                pipeline_ids.add((pipeline.pipeline_id, pipeline.stage_id))

        if len(pipeline_ids) == 1:
            return next(iter(pipeline_ids))
        elif len(pipeline_ids) > 1:
            logger.bind(contact_ids=contact_ids, pipeline_ids=pipeline_ids).warning(
                "Unable to resolve pipeline for contact ids - multiple pipelines found across contacts"
            )
            return None, None
        else:
            logger.info("No active pipelines found for contact ids")
            return None, None

    async def create_meeting(
        self, organization_id: UUID, user_id: UUID, request: CreateMeetingRequest
    ) -> MeetingDto:
        logger.bind(
            organization_id=organization_id, user_id=user_id, request=request
        ).info("Create meeting request")

        event_conferencing = request.event_conferencing
        if (
            event_conferencing
            and event_conferencing.use_bot
            and (
                not event_conferencing.meeting_url
                or not request.reference_id_type.supports_video_conferencing()
                or not MeetingProvider.supports_video_conferencing(request.platform)
            )
        ):
            logger.bind(
                organization_id=organization_id,
                meeting_url=event_conferencing.meeting_url,
                platform=request.platform,
                reference_id_type=request.reference_id_type,
            ).error("Invalid parameters for bot use")
            raise InvalidArgumentError("Invalid parameters for bot use")

        pipeline_id, pipeline_select_list_value_id = await self._resolve_pipeline_ids(
            organization_id=organization_id,
            invitees=request.invitees,
            request_pipeline_id=request.pipeline_id,
            request_pipeline_select_list_value_id=request.pipeline_select_list_value_id,
        )

        organizer = next((i for i in request.invitees if i.is_organizer), None)
        # Resolve invitees.  Includes lookup by id to get email.
        invitees, contacts_by_id = await self._map_request_invitees_to_db_invitees(
            invitees=request.invitees,
            organizer=organizer,
            organization_id=organization_id,
        )

        account_id = await self._resolve_account_id(
            organization_id=organization_id,
            request_account_id=request.account_id,
            contacts=list(contacts_by_id.values()),
            invitees=request.invitees,
        )

        sequence_id = await self._resolve_sequence_id(
            organization_id=organization_id,
            contacts=list(contacts_by_id.values()),
            request_sequence_id=request.sequence_id,
        )

        existing_meeting = (
            await self.meeting_repository.get_meeting_by_reference_id_and_type(
                organization_id=organization_id,
                reference_id_type=request.reference_id_type,
                reference_id=str(request.reference_id),
            )
        )
        if existing_meeting:
            logger.bind(
                organization_id=organization_id,
                created_at=existing_meeting.created_at,
                reference_id_type=request.reference_id_type,
                reference_id=request.reference_id,
            ).info("Meeting already exists for reference id")
            raise ConflictResourceError("Meeting already exists for reference id")

        time_now = zoned_utc_now()
        ends_at = (
            request.ends_at
            if request.ends_at
            else request.starts_at + timedelta(minutes=30)
        )
        new_meeting_status: MeetingStatus = MeetingStatus.SCHEDULED
        if (
            request.reference_id_type
            in (
                MeetingReferenceIdType.VOICE,
                MeetingReferenceIdType.VOICE_V2,
            )
            and request.starts_at <= zoned_utc_now()
        ):
            # For voice, we immediately transition to ACTIVE as we are in progress on a
            # call by the time meeting is created.
            new_meeting_status = MeetingStatus.ACTIVE

        is_sales_meeting = any(c.has_account for c in contacts_by_id.values())
        user = await self.user_service.get_by_id_and_organization_id(
            user_id=user_id, organization_id=organization_id
        )
        is_external_meeting = any(
            user
            and user.email_domain
            and c.email_domain
            and c.email_domain != user.email_domain
            for c in contacts_by_id.values()
        )

        owner_user_id = None
        if organizer:
            owner_user_id = organizer.user_id
        # TODO derive owner from contact or account owner

        logger.bind(
            organization_id=organization_id,
            organizer=organizer,
            invitees=invitees,
            ends_at=ends_at,
            meeting_status=new_meeting_status,
        ).info("Persisting new meeting")

        result_meeting = await self.meeting_repository.insert(
            Meeting(
                id=uuid.uuid4(),
                reference_id=str(request.reference_id),
                reference_id_type=request.reference_id_type,
                meeting_url=event_conferencing.meeting_url
                if event_conferencing
                else None,
                meeting_platform=request.platform,
                conferencing_details=event_conferencing.conferencing_details
                if event_conferencing
                else None,
                created_at=time_now,
                updated_at=time_now,
                starts_at=request.starts_at,
                started_at=request.started_at,
                ends_at=ends_at,
                title=request.title,
                description=request.description,
                location=event_conferencing.location if event_conferencing else None,
                organizer_user_id=organizer.user_id if organizer else None,
                invitees=invitees,
                organization_id=organization_id,
                consent_id=event_conferencing.consent_id
                if event_conferencing
                else None,
                status=new_meeting_status,
                event_schedule_id=event_conferencing.event_schedule_id
                if event_conferencing
                else None,
                created_by_user_id=user_id,
                pipeline_id=pipeline_id,
                pipeline_select_list_value_id=pipeline_select_list_value_id,
                account_id=account_id,
                is_sales=is_sales_meeting,
                is_external=is_external_meeting,
                owner_user_id=owner_user_id,
                sequence_id=sequence_id,
            )
        )

        result_meeting_bot = None
        if event_conferencing and event_conferencing.use_bot:
            result_meeting_bot = (
                await self.meeting_bot_service.create_scheduled_meeting_bot(
                    meeting_id=result_meeting.id,
                    meeting_url=not_none(result_meeting.meeting_url),
                    bot_name=await self._get_bot_name(
                        invitees=result_meeting.invitees_or_empty_list(),
                        organization_id=organization_id,
                    ),
                    meeting_provider=not_none(request.platform),
                    scheduled_at=calculate_bot_join_time(result_meeting.starts_at),
                    organization_id=organization_id,
                )
            )

        result = await self._populate_meeting_dto_for_client_response(
            organization_id=organization_id,
            meeting=result_meeting,
            meeting_bot=result_meeting_bot,
            contact_summaries=contacts_by_id,
        )

        if request.reference_id_type != MeetingReferenceIdType.VOICE_V2:
            await self.activity_service.insert_activity(
                insert_activity_request=_MeetingServiceMapper.map_meeting_to_activity(
                    meeting_dto=result,
                    sub_type=ActivitySubType.MEETING_SCHEDULED,
                ),
                organization_id=organization_id,
            )

        logger.bind(meeting_id=result_meeting.id, organization_id=organization_id).info(
            "Completed meeting creation"
        )
        self._emit_state_change_metric(
            pre_update_meeting=None, updated_meeting=result_meeting
        )
        # start meeting agent workflow
        meeting_id = result_meeting.id
        client = await get_temporal_client()
        await client.start_workflow(
            MeetingAgentWorkflow.run,
            args=[meeting_id, organization_id],
            id=f"meeting_agent_workflow_{meeting_id}",
            task_queue=MEETING_TASK_QUEUE,
        )

        try:
            await self._create_meeting_crm_association(
                meeting=result_meeting,
                organization_id=organization_id,
                user_id=user_id,
                pipeline_id=pipeline_id,
                account_id=account_id,
                user_calendar_event_id=request.user_calendar_event_id,
            )
        except Exception as e:
            logger.bind(
                meeting_id=result_meeting.id,
                organization_id=organization_id,
                exc_info=e,
            ).error("Error creating meeting crm association")

        return result

    @staticmethod
    def _emit_state_change_metric(
        pre_update_meeting: Meeting | None,
        updated_meeting: Meeting,
    ) -> None:
        custom_metric.increment(
            MeetingMetrics.STATE_CHANGE_METRIC_NAME,
            tags=[
                f"{MeetingMetrics.SOURCE_STATUS_TAG}:{pre_update_meeting.status if pre_update_meeting else 'None'}",
                f"{MeetingMetrics.RESULT_STATUS_TAG}:{updated_meeting.status}",
                f"{MeetingMetrics.REFERENCE_TYPE_TAG}:{updated_meeting.reference_id_type}",
            ],
        )

    async def _get_contact_summaries(
        self, contact_ids: list[UUID], organization_id: UUID
    ) -> dict[UUID, ContactSummary]:
        """
        Get mapped contacts, <contact_id>:<contact summary>


        Returns a dict of <contract_id>: <name>.  Prefers display name if set, otherwise
        forms name from first and last name (may result in empty string name).

        """
        if not contact_ids:
            return {}

        contacts_list = await self.contact_query_service.list_by_ids(
            contact_ids=contact_ids,
            organization_id=organization_id,
        )
        contacts = {contact.id: contact for contact in contacts_list}
        contact_email_map = (
            await self.contact_query_service.get_primary_emails_by_contact_ids(
                organization_id=organization_id,
                contact_ids=set(contacts.keys()),
            )
        )
        account_id_map = await self.contact_resolve_service.resolve_account_by_contacts(
            organization_id=organization_id,
            contact_ids=list(contacts.keys()),
        )
        ids_to_name = {}
        for k, v in contacts.items():
            preferred_name = (
                v.display_name
                if v.display_name
                else f"{v.first_name} {v.last_name}".strip()
            )

            if not preferred_name:
                logger.bind(organization_id=organization_id).warning(
                    "Unable to find name for contact"
                )
            contact_email = contact_email_map.get(k)
            ids_to_name[k] = ContactSummary(
                id=k,
                email=contact_email,
                name=preferred_name,
                has_account=bool(account_id_map.get(v.id)),
                primary_account_id=account_id_map.get(v.id),
            )

        return ids_to_name

    async def _fetch_users(
        self, user_ids: list[UUID], organization_id: UUID
    ) -> dict[UUID, UserDTO]:
        # TODO Deprecate this, switch to user_service.get_by_ids_and_organization
        # fetch user from user_organization_association(user id , org id, ...)
        users = []
        for user_id in user_ids:
            user = await self.user_service.get_by_id_and_organization_id(
                user_id=user_id, organization_id=organization_id, include_inactive=True
            )
            if user:
                users.append(user)
        return {user.id: user for user in users}

    async def _get_contact_and_user_maps(
        self, meeting_invitees: list[MeetingInvitee] | None, organization_id: UUID
    ) -> tuple[dict[UUID, ContactSummary], dict[UUID, UserDTO]]:
        contact_ids_to_summaries = (
            await self._get_contact_summaries(
                contact_ids=[i.contact_id for i in meeting_invitees if i.contact_id],
                organization_id=organization_id,
            )
            if meeting_invitees
            else {}
        )
        user_ids_to_user = (
            await self._fetch_users(
                user_ids=[i.user_id for i in meeting_invitees if i.user_id],
                organization_id=organization_id,
            )
            if meeting_invitees
            else {}
        )
        return contact_ids_to_summaries, user_ids_to_user

    async def _map_request_invitees_to_db_invitees(
        self, invitees: list[Invitee], organizer: Invitee | None, organization_id: UUID
    ) -> tuple[list[MeetingInvitee], dict[UUID, ContactSummary]]:
        contact_by_id = await self._get_contact_summaries(
            contact_ids=[i.contact_id for i in invitees if i.contact_id],
            organization_id=organization_id,
        )

        user_by_id = await self._fetch_users(
            user_ids=[i.user_id for i in invitees if i.user_id],
            organization_id=organization_id,
        )

        db_invitees = []
        for invitee in invitees:
            if invitee.user_id and invitee.user_id not in user_by_id:
                logger.bind(invitee=invitee, organization_id=organization_id).error(
                    "Unable to resolve invitees - no user found"
                )
                raise InvalidArgumentError("Unable to resolve invitees")
            elif invitee.contact_id and invitee.contact_id not in contact_by_id:
                logger.bind(invitee=invitee, organization_id=organization_id).error(
                    "Unable to resolve invitees - no contact found"
                )
                raise InvalidArgumentError("Unable to resolve invitees")

            db_invitees.append(
                MeetingInvitee(
                    user_id=invitee.user_id,
                    contact_id=invitee.contact_id,
                    contact_email=invitee.email,
                    is_organizer=organizer is not None and invitee == organizer,
                    account_id=invitee.account_id,
                )
            )
        return db_invitees, contact_by_id

    async def _get_bot_name(  # noqa: C901
        self, organization_id: UUID, invitees: list[MeetingInvitee]
    ) -> str:
        # Use organizer's user to get bot name, fall back to default if no organizer
        user_id_for_bot_name = None
        for invitee in invitees:
            if invitee.is_organizer and invitee.user_id:
                user_id_for_bot_name = invitee.user_id
                break

        if not user_id_for_bot_name:
            # Organizer is not a user - if we have one invitee that is a user, use that
            # person's settings.
            user_invitees = [i for i in invitees if i.user_id]
            if len(user_invitees) == 1:
                user_id_for_bot_name = user_invitees[0].user_id

        # Fall back to contact owner if we still don't have user_id_for_bot_name
        if not user_id_for_bot_name:
            # Look for any invitee with a contact_id and try to get its owner user_id
            for invitee in invitees:
                if invitee.contact_id and invitee.is_organizer:
                    try:
                        contact = await self.contact_query_service.get_contact_v2(
                            contact_id=invitee.contact_id,
                            organization_id=organization_id,
                        )
                        if contact and contact.owner_user_id:
                            user_id_for_bot_name = contact.owner_user_id
                            logger.bind(
                                organization_id=organization_id,
                                contact_id=invitee.contact_id,
                                owner_user_id=user_id_for_bot_name,
                            ).info("Using contact owner for bot name")
                            break  # Found a valid contact owner, no need to check more
                    except Exception as e:
                        logger.bind(
                            contact_id=invitee.contact_id,
                            organization_id=organization_id,
                        ).error(
                            "Error getting contact owner for bot name",
                            exc_info=e,
                        )

        user = None
        try:
            if user_id_for_bot_name:
                user = await self.user_service.get_by_id_and_organization_id(
                    user_id=user_id_for_bot_name, organization_id=organization_id
                )
                logger.bind(
                    organization_id=organization_id,
                    user_id_for_bot_name=user_id_for_bot_name,
                ).info("Using user id for bot name")
        except Exception as e:
            logger.bind(
                user_id_for_bot_name=user_id_for_bot_name,
                organization_id=organization_id,
            ).error(
                "Error getting user for bot name",
                exc_info=e,
            )

        return get_meeting_bot_name(
            user_first_name=user.first_name if user else None,
            user_profile=user.profile if user else None,
        )

    async def _lookup_meeting_by_id_or_error(
        self, meeting_id: UUID, organization_id: UUID
    ) -> Meeting:
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting, organization_id=organization_id, id=meeting_id
        )
        if not meeting:
            raise ResourceNotFoundError(
                f"Meeting not found for id {meeting_id}, organization {organization_id}"
            )
        return meeting

    async def _lookup_meeting_by_reference_id_and_type_or_error(
        self,
        reference_id: MeetingReferenceId,
        reference_id_type: MeetingReferenceIdType,
        organization_id: UUID,
    ) -> Meeting:
        meeting = await self.meeting_repository.get_meeting_by_reference_id_and_type(
            organization_id=organization_id,
            reference_id=str(reference_id),
            reference_id_type=reference_id_type,
        )
        if not meeting:
            raise ResourceNotFoundError(
                f"Meeting not found for reference id {reference_id}, type {reference_id_type}, organization {organization_id}"
            )
        return meeting

    async def get_contact_summaries(
        self, contact_ids: list[UUID], organization_id: UUID
    ) -> dict[UUID, ContactSummary]:
        """
        Get contact_id to ContactSummary mapping. Public method used by other services.
        """
        return await self._get_contact_summaries(
            contact_ids=contact_ids, organization_id=organization_id
        )

    async def cancel_meeting_by_reference_id(
        self,
        reference_id: MeetingReferenceId,
        reference_id_type: MeetingReferenceIdType,
        organization_id: UUID,
        cancel_reason: MeetingCancelReason,
    ) -> MeetingDto:
        meeting = await self._lookup_meeting_by_reference_id_and_type_or_error(
            reference_id=reference_id,
            reference_id_type=reference_id_type,
            organization_id=organization_id,
        )
        return await self._cancel_meeting(meeting=meeting, cancel_reason=cancel_reason)

    async def cancel_meeting(
        self,
        meeting_id: UUID,
        organization_id: UUID,
        cancel_reason: MeetingCancelReason,
    ) -> MeetingDto:
        logger.bind(meeting_id=meeting_id, organization_id=organization_id).info(
            "Canceling meeting"
        )
        meeting = await self._lookup_meeting_by_id_or_error(
            meeting_id=meeting_id, organization_id=organization_id
        )
        return await self._cancel_meeting(meeting, cancel_reason=cancel_reason)

    async def _cancel_meeting(
        self, meeting: Meeting, cancel_reason: MeetingCancelReason
    ) -> MeetingDto:
        if meeting.status == MeetingStatus.CANCELED:
            logger.bind(
                meeting_id=meeting.id, organization_id=meeting.organization_id
            ).info("Meeting already canceled, skipping cancel")
            return await self._populate_meeting_dto_for_client_response(
                organization_id=meeting.organization_id,
                meeting=meeting,
                meeting_bot=None,
            )

        organization_id = meeting.organization_id
        meeting_bot = (
            await self.meeting_bot_service.get_active_meeting_bot_by_meeting_id(
                meeting_id=meeting.id,
                organization_id=organization_id,
            )
        )

        if not meeting_bot:
            logger.bind(
                meeting_id=meeting.id, organization_id=meeting.organization_id
            ).info("No bot found for meeting, bot delete not needed")
        elif meeting.status == MeetingStatus.SCHEDULED:
            await self.meeting_bot_service.remove_bot_from_meeting(
                meeting_bot=meeting_bot
            )
        elif meeting.status == MeetingStatus.COMPLETED:
            logger.bind(
                meeting_id=meeting.id, organization_id=meeting.organization_id
            ).info("Meeting already completed, no bot action needed")
        else:
            # Should not get this far with meeting status check above, but in case of
            # unexpected status, log error for visibility.
            logger.bind(
                meeting_bot_id=meeting_bot.id,
                status=meeting_bot.status,
                organization_id=meeting_bot.organization_id,
            ).warning("Meeting bot not found or not in state for cancel")

        time_now = zoned_utc_now()
        updated_meeting = not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=organization_id,
                primary_key_to_value={"id": meeting.id},
                column_to_update={
                    "status": MeetingStatus.CANCELED,
                    "cancel_reason": cancel_reason,
                    "updated_at": time_now,
                    "canceled_at": time_now,
                },
            )
        )

        # TODO: May need to modify transcript state such that we would exclude from future search.

        result = await self._populate_meeting_dto_for_client_response(
            organization_id=organization_id,
            meeting=updated_meeting,
            meeting_bot=None,
        )
        await self.activity_service.insert_activity(
            insert_activity_request=_MeetingServiceMapper.map_meeting_to_activity(
                meeting_dto=result,
                sub_type=ActivitySubType.MEETING_CANCELED,
            ),
            organization_id=organization_id,
        )

        logger.bind(
            meeting_id=meeting.id, organization_id=meeting.organization_id
        ).info("Canceled meeting")
        self._emit_state_change_metric(
            pre_update_meeting=meeting, updated_meeting=updated_meeting
        )
        return result

    async def get_meeting_by_reference_id(
        self,
        reference_id: MeetingReferenceId,
        reference_id_type: MeetingReferenceIdType,
        organization_id: UUID,
    ) -> MeetingDto:
        meeting = await self._lookup_meeting_by_reference_id_and_type_or_error(
            reference_id=reference_id,
            reference_id_type=reference_id_type,
            organization_id=organization_id,
        )
        return await self._get_meeting(meeting=meeting, organization_id=organization_id)

    async def get_meeting(self, meeting_id: UUID, organization_id: UUID) -> MeetingDto:
        meeting = await self._lookup_meeting_by_id_or_error(
            meeting_id=meeting_id, organization_id=organization_id
        )
        return await self._get_meeting(meeting=meeting, organization_id=organization_id)

    async def _get_meeting(self, meeting: Meeting, organization_id: UUID) -> MeetingDto:
        # TODO This function is deprecated, replace with meeting_query_service.
        meeting_bot = (
            await self.meeting_bot_service.get_latest_meeting_bot_by_meeting_id(
                meeting_id=meeting.id, organization_id=organization_id
            )
        )

        return await self._populate_meeting_dto_for_client_response(
            organization_id=organization_id, meeting=meeting, meeting_bot=meeting_bot
        )

    async def _populate_meeting_dto_for_client_response(
        self,
        organization_id: UUID,
        meeting: Meeting,
        meeting_bot: MeetingBot | None,
        contact_summaries: dict[UUID, ContactSummary] | None = None,
    ) -> MeetingDto:
        """
        Helper function to fill in MeetingDto, given common case of having meeting and meeting bot on hand.
        """
        contacts = (
            contact_summaries
            if contact_summaries
            else await self._get_contact_summaries(
                contact_ids=[
                    invitee.contact_id
                    for invitee in meeting.invitees_or_empty_list()
                    if invitee.contact_id
                ],
                organization_id=organization_id,
            )
        )

        users = await self._fetch_users(
            user_ids=[
                invitee.user_id
                for invitee in meeting.invitees_or_empty_list()
                if invitee.user_id
            ],
            organization_id=organization_id,
        )

        return MeetingDto.from_db_objects(
            meeting=meeting,
            meeting_bot=meeting_bot,
            contact_ids_to_summary=contacts,
            user_ids_to_dto=users,
        )

    @staticmethod
    def _populate_meeting_dto_for_webhook_response(
        meeting: Meeting, meeting_bot: MeetingBot
    ) -> MeetingDto:
        # Optimization: we don't fetch associated fields to save on overhead, as webhook
        # path won't make use of them anyway.
        return MeetingDto.from_db_objects(
            meeting=meeting,
            meeting_bot=meeting_bot,
        )

    async def _get_fields_to_update_for_patch(  # type: ignore[explicit-any] # TODO: fix-any-annotation  # noqa: C901, PLR0912, PLR0915
        self,
        existing_meeting: Meeting,
        request: PatchMeetingRequest,
        request_event_conferencing: MeetingEventConferencing | None,
    ) -> dict[str, Any]:
        fields_to_update: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        # Basic fields
        if request.verbal_consent_at not in [UNSET, existing_meeting.verbal_consent_at]:
            fields_to_update["verbal_consent_at"] = request.verbal_consent_at
        if request.title not in [UNSET, existing_meeting.title]:
            fields_to_update["title"] = request.title
        if request.started_at not in [UNSET, existing_meeting.started_at]:
            fields_to_update["started_at"] = request.started_at
        if request.starts_at not in [UNSET, existing_meeting.starts_at]:
            fields_to_update["starts_at"] = request.starts_at
        if request.ends_at not in [UNSET, existing_meeting.ends_at]:
            fields_to_update["ends_at"] = request.ends_at
        if request.platform not in [UNSET, existing_meeting.meeting_platform]:
            fields_to_update["meeting_platform"] = request.platform
        if request.is_no_show not in [UNSET, existing_meeting.is_no_show]:
            fields_to_update["is_no_show"] = request.is_no_show
        if request.is_sales_meeting not in [UNSET, existing_meeting.is_sales]:
            fields_to_update["is_sales"] = request.is_sales_meeting
        if request.is_external_meeting not in [UNSET, existing_meeting.is_external]:
            fields_to_update["is_external"] = request.is_external_meeting
        if request.is_bot_enabled not in [UNSET, existing_meeting.is_bot_enabled]:
            fields_to_update["is_bot_enabled"] = request.is_bot_enabled
        if request.status not in [UNSET, existing_meeting.status]:
            base_logger = logger.bind(
                organization_id=existing_meeting.organization_id,
                meeting_id=existing_meeting.id,
            )

            if existing_meeting.status == MeetingStatus.CANCELED:
                if request.status == MeetingStatus.SCHEDULED:
                    fields_to_update["status"] = MeetingStatus.SCHEDULED
                    fields_to_update["canceled_at"] = None
                    fields_to_update["cancel_reason"] = None
                    base_logger.info(
                        "Meeting status changed from canceled to scheduled"
                    )
                else:
                    base_logger.bind(request_status=request.status).error(
                        "Invalid status transition attempted"
                    )
                    raise ValueError(
                        "Invalid status transition: Meeting status cannot change from CANCELED to any other state except SCHEDULED"
                    )

        if request.agenda not in [UNSET, existing_meeting.agenda]:
            fields_to_update["agenda"] = request.agenda
        if request.key_talking_points not in [
            UNSET,
            existing_meeting.key_talking_points,
        ]:
            fields_to_update["key_talking_points"] = request.key_talking_points

        # Invitee list
        contacts_by_id = None
        if request.invitees != UNSET:
            # Resolve invitees.  Includes lookup by id to get email.
            request_invitees = cast(list[Invitee], request.invitees)
            organizer = next((i for i in request_invitees if i.is_organizer), None)
            (
                resolved_request_invitees,
                contacts_by_id,
            ) = await self._map_request_invitees_to_db_invitees(
                invitees=cast(list[Invitee], request.invitees),
                organizer=organizer,
                organization_id=existing_meeting.organization_id,
            )
            existing_invitees = existing_meeting.invitees_or_empty_list()

            if any(
                p not in existing_invitees for p in resolved_request_invitees
            ) or len(resolved_request_invitees) != len(existing_invitees):
                fields_to_update["invitees"] = resolved_request_invitees

            if (
                organizer is not None
                and organizer.user_id != existing_meeting.organizer_user_id
            ):
                fields_to_update["organizer_user_id"] = organizer.user_id

        # Event conferencing
        if request_event_conferencing:
            if (
                request_event_conferencing.event_schedule_id
                != existing_meeting.event_schedule_id
            ):
                fields_to_update["event_schedule_id"] = (
                    request_event_conferencing.event_schedule_id
                )
            if (
                request_event_conferencing.conferencing_details
                != existing_meeting.conferencing_details
            ):
                fields_to_update["conferencing_details"] = (
                    request_event_conferencing.conferencing_details
                )
                if (
                    request_event_conferencing.conferencing_details.get("url")
                    and existing_meeting.meeting_url
                    != request_event_conferencing.conferencing_details["url"]
                ):
                    fields_to_update["meeting_url"] = (
                        request_event_conferencing.conferencing_details["url"]
                    )
            if request_event_conferencing.consent_id != existing_meeting.consent_id:
                fields_to_update["consent_id"] = request_event_conferencing.consent_id

            if request_event_conferencing.location != existing_meeting.location:
                fields_to_update["location"] = request_event_conferencing.location

        if specified(request.reference_id):
            fields_to_update["reference_id"] = str(request.reference_id)

        if fields_to_update:
            fields_to_update["updated_at"] = zoned_utc_now()

        if specified(request.pipeline_id):
            fields_to_update["pipeline_id"] = request.pipeline_id

        if (
            (
                settings.enable_patch_meeting_pipeline_account_update
                or str(existing_meeting.organization_id)
                in settings.enable_patch_meeting_pipeline_account_update_org_ids
            )
            and "invitees" in fields_to_update
            and request.invitees != UNSET
        ):
            if (
                not existing_meeting.pipeline_id
                and "pipeline_id" not in fields_to_update
            ):
                pipeline_id, _ = await self._resolve_pipeline_ids(
                    organization_id=existing_meeting.organization_id,
                    invitees=specified_or_default(request.invitees, []),
                    request_pipeline_id=None,
                    request_pipeline_select_list_value_id=None,
                )
                fields_to_update["pipeline_id"] = pipeline_id
            if (
                not existing_meeting.account_id
                and "account_id" not in fields_to_update
                and contacts_by_id
            ):
                account_id = await self._resolve_account_id(
                    organization_id=existing_meeting.organization_id,
                    invitees=specified_or_default(request.invitees, []),
                    contacts=list(contacts_by_id.values()),
                    request_account_id=None,
                )
                fields_to_update["account_id"] = account_id

        return fields_to_update

    async def _update_bot_use_for_patch(
        self,
        existing_meeting: Meeting,
        result_meeting: Meeting,
        existing_meeting_bot: MeetingBot | None,
        request_event_conferencing: MeetingEventConferencing | None,
    ) -> MeetingBot | None:
        if settings.check_status_for_bot_update and result_meeting.status in [
            MeetingStatus.COMPLETED,
            MeetingStatus.ENDED,
            MeetingStatus.ANALYZING,
            MeetingStatus.CANCELED,
        ]:
            logger.bind(
                meeting_id=existing_meeting.id,
                organization_id=existing_meeting.organization_id,
                meeting_status=existing_meeting.status,
            ).warning("Meeting is beyond active status, skipping bot update")
            return existing_meeting_bot

        meeting_id = result_meeting.id
        organization_id = result_meeting.organization_id

        is_rescheduled = existing_meeting.id != result_meeting.id
        bot_requested = (
            request_event_conferencing and request_event_conferencing.use_bot
        )
        bot_requested_or_not_specified = (
            request_event_conferencing is None or request_event_conferencing.use_bot
        )

        # Check is bot enabled, this supercedes all the other checks
        is_bot_enabled = result_meeting.is_bot_enabled

        # Check if we should consider is_bot_enabled based on settings instead of feature flag
        is_bot_enabled_ff = (
            settings.enable_patch_meeting_bot_enable_override
            or str(organization_id)
            in settings.enable_patch_meeting_bot_enable_override_org_ids
        )

        # Only consider is_bot_enabled if setting is enabled
        if is_bot_enabled_ff and is_bot_enabled is not None:
            # If is_bot_enabled is explicitly set, it overrides the event_conferencing.use_bot
            bot_requested = is_bot_enabled
            bot_requested_or_not_specified = is_bot_enabled

        add_bot = (
            (not existing_meeting_bot and bot_requested)
            or (
                existing_meeting_bot
                and existing_meeting_bot.status == MeetingBotStatus.PENDING
                and bot_requested
            )
            or (
                existing_meeting_bot
                and is_rescheduled
                and bot_requested_or_not_specified
            )
        )
        update_existing_bot = (
            existing_meeting_bot
            and not is_rescheduled
            and bot_requested_or_not_specified
            and (
                result_meeting.meeting_platform != existing_meeting.meeting_platform
                or result_meeting.meeting_url != existing_meeting.meeting_url
                or result_meeting.starts_at != existing_meeting.starts_at
            )
        )
        result_meeting_bot = existing_meeting_bot
        with logger.contextualize(
            organization_id=existing_meeting.organization_id,
            meeting_id=existing_meeting.id,
            existing_bot=bool(existing_meeting_bot),
            add_bot=add_bot,
            update_existing_bot=update_existing_bot,
            is_bot_enabled=is_bot_enabled,
        ):
            if add_bot:
                logger.info("Create scheduled meeting bot")
                result_meeting_bot = (
                    await self.meeting_bot_service.create_scheduled_meeting_bot(
                        meeting_id=meeting_id,
                        meeting_url=not_none(result_meeting.meeting_url),
                        bot_name=await self._get_bot_name(
                            invitees=result_meeting.invitees_or_empty_list(),
                            organization_id=organization_id,
                        ),
                        meeting_provider=not_none(result_meeting.meeting_platform),
                        scheduled_at=calculate_bot_join_time(result_meeting.starts_at),
                        organization_id=organization_id,
                    )
                )
            elif update_existing_bot:
                logger.info("Update existing meeting bot")
                result_meeting_bot = (
                    await self.meeting_bot_service.update_scheduled_meeting_bot(
                        meeting_bot=not_none(existing_meeting_bot),
                        meeting_id=meeting_id,
                        meeting_url=not_none(result_meeting.meeting_url),
                        bot_name=await self._get_bot_name(
                            invitees=result_meeting.invitees_or_empty_list(),
                            organization_id=organization_id,
                        ),
                        meeting_provider=not_none(result_meeting.meeting_platform),
                        scheduled_at=calculate_bot_join_time(result_meeting.starts_at),
                        organization_id=organization_id,
                    )
                )
            elif existing_meeting_bot and (
                (request_event_conferencing and not request_event_conferencing.use_bot)
                or is_bot_enabled is False
            ):
                logger.info("Removing existing bot")
                result_meeting_bot = (
                    await self.meeting_bot_service.remove_bot_from_meeting(
                        meeting_bot=existing_meeting_bot
                    )
                )
            else:
                logger.info("No bot changes needed")

            return result_meeting_bot

    async def patch_meeting_v2(
        self,
        *,
        meeting_id: UUID,
        organization_id: UUID,
        request: PatchMeetingRequest,
        user_id: UUID,
    ) -> MeetingV2:
        updated_meeting = await self.patch_meeting(
            meeting_id=meeting_id,
            organization_id=organization_id,
            request=request,
            user_id=user_id,
        )

        return await self.meeting_query_service.meeting_v2_from_db_objects(
            organization_id=organization_id,
            user_id=user_id,
            meeting=updated_meeting.meeting,
            meeting_bot=updated_meeting.meeting_bot,
        )

    async def patch_meeting(  # noqa C901, PLR0912
        self,
        *,
        meeting_id: UUID,
        organization_id: UUID,
        request: PatchMeetingRequest,
        user_id: UUID,
    ) -> MeetingDto:
        logger.bind(
            meeting_id=meeting_id,
            organization_id=organization_id,
            request=request,
        ).info("Patch meeting request")
        existing_meeting = await self._lookup_meeting_by_id_or_error(
            meeting_id=meeting_id, organization_id=organization_id
        )

        # delegate sales action types patch to meeting ai rec service
        # todo xw: we need to refactor core crud services to wrap around with general AI rec patterns.
        if specified(request.sales_action_types):
            await self.meeting_ai_rec_service.patch_record(
                record_id=meeting_id,
                organization_id=organization_id,
                patch_request=PatchMeetingSalesActionsRequest(
                    sales_action_types=request.sales_action_types,
                ),
                user_id=user_id,
            )
            request = request.model_copy(
                update={
                    "sales_action_types": UNSET,
                }
            )
            existing_meeting = await self._lookup_meeting_by_id_or_error(
                meeting_id=meeting_id, organization_id=organization_id
            )

        existing_meeting_bot = (
            await self.meeting_bot_service.get_active_meeting_bot_by_meeting_id(
                meeting_id=meeting_id,
                organization_id=organization_id,
            )
        )

        if (
            specified(request.custom_field_data)
            and request.custom_field_data
            and user_id
        ):
            await (
                self.custom_object_service.update_custom_object_data_by_extension_id_v2(
                    user_id=user_id,
                    organization_id=organization_id,
                    parent_object_name=ExtendableStandardObject.meeting,
                    extension_id=meeting_id,
                    custom_field_data_by_field_id=request.custom_field_data,
                )
            )

        result_meeting = existing_meeting
        if request.status not in [UNSET, existing_meeting.status]:
            logger.bind(
                organization_id=organization_id,
                meeting_id=existing_meeting.id,
                existing_status=existing_meeting.status,
                request_status=request.status,
            ).info("Meeting status change from patch meeting")
            # Restriction: the only transition we make through this endpoint is for the
            # unrecorded call switching to Completed status.
            if (
                existing_meeting.status == MeetingStatus.SCHEDULED
                and request.status == MeetingStatus.ENDED
                and not existing_meeting_bot
            ):
                result_meeting = await self._end_unrecorded_meeting(
                    meeting=existing_meeting, organization_id=organization_id
                )
            elif request.status == MeetingStatus.SCHEDULED:
                # We often need to allow meeting recovery, such as:
                # - Canceled meeting that the user brings back
                # - No show meeting that started and the user pushes out
                logger.bind(
                    organization_id=organization_id,
                    meeting_id=existing_meeting.id,
                    meeting_status=existing_meeting.status,
                    request_stats=request.status,
                ).info("Allowing recovery of meeting")
            else:
                logger.bind(
                    organization_id=organization_id,
                    meeting_id=existing_meeting.id,
                    meeting_status=existing_meeting.status,
                    request_status=request.status,
                ).error("Invalid meeting transition")
                raise InvalidArgumentError("Meeting not eligible for status change")

        request_event_conferencing = None
        if request.event_conferencing != UNSET:
            request_event_conferencing = cast(
                MeetingEventConferencing, request.event_conferencing
            )

        fields_to_update = await self._get_fields_to_update_for_patch(
            existing_meeting=existing_meeting,
            request=request,
            request_event_conferencing=request_event_conferencing,
        )

        if fields_to_update:
            logger.bind(
                organization_id=organization_id,
                meeting_id=meeting_id,
                existing_meeting=existing_meeting,
                fields_to_update=fields_to_update,
            ).info("Updating meeting")

            if (
                existing_meeting.status != MeetingStatus.SCHEDULED
                and "starts_at" in fields_to_update
                and fields_to_update["starts_at"] > zoned_utc_now()
            ):
                # Reschedule meeting: We create a second instance, while allowing the
                # original meeting to complete its lifecycle.  The second meeting
                # inherits all fields from existing, except for those specified in patch
                # request plus these set here.
                new_rescheduled_meeting_id = uuid.uuid4()
                fields_to_update["id"] = new_rescheduled_meeting_id
                fields_to_update["status"] = MeetingStatus.SCHEDULED
                fields_to_update["created_at"] = fields_to_update["updated_at"]
                fields_to_update["updated_at"] = fields_to_update["updated_at"]
                fields_to_update["rescheduled_from_id"] = existing_meeting.id
                update_reference_id = (
                    fields_to_update["reference_id"]
                    if specified(request.reference_id)
                    else existing_meeting.reference_id
                )

                (
                    _,
                    new_rescheduled_meeting,
                ) = await self.meeting_repository.create_meeting_for_reschedule(
                    organization_id=organization_id,
                    existing_meeting_id=existing_meeting.id,
                    existing_meeting_fields_to_update={
                        "is_rescheduled": True,
                        "updated_at": fields_to_update["updated_at"],
                        "reference_id": f"{update_reference_id}-{new_rescheduled_meeting_id}",
                    },
                    rescheduled_meeting_fields_to_update=fields_to_update,
                )
                logger.bind(
                    new_meeting_id=result_meeting.id,
                    previous_meeting_id=existing_meeting.id,
                    organization_id=organization_id,
                ).info("Updated existing meeting, meeting rescheduled")
                result_meeting = not_none(new_rescheduled_meeting)
                self._emit_state_change_metric(
                    pre_update_meeting=None, updated_meeting=result_meeting
                )
            else:
                result_meeting = not_none(
                    await self.meeting_repository.update_by_tenanted_primary_key(
                        table_model=Meeting,
                        organization_id=organization_id,
                        primary_key_to_value={"id": meeting_id},
                        column_to_update=fields_to_update,
                    )
                )
                logger.bind(
                    organization_id=organization_id, meeting_id=result_meeting.id
                ).info("Updated existing meeting")

            try:
                await self._update_meeting_crm_associations(
                    existing_meeting=existing_meeting,
                    updated_meeting=result_meeting,
                    patch_request=request,
                    user_id=user_id,
                    organization_id=organization_id,
                )
            except Exception as e:
                logger.bind(e=e, meeting_id=result_meeting.id).error(
                    "Failed to update crm association by patch_meeting"
                )
        else:
            logger.bind(
                meeting_id=meeting_id, organization_id=organization_id, request=request
            ).info("No meeting fields require update")

        if (
            result_meeting.id == existing_meeting.id
            and result_meeting.status == MeetingStatus.COMPLETED
        ):
            # Non rescheduled, completed - avoid any bot handling altogether
            logger.info(
                "Patch update for completed, non-rescheduled meeting - skipping bot updates"
            )
            return await self._populate_meeting_dto_for_client_response(
                organization_id=organization_id,
                meeting=result_meeting,
                meeting_bot=existing_meeting_bot,
            )

        # TODO update bot based on is_external/is_sales change + org policy
        result_meeting_bot = await self._update_bot_use_for_patch(
            existing_meeting=existing_meeting,
            result_meeting=result_meeting,
            existing_meeting_bot=existing_meeting_bot,
            request_event_conferencing=request_event_conferencing,
        )

        result = await self._populate_meeting_dto_for_client_response(
            organization_id=organization_id,
            meeting=result_meeting,
            meeting_bot=result_meeting_bot,
        )
        if result_meeting.reference_id_type != MeetingReferenceIdType.VOICE_V2:
            await self._emit_meeting_update_activity_if_modified(
                existing_meeting=existing_meeting,
                existing_meeting_bot=existing_meeting_bot,
                result=result,
                organization_id=organization_id,
            )

        return result

    async def _emit_meeting_update_activity_if_modified(
        self,
        existing_meeting: Meeting,
        existing_meeting_bot: MeetingBot | None,
        result: MeetingDto,
        organization_id: UUID,
    ) -> None:
        result_meeting = result.meeting
        result_meeting_bot = result.meeting_bot
        if (
            result_meeting != existing_meeting
            or result_meeting_bot != existing_meeting_bot
        ):
            # If anything changed, record activity
            is_rescheduled = existing_meeting.id != result_meeting.id
            if is_rescheduled:
                pre_update_meeting = None
                sub_type = ActivitySubType.MEETING_SCHEDULED
            else:
                pre_update_meeting = existing_meeting
                sub_type = ActivitySubType.MEETING_UPDATED

            await self.activity_service.insert_activity(
                insert_activity_request=_MeetingServiceMapper.map_meeting_to_activity(
                    meeting_dto=result,
                    sub_type=sub_type,
                    pre_update_meeting=pre_update_meeting,
                ),
                organization_id=organization_id,
            )

    async def end_meeting(
        self, meeting_id: UUID, organization_id: UUID, request: EndMeetingRequest
    ) -> MeetingV2:
        logger.bind(
            meeting_id=meeting_id, organization_id=organization_id, request=request
        ).info("End meeting request")
        existing_meeting = await self._lookup_meeting_by_id_or_error(
            meeting_id=meeting_id, organization_id=organization_id
        )
        existing_meeting_bot = (
            await self.meeting_bot_service.get_active_meeting_bot_by_meeting_id(
                meeting_id=meeting_id,
                organization_id=organization_id,
            )
        )

        if not existing_meeting_bot:
            # If there is no bot, we need to explicity end meeting since we won't have webhooks to help us
            # update state like usual.
            meeting = await self._end_unrecorded_meeting(
                meeting=existing_meeting,
                organization_id=organization_id,
                is_no_show=request.is_no_show,
            )
        else:
            # If there is a bot let it transition naturally, but still record now show specification
            meeting = not_none(
                await self.meeting_repository.update_by_tenanted_primary_key(
                    table_model=Meeting,
                    organization_id=organization_id,
                    primary_key_to_value={"id": meeting_id},
                    column_to_update={
                        "is_no_show": request.is_no_show,
                        "updated_at": zoned_utc_now(),
                    },
                )
            )
        updated_meetings = await self.meeting_query_service.list_meeting_v2(
            organization_id=organization_id,
            user_id=None,
            only_include_meeting_ids={meeting.id},
            include_custom_object=False,
        )
        return updated_meetings[0]

    async def authed_end_meeting(
        self,
        user_auth_context: UserAuthContext,
        meeting_id: UUID,
        request: EndMeetingRequest,
    ) -> MeetingV2:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=meeting_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.end_meeting(
            meeting_id, user_auth_context.organization_id, request
        )

    async def _end_unrecorded_meeting(
        self, meeting: Meeting, organization_id: UUID, is_no_show: bool | None = None
    ) -> Meeting:
        logger.bind(
            organization_id=organization_id,
            meeting_id=meeting.id,
            meeting_status=meeting.status,
            is_no_show=is_no_show,
        ).info("Ending unrecorded meeting")
        (
            contact_ids_to_summary,
            user_ids_to_dto,
        ) = await self._get_contact_and_user_maps(
            meeting_invitees=meeting.invitees, organization_id=organization_id
        )
        updated_meeting_dto, _ = await self._end_active_meeting(
            meeting=meeting,
            meeting_bot=None,
            organization_id=organization_id,
            attendees=_MeetingServiceMapper.map_meeting_invitees_to_attendees(
                invitees=meeting.invitees,
                contact_ids_to_summary=contact_ids_to_summary,
                user_ids_to_dto=user_ids_to_dto,
            ),
            is_no_show=is_no_show,
        )
        updated_meeting = updated_meeting_dto.meeting

        # Create placeholder insight sections that may have fields added in a user
        # driven (non-automated) flow, as we don't have transcription or automated
        # insights in the unrecorded use case.
        if not is_no_show:
            extraction_config_dtos = await self.meeting_insight_service.create_meeting_insights_from_extraction_config(
                organization_id=organization_id,
                meeting_id=updated_meeting.id,
                feature=Feature.POST_CALL_ANALYTICS,
            )

            # For unrecorded calls, insert task
            if (
                settings.enable_insight_task_creation
                and meeting.organizer_user_id
                and extraction_config_dtos
            ):
                logger.info("Creating task to define insights")
                await self.task_v2_service.insert_task_v2(
                    created_by_user_id=meeting.organizer_user_id,
                    organization_id=organization_id,
                    request=CreateTaskRequest(
                        title=f"Create insights for call {updated_meeting.title}",
                        status=TaskStatus.OPEN,
                        priority=TaskPriority.MEDIUM,
                        type=TaskType.REMINDER,
                        meeting_id=updated_meeting.id,
                        owner_user_id=meeting.organizer_user_id,
                        due_at=zoned_utc_now() + timedelta(days=2),
                        source_type=TaskSourceType.SYSTEM,
                    ),
                )

        return updated_meeting

    async def end_active_meeting(
        self,
        reference_id: MeetingReferenceId,
        reference_id_type: MeetingReferenceIdType,
        organization_id: UUID,
        attendees: list[Attendee] | None = None,
    ) -> tuple[MeetingDto, UUID]:  # return the activity_uuid for voice call
        logger.bind(
            organization_id=organization_id,
            reference_id=reference_id,
            reference_id_type=reference_id_type,
            attendees=attendees,
        ).info("Ending active meeting request")
        meeting = await self._lookup_meeting_by_reference_id_and_type_or_error(
            reference_id=reference_id,
            reference_id_type=reference_id_type,
            organization_id=organization_id,
        )
        if meeting.status not in (MeetingStatus.ACTIVE, MeetingStatus.SCHEDULED):
            logger.bind(
                meeting_id=meeting.id,
                organization_id=organization_id,
                meeting_status=meeting.status,
            ).error("Meeting cannot be ended in current state")
            raise InvalidArgumentError("Meeting not eligible for end state")

        meeting_bot = (
            await self.meeting_bot_service.get_active_meeting_bot_by_meeting_id(
                meeting_id=meeting.id, organization_id=organization_id
            )
        )
        if meeting_bot:
            # In this case, we expect bot to help with status transitions: this path
            # is not yet open to the bot/calendar case
            logger.bind(
                organization_id=organization_id,
                meeting_id=meeting.id,
                meeting_status=meeting.status,
                meeting_bot_id=meeting_bot.id,
            ).error("Meeting cannot be ended due to active bot")
            raise InvalidArgumentError("Meeting not eligible for end state")

        contact_ids_to_summary, user_ids_to_dto = await self._get_contact_and_user_maps(
            meeting_invitees=meeting.invitees, organization_id=organization_id
        )
        return await self._end_active_meeting(
            meeting=meeting,
            meeting_bot=meeting_bot,
            organization_id=organization_id,
            attendees=_MeetingServiceMapper.map_input_attendees_to_db_attendees(
                invitees=meeting.invitees,
                attendees=attendees,
                contact_ids_to_summary=contact_ids_to_summary,
                user_ids_to_dto=user_ids_to_dto,
            )
            if attendees
            else None,
        )

    async def _end_active_meeting(
        self,
        meeting: Meeting,
        meeting_bot: MeetingBot | None,
        organization_id: UUID,
        attendees: list[MeetingAttendee] | None,
        is_no_show: bool | None = None,
    ) -> tuple[MeetingDto, UUID]:  # return the activity_uuid for voice call
        logger.bind(
            meeting_id=meeting.id, organization_id=organization_id, attendees=attendees
        ).info("Ending active meeting")

        if not self.meeting_fsm.is_valid_progression(
            current_state=meeting.status, end_state=MeetingStatus.ENDED
        ):
            logger.bind(
                organization_id=organization_id,
                meeting_id=meeting.id,
                meeting_status=meeting.status,
            ).warning("Invalid attempt to end meeting")
            raise IllegalStateError("Invalid meeting state transition")

        time_now = zoned_utc_now()
        fields_to_update = {
            "status": MeetingStatus.ENDED,
            "updated_at": time_now,
            "ended_at": time_now,
            "attendees": attendees,
        }
        if is_no_show is not None:
            fields_to_update["is_no_show"] = is_no_show

        updated_meeting = not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=organization_id,
                primary_key_to_value={"id": meeting.id},
                column_to_update=fields_to_update,
            )
        )
        self._emit_state_change_metric(
            pre_update_meeting=meeting, updated_meeting=updated_meeting
        )

        result = await self._populate_meeting_dto_for_client_response(
            organization_id=organization_id,
            meeting=updated_meeting,
            meeting_bot=meeting_bot,
        )

        activity_uuid = await self.activity_service.insert_activity(
            # for voice call, the sub_type will be changed in voice_call_service
            insert_activity_request=_MeetingServiceMapper.map_meeting_to_activity(
                meeting_dto=result,
                sub_type=ActivitySubType.MEETING_COMPLETED,
            ),
            organization_id=organization_id,
        )

        return result, activity_uuid

    @staticmethod
    def _get_meeting_db_filters(
        request_filters: list[StandardValueFilter] | None,
    ) -> list[StandardValueFilter]:
        """
        return "is_sales_meeting", "meeting_status" fields only in request_filters if they exist in the request
        ignore other fields in the request.
        """
        if not request_filters:
            return []

        db_filters = []
        for request_filter in request_filters:
            if request_filter.field in [
                "is_sales_meeting",
                "meeting_status",
                "meeting_platform",
            ]:
                db_filters.append(request_filter)
        return db_filters

    async def clear_future_bots_async(self, organization_id: UUID) -> None:
        logger.bind(organization_id=organization_id).info(
            "Clearing future bots - async"
        )

        # Handled async since there may be many calls out to Recall
        temporal_client = await get_temporal_client()
        await temporal_client.start_workflow(
            ClearFutureMeetingBotsWorkflow.run,
            args=[ClearFutureMeetingBotsInput(organization_id=organization_id)],
            id=f"meeting_clear_bots_{organization_id}",
            task_queue=MEETING_TASK_QUEUE,
        )

    # TODO separate some of these service-to-service/internal functions to separate class,
    # to help reduce size of this one
    async def sync_scheduled_bot_to_meeting(
        self,
        organization_id: UUID,
        meeting_id: UUID,
        meeting_bot_id: UUID,
    ) -> MeetingBot | None:
        logger.bind(meeting_id=meeting_id, organization_id=organization_id).info(
            "Syncing bot to meeting"
        )

        meeting = await self._lookup_meeting_by_id_or_error(
            meeting_id=meeting_id, organization_id=organization_id
        )
        meeting_bot = (
            await self.meeting_bot_service.get_active_meeting_bot_by_meeting_id(
                meeting_id=meeting_id, organization_id=organization_id
            )
        )
        if not meeting_bot:
            logger.bind(meeting_id=meeting_id, organization_id=organization_id).info(
                "No active bot found for meeting"
            )
            return None
        if meeting_bot.id != meeting_bot_id:
            logger.bind(
                input_meeting_bot_id=meeting_bot_id, meeting_bot_id=meeting_bot.id
            ).info("Bot ID mismatch, taking no action")
            return None
        if meeting_bot.status != MeetingBotStatus.SCHEDULED:
            logger.bind(
                meeting_bot_id=meeting_bot_id, meeting_bot_status=meeting_bot.status
            ).info("Bot is not scheduled, taking no action")
            return None

        return await self.meeting_bot_service.update_scheduled_meeting_bot(
            meeting_bot=not_none(meeting_bot),
            meeting_id=meeting_id,
            meeting_url=not_none(meeting.meeting_url),
            bot_name=not_none(meeting_bot.name),
            meeting_provider=not_none(meeting.meeting_platform),
            scheduled_at=calculate_bot_join_time(meeting.starts_at),
            organization_id=organization_id,
        )

    async def clear_future_bots(self, organization_id: UUID) -> list[MeetingBot]:
        logger.bind(organization_id=organization_id).info("Clearing future bots")

        # TODO support user specific, also preserving if other users exist and are part of meeting

        all_future_bots = (
            await self.meeting_repository.find_future_scheduled_meeting_bots(
                organization_id=organization_id,
            )
        )

        removed_bots = []
        for bot in all_future_bots:
            removed_bots.append(
                await self.meeting_bot_service.remove_bot_from_meeting(
                    meeting_bot=bot,
                    is_consent_denied=False,
                )
            )
        return removed_bots

    async def delete_bots_from_meeting(
        self, meeting_id: UUID, organization_id: UUID, user_id: UUID
    ) -> MeetingV2:
        logger.bind(
            meeting_id=meeting_id, organization_id=organization_id, user_id=user_id
        ).info("Deleting bots from meeting")
        meeting = await self._lookup_meeting_by_id_or_error(
            meeting_id=meeting_id, organization_id=organization_id
        )
        meeting_bots = (
            await self.meeting_bot_service.get_all_meeting_bots_by_meeting_id(
                meeting_id=meeting.id, organization_id=organization_id
            )
        )
        for meeting_bot in meeting_bots:
            # Usually there is just one, but in case there are extra remove all
            await self.meeting_bot_service.remove_bot_from_meeting(
                meeting_bot=meeting_bot,
                is_consent_denied=False,
            )

        if meeting.metadata and meeting.metadata.get("schedule_bot", False):
            meeting.metadata["schedule_bot"] = False
            meeting = not_none(
                await self.meeting_repository.update_by_tenanted_primary_key(
                    table_model=Meeting,
                    organization_id=organization_id,
                    primary_key_to_value={"id": meeting.id},
                    column_to_update={
                        "metadata": meeting.metadata,
                        "updated_at": zoned_utc_now(),
                    },
                )
            )
        return await self.meeting_query_service.meeting_v2_from_db_objects(
            organization_id=organization_id,
            user_id=user_id,
            meeting=meeting,
            meeting_bot=None,
        )

    async def authed_upsert_bot_to_meeting(
        self,
        user_auth_context: UserAuthContext,
        request: AttachBotRequest,
    ) -> MeetingV2:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=request.meeting_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        organization_id = user_auth_context.organization_id
        logger.bind(
            organization_id=organization_id,
            meeting_id=request.meeting_id,
            request=request,
        ).info("Add / Update bot to meeting.")
        if not request.is_verbal_consent_granted:
            # Current policy: we always require verbal consent no matter the bot
            # provider or scenario
            raise InvalidArgumentError("Cannot add new bot without verbal consent")

        meeting = await self._lookup_meeting_by_id_or_error(
            meeting_id=request.meeting_id, organization_id=organization_id
        )
        if (
            not meeting.meeting_url
            or not meeting.meeting_platform
            or not MeetingProvider.supports_video_conferencing(meeting.meeting_platform)
            or not MeetingProvider.is_bot_eligible(
                meeting_provider=meeting.meeting_platform,
                organization_id=organization_id,
            )
        ):
            logger.bind(
                organization_id=organization_id,
                meeting_id=meeting.id,
                meeting_url=meeting.meeting_url,
                meeting_platform=meeting.meeting_platform,
            ).warning("Invalid request for adding bot to meeting")
            raise InvalidArgumentError(
                "Cannot attach new bot, no valid conferencing details found"
            )

        meeting_bot = (
            await self.meeting_bot_service.get_active_meeting_bot_by_meeting_id(
                meeting_id=meeting.id, organization_id=organization_id
            )
        )
        if meeting_bot and not MeetingBotStatus.is_terminal(meeting_bot.status):
            # Update the meeting bot only if it's not in a terminal state, which requires the starts_at parameter

            if not request.starts_at:
                logger.bind(
                    organization_id=organization_id,
                    meeting_id=meeting.id,
                    meeting_bot_id=meeting_bot.id,
                    meeting_bot_status=meeting_bot.status,
                    starts_at=request.starts_at,
                ).warning("Invalid starts_at parameter for adding bot to meeting")
                raise InvalidArgumentError(
                    "Cannot attach bot: Invalid starts_at parameter"
                )

            upserted_meeting_bot = (
                await self.meeting_bot_service.update_scheduled_meeting_bot(
                    meeting_bot=meeting_bot,
                    meeting_id=meeting.id,
                    meeting_url=meeting.meeting_url,
                    bot_name=not_none(meeting_bot.name),
                    meeting_provider=meeting.meeting_platform,
                    scheduled_at=request.starts_at,
                    organization_id=organization_id,
                )
            )
        else:
            upserted_meeting_bot = (
                await self.meeting_bot_service.create_scheduled_meeting_bot(
                    meeting_url=meeting.meeting_url,
                    bot_name=await self._get_bot_name(
                        invitees=meeting.invitees_or_empty_list(),
                        organization_id=organization_id,
                    ),
                    meeting_provider=meeting.meeting_platform,
                    scheduled_at=calculate_bot_join_time(meeting.starts_at),
                    organization_id=organization_id,
                    meeting_id=meeting.id,
                )
            )

        time_now = zoned_utc_now()
        updated_meeting = not_none(
            await self.meeting_repository.update_by_tenanted_primary_key(
                table_model=Meeting,
                organization_id=organization_id,
                primary_key_to_value={"id": meeting.id},
                column_to_update={
                    "verbal_consent_at": time_now,
                    "updated_at": time_now,
                },
            )
        )

        return await self.meeting_query_service.meeting_v2_from_db_objects(
            organization_id=organization_id,
            user_id=None,
            meeting=updated_meeting,
            meeting_bot=upserted_meeting_bot,
        )

    async def import_bot_for_meeting(
        self, organization_id: UUID, request: ImportBotRequest
    ) -> MeetingV2:
        """
        Persists new bot, transcript based on request.  Triggers transition to Analyzing
        for meeting.
        """

        logger.bind(organization_id=organization_id, request=request).info(
            "Importing bot for meeting."
        )

        # If we have any non terminated bot, error
        meeting = await self._lookup_meeting_by_id_or_error(
            meeting_id=request.meeting_id, organization_id=organization_id
        )
        existing_active_meeting_bot = (
            await self.meeting_bot_service.get_active_meeting_bot_by_meeting_id(
                meeting_id=request.meeting_id, organization_id=organization_id
            )
        )
        if existing_active_meeting_bot:
            logger.warning("Cannot import bot if there is an existing active one")
            raise InvalidArgumentError(
                "Cannot import bot if there is an existing active one"
            )

        # Get bot info from Recall
        external_bot = await self.meeting_bot_service.recallai_client_v1.get_bot(
            bot_id=request.external_meeting_bot_id
        )
        # Validate bot state - we expect call to be over
        if not external_bot.has_status_change(RecallStatusCode.CALL_ENDED):
            raise InvalidArgumentError("External bot not eligible for import")

        # Backfill meeting bot
        time_now = zoned_utc_now()
        meeting_bot = MeetingBot(
            id=uuid.uuid4(),
            provider=BotProvider.RECALLAI,
            meeting_id=meeting.id,
            external_meeting_bot_id=str(external_bot.id),
            external_recording_id=external_bot.recording,
            external_media_url=str(external_bot.video_url),
            external_media_retention_ended_at=external_bot.media_retention_end,
            status_history=BotStatusHistory(
                status_history=[
                    BotStatusEvent(
                        status=status_change.code,
                        sub_code=status_change.sub_code,
                        status_at=status_change.created_at,
                    )
                    for status_change in external_bot.status_changes
                ]
            ),
            joined_at=external_bot.join_at,
            created_at=time_now,
            updated_at=time_now,
            completed_at=time_now,
            organization_id=organization_id,
            scheduled_at=external_bot.join_at,
            status=MeetingBotStatus.EXITED,
            meeting_participant=RecallMeetingParticipants(
                meeting_participants=external_bot.meeting_participants
            ),
            recording_metadata=RecallRecordingData(
                recordings=[
                    RecallRecording(
                        id=r.id, started_at=r.started_at, completed_at=r.completed_at
                    )
                    for r in external_bot.recordings
                ]
                if external_bot.recordings
                else None
            ),
        )

        # Backfill transcript
        transcript = Transcript(
            id=uuid.uuid4(),
            reference_id=str(meeting_bot.id),
            reference_id_type=TranscriptReferenceIdType.MEETING_BOT,
            organization_id=organization_id,
            provider=TranscriptProvider.ASSEMBLYAI,
            created_at=time_now,
            updated_at=time_now,
        )

        await self.meeting_repository.insert(meeting_bot)
        await self.meeting_repository.insert(transcript)

        logger.bind(meeting_bot_id=meeting_bot.id, transcript_id=transcript.id).info(
            "Backfilled meeting bot and transcription"
        )

        # Update state for meeting
        (
            contact_ids_to_summary,
            user_ids_to_dto,
        ) = await self._get_contact_and_user_maps(
            meeting_invitees=meeting.invitees,
            organization_id=meeting.organization_id,
        )
        meeting_attendees = (
            _MeetingServiceMapper.map_bot_participants_to_meeting_attendees(
                invitees=meeting.invitees,
                bot_participants=external_bot.meeting_participants,
                contact_ids_to_summary=contact_ids_to_summary,
                user_ids_to_dto=user_ids_to_dto,
            )
        )
        ended_meeting_dto, _ = await self._end_active_meeting(
            meeting=meeting,
            meeting_bot=meeting_bot,
            organization_id=meeting.organization_id,
            attendees=meeting_attendees,
        )

        # Trigger analysis, and switch meeting to Analyzing
        result_meeting, _ = await self.analyze_from_bot_media(
            organization_id=organization_id,
            meeting=ended_meeting_dto.meeting,
            meeting_bot=meeting_bot,
            transcript=transcript,
        )

        logger.info("Imported bot")
        return await self.meeting_query_service.meeting_v2_from_db_objects(
            organization_id=organization_id,
            user_id=None,
            meeting=result_meeting if result_meeting else ended_meeting_dto.meeting,
            meeting_bot=meeting_bot,
        )

    async def authed_import_bot_for_meeting(
        self,
        user_auth_context: UserAuthContext,
        request: ImportBotRequest,
    ) -> MeetingV2:
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=request.meeting_id,
            access_check_function=self.can_access_entity_for_patch,
        )
        return await self.import_bot_for_meeting(
            organization_id=user_auth_context.organization_id, request=request
        )

    async def get_meeting_by_external_event_id(
        self,
        external_event_id: str,
        external_calendar_id: str,
        user_id: UUID,
        organization_id: UUID,
    ) -> MeetingV2 | None:
        user_calendar = (
            await self.user_calendar_repository.find_user_calendar_by_external_id(
                external_id=external_calendar_id,
                organization_id=organization_id,
                user_id=user_id,
            )
        )
        if not user_calendar:
            raise ResourceNotFoundError(
                f"User calendar not found for {external_calendar_id}"
            )
        user_calendar_event = await self.user_calendar_repository.find_user_calendar_event_by_user_calendar_id_and_external_id(
            user_calendar_id=user_calendar.id,
            external_id=external_event_id,
            organization_id=organization_id,
            exclude_deleted_or_archived=True,
        )

        if not user_calendar_event:
            logger.info("No event found.", external_event_id=external_event_id)
            raise ResourceNotFoundError(f"Event not found for {external_event_id}")

        meeting = await self._lookup_meeting_by_reference_id_and_type_or_error(
            reference_id=user_calendar_event.group_key,
            reference_id_type=MeetingReferenceIdType.USER_CALENDAR_EVENT,
            organization_id=organization_id,
        )

        return await self.meeting_query_service.get_meeting_v2(
            meeting_id=meeting.id,
            organization_id=organization_id,
        )

    async def authed_get_meeting_by_external_event_id(
        self,
        user_auth_context: UserAuthContext,
        external_event_id: str,
        external_calendar_id: str,
    ) -> MeetingV2 | None:
        meeting = await self.get_meeting_by_external_event_id(
            external_event_id=external_event_id,
            external_calendar_id=external_calendar_id,
            user_id=user_auth_context.user_id,
            organization_id=user_auth_context.organization_id,
        )
        if not meeting:
            raise ResourceNotFoundError(
                f"Meeting not found for {external_event_id} and {external_calendar_id}"
            )
        await self.error_if_no_entity_access(
            user_auth_context=user_auth_context,
            entity_id=meeting.id,
            access_check_function=self.can_access_entity_for_read,
        )
        return meeting

    async def import_meeting_from_external_recording(  # noqa: C901, PLR0912, PLR0915
        self, organization_id: UUID, request: ImportMeetingFromExternalRecordingRequest
    ) -> MeetingV2:
        logger.bind(organization_id=organization_id, request=request).info(
            "Importing meeting from external recording"
        )

        existing_meeting = (
            await self.meeting_repository.get_meeting_by_reference_id_and_type(
                organization_id=organization_id,
                reference_id=str(request.reference_id),
                reference_id_type=MeetingReferenceIdType.EXTERNAL_RECORDING,
            )
        )

        if existing_meeting:
            logger.bind(
                organization_id=organization_id,
                reference_id=request.reference_id,
                meeting_id=existing_meeting.id,
            ).info("Meeting already exists for reference id")

            return await self.meeting_query_service.meeting_v2_from_db_objects(
                organization_id=organization_id,
                user_id=request.user_id,
                meeting=existing_meeting,
                meeting_bot=None,
            )

        # look up users by name
        all_users: list[OrganizationUserV2] = await self.user_service.list_users_v2(
            organization_id=organization_id
        )

        users: list[OrganizationUserV2] = (
            [user for user in all_users if user.display_name in request.user_names]
            if request.user_names
            else []
        )

        # select first user as organizer
        organizer_user = users[0] if users else None
        email_contact_id_display_name_map = await self.contact_resolve_service.resolve_contact_id_display_name_by_emails(
            organization_id=organization_id, emails=set(request.contact_emails)
        )

        for email, result in email_contact_id_display_name_map.items():
            if result is None or result.contact_id is None:
                logger.bind(
                    request_contact_emails=request.contact_emails,
                    resolved_email_contact_id_map=email_contact_id_display_name_map,
                ).warning(f"Could not resolve contact through email: {email}")
                raise ResourceNotFoundError("One or more contacts not found")

        # Form invitees from list of users and contacts
        request_invitees = []
        request_invitees.extend(
            [
                Invitee(contact_id=result.contact_id, email=email)
                for email, result in email_contact_id_display_name_map.items()
                if result
            ]
        )

        # Add non-organizer users as invitees
        # organizer_user can be None if no users were found by display_name
        # in that case we want to include all users from the users list
        request_invitees.extend(
            [
                Invitee(user_id=user.id)
                for user in users
                if not organizer_user or user.id != organizer_user.id
            ]
        )

        # add organizer as an invitee with is_organizer flag
        if organizer_user:
            request_invitees.append(
                Invitee(user_id=organizer_user.id, is_organizer=True)
            )
        else:
            request_invitees.append(Invitee(user_id=request.user_id, is_organizer=True))

        invitees, _ = await self._map_request_invitees_to_db_invitees(
            invitees=request_invitees,
            organizer=next(i for i in request_invitees if i.is_organizer),
            organization_id=organization_id,
        )

        # Form attendees list
        attendees = []
        for invitee in invitees:
            transcript_speaker_name = None
            if invitee.contact_id:
                transcript_speaker_name = next(
                    (
                        result.display_name
                        for result in email_contact_id_display_name_map.values()
                        if result is not None
                        and result.contact_id == invitee.contact_id
                    ),
                    None,
                )
            elif invitee.user_id:
                user = not_none(
                    await self.user_service.get_by_id_and_organization_id(
                        invitee.user_id, organization_id
                    )
                )

                transcript_speaker_name = user.display_name

            attendees.append(
                MeetingAttendee(
                    user_id=invitee.user_id,
                    contact_id=invitee.contact_id,
                    is_organizer=invitee.is_organizer,
                    account_id=invitee.account_id,
                    transcript_speaker_name=transcript_speaker_name,
                )
            )

        # set account id
        account_id = None
        if request.account_id:
            account_id = request.account_id
        else:
            contact_summaries_map = await self._get_contact_summaries(
                contact_ids=[
                    result.contact_id
                    for result in email_contact_id_display_name_map.values()
                    if result
                ],
                organization_id=organization_id,
            )
            contact_summaries = [
                contact_summaries_map[contact_id]
                for contact_id in contact_summaries_map
            ]
            account_id = await self._resolve_account_id(
                organization_id=organization_id,
                request_account_id=request.account_id,
                contacts=contact_summaries,
            )

        # set pipeline id
        pipeline_id = None
        if request.pipeline_id:
            pipeline = await self.pipeline_query_service.get_pipeline_by_id(
                pipeline_id=request.pipeline_id,
                organization_id=organization_id,
            )
            pipeline_id = pipeline.id
        elif request.pipeline_display_name:
            pipelines = await self.pipeline_query_service.list_pipelines(
                organization_id=organization_id,
            )

            for pipeline in pipelines:
                if pipeline.display_name == request.pipeline_display_name:
                    pipeline_id = pipeline.id
                    break
        else:
            # Get pipeline fields
            (
                pipeline_id,
                pipeline_select_list_value_id,
            ) = await self._resolve_pipeline_ids(
                organization_id=organization_id,
                invitees=request_invitees,
                request_pipeline_id=None,
                request_pipeline_select_list_value_id=None,
            )

        # Persist meeting
        time_now = zoned_utc_now()
        start_time = request.date_of_meeting.replace(hour=8, minute=00)
        duration_seconds = request.duration_seconds
        to_insert = Meeting(
            id=uuid.uuid4(),
            reference_id=str(request.reference_id),
            reference_id_type=MeetingReferenceIdType.EXTERNAL_RECORDING,
            meeting_url=None,
            meeting_platform=MeetingProvider.RECORDING,
            conferencing_details=None,
            created_at=time_now,
            updated_at=time_now,
            started_at=start_time,
            ended_at=start_time + timedelta(seconds=duration_seconds),
            starts_at=start_time,
            ends_at=start_time + timedelta(seconds=duration_seconds),
            title=request.title,
            description=request.description,
            location=None,
            organizer_user_id=request.user_id,
            invitees=invitees,
            attendees=attendees,
            organization_id=organization_id,
            consent_id=None,
            status=MeetingStatus.ENDED,
            event_schedule_id=None,
            created_by_user_id=request.user_id,
            pipeline_id=pipeline_id,
            pipeline_select_list_value_id=pipeline_select_list_value_id
            if pipeline_select_list_value_id
            else None,
            account_id=account_id,
        )

        await self.meeting_repository.insert(to_insert)

        try:
            await self._create_meeting_crm_association(
                meeting=to_insert,
                organization_id=organization_id,
                user_id=request.user_id,
                pipeline_id=pipeline_id,
                account_id=account_id,
            )
        except Exception as e:
            logger.bind(
                meeting_id=to_insert.id,
                organization_id=organization_id,
                exc_info=e,
            ).error(
                "Error creating meeting crm association - import from external recording"
            )
        # Trigger analysis flow
        ended_meeting_dto = await self.analyze_ended_meeting(
            reference_id=request.reference_id,
            reference_id_type=MeetingReferenceIdType.EXTERNAL_RECORDING,
            organization_id=organization_id,
            media_url=request.presigned_media_url,
        )

        return await self.meeting_query_service.meeting_v2_from_db_objects(
            organization_id=organization_id,
            user_id=request.user_id,
            meeting=ended_meeting_dto.meeting,
            meeting_bot=None,
        )

    async def get_latest_meeting_by_pipeline_id(
        self, organization_id: UUID, pipeline_id: UUID
    ) -> MeetingV2 | None:
        meetings = await self.meeting_repository.list_meetings_by_pipeline_id(
            organization_id=organization_id, pipeline_id=pipeline_id
        )
        sorted_meetings = sorted(
            [meeting for meeting in meetings if meeting.ended_at],
            key=lambda x: not_none(x.ended_at),
            reverse=True,
        )
        if not sorted_meetings:
            return None
        else:
            return await self.meeting_query_service.meeting_v2_from_db_objects(
                organization_id=organization_id,
                user_id=None,
                meeting=sorted_meetings[0],
                meeting_bot=None,
            )

    def _extract_raw_invitees_from_meeting_dto(
        self,
        meeting_dto: MeetingDto,
    ) -> list[Invitee]:
        # temp workaround to generate raw invitees from meeting object, should deprecate soon
        raw_invitees = []
        for i in meeting_dto.meeting.invitees or []:
            raw_invitees.append(
                Invitee(
                    email=i.contact_email,
                    user_id=i.user_id,
                    contact_id=i.contact_id,
                    is_organizer=i.is_organizer,
                    account_id=i.account_id,
                    status=i.status,
                )
            )

        for a in meeting_dto.meeting.attendees or []:
            raw_invitees.append(
                Invitee(
                    email=a.contact_email,
                    user_id=a.user_id,
                    contact_id=a.contact_id,
                    is_organizer=a.is_organizer,
                    account_id=a.account_id,
                )
            )

        return raw_invitees

    async def resolve_latest_account_id_for_meeting(
        self,
        meeting_dto: MeetingDto,
        organization_id: UUID,
    ) -> UUID | None:
        # [MULTI EMAIL/ACCOUNT SUPPORT + RESOLUTION]
        return await self._resolve_account_id(
            organization_id=organization_id,
            request_account_id=None,
            contacts=list(meeting_dto.contact_ids_to_summary.values()),
            invitees=self._extract_raw_invitees_from_meeting_dto(meeting_dto),
        )

    async def resolve_latest_pipeline_id_for_meeting(
        self,
        meeting_dto: MeetingDto,
        organization_id: UUID,
    ) -> tuple[UUID, UUID] | tuple[None, None]:
        return await self._resolve_pipeline_ids(
            organization_id=organization_id,
            request_pipeline_id=None,
            request_pipeline_select_list_value_id=None,
            invitees=self._extract_raw_invitees_from_meeting_dto(meeting_dto),
        )

    async def error_if_no_meeting_access(
        self,
        user_auth_context: UserAuthContext,
        meeting_id: UUID,
        permission: SubServiceMeetingPermission,
    ) -> None:
        # no need to check object-level permissions if user is an ADMIN.
        if user_auth_context.is_admin:
            return

        if permission == SubServiceMeetingPermission.READ:
            # We currently allow read access to resources no matter owner or participants
            return

        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=user_auth_context.organization_id,
            id=meeting_id,
        )
        if not meeting:
            raise ResourceNotFoundError(f"No meeting found for id {meeting_id}")

        allowed_users = AllowedUsers(
            owner_user_id=meeting.owner_user_id,
            participant_user_ids=[
                invitee.user_id
                for invitee in (meeting.invitees or [])
                if invitee.user_id is not None
            ],
        )
        if not allowed_users.is_owner_or_participant(user_id=user_auth_context.user_id):
            raise ForbiddenError(
                "You do not have permission to update or read this meeting share"
            )

    async def get_entity(
        self, entity_id: UUID, organization_id: UUID, user_id: UUID
    ) -> MeetingV2:
        meeting = await self.meeting_query_service.get_meeting_v2(
            meeting_id=entity_id,
            organization_id=organization_id,
            user_id=user_id,
        )
        if not meeting:
            raise ResourceNotFoundError(f"No meeting found with id {entity_id}")
        return meeting

    async def create_entity(
        self, organization_id: UUID, user_id: UUID, request: CreateMeetingRequest
    ) -> MeetingV2:
        raise NotImplementedError("Meetings cannot be created via API")

    async def remove_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity_id: UUID,
    ) -> DeleteEntityResponse:
        raise NotImplementedError("Meetings cannot be deleted via API")

    # Meeting updates are driven through calendar updates, so we don't need to implement this
    async def update_entity(
        self,
        organization_id: UUID,
        user_id: UUID,
        entity: MeetingV2,
        request: BasePatchRequest,
    ) -> MeetingV2:
        raise NotImplementedError("MeetingV2 is not editable")

    @override
    async def get_allowed_users_from_entity(
        self, entity: MeetingV2, organization_id: UUID | None = None
    ) -> AllowedUsers:
        invitee_user_ids = entity.invitee_user_id_list
        owner_user_id = entity.owner_user_id

        return AllowedUsers(
            owner_user_id=owner_user_id, participant_user_ids=invitee_user_ids
        )

    async def _create_meeting_crm_association(
        self,
        meeting: Meeting,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID | None = None,
        account_id: UUID | None = None,
        user_calendar_event_id: UUID | None = None,
    ) -> None:
        """Create domain CRM associations for meeting entities.

        Args:
            meeting: The meeting to create association for
            organization_id: The organization ID
            user_id: The user ID who created the meeting
            pipeline_id: Associated pipeline ID if available
            pipeline_select_list_value_id: Associated pipeline select list value ID if available
            account_id: Associated account ID if available
        """
        if not settings.enable_meeting_domain_crm_association:
            return
        # Skip creating domain CRM association for voice call meetings
        # since they are handled separately
        if meeting.reference_id_type in (MeetingReferenceIdType.VOICE_V2,):
            return

        # TODO: (taiyan) support multiple accounts
        associations_to_create: list[CreateMeetingCrmAssociation] = []
        associations_to_create.extend(
            await _MeetingCrmAssociationMapper.map_meeting_to_user_associations_create(
                meeting=meeting,
                organization_id=organization_id,
                user_id=user_id,
                pipeline_id=pipeline_id,
                account_id=account_id,
                user_calendar_event_id=user_calendar_event_id,
            )
        )
        associations_to_create.extend(
            await _MeetingCrmAssociationMapper.map_meeting_to_contact_associations_create(
                meeting=meeting,
                organization_id=organization_id,
                user_id=user_id,
                pipeline_id=pipeline_id,
                account_id=account_id,
                user_calendar_event_id=user_calendar_event_id,
            )
        )
        await self.domain_crm_association_service.bulk_create_domain_crm_associations(
            associations_to_create
        )

    async def _update_meeting_crm_associations(
        self,
        existing_meeting: Meeting,
        updated_meeting: Meeting,
        patch_request: PatchMeetingRequest,
        organization_id: UUID,
        user_id: UUID,
    ) -> None:
        if not settings.enable_meeting_domain_crm_association:
            return

        if updated_meeting.reference_id_type in (MeetingReferenceIdType.VOICE_V2,):
            return

        # TODO: (taiyan) support multiple accounts

        (
            user_associations_to_create,
            user_associations_to_delete,
        ) = await _MeetingCrmAssociationMapper.map_meeting_to_user_associations_update(
            existing_meeting=existing_meeting,
            updated_meeting=updated_meeting,
            patch_request=patch_request,
            organization_id=organization_id,
            user_id=user_id,
        )
        (
            contact_associations_to_create,
            contact_associations_to_delete,
        ) = await _MeetingCrmAssociationMapper.map_meeting_to_contact_associations_update(
            existing_meeting=existing_meeting,
            updated_meeting=updated_meeting,
            patch_request=patch_request,
            organization_id=organization_id,
            user_id=user_id,
        )
        await self.domain_crm_association_service.bulk_create_domain_crm_associations(
            user_associations_to_create + contact_associations_to_create
        )
        await self.domain_crm_association_service.bulk_delete_domain_crm_associations(
            domain_crm_associations=user_associations_to_delete
            + contact_associations_to_delete,
            deleted_by_user_id=user_id,
        )

    # Necessary due to circular dependency
    async def start_sales_action_role_classification_workflow(
        self,
        organization_id: UUID,
        pipeline_id: UUID,
        source_object: CriteriaExtractionSourceObjectId,
    ) -> None:
        from salestech_be.core.ai.event_handlers.sales_action_role_classification import (
            start_sales_action_role_classification_workflow as _start_workflow,
        )

        await _start_workflow(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            source_object=source_object,
        )


class SingletonMeetingService(Singleton, MeetingService):
    pass


def meeting_service_factory_general(db_engine: DatabaseEngine) -> MeetingService:
    if SingletonMeetingService.has_instance():
        return SingletonMeetingService.get_singleton_instance()
    service_instance = SingletonMeetingService(
        meeting_s3_manager=get_s3_bucket_manager_by_bucket_name(
            bucket_name=settings.meeting_bucket_name,
        ),
        call_recording_s3=get_s3_bucket_manager_by_bucket_name(
            bucket_name=settings.call_recording_bucket_name,
        ),
        meeting_bot_service=meeting_bot_service_general(db_engine=db_engine),
        meeting_repository=MeetingRepository(engine=db_engine),
        meeting_insight_service=meeting_insight_service_factory_general(
            db_engine=db_engine
        ),
        user_service=get_user_service_general(db_engine=db_engine),
        task_v2_service=get_task_v2_service_general(db_engine=db_engine),
        activity_service=get_activity_service_general(db_engine=db_engine),
        transcript_service=transcript_service_from_engine(engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
        contact_resolve_service=get_contact_resolve_service(db_engine=db_engine),
        user_calendar_repository=UserCalendarRepository(engine=db_engine),
        custom_object_service=get_custom_object_service(db_engine=db_engine),
        meeting_query_service=get_meeting_query_service(db_engine=db_engine),
        meeting_stats_service=get_meeting_stats_service(db_engine=db_engine),
        job_service=job_service_from_engine(engine=db_engine),
        reference_type_strategy_factory=get_meeting_reference_type_strategy_factory_db_engine(
            db_engine=db_engine
        ),
        pipeline_query_service=get_pipeline_query_service(db_engine=db_engine),
        sequence_enrollment_query_service=get_sequence_enrollment_query_service_by_db_engine(
            db_engine=db_engine
        ),
        domain_crm_association_service=get_domain_crm_association_service(
            db_engine=db_engine
        ),
        meeting_ai_rec_service=get_meeting_ai_rec_service(db_engine=db_engine),
    )
    if settings.enable_meeting_service_debug:
        import os
        import threading

        thread_info = threading.current_thread()
        logger.info(
            f"Created meeting service instance at {hex(id(service_instance))} "
            f"on process {os.getpid()} "
            f"on thread {thread_info.name} (id: {thread_info.ident})"
        )
    return service_instance


def meeting_service_factory(request: Request) -> MeetingService:
    return meeting_service_factory_general(db_engine=get_db_engine(request))
