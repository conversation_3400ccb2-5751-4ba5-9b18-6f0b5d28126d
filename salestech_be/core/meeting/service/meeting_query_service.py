import time
from asyncio import Task, TaskGroup, gather
from collections.abc import Mapping
from typing import Annotated
from uuid import UUID

from fastapi import Depends

from salestech_be.common.core_crm.sales_action import StandardSalesActionType
from salestech_be.common.exception.exception import ForbiddenError
from salestech_be.common.query_util.domain_fetch_hints import DomainFetchHints
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.common.domain_fetching_hint import to_sql_selection_spec
from salestech_be.core.common.domain_service import (
    AllowedUsers,
    DomainQueryService,
)
from salestech_be.core.common.property_metadata import PropertyMetadata
from salestech_be.core.common.types import GenericDataMap, UserAuthContext
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.contact.types_v2 import ContactAccountIdPair, ContactV2
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.files.service.file_service import (
    FileService,
    file_service_from_engine,
    get_file_service,
)
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.meeting_ai_rec_service import (
    MeetingAIRecService,
    get_meeting_ai_rec_service,
)
from salestech_be.core.meeting.meeting_bot_service import (
    MeetingBotService,
    meeting_bot_service_general,
)
from salestech_be.core.meeting.service.meeting_fetch_config import (
    meeting_domain_db_mapping,
)
from salestech_be.core.meeting.service.meeting_reference_type_strategy import (
    MeetingReference,
    _ReferenceTypeStrategyFactory,
    get_meeting_reference_type_strategy_factory,
    get_meeting_reference_type_strategy_factory_db_engine,
)
from salestech_be.core.meeting.types.meeting_bot_status_event_type_v2 import (
    MeetingBotStatusEventV2,
)
from salestech_be.core.meeting.types.meeting_participant_type_v2 import (
    MeetingParticipantV2,
)
from salestech_be.core.meeting.types.meeting_screen_share_ranges import (
    MeetingScreenShareRanges,
)
from salestech_be.core.meeting.types.meeting_types_v2 import (
    BotError,
    MeetingType,
    MeetingV2,
)
from salestech_be.core.meeting.utils import (
    get_bot_error_from_status_event,
    is_meeting_recorded,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    get_select_list_service,
)
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dao.meeting_repository import MeetingRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.meeting import (
    ConfirmationState,
    Meeting,
    MeetingBot,
    MeetingReferenceIdType,
    MeetingView,
)
from salestech_be.integrations.s3.s3_bucket_manager import (
    S3BucketManager,
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.validation import not_none
from salestech_be.web.api.meeting.schema import (
    ListMeetingHistoryRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    MeetingReferenceId,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)

MEDIA_EXPIRATION_SECONDS = 86400  # 1 day


class MeetingQueryService(DomainQueryService[MeetingV2]):
    def __init__(
        self,
        meeting_repository: Annotated[MeetingRepository, Depends()],
        custom_object_service: Annotated[CustomObjectService, Depends()],
        meeting_bot_service: Annotated[MeetingBotService, Depends()],
        contact_query_service: Annotated[ContactQueryService, Depends()],
        meeting_s3_manager: Annotated[S3BucketManager, Depends()],
        reference_type_strategy_factory: Annotated[
            _ReferenceTypeStrategyFactory,
            Depends(get_meeting_reference_type_strategy_factory),
        ],
        file_service: Annotated[FileService, Depends(get_file_service)],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
        meeting_ai_rec_service: Annotated[MeetingAIRecService, Depends()],
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        super().__init__(feature_flag_service=feature_flag_service)
        self.meeting_repository = meeting_repository
        self.custom_object_service = custom_object_service
        self.meeting_bot_service = meeting_bot_service
        self.contact_query_service = contact_query_service
        self.meeting_s3_manager = meeting_s3_manager
        self.reference_type_strategy_factory = reference_type_strategy_factory
        self.file_service = file_service
        self.meeting_ai_rec_service = meeting_ai_rec_service

    async def get_allowed_users_from_entity(
        self, domain_entity: MeetingV2
    ) -> AllowedUsers:
        """
        Returns AllowedUsers for a domain entity object.

        This sub class overrides the inherited method as invitee_user_id_list is used instead of participant_user_id_list.
        """
        return AllowedUsers(
            owner_user_id=domain_entity.owner_user_id,
            participant_user_ids=domain_entity.invitee_user_id_list,
        )

    async def list_meetings_by_contact_ids(
        self,
        organization_id: UUID,
        contact_ids: list[UUID],
        user_id: UUID | None = None,
        include_custom_object: bool = False,
        include_property_metadata: bool = False,
    ) -> list[MeetingV2]:
        if not contact_ids:
            logger.info("No contact IDs provided to list_meetings_by_contact_ids")
            return []

        start_time = time.perf_counter()
        meetings = await self.meeting_repository.list_meetings_by_contact_ids(
            organization_id=organization_id, contact_ids=contact_ids
        )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info(
            "list_meetings_by_contact_ids meeting fetch"
        )
        if not meetings:
            return []

        return await self._form_meetings_v2_list(
            organization_id=organization_id,
            user_id=user_id,
            meetings=meetings,
            include_custom_object=include_custom_object,
            fetch_meeting_property_metadata=include_property_metadata,
        )

    async def list_meetings_by_primary_account_ids(
        self,
        organization_id: UUID,
        primary_account_ids: list[UUID],
        user_id: UUID | None = None,
        include_custom_object: bool = False,
        include_property_metadata: bool = False,
    ) -> list[MeetingV2]:
        if not primary_account_ids:
            logger.info(
                "No primary account IDs provided to list_meetings_by_primary_account_ids"
            )
            return []

        start_time = time.perf_counter()
        meetings = await self.meeting_repository.list_meetings_by_primary_account_ids(
            organization_id=organization_id,
            primary_account_ids=primary_account_ids,
        )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info(
            "list_meetings_by_primary_account_ids meeting fetch"
        )
        if not meetings:
            return []

        return await self._form_meetings_v2_list(
            organization_id=organization_id,
            user_id=user_id,
            meetings=meetings,
            include_custom_object=include_custom_object,
            fetch_meeting_property_metadata=include_property_metadata,
        )

    async def list_meeting_v2(
        self,
        *,
        organization_id: UUID,
        user_id: UUID | None,
        include_custom_object: bool,
        only_include_meeting_ids: UnsetAware[set[UUID]] = UNSET,
        only_include_meeting_for_user: UnsetAware[UUID] = UNSET,
    ) -> list[MeetingV2]:
        if specified(only_include_meeting_ids) and len(only_include_meeting_ids) == 1:
            meeting = await self.get_meeting_v2(
                meeting_id=next(iter(only_include_meeting_ids)),
                organization_id=organization_id,
                user_id=only_include_meeting_for_user
                if specified(only_include_meeting_for_user)
                else None,
                include_custom_object=include_custom_object,
            )
            return [meeting] if meeting else []

        start_time = time.perf_counter()
        if specified(only_include_meeting_for_user) and only_include_meeting_for_user:
            meetings = await self.meeting_repository.list_meetings_by_user_id(
                organization_id=organization_id,
                user_id=not_none(only_include_meeting_for_user),
            )
        else:
            meetings = await self.meeting_repository.list_meetings_by_organization_id(
                organization_id=organization_id,
                meeting_ids=(
                    list(only_include_meeting_ids)
                    if specified(only_include_meeting_ids)
                    else None
                ),
            )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info("_list_meetings meeting fetch")
        if not meetings:
            return []

        return await self._form_meetings_v2_list(
            organization_id=organization_id,
            user_id=user_id,
            meetings=meetings,
            include_custom_object=include_custom_object,
        )

    async def list_meetings_v2_with_domain_fetch_hints(
        self,
        *,
        organization_id: UUID,
        user_id: UUID | None,
        include_custom_object: bool = False,
        only_include_meeting_ids: UnsetAware[set[UUID]] = UNSET,
        include_property_metadata: bool = False,
        domain_fetch_hints: DomainFetchHints | None = None,
    ) -> list[MeetingV2]:
        logger.bind(domain_fetch_hints=domain_fetch_hints).info(
            "Using list_meetings_v2_with_domain_fetch_hints"
        )
        sql_selection_spec = to_sql_selection_spec(
            domain_fetch_hints=domain_fetch_hints if domain_fetch_hints else None,
            domain_to_table_model_field_mapping=meeting_domain_db_mapping,
            enable_json_spec=True,
        )

        logger.bind(
            sql_selection_spec=sql_selection_spec,
        ).info("sql selection spec")

        meetings_view = await self.meeting_repository.list_by_selection_spec(
            table_model=MeetingView,
            organization_id=organization_id,
            selection_spec=sql_selection_spec,
        )
        meetings = [Meeting.from_view(meeting_view) for meeting_view in meetings_view]
        if not meetings:
            return []

        return await self._form_meetings_v2_list(
            organization_id=organization_id,
            user_id=user_id,
            meetings=meetings,
            include_custom_object=include_custom_object,
            only_include_meeting_ids=only_include_meeting_ids
            if specified(only_include_meeting_ids)
            else UNSET,
            fetch_meeting_property_metadata=include_property_metadata,
        )

    async def get_meeting_v2(
        self,
        meeting_id: UUID,
        organization_id: UUID,
        user_id: UUID | None = None,
        include_custom_object: bool = False,
    ) -> MeetingV2 | None:
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting, organization_id=organization_id, id=meeting_id
        )
        if not meeting:
            return None

        meeting_bot = (
            await self.meeting_bot_service.get_latest_meeting_bot_by_meeting_id(
                meeting_id=meeting.id, organization_id=organization_id
            )
        )

        return await self.meeting_v2_from_db_objects(
            organization_id=organization_id,
            meeting=meeting,
            meeting_bot=meeting_bot,
            user_id=user_id,
        )

    async def _get_all_meeting_references(
        self,
        organization_id: UUID,
        meetings: list[Meeting],
    ) -> dict[MeetingReferenceIdType, dict[MeetingReferenceId, list[MeetingReference]]]:
        result: dict[
            MeetingReferenceIdType, dict[MeetingReferenceId, list[MeetingReference]]
        ] = {}

        meeting_reference_types: list[MeetingReferenceIdType] = list(
            MeetingReferenceIdType
        )

        # We store the async tasks so we can parallelize this fetch
        tasks = []
        task_index_to_ref_type: dict[int, MeetingReferenceIdType] = {}

        task_index = 0
        for meeting_reference_type in meeting_reference_types:
            if any(
                meeting.reference_id_type == meeting_reference_type
                for meeting in meetings
            ):
                strategy = self.reference_type_strategy_factory.get_instance(
                    meeting_reference_type
                )
                tasks.append(
                    strategy.get_all_references(
                        organization_id=organization_id,
                        meetings=meetings,
                    )
                )
                task_index_to_ref_type[task_index] = meeting_reference_type
                task_index += 1

        if tasks:
            references_results = await gather(*tasks)
            for task_index, ref_type in task_index_to_ref_type.items():
                result[ref_type] = references_results[task_index]

        return result

    async def meeting_v2_from_db_objects(
        self,
        organization_id: UUID,
        meeting: Meeting,
        meeting_bot: MeetingBot | None,
        user_id: UUID | None,
    ) -> MeetingV2:
        meeting_reference_id_to_references = await self._get_all_meeting_references(
            organization_id=organization_id, meetings=[meeting]
        )
        contacts_account_id_pairs = await self.contact_query_service.find_primary_account_id_with_contact_id_pair(
            organization_id=organization_id,
            only_include_contact_ids=[
                invitee.contact_id
                for invitee in meeting.invitees_or_empty_list()
                if invitee.contact_id
            ],
        )
        pipeline_id_to_earliest_meeting_id = (
            await self.meeting_repository.list_earliest_meeting_id_by_pipeline(
                organization_id=organization_id,
                meeting_ids=[meeting.id],
            )
        )

        return await self._populate_meeting_v2(
            meeting=meeting,
            meeting_bot=meeting_bot,
            meeting_references=meeting_reference_id_to_references[
                meeting.reference_id_type
            ].get(meeting.reference_id, []),
            contacts_account_id_pairs=contacts_account_id_pairs,
            pipeline_id_to_earliest_meeting_id=pipeline_id_to_earliest_meeting_id,
            is_details_fetch=True,
            user_id=user_id,
        )

    async def _populate_meeting_v2(
        self,
        meeting: Meeting,
        meeting_bot: MeetingBot | None,
        meeting_references: list[MeetingReference],
        contacts_account_id_pairs: list[ContactAccountIdPair],
        pipeline_id_to_earliest_meeting_id: dict[UUID, UUID],
        is_details_fetch: bool,
        user_id: UUID | None,
        custom_field_data: GenericDataMap | None = None,
        fetch_meeting_property_metadata: bool = False,
    ) -> MeetingV2:
        # computed values
        meeting_calendar_group_key = (
            meeting.reference_id
            if meeting.reference_id_type == MeetingReferenceIdType.USER_CALENDAR_EVENT
            else ""
        )
        meeting_bot_removed_at = meeting_bot.removed_at if meeting_bot else None
        invitee_user_id_list = [
            invitee.user_id
            for invitee in meeting.invitees_or_empty_list()
            if invitee.user_id
        ]
        invitee_contact_id_list = [
            invitee.contact_id
            for invitee in meeting.invitees_or_empty_list()
            if invitee.contact_id
        ]
        attendee_user_id_list = [
            attendee.user_id
            for attendee in meeting.attendee_or_empty_list()
            if attendee.user_id
        ]
        attendee_contact_id_list = [
            attendee.contact_id
            for attendee in meeting.attendee_or_empty_list()
            if attendee.contact_id
        ]
        is_recorded = is_meeting_recorded(meeting, meeting_bot)

        meeting_participants: list[MeetingParticipantV2] = []
        confirmation_state = ConfirmationState.UNCONFIRMED
        meeting_reference = next(
            (
                meeting_reference
                for meeting_reference in meeting_references
                if meeting_reference.is_owned_by_organizer
            ),
            meeting_references[0] if meeting_references else None,
        )

        if meeting_reference:
            strategy_reference_participants = (
                meeting_reference.meeting_reference_participants
            )
            non_organizer_strategy_reference_participants = [
                p for p in strategy_reference_participants if not p.is_organizer
            ]
            # CONFIRMED state:
            # 1. Any none user accepts, or
            # 2. All are users and any accepted

            if any(
                p.status == "yes" and p.user_id is None
                for p in non_organizer_strategy_reference_participants
            ) or (
                all(
                    p.user_id is not None
                    for p in non_organizer_strategy_reference_participants
                )
                and any(
                    p.status == "yes"
                    for p in non_organizer_strategy_reference_participants
                )
            ):
                confirmation_state = ConfirmationState.CONFIRMED

            meeting_participants = [
                MeetingParticipantV2.from_meeting_reference_strategy_type(participant)
                for participant in meeting_reference.meeting_reference_participants
            ]

        owner_calendar_event_id = (
            UUID(str(meeting_reference.reference_id))
            if meeting_reference
            and meeting_reference.is_owned_by_organizer
            and meeting.reference_id_type == MeetingReferenceIdType.USER_CALENDAR_EVENT
            else None
        )

        current_user_calendar_event_id = (
            UUID(str(meeting_reference.reference_id))
            if meeting_reference
            and user_id
            and meeting_reference.user_id == user_id
            and meeting.reference_id_type == MeetingReferenceIdType.USER_CALENDAR_EVENT
            else None
        )
        # Create bot_error from the last status event if available it has sub_code
        bot_error = await self.get_bot_error_from_meetig_bot(meeting_bot)

        if meeting_bot and meeting_bot.screen_share_ranges:
            screen_share_ranges = [
                MeetingScreenShareRanges.from_screen_share_range(screen_share_range)
                for screen_share_range in meeting_bot.screen_share_ranges
            ]
        else:
            screen_share_ranges = []

        pre_signed_url = None
        expire_time = None
        pre_signed_vtt_url: str | None = None
        pre_signed_sprite_url: str | None = None
        pre_signed_preview_thumbnail_url: str | None = None
        property_metadata: list[PropertyMetadata] | None = None
        reference_type_strategy = self.reference_type_strategy_factory.get_instance(
            meeting.reference_id_type
        )

        meeting_dto = MeetingDto.from_db_objects(
            meeting=meeting,
            meeting_bot=meeting_bot,
        )
        pre_signed_url_task: (
            Task[tuple[str, ZoneRequiredDateTime] | tuple[None, None]] | None
        ) = None
        property_metadata_task: Task[Mapping[UUID, list[PropertyMetadata]]] | None = (
            None
        )
        async with TaskGroup() as tg:
            if is_details_fetch:
                # Optimization: Actual video file is only needed when viewing details of specific meeting
                pre_signed_url_task = tg.create_task(
                    reference_type_strategy.generate_pre_signed_url(
                        meeting_dto=meeting_dto,
                        meeting_reference=meeting_reference,
                        expiration_seconds=MEDIA_EXPIRATION_SECONDS,
                    )
                )
            if fetch_meeting_property_metadata or is_details_fetch:
                property_metadata_task = tg.create_task(
                    self.meeting_ai_rec_service.find_property_metadata_for_record(
                        organization_id=meeting.organization_id,
                        record_ids={meeting.id},
                        sobject_name=StdObjectIdentifiers.meeting,
                    )
                )

            pre_signed_vtt_task = tg.create_task(
                reference_type_strategy.generate_pre_signed_vtt_url(
                    meeting_dto=meeting_dto,
                    meeting_reference=meeting_reference,
                    expiration_seconds=MEDIA_EXPIRATION_SECONDS,
                )
            )

            pre_signed_sprite_task = tg.create_task(
                reference_type_strategy.generate_pre_signed_sprite_url(
                    meeting_dto=meeting_dto,
                    meeting_reference=meeting_reference,
                    expiration_seconds=MEDIA_EXPIRATION_SECONDS,
                )
            )

            pre_signed_preview_task = tg.create_task(
                reference_type_strategy.generate_pre_signed_preview_thumbnail_url(
                    meeting_dto=meeting_dto,
                    meeting_reference=meeting_reference,
                    expiration_seconds=MEDIA_EXPIRATION_SECONDS,
                )
            )

        # Get results after all tasks complete
        if pre_signed_url_task:
            pre_signed_url, expire_time = pre_signed_url_task.result()
        if property_metadata_task:
            property_metadata = (property_metadata_task.result()).get(meeting.id, None)
        pre_signed_vtt_url, _ = pre_signed_vtt_task.result()
        pre_signed_sprite_url, _ = pre_signed_sprite_task.result()
        pre_signed_preview_thumbnail_url, _ = pre_signed_preview_task.result()

        contact_id_to_account_id_map = {
            id_pair.contact_id: id_pair.account_id
            for id_pair in contacts_account_id_pairs
        }

        return MeetingV2(
            id=meeting.id,
            meeting_url=meeting.meeting_url,
            meeting_calendar_group_key=meeting_calendar_group_key,
            meeting_platform=meeting.meeting_platform,
            meeting_created_at=meeting.created_at,
            starts_at=meeting.starts_at,
            started_at=meeting.started_at,
            title=meeting.title,
            # The provider returns a plain text description, which causes display issues in the UI.
            # Converting it to HTML format for improved appearance.
            description=f"<div>{meeting.description.replace('\n', '<br>').replace('\\/', '/')}</div>"
            if meeting.description
            else None,
            is_recorded=is_recorded,
            has_active_bot=meeting_bot.is_active() if meeting_bot else False,
            meeting_status=meeting.status,
            meeting_type=(
                MeetingType.first_meeting
                if meeting.pipeline_id is None
                or meeting.id
                == pipeline_id_to_earliest_meeting_id.get(meeting.pipeline_id, None)
                else MeetingType.follow_up
            ),
            meeting_participants=meeting_participants,
            ends_at=meeting.ends_at,
            ended_at=meeting.ended_at,
            meeting_bot_status=meeting_bot.status if meeting_bot else None,
            meeting_bot_status_history=await self._get_meeting_bot_status_events(
                meeting_bot
            ),
            bot_error=bot_error,
            pre_signed_media_url=pre_signed_url,
            pre_signed_vtt_url=pre_signed_vtt_url,
            pre_signed_sprite_url=pre_signed_sprite_url,
            pre_signed_preview_thumbnail_url=pre_signed_preview_thumbnail_url,
            pre_signed_media_expire_time=expire_time,
            meeting_bot_removed_at=meeting_bot_removed_at,
            consent_declined_at=meeting_bot.consent_declined_at
            if meeting_bot
            else None,
            invitee_user_id_list=invitee_user_id_list,
            invitee_contact_id_list=invitee_contact_id_list,
            attendee_user_id_list=attendee_user_id_list,
            attendee_contact_id_list=attendee_contact_id_list,
            is_sales_meeting=meeting.is_sales
            if meeting.is_sales is not None
            else any(
                meeting_participant.contact_id in contact_id_to_account_id_map
                for meeting_participant in meeting_participants
                if meeting_participant.contact_id
            ),
            is_external_meeting=meeting.is_external,
            organizer_user_id=meeting.organizer_user_id,
            created_by_user_id=meeting.created_by_user_id,
            event_schedule_id=meeting.event_schedule_id,
            owner_user_calendar_event_id=owner_calendar_event_id,
            current_user_calendar_event_id=current_user_calendar_event_id,
            pipeline_id=meeting.pipeline_id,
            pipeline_select_list_value_id=meeting.pipeline_select_list_value_id,
            custom_field_data=custom_field_data,
            is_no_show=meeting.is_no_show,
            reference_id=meeting.reference_id,
            reference_id_type=meeting.reference_id_type,
            confirmation_state=confirmation_state,
            agenda=meeting.agenda,
            key_talking_points=meeting.key_talking_points,
            screen_share_ranges=screen_share_ranges,
            rescheduled_from_id=meeting.rescheduled_from_id,
            is_rescheduled=meeting.is_rescheduled,
            account_id=meeting.account_id,
            duration_seconds=meeting.duration_seconds,
            owner_user_id=meeting.owner_user_id,
            is_bot_enabled=meeting.is_bot_enabled,
            sequence_id=meeting.sequence_id,
            sales_action_types=meeting.sales_action_types,
            property_metadata=property_metadata,
        )

    async def _get_meeting_bot_status_events(
        self,
        meeting_bot: MeetingBot | None,
    ) -> list[MeetingBotStatusEventV2]:
        if not meeting_bot:
            return []
        status_history = meeting_bot.status_history
        if not status_history:
            return []
        return [
            MeetingBotStatusEventV2.from_bot_status_event(status_event)
            for status_event in status_history.status_history
            if status_history
        ]

    async def get_bot_error_from_meetig_bot(
        self,
        meeting_bot: MeetingBot | None,
    ) -> BotError | None:
        if not meeting_bot:
            return None
        meeting_bot_status_events = await self._get_meeting_bot_status_events(
            meeting_bot
        )
        bot_error = None
        if meeting_bot_status_events:
            last_status_event = meeting_bot_status_events[-1]

            error_code, error_desc = get_bot_error_from_status_event(
                status=last_status_event.status, sub_code=last_status_event.sub_code
            )

            if error_code and error_desc:
                bot_error = BotError(error_code=error_code, error_desc=error_desc)
        return bot_error

    async def _get_contacts_with_accounts_for_meeting(
        self,
        organization_id: UUID,
        meeting: Meeting,
    ) -> list[ContactV2]:
        contacts = await self.contact_query_service.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids={
                invitee.contact_id
                for invitee in meeting.invitees_or_empty_list()
                if invitee.contact_id
            },
        )
        return [contact for contact in contacts if contact.primary_account_id]

    async def _form_meetings_v2_list(
        self,
        *,
        organization_id: UUID,
        user_id: UUID | None,
        meetings: list[Meeting],
        include_custom_object: bool,
        only_include_meeting_ids: UnsetAware[set[UUID]] = UNSET,
        fetch_meeting_property_metadata: bool = False,
    ) -> list[MeetingV2]:
        """
        Common function for creating a list of V2 Meeting objects after an initial meeting lookup.
        Fetches related objects and leverages _populate_meeting_v2 to create each list element.
        """
        if not meetings:
            return []

        meeting_ids = [meeting.id for meeting in meetings]

        # Pre-fetch all required data
        start_time = time.perf_counter()
        meeting_id_to_bot = await self.meeting_bot_service.get_meeting_id_to_bot_dict(
            organization_id=organization_id,
            meeting_ids=meeting_ids,
        )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info("_list_meetings bot fetch")

        start_time = time.perf_counter()
        meeting_reference_id_to_references = await self._get_all_meeting_references(
            organization_id=organization_id,
            meetings=meetings,
        )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info("_list_meetings references fetch")

        # Pre-fetch custom object data for all meetings
        start_time = time.perf_counter()
        extension_custom_object_data_group_dto = (
            await self.custom_object_service.map_custom_object_data_by_extension_ids_if_custom_object_exists(
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.meeting,
                extension_ids={meeting.id for meeting in meetings},
            )
            if include_custom_object
            else None
        )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).debug(
            "_list_meetings custom objects fetch"
        )

        is_details_fetch = len(meetings) == 1

        # Lookup contacts with accounts - used for sales classification
        # TODO Fully switch over to meeting is_sales property
        only_include_contact_ids = None
        if is_details_fetch:
            only_include_contact_ids = list(
                {
                    invitee.contact_id
                    for invitee in meetings[0].invitees_or_empty_list()
                    if invitee.contact_id
                }
            )

        start_time = time.perf_counter()
        contacts_account_id_pairs = await self.contact_query_service.find_primary_account_id_with_contact_id_pair(
            organization_id=organization_id,
            only_include_contact_ids=only_include_contact_ids,
        )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info("_list_meetings contact fetch")

        # Lookup meeting pipeline info - used for type classification
        start_time = time.perf_counter()
        pipeline_id_to_earliest_meeting_id = (
            await self.meeting_repository.list_earliest_meeting_id_by_pipeline(
                organization_id=organization_id,
                meeting_ids=(
                    [meeting.id for meeting in meetings]
                    if only_include_meeting_ids and specified(only_include_meeting_ids)
                    else None
                ),
            )
        )
        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info("_list_meetings pipeline fetch")

        meetings_v2_list: list[MeetingV2] = []
        start_time = time.perf_counter()

        # Create tasks for each meeting
        populate_tasks = []
        for meeting in meetings:
            list_of_meeting_references = meeting_reference_id_to_references.get(
                meeting.reference_id_type, {}
            ).get(meeting.reference_id, [])

            meeting_bots = meeting_id_to_bot.get(meeting.id, [])
            latest_meeting_bot = self.meeting_bot_service.filter_latest_meeting_bot(
                meeting_bots=meeting_bots
            )

            # Get custom field data from pre-fetched data
            custom_field_data = (
                extension_custom_object_data_group_dto.to_generic_custom_field_value(
                    extension_id=meeting.id
                )
                if extension_custom_object_data_group_dto
                else None
            )

            populate_tasks.append(
                self._populate_meeting_v2(
                    meeting=meeting,
                    meeting_bot=latest_meeting_bot,
                    meeting_references=list_of_meeting_references,
                    contacts_account_id_pairs=contacts_account_id_pairs,
                    pipeline_id_to_earliest_meeting_id=pipeline_id_to_earliest_meeting_id,
                    is_details_fetch=is_details_fetch,
                    user_id=user_id,
                    custom_field_data=(custom_field_data if custom_field_data else {}),
                    fetch_meeting_property_metadata=fetch_meeting_property_metadata,
                )
            )

        # Gather all tasks
        meetings_v2_list = await gather(*populate_tasks)

        elapsed_time = (time.perf_counter() - start_time) * 1000
        logger.bind(elapsed_time=elapsed_time).info("_list_meetings meeting v2 list")
        return meetings_v2_list

    async def list_meeting_history(
        self, organization_id: UUID, request: ListMeetingHistoryRequest
    ) -> list[MeetingV2]:
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=organization_id,
            id=request.meeting_id,
        )
        if not meeting:
            return []

        meetings: list[Meeting] = []
        if meeting.pipeline_id:
            meetings = await self.meeting_repository.list_meetings_by_pipeline_id(
                pipeline_id=meeting.pipeline_id,
                organization_id=organization_id,
            )
        elif meeting.account_id:
            meetings = await self.meeting_repository.list_meetings_by_account_id(
                account_id=meeting.account_id,
                organization_id=organization_id,
            )
        else:
            logger.bind(meeting_id=meeting.id).info(
                "Meeting does not have pipeline_id or account_id"
            )

        if not meetings:
            return []

        return await self._form_meetings_v2_list(
            organization_id=organization_id,
            user_id=None,
            meetings=meetings,
            include_custom_object=False,
            only_include_meeting_ids=UNSET,
        )

    async def authed_list_meeting_history(
        self,
        user_auth_context: UserAuthContext,
        request: ListMeetingHistoryRequest,
    ) -> list[MeetingV2]:
        meeting = await self.meeting_repository.find_by_tenanted_primary_key(
            table_model=Meeting,
            organization_id=user_auth_context.organization_id,
            id=request.meeting_id,
        )
        if not meeting:
            return []

        meeting_v2_list = await self._form_meetings_v2_list(
            organization_id=user_auth_context.organization_id,
            user_id=user_auth_context.user_id,
            meetings=[meeting],
            include_custom_object=False,
            only_include_meeting_ids=UNSET,
        )
        meeting_v2 = meeting_v2_list[0]

        # Convert Meeting to MeetingV2 before checking viewability
        if not await self.is_entity_viewable_by_user(user_auth_context, meeting_v2):
            raise ForbiddenError("You do not have permission to access meeting history")

        meetings: list[Meeting] = []
        if meeting.pipeline_id:
            meetings = await self.meeting_repository.list_meetings_by_pipeline_id(
                pipeline_id=meeting.pipeline_id,
                organization_id=user_auth_context.organization_id,
            )
        elif meeting.account_id:
            meetings = await self.meeting_repository.list_meetings_by_account_id(
                account_id=meeting.account_id,
                organization_id=user_auth_context.organization_id,
            )
        else:
            logger.bind(meeting_id=meeting.id).info(
                "Meeting does not have pipeline_id or account_id"
            )

        if not meetings:
            return []

        return await self._form_meetings_v2_list(
            organization_id=user_auth_context.organization_id,
            user_id=None,
            meetings=meetings,
            include_custom_object=False,
            only_include_meeting_ids=UNSET,
        )

    async def list_meetings_by_sales_action_types_for_pipeline_criteria(
        self,
        user_id: UUID,
        organization_id: UUID,
        sales_action_type_filters: list[list[StandardSalesActionType]],
        pipeline_id: UUID,
    ) -> list[MeetingV2]:
        """
        List meetings that have any of the specified sales action types.

        Args:
            organization_id: Organization ID to filter by
            sales_action_type_filters: List of sales action types to filter by
                Each list contains a set of sales action types to filter by
                e.g. [[StandardSalesActionType.INTRO], [StandardSalesActionType.FOLLOWUP]]
                will return meetings with either intro or followup sales action types
            pipeline_id: Optional pipeline ID to further filter results
            user_id: Optional user ID for permission checks

        Returns:
            List of MeetingV2 objects matching the criteria
        """
        meetings = await self.meeting_repository.list_meetings_by_sales_action_types_for_pipeline(
            organization_id=organization_id,
            sales_action_type_filters=sales_action_type_filters,
            pipeline_id=pipeline_id,
        )

        logger.info(
            "[pre enrichment] list_meetings_by_sales_action_types_for_pipeline_criteria",
            organization_id=organization_id,
            user_id=user_id,
            sales_action_type_filters=sales_action_type_filters,
            pipeline_id=pipeline_id,
            meetings=[
                {
                    "id": meeting.id,
                    "sales_action_types": meeting.sales_action_types,
                }
                for meeting in meetings
            ],
        )

        if not meetings:
            return []

        # Convert DB models to domain models
        post_enrichment = await self._form_meetings_v2_list(
            organization_id=organization_id,
            user_id=user_id,
            meetings=meetings,
            include_custom_object=False,
            only_include_meeting_ids={meeting.id for meeting in meetings},
            fetch_meeting_property_metadata=True,
        )
        logger.info(
            "[post enrichment] list_meetings_by_sales_action_types_for_pipeline_criteria",
            organization_id=organization_id,
            user_id=user_id,
            sales_action_type_filters=sales_action_type_filters,
            pipeline_id=pipeline_id,
            meetings=[
                {
                    "id": meeting.id,
                    "sales_action_types": meeting.sales_action_types,
                }
                for meeting in post_enrichment
            ],
        )
        return post_enrichment


class SingletonMeetingQueryService(Singleton, MeetingQueryService):
    pass


def get_meeting_query_service(
    db_engine: DatabaseEngine,
) -> MeetingQueryService:
    if SingletonMeetingQueryService.has_instance():
        return SingletonMeetingQueryService.get_singleton_instance()
    return SingletonMeetingQueryService(
        meeting_repository=MeetingRepository(
            engine=db_engine,
        ),
        custom_object_service=CustomObjectService(
            custom_object_repo=CustomObjectRepository(engine=db_engine),
            select_list_service=get_select_list_service(engine=db_engine),
        ),
        meeting_bot_service=meeting_bot_service_general(db_engine=db_engine),
        contact_query_service=get_contact_query_service(db_engine=db_engine),
        meeting_s3_manager=get_s3_bucket_manager_by_bucket_name(
            settings.meeting_bucket_name,
        ),
        reference_type_strategy_factory=get_meeting_reference_type_strategy_factory_db_engine(
            db_engine=db_engine
        ),
        file_service=file_service_from_engine(engine=db_engine),
        feature_flag_service=get_feature_flag_service(),
        meeting_ai_rec_service=get_meeting_ai_rec_service(db_engine=db_engine),
    )
