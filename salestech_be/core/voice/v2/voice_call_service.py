"""Voice call service."""

import io
from datetime import UTC, datetime
from typing import Annotated, Any, Literal, cast
from uuid import UUID, uuid4

from fastapi import Depends, Request
from pydantic import SecretStr
from temporalio.common import WorkflowIDReusePolicy
from temporalio.exceptions import WorkflowAlreadyStartedError

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET
from salestech_be.core.activity.service.activity_service import (
    ActivityService,
    get_activity_service_general,
)
from salestech_be.core.activity.types import (
    Activity,
    ActivityPatchRequest,
)
from salestech_be.core.contact.service.contact_query_service import (
    ContactQueryService,
    get_contact_query_service,
)
from salestech_be.core.domain_crm_association.domain_crm_association_service import (
    DomainCRMAssociationService,
    get_domain_crm_association_service,
)
from salestech_be.core.domain_crm_association.types import (
    <PERSON>reateVoiceCallCrmAssociation,
    UpdateDomainCrmAssociation,
)
from salestech_be.core.email.account.email_account_ext import (
    EmailAccountServiceExt,
    get_email_account_service_ext_by_db_engine,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.ff.types import FeatureFlagRequest
from salestech_be.core.files.service.file_service import FileService
from salestech_be.core.meeting.dto.meeting_dto import MeetingDto
from salestech_be.core.meeting.meeting_service import (
    MeetingService,
    meeting_service_factory_general,
)
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.notification.service.notification_service import (
    NotificationService,
    get_notification_service_by_db_engine,
)
from salestech_be.core.notification.types import SendNotificationRequest
from salestech_be.core.organization.service.organization_preference_service import (
    OrganizationPreferenceService,
    organization_preference_service_from_engine,
)
from salestech_be.core.quota.service.quota_service import (
    QuotaService,
    get_quota_service_by_db_engine,
)
from salestech_be.core.task.service.task_v2_service import (
    TaskV2Service,
    get_task_v2_service_general,
)
from salestech_be.core.user.service.user_phone_number_service import (
    UserPhoneNumberService,
    user_phone_number_service_from_engine,
)
from salestech_be.core.user.service.user_preference_service import (
    UserPreferenceService,
    user_preference_service_from_engine,
)
from salestech_be.core.user.service.user_service import (
    UserService,
    get_user_service_general,
)
from salestech_be.core.voice.v2.provider_client import (
    VoiceProviderClient,
    get_provider_client,
)
from salestech_be.core.voice.v2.types import (
    VoiceResponse,
    VoiceTokenResponse,
)
from salestech_be.db.dao.voice_call_repository import VoiceCallRepository
from salestech_be.db.dao.voice_phone_number_repository import VoicePhoneNumberRepository
from salestech_be.db.dao.voice_provider_repository import VoiceProviderRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.activity import (
    ActivitySubType,
    ActivityType,
)
from salestech_be.db.models.domain_crm_association import (
    DomainType,
)
from salestech_be.db.models.meeting import (
    MeetingProvider,
    MeetingReferenceIdType,
    MeetingStatus,
)
from salestech_be.db.models.notification import (
    Notification,
    NotificationReferenceIdType,
    NotificationVoiceCallData,
    NotificationWorkflow,
)
from salestech_be.db.models.organization_info import OrganizationPreferenceKeys
from salestech_be.db.models.quota import (
    QuotaConsumerEntityType,
    QuotaConsumingResource,
)
from salestech_be.db.models.task import TaskReferenceRelationshipType
from salestech_be.db.models.voice_v2 import (
    Call,
    CallDirection,
    CallStatus,
    CallType,
    VoiceProviderAccount,
    VoiceUsageCategory,
)
from salestech_be.integrations.s3.s3_bucket_manager import (
    S3BucketManager,
    get_s3_bucket_manager_by_bucket_name,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import VOICE_TASK_QUEUE
from salestech_be.integrations.twilio.type import UsageRecordsResponse
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.encryptions import fernet_encryption_manager
from salestech_be.settings import settings
from salestech_be.temporal.workflows.voice.send_follow_up_email import (
    VoiceCallFollowUpEmailWorkflow,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.temporal.workflows.voice.types import (
    VoiceCallFollowUpEmailWorkflowInput,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.util.pydantic_types.time import RFC2822Timestamp, ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none
from salestech_be.web.api.meeting.schema import (
    Attendee,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    CreateMeetingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    Invitee,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    PatchMeetingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.organization.schema import (
    FollowupEmailPreferences as OrganizationFollowupEmailPreferences,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.task.schema import (
    PatchTaskRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.user.schema import (
    CallForwardingPreferences,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    DisplayReevoNumberAsCallerPreferences,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    UserPreferenceKeys,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    UserPreferenceResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    VoiceMailPreferences,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.user.schema import (
    FollowupEmailPreferences as UserFollowupEmailPreferences,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)
from salestech_be.web.api.voice.v2.schema import (
    InitiateCallRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    InitiateCallResponse,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    UpdateCallRecordingRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    UpdateCallRecordingStatus,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
    UpdateCallRequest,  # fmt: skip  # tach-ignore(TODO: complete service layer isolation)
)

logger = get_logger(__name__)

MAX_PHONE_NUMBER_LENGTH = 11

CALL_STATUS_LIST_FOR_FOLLOW_UP = [
    CallStatus.VOICEMAIL_LEFT,
    CallStatus.NO_ANSWER,
    CallStatus.BUSY,
]


class VoiceCallService:
    """Voice call service."""

    def __init__(
        self,
        voice_provider_repository: Annotated[VoiceProviderRepository, Depends()],
        voice_phone_number_repository: Annotated[VoicePhoneNumberRepository, Depends()],
        contact_query_service: Annotated[ContactQueryService, Depends()],
        activity_service: Annotated[ActivityService, Depends()],
        voice_call_repository: Annotated[VoiceCallRepository, Depends()],
        user_service: Annotated[UserService, Depends()],
        call_recording_s3_manager: Annotated[S3BucketManager, Depends()],
        voicemail_greeting_s3_manager: Annotated[S3BucketManager, Depends()],
        meeting_service: Annotated[MeetingService, Depends()],
        user_preference_service: Annotated[UserPreferenceService, Depends()],
        organization_preference_service: Annotated[
            OrganizationPreferenceService, Depends()
        ],
        notification_service: Annotated[NotificationService, Depends()],
        ff_service: Annotated[FeatureFlagService, Depends(get_feature_flag_service)],
        email_account_service: Annotated[
            EmailAccountServiceExt, Depends(get_email_account_service_ext_by_db_engine)
        ],
        task_v2_service: Annotated[TaskV2Service, Depends(get_task_v2_service_general)],
        domain_crm_association_service: Annotated[
            DomainCRMAssociationService, Depends()
        ],
        quota_service: Annotated[QuotaService, Depends()],
        user_phone_number_service: Annotated[UserPhoneNumberService, Depends()],
    ):
        self.voice_provider_repository = voice_provider_repository
        self.voice_phone_number_repository = voice_phone_number_repository
        self.contact_query_service = contact_query_service
        self.activity_service = activity_service
        self.voice_call_repository = voice_call_repository
        self.encryption_manager = fernet_encryption_manager
        self.user_service = user_service
        self.call_recording_s3_manager = call_recording_s3_manager
        self.voicemail_greeting_s3_manager = voicemail_greeting_s3_manager
        self.meeting_service = meeting_service
        self.user_preference_service = user_preference_service
        self.organization_preference_service = organization_preference_service
        self.notification_service = notification_service
        self.ff_service = ff_service
        self.email_account_service = email_account_service
        self.task_v2_service = task_v2_service
        self.domain_crm_association_service = domain_crm_association_service
        self.quota_service = quota_service
        self.user_phone_number_service = user_phone_number_service

    def _provider_client(self, provider: str) -> VoiceProviderClient:
        return get_provider_client(
            self.voice_provider_repository,
            provider=provider,
        )

    def _convert_rfc2822_to_zone_required(
        self, timestamp: RFC2822Timestamp
    ) -> ZoneRequiredDateTime:
        """Convert RFC2822 timestamp to ZoneRequiredDateTime."""

        # Convert to UTC timezone if not already
        return timestamp.astimezone(UTC)

    def _format_phone_number(
        self, phone_number: str | None, extension: str | None
    ) -> str | None:
        """Format phone number to standard format (+1 (XXX) XXX-XXXX)."""

        # TODO: phone number extension
        if not phone_number:
            return None

        # Remove all non-digit characters
        digits = "".join(char for char in phone_number if char.isdigit())

        if len(digits) == MAX_PHONE_NUMBER_LENGTH and digits.startswith(
            "1"
        ):  # US number with country code
            formatted = f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
            formatted = f"{formatted},{extension}" if extension else formatted
            return f"+1 {formatted}"

        # Return original if formatting fails
        return phone_number

    async def get_usage_record(
        self,
        organization_id: UUID,
        user_id: UUID,
        provider: str,
        start_date: datetime,
        end_date: datetime,
        category: list[VoiceUsageCategory],
    ) -> UsageRecordsResponse:
        """View a call record.

        Args:
            organization_id (UUID): The organization ID.
            user_id (UUID): The user ID.
            provider (str): The voice provider.
            start_date (datetime): The start date.
            end_date (datetime): The end date.
            category (list[VoiceUsageCategory]): List of categories to retrieve.

        Returns:
            UsageRecordsResponse: The call usage records.
        """
        # Convert dates to UTC timezone if they have timezone info
        # or assume they're already UTC if naive
        start_date = (
            start_date.astimezone(UTC)
            if start_date.tzinfo
            else start_date.replace(tzinfo=UTC)
        )

        end_date = (
            end_date.astimezone(UTC)
            if end_date.tzinfo
            else end_date.replace(tzinfo=UTC)
        )

        # Get usage record from provider client and return it directly
        return await self._provider_client(provider).get_usage_record(
            organization_id=organization_id,
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            category=category,
        )

    async def create_token(
        self, *, organization_id: UUID, user_id: UUID, provider: str, token_type: str
    ) -> VoiceTokenResponse:
        """Create a voice token for the user.

        Args:
            organization_id (UUID): The organization ID.
            user_id (UUID): The user ID.
            provider (str): The voice provider.
            token_type (str): The token type.

        Returns:
            VoiceTokenResponse: The voice token response (follwing provider format).
        """

        # Get the provider account for the organization
        provider_account = await self.voice_provider_repository.get_provider_account_by_organization_and_provider(
            organization_id=organization_id,
            provider=provider,
        )
        if not provider_account:
            raise ResourceNotFoundError(
                "No voice provider account found for organization"
            )

        # Get the application SID from metadata
        if not provider_account.metadata:
            raise ResourceNotFoundError("No metadata found for voice provider account")
        application_sid = provider_account.metadata.get("application_sid", "")
        if not application_sid:
            raise ResourceNotFoundError(
                "No application found for voice provider account"
            )

        # Decrypt the API secret
        api_secret = SecretStr(
            self.encryption_manager.decrypt(provider_account.api_secret)
        )

        # Create the voice token
        voice_token = self._provider_client(provider).create_voice_token(
            account_sid=provider_account.external_id,
            api_key=provider_account.api_key,
            api_secret=api_secret,
            app_sid=application_sid,
            user_id=user_id,
        )

        return VoiceTokenResponse(
            identity=voice_token.identity,
            token=voice_token.jwt_token,
            type=token_type,
        )

    async def _create_call_record(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *,
        to_number: str,
        from_number: str,
        direction: CallDirection,
        organization_id: UUID,
        organization_phone_number_id: UUID,
        provider_account: VoiceProviderAccount,
        contact_id: UUID | None = None,
        pipeline_id: UUID | None = None,
        user_id: UUID | None = None,
        account_id: UUID | None = None,
        call_type: CallType = CallType.WEBRTC,
        call_status: CallStatus = CallStatus.INITIATED,
        metadata: dict[str, Any] | None = None,
        external_id: str | None = None,
        caller_extension: str | None = None,
        recipient_extension: str | None = None,
        task_id: UUID | None = None,
    ) -> Call:
        """Create a call record.

        Args:
            to_number (str): The to number.
            from_number (str): The from number.
            direction (CallDirection): The call direction.
            organization_id (UUID): The organization ID.
            organization_phone_number_id (UUID): The organization phone number ID.
            provider_account (VoiceProviderAccount): The voice provider account.
            contact_id (UUID | None, optional): The contact ID. Defaults to None.
            pipeline_id (UUID | None, optional): The pipeline ID. Defaults to None.
            user_id (UUID | None, optional): The user ID. Defaults to None.
            account_id (UUID | None, optional): The account ID. Defaults to None.
            call_type (CallType, optional): The call type. Defaults to CallType.WEBRTC.
            call_status (CallStatus, optional): The call status. Defaults to CallStatus.INITIATED.
            metadata (dict[str, Any] | None, optional): The metadata. Defaults to None.
            external_id (str | None, optional): The external ID. Defaults to None.
            caller_extension (str | None, optional): The caller extension. Defaults to None.
            recipient_extension (str | None, optional): The recipient extension. Defaults to None.
            task_id (UUID | None, optional): The task ID. Defaults to None.
        Returns:
            Call: The call record.
        """
        # Initialize variables that must be set before creating call record
        metadata = metadata or {}
        metadata["call_sid"] = external_id
        metadata["provider"] = provider_account.provider
        if task_id:
            metadata["task_id"] = task_id

        now = zoned_utc_now()
        call = Call(
            id=uuid4(),
            organization_id=organization_id,
            organization_phone_number_id=organization_phone_number_id,
            voice_provider_account_id=provider_account.id,
            call_type=call_type,
            caller_number=from_number,
            status=call_status,
            direction=direction,
            created_by_user_id=user_id,  # always use the phone number owner as the creator
            caller_id=user_id if direction == CallDirection.OUTBOUND else contact_id,
            contact_id=contact_id,
            pipeline_id=pipeline_id,
            account_id=account_id,
            recipient_number=to_number,
            recipient_id=contact_id if direction == CallDirection.OUTBOUND else user_id,
            metadata=metadata,
            external_id=external_id,
            created_at=now,
            updated_at=now,
            caller_extension=caller_extension,
            recipient_extension=recipient_extension,
        )
        return await self.voice_call_repository.insert(call)

    async def _get_display_caller_number(
        self,
        organization_id: UUID,
        user_id: UUID,
        from_number: str,
    ) -> str:
        """Determine the display caller number based on user preferences.

        Args:
            organization_id (UUID): The organization ID.
            user_id (UUID): The user ID.
            from_number (str): The original from number.

        Returns:
            str: The display caller number to use.
        """
        # Default to the original from_number
        display_caller_number = from_number

        # Get the user
        user = await self.user_service.get_by_id_and_organization_id(
            user_id=user_id,
            organization_id=organization_id,
        )

        if not user:
            return display_caller_number

        # Get user preferences
        user_preference = await self.user_preference_service.get_user_preference(
            user_id=user_id,
            organization_id=organization_id,
            key=UserPreferenceKeys.VOICE,
        )

        # Check if the display_reevo_number_as_caller preference is enabled
        if (
            user_preference
            and user_preference.voice
            and isinstance(
                user_preference.voice.display_reevo_number_as_caller,
                DisplayReevoNumberAsCallerPreferences,
            )
            and not user_preference.voice.display_reevo_number_as_caller.enabled
        ):
            # Try to get the verified primary phone number
            primary_phone_number = (
                await self.user_phone_number_service.get_primary_phone_number(
                    organization_id=organization_id,
                    user_id=user_id,
                )
            )

            # Use the verified primary phone number if available
            if primary_phone_number and primary_phone_number.verified:
                display_caller_number = primary_phone_number.phone_number

        return display_caller_number

    async def handle_voice_call(
        self,
        *,
        call_sid: str,
        to_number: str,
        from_number: str,
        caller: str,
        direction: CallDirection,
        account_sid: str | None = None,
        contact_id: UUID | None = None,
        pipeline_id: UUID | None = None,
        account_id: UUID | None = None,
        caller_extension: str | None = None,
        recipient_extension: str | None = None,
        task_id: UUID | None = None,
    ) -> VoiceResponse:
        """Handle a call by creating a voice response for the user.

        Args:
            call_sid (str): The call SID.
            to_number (str): The to number.
            from_number (str): The from number.
            caller (str): The caller.
            direction (CallDirection): The call direction.
            account_sid (str | None, optional): The account SID. Defaults to None.
            contact_id (UUID | None, optional): The contact ID. Defaults to None.
            pipeline_id (UUID | None, optional): The pipeline ID. Defaults to None.
            account_id (UUID | None, optional): The account ID. Defaults to None.
            task_id (UUID | None, optional): The task ID that will be linked to meeting. Defaults to None.
            caller_extension (str | None, optional): The caller extension. Defaults to None.
            recipient_extension (str | None, optional): The recipient extension. Defaults to None.

        Returns:
            VoiceResponse: The voice response.
        """

        provider_account: VoiceProviderAccount | None = None
        organization_phone_number_id: UUID | None = None
        user_id: UUID | None = None

        # Get the provider account
        if account_sid:
            provider_account = await self.voice_provider_repository.get_provider_account_by_external_id(
                external_id=account_sid,
            )
        if not provider_account:
            logger.error(
                f"No voice provider account found for external account id {account_sid}"
            )
            raise ResourceNotFoundError(
                "No voice provider account found for organization"
            )

        # Get the organization phone number, and verify integrity
        organization_id = provider_account.organization_id
        provider = provider_account.provider
        direction = self._provider_client(provider).get_actual_call_direction(
            caller, direction
        )
        org_phone_number_str: str = (
            from_number if direction == CallDirection.OUTBOUND else to_number
        )
        org_phone_number_and_assignment = await self.voice_phone_number_repository.find_organization_phone_number_by_organization_id_and_number(
            organization_id=organization_id,
            phone_number=org_phone_number_str,
        )
        if not org_phone_number_and_assignment:
            return self._provider_client(provider).build_removed_phone_number_response(
                to_number=to_number,
            )

        user_id = org_phone_number_and_assignment.assigned_to
        organization_phone_number_id = (
            org_phone_number_and_assignment.organization_phone_number_id
        )
        if organization_phone_number_id is None or user_id is None:
            logger.error(
                f"Missing required fields for call record: organization_phone_number_id={organization_phone_number_id}, user_id={user_id}, call_sid={call_sid}"
            )
            raise ResourceNotFoundError("Missing required fields for call record")

        contact_ids = await self._find_contacts_from_call(
            contact_id, direction, organization_id, from_number
        )
        contact_id = contact_ids[0] if contact_ids else None

        # Create the call record and upstream meeting record
        call = await self._create_call_record(
            external_id=call_sid,
            to_number=to_number,
            from_number=from_number,
            direction=direction,
            organization_id=organization_id,
            provider_account=provider_account,
            organization_phone_number_id=organization_phone_number_id,
            user_id=user_id,
            caller_extension=caller_extension if caller_extension else None,
            recipient_extension=recipient_extension if recipient_extension else None,
            contact_id=contact_id if contact_id else None,
            pipeline_id=pipeline_id if pipeline_id else None,
            account_id=account_id if account_id else None,
            task_id=task_id if task_id else None,
        )
        meetingdto = await self._create_meeting_from_call(call)
        meeting_id = meetingdto.meeting.id
        associations = []
        # Create domain_crm_association with call_id, meeting_id, contact_id, pipeline_id, account_id
        # Create contact record
        if direction == CallDirection.INBOUND and contact_ids and len(contact_ids) > 1:
            for c_id in contact_ids:
                associations.append(
                    await self.domain_crm_association_service.create_domain_crm_association(
                        CreateVoiceCallCrmAssociation(
                            organization_id=organization_id,
                            call_id=call.id,
                            meeting_id=meeting_id,
                            task_id=task_id,
                            phone_number=from_number,
                            contact_id=c_id,
                            pipeline_id=pipeline_id,
                            account_id=account_id,
                            created_by_user_id=user_id,
                        )
                    )
                )
        else:
            associations.append(
                await self.domain_crm_association_service.create_domain_crm_association(
                    CreateVoiceCallCrmAssociation(
                        organization_id=organization_id,
                        call_id=call.id,
                        meeting_id=meeting_id,
                        task_id=task_id,
                        phone_number=to_number,
                        contact_id=contact_id,
                        pipeline_id=pipeline_id,
                        account_id=account_id,
                        created_by_user_id=user_id,
                    )
                )
            )

        # Create user record
        # don't add to asssciatoins since this does not include contact_id
        await self.domain_crm_association_service.create_domain_crm_association(
            CreateVoiceCallCrmAssociation(
                organization_id=organization_id,
                call_id=call.id,
                meeting_id=meeting_id,
                task_id=task_id,
                user_id=user_id,
                pipeline_id=pipeline_id,
                account_id=account_id,
                created_by_user_id=user_id,
            )
        )

        if task_id:
            task = await self.task_v2_service.get_entity(
                entity_id=task_id, organization_id=organization_id, user_id=user_id
            )
            if task:
                await self.task_v2_service.update_entity(
                    entity=task,
                    organization_id=organization_id,
                    user_id=user_id,
                    request=PatchTaskRequest(
                        meeting_id=meeting_id,
                        relationship_type=TaskReferenceRelationshipType.RESULT,
                    ),
                )

        # Get the display caller number based on user preferences
        display_caller_number = await self._get_display_caller_number(
            organization_id=organization_id,
            user_id=user_id,
            from_number=from_number,
        )

        # Build the call response and response to the provider (e.g. Twilio)
        return self._provider_client(provider).build_call_response(
            from_number=from_number,
            to_number=to_number,
            user_id=user_id,
            direction=direction,
            recipient_extension=recipient_extension if recipient_extension else None,
            call_sid=call_sid,
            display_caller_number=display_caller_number,
        )

    async def _find_contacts_from_call(
        self,
        contact_id: UUID | None,
        direction: CallDirection,
        organization_id: UUID,
        from_number: str,
    ) -> list[UUID]:
        contact_ids: list[UUID] = []
        if not contact_id and direction == CallDirection.INBOUND:
            # TODO: phone number extension
            contact_ids = (
                await self.contact_query_service.list_contact_ids_by_phone_number(
                    organization_id=organization_id,
                    phone_number=from_number,
                )
            )
        if not contact_id and direction == CallDirection.OUTBOUND:
            raise ValueError("contact_id is required for outbound calls")
        if contact_id:
            contact_ids = [contact_id]

        return contact_ids

    async def _create_meeting_from_call(self, call: Call) -> MeetingDto:
        """Create a meeting from a voice call.

        Args:
            call (Call): The call record

        Returns:
            MeetingDto: The meeting
        """
        # Format phone number for display
        external_phone_number = not_none(
            self._format_phone_number(
                phone_number=call.caller_number
                if call.direction == CallDirection.INBOUND
                else call.recipient_number,
                extension=call.caller_extension
                if call.direction == CallDirection.INBOUND
                else call.recipient_extension,
            )
        )

        if not call.contact_id:
            title = f"Call with {external_phone_number}"
        else:
            contact = await self.contact_query_service.get_by_id(
                contact_id=call.contact_id,
                organization_id=call.organization_id,
            )
            title = (
                f"Call with {external_phone_number}"
                if not contact
                else f"Call with {contact.display_name} ({external_phone_number})"
            )

        user_id = not_none(
            call.caller_id
            if call.direction == CallDirection.OUTBOUND
            else call.recipient_id
        )
        invitees = [Invitee(user_id=user_id, is_organizer=True)]
        if call.contact_id:
            invitees.append(Invitee(contact_id=call.contact_id))

        return await self.meeting_service.create_meeting(
            organization_id=call.organization_id,
            user_id=user_id,
            request=CreateMeetingRequest(
                platform=MeetingProvider.VOICE,
                reference_id=call.id,
                reference_id_type=MeetingReferenceIdType.VOICE_V2,
                starts_at=call.created_at,
                started_at=call.created_at,
                title=title,
                invitees=invitees,
                account_id=call.account_id,
            ),
        )

    async def _should_send_notification(
        self, call: Call, call_status: CallStatus
    ) -> bool:
        """Determine if a notification should be sent for a given call and status.

        Args:
            call (Call): The call to check.
            call_status (CallStatus): The status of the call.

        Returns:
            bool: True if a notification should be sent, False otherwise.
        """

        # No notifications for any outbound calls
        if call.direction == CallDirection.OUTBOUND:
            return False

        # No notifications for normal call status (e.g. ANSWERED, COMPLETED)
        if call_status not in CALL_STATUS_LIST_FOR_FOLLOW_UP:
            return False

        # Feature flag
        if (
            not settings.voice_enable_notifications
            and not await self.ff_service.is_enabled(
                request=FeatureFlagRequest(
                    flag_key="voice-enable-notifications-vfeb2025",
                    organization_id=call.organization_id,
                )
            )
        ):
            return False

        # Trigger notifications for the rest
        return True

    def _get_notification_type_from_call_status(
        self, call_status: CallStatus
    ) -> (
        Literal[
            NotificationWorkflow.VOICE_CALL_VOICEMAIL_LEFT,
            NotificationWorkflow.VOICE_CALL_MISSED,
        ]
        | None
    ):
        """Get the notification type from the call status.

        Args:
            call_status (CallStatus): The call status.

        Returns:
            NotificationWorkflow.VOICE_CALL_VOICEMAIL_LEFT | NotificationWorkflow.VOICE_CALL_MISSED
            None if no eligible notification type.
        """

        result = {
            CallStatus.VOICEMAIL_LEFT: NotificationWorkflow.VOICE_CALL_VOICEMAIL_LEFT,
            CallStatus.NO_ANSWER: NotificationWorkflow.VOICE_CALL_MISSED,
            CallStatus.BUSY: NotificationWorkflow.VOICE_CALL_MISSED,
        }.get(call_status)

        return cast(
            Literal[
                NotificationWorkflow.VOICE_CALL_VOICEMAIL_LEFT,
                NotificationWorkflow.VOICE_CALL_MISSED,
            ]
            | None,
            result,
        )

    async def _send_notification(
        self, call: Call, activity_id: UUID, meeting: MeetingV2, call_status: CallStatus
    ) -> Notification | None:
        """Send a notification for a given call and status.

        Args:
            call (Call): The call to send a notification for.
            activity_id (UUID): The activity ID of the call.
            meeting (MeetingV2): The meeting associated with the call.
            call_status (CallStatus): The status of the call.

        Returns:
            Notification: The notification that was sent, or None if no notification was sent.
        """

        notification_type = self._get_notification_type_from_call_status(call_status)
        if not notification_type:
            return None

        contact_name: str | None = None
        user_name: str | None = None
        if meeting.meeting_participants:
            for participant in meeting.meeting_participants:
                if participant.contact_id and participant.contact_id == call.contact_id:
                    contact_name = participant.name
                    continue
                if (
                    participant.user_id
                    and participant.user_id == call.created_by_user_id
                ):
                    user_name = participant.name
                    continue

        notification_data = NotificationVoiceCallData(
            type=notification_type,
            call_id=str(call.id),
            call_status=call_status,
            meeting_page=f"/meetings/{meeting.id}",
            caller_number=call.caller_number,
            contact_id=str(call.contact_id),
            contact_name=contact_name,
            user_id=str(call.created_by_user_id),
            user_name=user_name,
        )

        notification = await self.notification_service.send_notification(
            send_notification_request=SendNotificationRequest(
                actor_user_id=call.created_by_user_id,  # Phone number owner
                recipient_user_ids=[not_none(call.created_by_user_id)],
                reference_id=str(meeting.id),
                reference_id_type=NotificationReferenceIdType.MEETING,
                activity_id=activity_id,
                idempotency_key=f"voice_call_{call.id}_{call.updated_at}",
                data=notification_data,
            ),
            organization_id=call.organization_id,
        )
        logger.bind(
            notification_id=notification.id,
            organization_id=call.organization_id,
            reference_id=notification.reference_id,
            reference_id_type=notification.reference_id_type,
            meeting_id=meeting.id,
            call_id=call.id,
            notification_data=notification_data,
        ).info("Notification sent for call")

        if (call.metadata or {}).get("latest_notification_id"):
            await self.notification_service.archive_notification_on_provider(
                notification_id=UUID(
                    (call.metadata or {}).get("latest_notification_id")
                ),
                organization_id=call.organization_id,
                user_id=not_none(call.created_by_user_id),
            )
        return notification

    async def _trigger_follow_up_email(self, call: Call) -> None:
        """Trigger a follow-up email for a call if eligible.

        Args:
            call (Call): The call to trigger a follow-up email for.
        """

        if not await self.ff_service.is_enabled(
            request=FeatureFlagRequest(
                flag_key="voice-enable-follow-up-email-vApr2025",
                organization_id=call.organization_id,
            )
        ):
            return

        if call.direction == CallDirection.INBOUND:
            logger.bind(call_id=call.id).info(
                "Inbound call, don't trigger follow up email"
            )
            return

        if call.metadata and "task_id" in call.metadata:
            logger.bind(metadata=call.metadata).info(
                "Call from task, don't trigger follow up email"
            )
            return

        call_status = call.status
        if call_status not in CALL_STATUS_LIST_FOR_FOLLOW_UP:
            return

        email_template_id = await self._get_email_template_id(call)
        if not call.contact_id or not email_template_id:
            logger.info(
                f"Call contact id or email template id not found for call {call.id}"
            )
            return

        logger.bind(
            call_id=call.id,
            external_id=call.external_id,
            call_status=call_status,
            contact_id=call.contact_id,
            created_by_user_id=call.created_by_user_id,
            organization_id=call.organization_id,
            email_template_id=email_template_id,
        ).info("Triggering voice call follow-up email")
        try:
            client = await get_temporal_client()
            workflow_id = f"voice_call_send_follow_up_email_{call.id}"

            await client.start_workflow(
                VoiceCallFollowUpEmailWorkflow.run,
                args=[
                    VoiceCallFollowUpEmailWorkflowInput(
                        call_id=call.id,
                        organization_id=call.organization_id,
                        user_id=not_none(call.created_by_user_id),
                        contact_id=not_none(call.contact_id),
                        email_template_id=email_template_id,
                        account_id=call.account_id,
                        pipeline_id=call.pipeline_id,
                    ),
                ],
                id=workflow_id,
                task_queue=VOICE_TASK_QUEUE,
                id_reuse_policy=WorkflowIDReusePolicy.REJECT_DUPLICATE,
            )
        except WorkflowAlreadyStartedError as e:
            logger.bind(
                call_id=call.id,
                organization_id=call.organization_id,
                user_id=call.created_by_user_id,
                external_id=call.external_id,
                call_status=call_status,
                contact_id=call.contact_id,
                error=e,
            ).warning("Workflow already started")
        except Exception as e:
            logger.bind(
                call_id=call.id,
                organization_id=call.organization_id,
                user_id=call.created_by_user_id,
                external_id=call.external_id,
                call_status=call_status,
                contact_id=call.contact_id,
                error=e,
            ).error("Error sending follow-up email")

    async def _get_email_template_id(self, call: Call) -> UUID | None:
        """Get the email template ID for a call.

        Args:
            call (Call): The call to get the email template ID for.

        Returns:
            UUID | None: The email template ID, or None if not found.
        """

        call_status = call.status
        email_template_id = None

        # Going through preference settings to get the email template
        org_email_settings = (
            await self.organization_preference_service.get_organization_preference(
                organization_id=call.organization_id,
                key=OrganizationPreferenceKeys.VOICE,
            )
        )

        # Organization level sets whether followup emails are enabled or not (with `voice` key)
        if (
            org_email_settings.voice is not None
            and isinstance(
                org_email_settings.voice.followup_email,
                OrganizationFollowupEmailPreferences,
            )
            and org_email_settings.voice.followup_email.enabled
        ):
            # User to choose a specific template from their own preferences
            user_email_settings = (
                await self.user_preference_service.get_user_preference(
                    user_id=not_none(call.created_by_user_id),
                    organization_id=call.organization_id,
                    key=UserPreferenceKeys.VOICE,
                )
            )
            if user_email_settings.voice is not None and isinstance(
                user_email_settings.voice.followup_email, UserFollowupEmailPreferences
            ):
                # First try to use the user's selected template if available
                email_template_id = (
                    user_email_settings.voice.followup_email.selected_template_id
                )

            # If no user preference, fall back to organization default template
            if not email_template_id:
                default_template_id = (
                    org_email_settings.voice.followup_email.default_template_id
                )
                if default_template_id:
                    email_template_id = default_template_id
                else:
                    logger.bind(
                        call_id=call.id,
                        external_id=call.external_id,
                        call_status=call_status,
                        user_id=call.created_by_user_id,
                    ).warning("No template selection or default template found")
        else:
            logger.bind(
                call_id=call.id,
                external_id=call.external_id,
                call_status=call_status,
                user_id=call.created_by_user_id,
            ).warning(
                "No followup email preferences found or disabled at organization level"
            )

        return email_template_id

    async def handle_voice_call_status(  # noqa: C901, PLR0912
        self,
        call_sid: str,
        call_status: CallStatus,
        parent_call_sid: str | None,
        sequence_number: int | None = None,
        timestamp: RFC2822Timestamp | None = None,
        call_duration: int | None = None,
        account_sid: str | None = None,
    ) -> None:
        """Handle a call status update from Twilio webhook.

        Args:
            call_sid (str): The call SID.
            call_status (CallStatus): The call status.
            parent_call_sid (str | None): The parent call SID.
            sequence_number (int | None, optional): The sequence number. Defaults to None.
            timestamp (RFC2822Timestamp | None, optional): The timestamp. Defaults to None.
            call_duration (int | None, optional): The call duration. Defaults to None.
            account_sid (str | None, optional): The account SID. Defaults to None.
        """

        # Get the call record
        call = await self.voice_call_repository.find_by_external_id(
            # Always update the parent call record if there is one
            external_id=not_none(parent_call_sid or call_sid)
        )
        if not call:
            logger.bind(
                account_sid=account_sid,
                call_sid=call_sid,
                parent_call_sid=parent_call_sid,
            ).warning("Call not found, could be due to soft release")
            return

        # Check if this is an old webhook event
        metadata = call.metadata or {}
        current_sequence = metadata.get("call_status_sequence_number", 0)
        if sequence_number is not None and sequence_number < current_sequence:
            logger.info(
                f"Status - ignoring outdated webhook {call_status} {call_sid} {parent_call_sid} {sequence_number}"
            )
            return

        now = zoned_utc_now()
        zone_required_timestamp = now
        if timestamp:
            zone_required_timestamp = self._convert_rfc2822_to_zone_required(timestamp)
        metadata["call_status_sequence_number"] = sequence_number
        if parent_call_sid:
            metadata["child_call_sid"] = call_sid
        updates: dict[str, Any] = {  # type: ignore[explicit-any] # TODO: fix-any-annotation
            "status": call_status,
            "status_changed_at": zone_required_timestamp,
            "updated_at": now,
            "metadata": metadata,
        }

        # Update specific fields based on call status
        match call_status:
            case CallStatus.INITIATED | CallStatus.RINGING | CallStatus.IN_PROGRESS:
                if not call.started_at:
                    updates["started_at"] = zone_required_timestamp
            case (
                CallStatus.COMPLETED
                | CallStatus.FAILED
                | CallStatus.BUSY
                | CallStatus.NO_ANSWER
                | CallStatus.ANSWERED
                | CallStatus.CANCELED
                | CallStatus.VOICEMAIL_LEFT
            ):
                updates["ended_at"] = zone_required_timestamp
                if call_duration is not None:
                    updates["duration"] = call_duration
                    # Consume seconds quota when call ends with duration
                    if call_duration > 0:
                        await self.quota_service.increase_usage(
                            organization_id=call.organization_id,
                            entity_id=not_none(call.created_by_user_id),
                            entity_type=QuotaConsumerEntityType.USER,
                            resource=QuotaConsumingResource.VOICE_SECONDS,
                            usage=call_duration,
                            timestamp=zoned_utc_now(),
                        )

        if call_status == CallStatus.IN_PROGRESS:
            meeting_dto = await self.meeting_service.get_meeting_by_reference_id(
                reference_id=call.id,
                reference_id_type=MeetingReferenceIdType.VOICE_V2,
                organization_id=call.organization_id,
            )

            await self.meeting_service.patch_meeting_v2(
                meeting_id=meeting_dto.meeting.id,
                organization_id=call.organization_id,
                request=PatchMeetingRequest(
                    started_at=zone_required_timestamp,
                ),
                user_id=not_none(call.created_by_user_id),
            )

        if call_status not in (
            CallStatus.INITIATED,
            CallStatus.RINGING,
            CallStatus.IN_PROGRESS,
        ):
            user_id = not_none(
                call.caller_id
                if call.direction == CallDirection.OUTBOUND
                else call.recipient_id
            )
            attendees = [Attendee(user_id=user_id)]
            if call.contact_id:
                attendees.append(Attendee(contact_id=call.contact_id))

            meeting_dto = await self.meeting_service.get_meeting_by_reference_id(
                reference_id=call.id,
                reference_id_type=MeetingReferenceIdType.VOICE_V2,
                organization_id=call.organization_id,
            )

            if meeting_dto.meeting.status == MeetingStatus.ACTIVE:
                await self.meeting_service.end_active_meeting(
                    reference_id=call.id,
                    reference_id_type=MeetingReferenceIdType.VOICE_V2,
                    organization_id=call.organization_id,
                    attendees=attendees,
                )
                activity = await self._get_activity_from_reference_id(
                    call=call,
                    reference_id=meeting_dto.meeting.id,
                )
                display_name = await self._get_new_activity_display_name(
                    call_status=call_status,
                    current_title=meeting_dto.meeting.title,
                )
                await self._update_activity(
                    call=call,
                    call_status=call_status,
                    call_sid=call_sid,
                    activity=not_none(activity),
                    display_name=display_name,
                )
                updated_meeting = await self.meeting_service.patch_meeting_v2(
                    meeting_id=meeting_dto.meeting.id,
                    organization_id=call.organization_id,
                    request=PatchMeetingRequest(
                        title=display_name,
                    ),
                    user_id=user_id,
                )
                if await self._should_send_notification(call, call_status):
                    notification = await self._send_notification(
                        not_none(call),
                        not_none(activity).id,
                        updated_meeting,
                        call_status,
                    )
                    if notification:
                        updates["metadata"].update(
                            {"latest_notification_id": notification.id}
                        )

        # Update the call record
        call = not_none(
            await self.voice_call_repository.update_by_id(
                call.id,
                column_to_update=updates,
            )
        )

        # additional actions
        if call.status in (
            CallStatus.BUSY,
            CallStatus.NO_ANSWER,
            # CallStatus.VOICEMAIL_LEFT, -- handle in another path, do not duplicate
        ):
            await self._trigger_follow_up_email(call)

    async def _get_activity_from_reference_id(
        self, call: Call, reference_id: UUID
    ) -> Activity | None:
        """Get activity from reference id (and type is VOICE_CALL).

        Args:
            call (Call): The call.
            reference_id (UUID): The reference id.

        Returns:
            Activity: The activity or None if not found.
        """

        list_of_activities = (
            await self.activity_service.list_activities_by_reference_ids_and_type(
                reference_ids=[str(reference_id)],
                organization_id=call.organization_id,
                activity_type=ActivityType.VOICE_CALL,
            )
        )

        if not list_of_activities:
            logger.warning(f"Status - no activities found for call {call.id}")
            return None

        if len(list_of_activities) > 1:
            raise ValueError("Multiple activities found for meeting")

        return list_of_activities[0]

    async def _update_activity(
        self,
        call: Call,
        call_sid: str,
        activity: Activity,
        call_status: CallStatus | None = None,
        display_name: str | None = None,
    ) -> None:
        """Update the activity record.

        Args:
            call (Call): The call.
            call_sid (str): The call sid.
            activity (Activity): The activity.
            call_status (CallStatus | None, optional): The call status. Defaults to None.
            display_name (str | None, optional): The display name. Defaults to None.
        """

        # TODO: (hao) revisit this
        if (
            activity.sub_type
            in (
                ActivitySubType.CALL_INBOUND_ANSWERED,
                ActivitySubType.CALL_OUTBOUND_ANSWERED,
                ActivitySubType.CALL_INBOUND_NO_ANSWER,
                ActivitySubType.CALL_OUTBOUND_NO_ANSWER,
            )
            and call_status != CallStatus.VOICEMAIL_LEFT
        ):
            logger.info(f"Status - activity {activity.id} is already updated")
            return

        try:
            sub_type = (
                await self._get_activity_sub_type_for_call(call, call_status) or UNSET
            )
        except ValueError as e:
            logger.error(f"Invalid call direction or status: {e}")
            return

        # create activity record
        if call.created_by_user_id is None:
            logger.error(f"Status - no created by user id for call {call_sid}")
            return
        else:
            await self.activity_service.patch_activity_by_id(
                activity_id=activity.id,
                organization_id=call.organization_id,
                req=ActivityPatchRequest(
                    sub_type=sub_type,
                    display_name=display_name if display_name is not None else UNSET,
                ),
            )

    async def _get_new_activity_display_name(
        self,
        *,
        call_status: CallStatus,
        current_title: str,
    ) -> str:
        """Get the new activity display name.

        Args:
            call_status (CallStatus): The call status.
            current_title (str): The current activity title.

        Returns:
            str: The new activity display name.
        """
        match call_status:
            case CallStatus.ANSWERED | CallStatus.COMPLETED:
                return current_title
            case CallStatus.VOICEMAIL_LEFT:
                current_title = current_title.replace("Call with", "Voicemail from")
                return current_title.replace("Missed call from", "Voicemail from")
            case _:  # All other inbound statuses
                return current_title.replace("Call with", "Missed call from")

    async def _get_activity_sub_type_for_call(
        self,
        call: Call,
        call_status: CallStatus | None = None,
    ) -> ActivitySubType | None:
        """Get the activity sub type for a call.

        Args:
            call (Call): The call.
            call_status (CallStatus | None, optional): The call status. Defaults to None.

        Returns:
            ActivitySubType | None: The activity sub type.
        """

        direction = call.direction

        match (direction, call_status):
            case (CallDirection.INBOUND, CallStatus.ANSWERED | CallStatus.COMPLETED):
                return ActivitySubType.CALL_INBOUND_ANSWERED
            case (CallDirection.INBOUND, _):  # All other inbound statuses
                return ActivitySubType.CALL_INBOUND_NO_ANSWER
            case (CallDirection.OUTBOUND, CallStatus.ANSWERED | CallStatus.COMPLETED):
                return ActivitySubType.CALL_OUTBOUND_ANSWERED
            case (CallDirection.OUTBOUND, _):  # All other outbound statuses
                return ActivitySubType.CALL_OUTBOUND_NO_ANSWER
            case (_, None):
                return None
            case _:
                raise ValueError(
                    f"Invalid call direction or status: {direction} {call_status}"
                )

    async def handle_recording_completed(  # noqa C901
        self,
        call_sid: str,
        recording_sid: str,
        recording_url: str,
        account_sid: str,
        recording_duration: int | None = None,
    ) -> None:
        """Handle recording completed event.
           NOTE: meeting service will analyse transcript

        Args:
            call_sid (str): The call sid.
            recording_sid (str): The recording sid.
            recording_url (str): The recording url.
            account_sid (str): The account sid.
            recording_duration (int | None, optional): The recording duration. Defaults to None.
        """

        provider_account: VoiceProviderAccount | None = None
        if account_sid:
            provider_account = await self.voice_provider_repository.get_provider_account_by_external_id(
                external_id=account_sid,
            )
        if not provider_account:
            logger.error(
                f"No voice provider account found for external account id {account_sid}"
            )
            raise ResourceNotFoundError(
                "No voice provider account found for organization"
            )
        call = not_none(await self.voice_call_repository.find_by_external_id(call_sid))
        user_id = not_none(call.caller_id or call.recipient_id)

        # TODO: move to temporal worker
        recording = await self._provider_client(
            provider_account.provider
        ).download_recording(
            account_sid=account_sid,
            recording_sid=recording_sid,
        )
        s3_key = FileService.generate_s3_key(
            user_id=user_id,
            organization_id=call.organization_id,
            file_name=f"{call_sid}.mp3",
        )
        await self.call_recording_s3_manager.copy_chunks_to_s3(
            key=s3_key, chunks=FileService.fetch_file_in_chunks(io.BytesIO(recording))
        )
        metadata = call.metadata or {}
        metadata["external_recording_url"] = recording_url
        updates: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if recording_duration is not None:
            metadata["recording_duration"] = recording_duration
            updates = {
                "recording_url": s3_key,
                "updated_at": zoned_utc_now(),
                "metadata": metadata,
            }
            if metadata.get("voicemail_left", False) or self._provider_client(
                provider_account.provider
            ).is_voicemail_left(call, recording_duration):
                metadata["voicemail_left"] = True
                updates["status"] = CallStatus.VOICEMAIL_LEFT
        presigned_url, _ = await self.call_recording_s3_manager.generate_presigned_url(
            key=s3_key,
        )

        if metadata.get("voicemail_left", False):
            meeting_dto = await self.meeting_service.get_meeting_by_reference_id(
                reference_id=call.id,
                reference_id_type=MeetingReferenceIdType.VOICE_V2,
                organization_id=call.organization_id,
            )
            if not meeting_dto:
                logger.warning(f"amd - meeting not found {call.id}")
                return

            activity = await self._get_activity_from_reference_id(
                call=call,
                reference_id=meeting_dto.meeting.id,
            )
            display_name = await self._get_new_activity_display_name(
                call_status=CallStatus.VOICEMAIL_LEFT,
                current_title=meeting_dto.meeting.title,
            )
            await self._update_activity(
                call=call,
                call_status=CallStatus.VOICEMAIL_LEFT,
                call_sid=call_sid,
                activity=not_none(activity),
                display_name=display_name,
            )
            updated_meeting = await self.meeting_service.patch_meeting_v2(
                meeting_id=meeting_dto.meeting.id,
                organization_id=call.organization_id,
                request=PatchMeetingRequest(
                    title=display_name,
                ),
                user_id=user_id,
            )
            if await self._should_send_notification(call, CallStatus.VOICEMAIL_LEFT):
                notification = await self._send_notification(
                    call=call,
                    activity_id=not_none(activity).id,
                    meeting=updated_meeting,
                    call_status=CallStatus.VOICEMAIL_LEFT,
                )
                if notification:
                    updates.setdefault("metadata", {})
                    updates["metadata"].update(
                        {"latest_notification_id": notification.id}
                    )

        if updates:
            call = not_none(
                await self.voice_call_repository.update_by_external_id(
                    call_sid,
                    column_to_update=updates,
                )
            )

        if call.status == CallStatus.VOICEMAIL_LEFT:
            await self._trigger_follow_up_email(call)

        await self.meeting_service.analyze_ended_meeting(
            reference_id=call.id,
            reference_id_type=MeetingReferenceIdType.VOICE_V2,
            organization_id=call.organization_id,
            media_url=presigned_url,
        )

    async def handle_answering_machine_detection(
        self,
        call_sid: str,
        account_sid: str,
        answered_by: str,
        parent_call_sid: str | None,
        call_id: UUID | None = None,
    ) -> None:
        """Handle answering machine detection event.

        Args:
            call_sid (str): The call sid.
            account_sid (str): The account sid.
            answered_by (str): The answered by.
            parent_call_sid (str | None): The parent call sid.
            call_id (UUID | None, optional): The call id. Defaults to None.
        """

        external_id = parent_call_sid or call_sid
        call: Call | None = None
        if external_id:
            call = await self.voice_call_repository.find_by_external_id(external_id)
        if call_id:
            call = await self.voice_call_repository.find_by_id(call_id)
        if not call:
            logger.error(
                f"amd - call not found {account_sid} {parent_call_sid} {call_sid} {call_id}"
            )
            raise ResourceNotFoundError("Call not found")
        metadata = call.metadata or {}
        metadata["answered_by"] = answered_by
        await self.voice_call_repository.update_by_id(
            call.id,
            column_to_update={"metadata": metadata},
        )

        logger.bind(
            call_sid=call_sid,
            answered_by=answered_by,
            parent_call_sid=parent_call_sid,
        ).info("amd - call status updated")

    async def hanlde_incoming_voicemail(
        self,
        call_sid: str | None,
        parent_call_sid: str | None,
        voicemail_left: bool,
        account_sid: str | None,
    ) -> VoiceResponse:
        """Handle incoming voicemail event, and hang up the call.

        Args:
            call_sid (str | None): The call sid.
            parent_call_sid (str | None): The parent call sid.
            voicemail_left (bool): The voicemail left.
            account_sid (str | None): The account sid.

        Returns:
            VoiceResponse: The voice response.
        """

        call = not_none(
            await self.voice_call_repository.find_by_external_id(
                not_none(parent_call_sid or call_sid)
            )
        )
        provider_account = not_none(
            await self.voice_provider_repository.get_provider_account_by_external_id(
                external_id=not_none(account_sid),
            )
        )
        metadata = call.metadata or {}
        metadata["voicemail_left"] = voicemail_left
        await self.voice_call_repository.update_by_external_id(
            not_none(call.external_id),
            column_to_update={"metadata": metadata},
        )
        return self._provider_client(provider_account.provider).hangup_response()

    async def handle_call_redirect(
        self,
        account_sid: str,
        call_sid: str,
        parent_call_sid: str | None,
        call_status: CallStatus | None,
    ) -> VoiceResponse:
        """Handle call redirect event and signal to provider.

        Args:
            account_sid (str): The account sid.
            call_sid (str): The call sid.
            parent_call_sid (str | None): The parent call sid.
            call_status (CallStatus | None): The call status.

        Returns:
            VoiceResponse: The voice response.
        """

        logger.bind(
            account_sid=account_sid,
            call_sid=call_sid,
            parent_call_sid=parent_call_sid,
            call_status=call_status,
        ).info("handle_call_redirect")
        call = not_none(
            await self.voice_call_repository.find_by_external_id(
                not_none(parent_call_sid or call_sid)
            )
        )
        provider_account = not_none(
            await self.voice_provider_repository.get_provider_account_by_external_id(
                external_id=not_none(account_sid),
            )
        )
        user = not_none(
            await self.user_service.get_by_id_and_organization_id(
                user_id=not_none(call.recipient_id),
                organization_id=call.organization_id,
            )
        )
        match call_status:
            case (
                CallStatus.RINGING  # the call will actually ring for a sec and ends with RINGING status even thoughth there is no web client connected
                | CallStatus.NO_ANSWER
                | CallStatus.BUSY
                | CallStatus.FAILED
            ):
                voicemail_settings: UserPreferenceResponse = (
                    await self.user_preference_service.get_user_preference(
                        user_id=user.id,
                        organization_id=call.organization_id,
                        key=UserPreferenceKeys.VOICE,
                    )
                )
                if (
                    voicemail_settings
                    and voicemail_settings.voice is not None
                    and type(voicemail_settings.voice.voicemail) is VoiceMailPreferences
                    and voicemail_settings.voice.voicemail.active_greeting_recording_s3_key
                ):
                    (
                        voicemail_greeting_url,
                        _,
                    ) = await self.voicemail_greeting_s3_manager.generate_presigned_url(
                        key=str(
                            voicemail_settings.voice.voicemail.active_greeting_recording_s3_key
                        )
                    )
                else:
                    voicemail_greeting_url = None

                logger.bind(
                    user_id=call.created_by_user_id,
                    organization_id=call.organization_id,
                    voicemail_greeting_url=voicemail_greeting_url,
                ).info("Building voicemail response")

                redirect_call = False
                if (
                    voicemail_settings.voice is not None
                    and isinstance(
                        voicemail_settings.voice.call_forwarding,
                        CallForwardingPreferences,
                    )
                    and isinstance(
                        voicemail_settings.voice.call_forwarding.enabled, bool
                    )
                ):
                    redirect_call = voicemail_settings.voice.call_forwarding.enabled
                else:
                    # If no forwarding preferences are set, use the global setting
                    redirect_call = settings.twilio_inbound_call_forwarding_enabled

                if redirect_call and user and user.phone_number:
                    return self._provider_client(
                        provider_account.provider
                    ).build_redirect_response(
                        from_number=call.caller_number,
                        user=user,
                        call_sid=call_sid,
                        voicemail_greeting_url=voicemail_greeting_url,
                    )
                return self._provider_client(
                    provider_account.provider
                ).build_voicemail_response(voicemail_greeting_url)
            case _:
                return self._provider_client(
                    provider_account.provider
                ).hangup_response()

    async def update_call_recording(
        self,
        request: UpdateCallRecordingRequest,
    ) -> None:
        """Update the recording status of a call, to the provider and track in db.

        Args:
            request (UpdateCallRecordingRequest): The request.
        """

        call = not_none(
            await self.voice_call_repository.find_by_external_id(
                request.external_call_id
            )
        )
        provider_account = not_none(
            await self.voice_provider_repository.get_provider_account(
                organization_id=call.organization_id,
                provider_account_id=call.voice_provider_account_id,
            )
        )
        await self._provider_client(provider_account.provider).update_call_recording(
            call_sid=request.external_call_id,
            status=request.status,
            provider_account_external_id=provider_account.external_id,
        )
        now = zoned_utc_now()
        metadata = call.metadata or {}
        match request.status:
            case UpdateCallRecordingStatus.PAUSED:
                recording_paused_at = metadata.get("recording_paused_at", [])
                recording_paused_at.append(now)
                metadata["recording_paused_at"] = recording_paused_at
            case UpdateCallRecordingStatus.IN_PROGRESS:
                recording_resumed_at = metadata.get("recording_resumed_at", [])
                recording_resumed_at.append(now)
                metadata["recording_resumed_at"] = recording_resumed_at
        await self.voice_call_repository.update_by_external_id(
            request.external_call_id, column_to_update={"metadata": metadata}
        )

    async def initiate_bridge_call(
        self,
        organization_id: UUID,
        user_id: UUID,
        request: InitiateCallRequest,
    ) -> InitiateCallResponse:
        """Initiate a bridge call (via provider).

        Args:
            organization_id (UUID): The organization ID.
            user_id (UUID): The user ID.
            request (InitiateCallRequest): The request.

        Returns:
            InitiateCallResponse: The response.
        """

        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            contact_id=request.contact_id,
            pipeline_id=request.pipeline_id,
            account_id=request.account_id,
            to_number=request.to_number,
            provider=request.provider,
        ).info("Initiating bridge call")

        user = await self.user_service.get_by_id_and_organization_id(
            user_id=user_id,
            organization_id=organization_id,
        )
        if not user or not user.phone_number:
            raise ResourceNotFoundError("User phone number not found")

        provider_account = await self.voice_provider_repository.get_provider_account_by_organization_and_provider(
            organization_id=organization_id,
            provider=request.provider,
        )
        if not provider_account:
            raise ResourceNotFoundError("Provider account not found")

        reevo_phone_number = (
            await self.voice_phone_number_repository.get_existing_number_owned_by_user(
                organization_id=organization_id,
                user_id=user_id,
            )
        )

        if not reevo_phone_number:
            raise ResourceNotFoundError("Reevo phone number not found")

        # outbound only for now
        direction = CallDirection.OUTBOUND
        call = await self._create_call_record(
            # external_id=external_call_object.external_id,
            to_number=request.to_number,
            from_number=user.phone_number,
            direction=direction,
            organization_id=organization_id,
            provider_account=provider_account,
            organization_phone_number_id=UUID(
                str(not_none(reevo_phone_number["organization_phone_number_id"]))
            ),
            user_id=user_id,
            contact_id=request.contact_id if request.contact_id else None,
            pipeline_id=request.pipeline_id if request.pipeline_id else None,
            account_id=request.account_id if request.account_id else None,
            call_type=not_none(request.type),
            metadata={
                "bridge_number": str(reevo_phone_number["number"]),
                "bridge_number_id": str(reevo_phone_number["phone_number_id"]),
            },
            caller_extension=request.caller_extension,
            recipient_extension=request.recipient_extension,
            task_id=request.task_id if request.task_id else None,
        )
        meeting_dto = await self._create_meeting_from_call(call)

        # Create domain_crm_association with call_id, meeting_id, contact_id, pipeline_id, account_id
        # Create contact record
        await self.domain_crm_association_service.create_domain_crm_association(
            CreateVoiceCallCrmAssociation(
                organization_id=organization_id,
                call_id=call.id,
                meeting_id=meeting_dto.meeting.id,
                task_id=request.task_id if request.task_id else None,
                phone_number=user.phone_number,
                contact_id=request.contact_id,
                pipeline_id=request.pipeline_id,
                account_id=request.account_id,
                created_by_user_id=user_id,
            )
        )

        # Create user record
        await self.domain_crm_association_service.create_domain_crm_association(
            CreateVoiceCallCrmAssociation(
                organization_id=organization_id,
                call_id=call.id,
                meeting_id=meeting_dto.meeting.id,
                task_id=request.task_id if request.task_id else None,
                user_id=user_id,
                pipeline_id=request.pipeline_id,
                account_id=request.account_id,
                created_by_user_id=user_id,
            )
        )

        client = self._provider_client(provider_account.provider)
        user_preference = await self.user_preference_service.get_user_preference(
            user_id=user_id,
            organization_id=organization_id,
            key=UserPreferenceKeys.VOICE,
        )
        display_caller_number = user.phone_number
        if (  # TODO: only use user preference for now, add org level control if we need
            user_preference
            and user_preference.voice
            and isinstance(
                user_preference.voice.display_reevo_number_as_caller,
                DisplayReevoNumberAsCallerPreferences,
            )
            and user_preference.voice.display_reevo_number_as_caller.enabled
        ):
            # Use the Reevo number as display caller number
            display_caller_number = str(reevo_phone_number["number"])

        external_call_object = await client.create_bridge_call(
            from_number=str(reevo_phone_number["number"]),
            caller_number=user.phone_number,
            to_number=request.to_number,
            recipient_extension=request.recipient_extension,
            display_caller_number=display_caller_number,
            external_id=provider_account.external_id,
            call_id=call.id,
        )
        await self.voice_call_repository.update_by_id(
            call.id,
            column_to_update={
                "external_id": external_call_object.external_id,
            },
        )
        return InitiateCallResponse(
            call_id=call.id,
            meeting_id=meeting_dto.meeting.id,
            external_call_id=external_call_object.external_id,
        )

    async def update_call(
        self,
        call_id: UUID,
        request: UpdateCallRequest,
    ) -> Call:
        """Update the disposition of a call."""
        call = await self.voice_call_repository.find_by_id(call_id)
        if not call:
            raise ResourceNotFoundError("Call not found")

        # Get the update data that will be applied to the call
        update_data = request.model_dump(exclude_unset=True)

        # Update the call in the database
        updated_call = await self.voice_call_repository.update_by_id(
            call.id,
            column_to_update=update_data,
        )
        if not updated_call:
            raise ResourceNotFoundError("Call not found")

        # Find existing domain_crm_associations for this call
        associations = await self.domain_crm_association_service.get_domain_crm_associations_by_related_id(
            domain_id=call_id,
            domain_type=DomainType.VOICE_CALL,
        )

        association_related_columns = [
            "contact_id",
            "pipeline_id",
            "account_id",
        ]

        if associations:
            for association in associations:
                crm_update_data = {
                    field: value
                    for field, value in update_data.items()
                    if (
                        field in association_related_columns
                        and value is not None
                        and (
                            field != "contact_id" or association.contact_id is not None
                            # only update contact_id to one of the associations
                        )
                    )
                }

                # Only update if we have data to update
                if crm_update_data:
                    await self.domain_crm_association_service.update_domain_crm_association(
                        association_id=association.id,
                        domain_crm_association=UpdateDomainCrmAssociation(
                            **crm_update_data
                        ),
                    )
        else:
            raise ResourceNotFoundError("Domain CRM association not found")

        return updated_call

    async def get_call(
        self,
        call_id: UUID,
    ) -> Call:
        """Get the disposition of a call."""
        call = await self.voice_call_repository.find_by_id(call_id)
        if not call:
            raise ResourceNotFoundError("Call not found")
        return call

    async def verify_phone_number(
        self,
        organization_id: UUID,
        user_id: UUID,
        phone_number: str,
        provider: str = "twilio",
    ) -> str:
        """Verify if a phone number is valid.

        Args:
            organization_id (UUID): The organization ID.
            user_id (UUID): The user ID.
            phone_number (str): The phone number to verify.
            provider (str, optional): The voice provider. Defaults to "twilio".

        Returns:
            bool: True if the phone number is valid, False otherwise.
        """
        # Get the provider account for the organization
        provider_account = await self.voice_provider_repository.get_provider_account_by_organization_and_provider(
            organization_id=organization_id,
            provider=provider,
        )
        if not provider_account:
            logger.warning(
                f"No provider account found for organization {organization_id}"
            )
            return "Error"

        # Use the provider client to verify the phone number
        return await self._provider_client(provider).verify_phone_number(
            phone_number=phone_number,
            user_id=user_id,
            organization_id=organization_id,
        )


class SingletonVoiceCallService(Singleton, VoiceCallService):
    pass


def voice_call_service_from_engine(engine: DatabaseEngine) -> VoiceCallService:
    """Create a VoiceCallService instance from a database engine."""
    return SingletonVoiceCallService(
        voice_provider_repository=VoiceProviderRepository(engine=engine),
        voice_phone_number_repository=VoicePhoneNumberRepository(engine=engine),
        contact_query_service=get_contact_query_service(db_engine=engine),
        activity_service=get_activity_service_general(db_engine=engine),
        voice_call_repository=VoiceCallRepository(engine=engine),
        user_service=get_user_service_general(db_engine=engine),
        call_recording_s3_manager=get_s3_bucket_manager_by_bucket_name(
            settings.call_recording_v2_bucket_name,
        ),
        voicemail_greeting_s3_manager=get_s3_bucket_manager_by_bucket_name(
            settings.voicemail_greetings_bucket_name,
        ),
        meeting_service=meeting_service_factory_general(db_engine=engine),
        notification_service=get_notification_service_by_db_engine(db_engine=engine),
        ff_service=get_feature_flag_service(),
        email_account_service=get_email_account_service_ext_by_db_engine(engine=engine),
        user_preference_service=user_preference_service_from_engine(db_engine=engine),
        organization_preference_service=organization_preference_service_from_engine(
            db_engine=engine
        ),
        task_v2_service=get_task_v2_service_general(db_engine=engine),
        domain_crm_association_service=get_domain_crm_association_service(
            db_engine=engine
        ),
        quota_service=get_quota_service_by_db_engine(db_engine=engine),
        user_phone_number_service=user_phone_number_service_from_engine(engine=engine),
    )


def voice_call_service_factory(request: Request) -> VoiceCallService:
    return voice_call_service_from_engine(engine=get_db_engine(request=request))
