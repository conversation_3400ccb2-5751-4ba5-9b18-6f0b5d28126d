from typing import Annotated
from uuid import UUID

from fastapi import Depends
from starlette.requests import Request

from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.results import Cursor
from salestech_be.common.singleton import Singleton
from salestech_be.core.activity.service.activity_query_service import (
    ActivityListRequest,
    ActivityQueryService,
    get_activity_query_service,
)
from salestech_be.core.activity.types import (
    Activity,
    ActivityPatchRequest,
    ActivityRequest,
)
from salestech_be.db.dao.activity_repository import ActivityRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.pipeline_repository import PipelineRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.activity import (
    ActivityType,
    ActivityV2,
    ActivityV2Update,
)
from salestech_be.ree_logging import get_logger


class ActivityService:
    def __init__(
        self,
        activity_repository: Annotated[ActivityRepository, Depends()],
        contact_repository: Annotated[ContactRepository, Depends()],
        pipeline_repository: Annotated[PipelineRepository, Depends()],
        activity_query_service: Annotated[ActivityQueryService, Depends()],
    ):
        super().__init__()
        self.contact_repository = contact_repository
        self.activity_repository = activity_repository
        self.pipeline_repository = pipeline_repository
        self.activity_query_service = activity_query_service
        self.logger = get_logger()

    async def insert_activity(
        self, insert_activity_request: ActivityRequest, organization_id: UUID
    ) -> UUID:
        pipeline_id = insert_activity_request.pipeline_id
        account_id = insert_activity_request.account_id
        contact_ids: set[UUID] = set()
        if insert_activity_request.sub_references:
            contact_ids = {
                sub_ref.contact_id
                for sub_ref in insert_activity_request.sub_references
                if sub_ref.contact_id is not None
            }
        if not account_id and pipeline_id:
            pipeline = await self.pipeline_repository.get_pipeline_by_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                exclude_archived=True,
            )
            insert_activity_request.account_id = pipeline.account_id
        if not account_id and contact_ids:
            contact_account_map = (
                await self.contact_repository.get_primary_account_ids_by_contact_ids(
                    organization_id=organization_id,
                    contact_ids=contact_ids,
                )
            )
            account_ids = set(contact_account_map.values())
            if len(account_ids) == 1:
                insert_activity_request.account_id = account_ids.pop()

        dirty_activity, dirty_activity_sub_references = (
            insert_activity_request.to_dirty_db_activity_and_references(
                organization_id=organization_id
            )
        )

        inserted_activity = await self.activity_repository.insert_activity(
            dirty_activity=dirty_activity,
            dirty_activity_sub_references=dirty_activity_sub_references,
        )
        return inserted_activity.id

    async def patch_activity_by_id(
        self,
        activity_id: UUID,
        organization_id: UUID,
        req: ActivityPatchRequest,
    ) -> ActivityV2:
        _db_activity_updates = ActivityV2Update(
            pipeline_id=req.pipeline_id,
            account_id=req.account_id,
            sub_type=req.sub_type,
            display_name=req.display_name,
        )

        return await self.activity_repository.patch_activity_by_id(
            activity_id=activity_id,
            organization_id=organization_id,
            activity_update=_db_activity_updates,
        )

    async def list_activities_by_ids(
        self,
        activity_ids: list[UUID],
        organization_id: UUID,
    ) -> list[Activity]:
        return await self.activity_query_service.list_activities_by_ids(
            activity_ids=activity_ids,
            organization_id=organization_id,
        )

    async def map_activities_by_contact_ids(
        self, organization_id: UUID, contact_ids: list[UUID]
    ) -> dict[UUID, list[Activity]]:
        return await self.activity_query_service.map_activities_by_contact_ids(
            organization_id=organization_id,
            contact_ids=contact_ids,
        )

    async def list_activities_by_reference_ids_and_type(
        self,
        organization_id: UUID,
        reference_ids: list[str],
        activity_type: ActivityType | None = None,
    ) -> list[Activity]:
        return (
            await self.activity_query_service.list_activities_by_reference_ids_and_type(
                organization_id=organization_id,
                reference_ids=reference_ids,
                activity_type=activity_type,
            )
        )

    async def list_activities_by_contact_ids(
        self, organization_id: UUID, contact_ids: list[UUID]
    ) -> list[Activity]:
        return await self.activity_query_service.list_activities_by_contact_ids(
            organization_id=organization_id,
            contact_ids=contact_ids,
        )

    async def list_activities_by_account_ids(
        self, organization_id: UUID, account_ids: list[UUID]
    ) -> list[Activity]:
        return await self.activity_query_service.list_activities_by_account_ids(
            organization_id=organization_id,
            account_ids=account_ids,
        )

    async def _enrich_db_activities(
        self,
        db_activities: list[ActivityV2],
        organization_id: UUID,
    ) -> list[Activity]:
        return await self.activity_query_service._enrich_db_activities(
            db_activities=db_activities,
            organization_id=organization_id,
        )

    async def list_activities(
        self, request: ActivityListRequest
    ) -> tuple[list[Activity], Cursor]:
        return await self.activity_query_service.list_activities(request=request)

    async def list_activities_without_pagination(
        self, request: ActivityListRequest
    ) -> list[Activity]:
        default_page_size = 1000

        cursor = Cursor(
            page_size=default_page_size,
            page_index=1,
        )
        activity_set: set[Activity] = set()

        while True:
            db_activities, cursor = await self.activity_query_service.list_activities(
                request=request,
            )

            if not db_activities:
                break

            activity_set.update(db_activities)

            if cursor.page_index == cursor.total_page_number:
                break
            cursor = cursor.model_copy(update={"page_index": cursor.page_index + 1})

        return list(activity_set)


class SingletonActivityService(Singleton, ActivityService):
    pass


def get_activity_service(request: Request) -> ActivityService:
    return get_activity_service_general(get_db_engine(request))


def get_activity_service_general(db_engine: DatabaseEngine) -> ActivityService:
    if SingletonActivityService.has_instance():
        return SingletonActivityService.get_singleton_instance()
    return SingletonActivityService(
        activity_repository=ActivityRepository(engine=db_engine),
        contact_repository=ContactRepository(engine=db_engine),
        pipeline_repository=PipelineRepository(engine=db_engine),
        activity_query_service=get_activity_query_service(db_engine),
    )
