from collections import defaultdict
from typing import Annotated, Any
from uuid import UUID

from fastapi import Depends
from pydantic import BaseModel, Field

from salestech_be.common.exception import (
    ResourceNotFoundError,
)
from salestech_be.common.lifespan import get_db_engine
from salestech_be.common.query_util.legacy.filter_schema import (
    StandardComparator,
    StandardValueFilter,
)
from salestech_be.common.query_util.legacy.sort_schema import SortSchema
from salestech_be.common.results import Cursor
from salestech_be.common.schema_manager.std_object_field_identifier import (
    ActivityField,
    ActivitySubReferenceField,
)
from salestech_be.common.singleton import Singleton
from salestech_be.core.activity.types import (
    Activity,
)
from salestech_be.db.dao.activity_repository import ActivityRepository
from salestech_be.db.dao.contact_repository import ContactRepository
from salestech_be.db.dao.pipeline_repository import PipelineRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.activity import (
    ActivitySubReference,
    ActivityType,
    ActivityV2,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.validation import uuid_or_error


class ActivityListRequest(BaseModel):
    sorters: list[SortSchema[str]] = Field(default_factory=list)
    filters: list[StandardValueFilter] = Field(default_factory=list)
    organization_id: UUID
    include_secondary_objects: bool = False
    cursor: Cursor = Field(default_factory=Cursor)

    def filter_for_field(self, field_name: str) -> StandardValueFilter | None:
        fs: list[StandardValueFilter] | None = self.filters
        return next((f for f in fs if f.field == field_name), None) if fs else None


class ActivityQueryService:
    def __init__(
        self,
        activity_repository: Annotated[ActivityRepository, Depends()],
        contact_repository: Annotated[ContactRepository, Depends()],
        pipeline_repository: Annotated[PipelineRepository, Depends()],
    ):
        super().__init__()
        self.contact_repository = contact_repository
        self.activity_repository = activity_repository
        self.pipeline_repository = pipeline_repository
        self.logger = get_logger()

    async def list_activities_by_ids(
        self,
        activity_ids: list[UUID],
        organization_id: UUID,
    ) -> list[Activity]:
        if not activity_ids:
            return []
        db_activities = await self.activity_repository.list_by_ids(
            activity_ids=activity_ids,
            organization_id=organization_id,
        )
        if len(db_activities) != len(activity_ids):
            raise ResourceNotFoundError(
                "Activity doesn't exist",
            )

        return await self._enrich_db_activities(
            db_activities=db_activities, organization_id=organization_id
        )

    async def map_activities_by_contact_ids(
        self, organization_id: UUID, contact_ids: list[UUID]
    ) -> dict[UUID, list[Activity]]:
        sub_references = (
            await self.activity_repository.list_activity_references_by_contact_ids(
                contact_ids=contact_ids, organization_id=organization_id
            )
        )
        # Group ActivitySubReference by contact
        grouped_by_contact_id: dict[UUID, list[ActivitySubReference]] = defaultdict(
            list[ActivitySubReference]
        )
        for sub_reference in sub_references:
            if sub_reference.contact_id:
                grouped_by_contact_id[sub_reference.contact_id].append(sub_reference)

        # fetch and enrich all activities from ids in sub_references
        all_activity_ids = set()
        for sub_reference in sub_references:
            all_activity_ids.add(sub_reference.activity_id)
        all_activities = await self.list_activities_by_ids(
            list(all_activity_ids), organization_id=organization_id
        )
        id_activity_map = {activity.id: activity for activity in all_activities}

        # build contact_id -> list[activity] map
        result_map: dict[UUID, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        for contact_id in contact_ids:
            result_map[contact_id] = []
            for sub_reference in grouped_by_contact_id.get(contact_id, []):
                if id_activity_map.get(sub_reference.activity_id):
                    result_map[contact_id].append(
                        id_activity_map.get(sub_reference.activity_id)
                    )
        return result_map

    async def list_activities_by_reference_ids_and_type(
        self,
        organization_id: UUID,
        reference_ids: list[str],
        activity_type: ActivityType | None = None,
    ) -> list[Activity]:
        db_activities = await self.activity_repository.list_by_reference_ids_and_type(
            reference_ids=reference_ids,
            activity_type=activity_type,
            organization_id=organization_id,
        )
        return (
            await self._enrich_db_activities(
                db_activities=db_activities, organization_id=organization_id
            )
            if db_activities
            else []
        )

    async def list_activities_by_contact_ids(
        self, organization_id: UUID, contact_ids: list[UUID]
    ) -> list[Activity]:
        db_activities = await self.activity_repository.list_by_contact_ids(
            contact_ids=contact_ids,
            organization_id=organization_id,
        )
        return (
            await self._enrich_db_activities(
                db_activities=db_activities, organization_id=organization_id
            )
            if db_activities
            else []
        )

    async def list_activities_by_account_ids(
        self, organization_id: UUID, account_ids: list[UUID]
    ) -> list[Activity]:
        db_activities = await self.activity_repository.list_by_account_ids(
            account_ids=account_ids,
            organization_id=organization_id,
        )
        return (
            await self._enrich_db_activities(
                db_activities=db_activities, organization_id=organization_id
            )
            if db_activities
            else []
        )

    async def list_activity_references_by_activity_ids(
        self,
        activity_ids: list[UUID],
        organization_id: UUID,
    ) -> list[ActivitySubReference]:
        return await self.activity_repository.list_activity_references_by_activity_ids(
            activity_ids=activity_ids,
            organization_id=organization_id,
        )

    async def _enrich_db_activities(
        self,
        db_activities: list[ActivityV2],
        organization_id: UUID,
    ) -> list[Activity]:
        activity_ids = [activity.id for activity in db_activities]
        db_activity_references = (
            await self.activity_repository.list_activity_references_by_activity_ids(
                activity_ids=activity_ids,
                organization_id=organization_id,
            )
        )

        activity_reference_map: dict[UUID, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        for activity_id in activity_ids:
            activity_reference_map[activity_id] = []
        for db_activity_reference in db_activity_references:
            activity_reference_map[db_activity_reference.activity_id].append(
                db_activity_reference
            )

        return [
            Activity.map_from_db(
                db_activity=db_activity,
                sub_references=activity_reference_map.get(db_activity.id, None),
            )
            for db_activity in db_activities
        ]

    async def list_activities(
        self,
        request: ActivityListRequest,
        allow_arbitrary_fields_in_filters: bool = False,
    ) -> tuple[list[Activity], Cursor]:
        """List activities based on primary object and inclusion settings"""
        cursor = request.cursor
        filters = request.filters or []
        contact_filter = (
            request.filter_for_field("contact_id")
            or request.filter_for_field(
                f"{ActivityField.sub_references}.{ActivitySubReferenceField.contact_id}"
            )
            or request.filter_for_field(ActivityField.contact_ids)
        )
        if contact_filter and contact_filter.field == ActivityField.contact_ids:
            contact_filter.field = (
                f"{ActivityField.sub_references}.{ActivitySubReferenceField.contact_id}"
            )
        account_filter = request.filter_for_field("account_id")
        pipeline_filter = request.filter_for_field("pipeline_id")
        has_primary_filter = bool(pipeline_filter or account_filter or contact_filter)
        # Add filters for secondary objects if needed
        or_filters: list[StandardValueFilter] = []
        if request.include_secondary_objects and has_primary_filter:
            # Key principle: Contact filters is a leaf node, so you can't grab secondary objects from it
            if not pipeline_filter and account_filter:
                # find the associated account engagement ids and contact ids
                account_ids: list[UUID] = (
                    [uuid_or_error(account_filter.value)]
                    if not isinstance(account_filter.value, list)
                    else [
                        uuid_or_error(account_id) for account_id in account_filter.value
                    ]
                )
                pipeline_account_id_map = (
                    await self.pipeline_repository.map_pipelines_by_account_ids(
                        organization_id=request.organization_id,
                        account_ids=account_ids,
                    )
                )
                pipeline_ids: list[UUID] = [
                    pipeline.id
                    for pipelines in pipeline_account_id_map.values()
                    for pipeline in pipelines
                ]
                or_filters.append(
                    StandardValueFilter(
                        field="pipeline_id",
                        predicate=StandardComparator.IN,
                        value=pipeline_ids,
                    )
                )
            if not account_filter and pipeline_filter:
                # Handle both single pipeline ID and list of IDs
                pipeline_ids = (
                    [uuid_or_error(pipeline_filter.value)]
                    if not isinstance(pipeline_filter.value, list)
                    else [
                        uuid_or_error(pipeline_id)
                        for pipeline_id in pipeline_filter.value
                    ]
                )
                pipelines = await self.pipeline_repository.list_by_ids(
                    organization_id=request.organization_id,
                    pipeline_ids=pipeline_ids,
                )
                account_ids = [pipeline.account_id for pipeline in pipelines]
                or_filters.append(
                    StandardValueFilter(
                        field="account_id",
                        predicate=StandardComparator.IN,
                        value=account_ids,
                    )
                )
            if not contact_filter:
                # Both account and pipeline have contact ids, so we need to union them
                contact_ids: set[UUID] = set()
                if pipeline_filter:
                    pipeline_ids = (
                        [uuid_or_error(pipeline_filter.value)]
                        if not isinstance(pipeline_filter.value, list)
                        else [
                            uuid_or_error(pipeline_id)
                            for pipeline_id in pipeline_filter.value
                        ]
                    )
                    pipeline_contact_ids_map = await self.pipeline_repository.map_contact_associations_for_org_pipelines_sql_grouped(
                        organization_id=request.organization_id,
                        db_pipeline_ids=set(pipeline_ids),
                    )
                    pipeline_contact_ids = [
                        contact_pipeline_assoc.contact_id
                        for pipeline_contact_assocs in pipeline_contact_ids_map.values()
                        for contact_pipeline_assoc in pipeline_contact_assocs
                    ]
                    contact_ids.update(pipeline_contact_ids)
                if account_filter:
                    account_ids = (
                        [uuid_or_error(account_filter.value)]
                        if not isinstance(account_filter.value, list)
                        else [
                            uuid_or_error(account_id)
                            for account_id in account_filter.value
                        ]
                    )
                    account_contact_id_mapping = await self.contact_repository.map_contact_by_primary_account_ids(
                        organization_id=request.organization_id,
                        primary_account_ids=set(account_ids),
                    )
                    account_contact_ids: list[UUID] = [
                        contact.id
                        for contacts in account_contact_id_mapping.values()
                        for contact in contacts
                    ]
                    contact_ids.update(account_contact_ids)
                if contact_ids:
                    or_filters.append(
                        StandardValueFilter(
                            field="contact_id",
                            predicate=StandardComparator.IN,
                            value=list(contact_ids),
                        )
                    )

        paginated_activities = await self.activity_repository.get_paginated_activities(
            and_filters=filters,
            or_filters=or_filters,
            sorters=request.sorters,
            organization_id=request.organization_id,
            cursor=cursor,
            allow_arbitrary_fields_in_filters=allow_arbitrary_fields_in_filters,
        )
        cursor.total_number = await self.activity_repository.count_by_filters(
            and_filters=filters,
            or_filters=or_filters,
            organization_id=request.organization_id,
            allow_arbitrary_fields_in_filters=allow_arbitrary_fields_in_filters,
        )
        cursor.total_page_number = (
            cursor.total_number // cursor.page_size
            if cursor.total_number % cursor.page_size == 0
            else cursor.total_number // cursor.page_size + 1
        )
        self.logger.info("updated cursor", cursor=cursor)
        return await self._enrich_db_activities(
            db_activities=paginated_activities,
            organization_id=request.organization_id,
        ), cursor


class SingletonActivityQueryService(Singleton, ActivityQueryService):
    pass


def get_activity_query_service(
    db_engine: Annotated[DatabaseEngine, Depends(get_db_engine)],
) -> ActivityQueryService:
    if SingletonActivityQueryService.has_instance():
        return SingletonActivityQueryService.get_singleton_instance()
    return SingletonActivityQueryService(
        activity_repository=ActivityRepository(engine=db_engine),
        contact_repository=ContactRepository(engine=db_engine),
        pipeline_repository=PipelineRepository(engine=db_engine),
    )
