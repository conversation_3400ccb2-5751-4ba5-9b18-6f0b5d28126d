from collections.abc import Sequence
from dataclasses import dataclass
from typing import <PERSON><PERSON><PERSON><PERSON>
from uuid import UUID

from salestech_be.common.exception.exception import IllegalStateError
from salestech_be.common.type.patch_request import specified_or_default
from salestech_be.core.metadata.converter import select_list_value_lite_from_db
from salestech_be.core.metadata.dto.select_list_dto import (
    PipelineStageSelectListValueDto,
)
from salestech_be.core.metadata.types import (
    PipelineStageSelectListValueLite,
    SelectListValueLite,
)
from salestech_be.core.pipeline.qualification_properties.competition import (
    Competition,
    competition_domain_model_from_db,
)
from salestech_be.core.pipeline.qualification_properties.decision_criteria import (
    DecisionCriteria,
    decision_criteria_domain_model_from_db,
)
from salestech_be.core.pipeline.qualification_properties.decision_process import (
    DecisionProcess,
    decision_process_domain_model_from_db,
)
from salestech_be.core.pipeline.qualification_properties.identified_pain import (
    IdentifiedPain,
    identified_pain_domain_model_from_db,
)
from salestech_be.core.pipeline.qualification_properties.metric import (
    Metric,
    metric_domain_model_from_db,
)
from salestech_be.core.pipeline.qualification_properties.paper_process import (
    PaperProcess,
    paper_process_domain_model_from_db,
)
from salestech_be.core.pipeline.service_api_schema import (
    ContactPipelineAssociationRequest,
    CreatePipelineRequest,
    InternalPatchPipelineRequest,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2 as DomainPipelineV2
from salestech_be.db.dto.custom_object_data_dto import ExtensionCustomObjectDataGroupDto
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
)
from salestech_be.db.models.core.types import EntityParticipant
from salestech_be.db.models.pipeline import (
    Pipeline as DbPipeline,
)
from salestech_be.db.models.pipeline import (
    PipelineUpdate as DbPipelineUpdate,
)
from salestech_be.db.models.pipeline_qualification_properties.constants import (
    PipelineQualificationPropertyName,
)
from salestech_be.db.models.pipeline_qualification_property import (
    PipelineQualificationProperty,
)
from salestech_be.db.models.pipeline_stage_select_list_value_metadata import (
    PipelineStageSelectListValueMetadata,
)
from salestech_be.db.models.select_list import SelectListValue
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger(__name__)


@dataclass
class PipelineSelectListValueContainer:
    stage_select_list_value_dto: PipelineStageSelectListValueDto
    source_select_list_value: SelectListValue | None = None
    type_select_list_value: SelectListValue | None = None
    closed_reason_select_list_values: list[SelectListValue] | None = None


class PipelineQualificationPropertyConversionResult(NamedTuple):
    competition: Competition | None = None
    decision_criteria: DecisionCriteria | None = None
    decision_process: DecisionProcess | None = None
    identified_pain: IdentifiedPain | None = None
    metric: Metric | None = None
    paper_process: PaperProcess | None = None


def pipeline_qualification_property_conversion_result_from_db(
    *,
    db_qualification_properties: Sequence[PipelineQualificationProperty],
) -> PipelineQualificationPropertyConversionResult:
    qualification_properties_by_name: dict[
        PipelineQualificationPropertyName, PipelineQualificationProperty
    ] = {prop.name: prop for prop in db_qualification_properties or []}

    competition = (
        competition_domain_model_from_db(prop=prop)
        if (
            prop := qualification_properties_by_name.get(
                PipelineQualificationPropertyName.competition
            )
        )
        else None
    )
    decision_criteria_prop = (
        decision_criteria_domain_model_from_db(prop=prop)
        if (
            prop := qualification_properties_by_name.get(
                PipelineQualificationPropertyName.decision_criteria
            )
        )
        else None
    )
    decision_process_prop = (
        decision_process_domain_model_from_db(prop=prop)
        if (
            prop := qualification_properties_by_name.get(
                PipelineQualificationPropertyName.decision_process
            )
        )
        else None
    )
    identified_pain_prop = (
        identified_pain_domain_model_from_db(prop=prop)
        if (
            prop := qualification_properties_by_name.get(
                PipelineQualificationPropertyName.identified_pain
            )
        )
        else None
    )
    metric_prop = (
        metric_domain_model_from_db(prop=prop)
        if (
            prop := qualification_properties_by_name.get(
                PipelineQualificationPropertyName.metric
            )
        )
        else None
    )
    paper_process_prop = (
        paper_process_domain_model_from_db(prop=prop)
        if (
            prop := qualification_properties_by_name.get(
                PipelineQualificationPropertyName.paper_process
            )
        )
        else None
    )

    return PipelineQualificationPropertyConversionResult(
        competition=competition,
        decision_criteria=decision_criteria_prop,
        decision_process=decision_process_prop,
        identified_pain=identified_pain_prop,
        metric=metric_prop,
        paper_process=paper_process_prop,
    )


def pipeline_stage_value_from_db(
    *,
    pipeline_stage_slv_dto: PipelineStageSelectListValueDto,
) -> PipelineStageSelectListValueLite:
    return PipelineStageSelectListValueLite(
        id=pipeline_stage_slv_dto.select_list_value_id,
        display_value=pipeline_stage_slv_dto.select_list_value.display_value,
        select_list_id=pipeline_stage_slv_dto.select_list_id,
        rank=pipeline_stage_slv_dto.select_list_value.rank,
        status=pipeline_stage_slv_dto.select_list_value.status,
        description=pipeline_stage_slv_dto.select_list_value.description,
        is_default=pipeline_stage_slv_dto.select_list_value.is_default,
        default_closing_probability=pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.default_closing_probability,
        outcome_state=pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata.outcome_state,
    )


def pipeline_v2_from_db(
    *,
    db_pipeline: DbPipeline,
    db_contact_pipeline_associations: Sequence[ContactPipelineAssociation],
    extension_custom_object_data_group_dto: ExtensionCustomObjectDataGroupDto
    | None = None,
    pipeline_select_list_value_container: PipelineSelectListValueContainer,
    db_qualification_properties: Sequence[PipelineQualificationProperty] | None = None,
) -> DomainPipelineV2:
    primary_pipeline_contact_id: UUID | None = None
    additional_pipeline_contact_ids: list[UUID] = []
    all_pipeline_contact_ids: list[UUID] = []
    sorted_associations = sorted(
        db_contact_pipeline_associations, key=lambda asso: asso.created_at, reverse=True
    )
    for asso in sorted_associations:
        if asso.is_primary:
            primary_pipeline_contact_id = asso.contact_id
            all_pipeline_contact_ids.append(asso.contact_id)
        else:
            additional_pipeline_contact_ids.append(asso.contact_id)
            all_pipeline_contact_ids.append(asso.contact_id)
    if not primary_pipeline_contact_id:
        logger.warning(
            "Pipeline has no primary contact pipeline association",
            pipeline_id=db_pipeline.id,
        )

    stage_value: PipelineStageSelectListValueLite = pipeline_stage_value_from_db(
        pipeline_stage_slv_dto=pipeline_select_list_value_container.stage_select_list_value_dto
    )
    # source_value: SelectListValueLite | None = (
    #     select_list_value_lite_from_db(
    #         select_list_value=pipeline_select_list_value_container.source_select_list_value
    #     )
    #     if pipeline_select_list_value_container.source_select_list_value
    #     else None
    # )
    type_value: SelectListValueLite | None = (
        select_list_value_lite_from_db(
            select_list_value=pipeline_select_list_value_container.type_select_list_value
        )
        if pipeline_select_list_value_container.type_select_list_value
        else None
    )
    if db_pipeline.type_id and not type_value:
        raise IllegalStateError(
            f"Pipeline type select list value with id {db_pipeline.type_id} not found"
        )

    closed_reason_select_list_values: list[SelectListValueLite] | None = (
        [
            select_list_value_lite_from_db(
                select_list_value=closed_reason_select_list_value
            )
            for closed_reason_select_list_value in pipeline_select_list_value_container.closed_reason_select_list_values
        ]
        if pipeline_select_list_value_container.closed_reason_select_list_values
        else None
    )

    if (
        db_pipeline.closed_reason_select_list_value_ids
        and not closed_reason_select_list_values
    ):
        raise IllegalStateError(
            f"Pipeline closed reason select list value with ids {db_pipeline.closed_reason_select_list_value_ids} not found"
        )

    converted_qualification_property = PipelineQualificationPropertyConversionResult()
    try:
        converted_qualification_property = (
            pipeline_qualification_property_conversion_result_from_db(
                db_qualification_properties=db_qualification_properties or []
            )
        )
    except Exception:
        logger.exception(
            "Error converting pipeline qualification properties",
            pipeline_id=db_pipeline.id,
            pipeline_qualification_properties=db_qualification_properties,
        )
    return DomainPipelineV2(
        account_id=db_pipeline.account_id,
        additional_contact_ids=additional_pipeline_contact_ids,
        all_contact_ids=all_pipeline_contact_ids,
        amount=db_pipeline.amount,
        anticipated_closing_at=db_pipeline.anticipated_closing_at,
        archived_at=db_pipeline.archived_at,
        archived_by_user_id=db_pipeline.archived_by_user_id,
        closed_at=db_pipeline.closed_at,
        closed_by_user_id=db_pipeline.closed_by_user_id,
        created_at=db_pipeline.created_at,
        created_by_user_id=db_pipeline.created_by_user_id,
        custom_field_data=extension_custom_object_data_group_dto.to_generic_custom_field_value(
            extension_id=db_pipeline.id
        )
        if extension_custom_object_data_group_dto
        else None,
        custom_field_data_v2=extension_custom_object_data_group_dto.to_typed_custom_field_data(
            extension_id=db_pipeline.id
        )
        if extension_custom_object_data_group_dto
        else None,
        display_name=db_pipeline.display_name,
        expires_at=db_pipeline.expires_at,
        id=db_pipeline.id,
        next_step_details=db_pipeline.next_step_details,
        next_step_due_at=db_pipeline.next_step_due_at,
        organization_id=db_pipeline.organization_id,
        owner_user_id=db_pipeline.owner_user_id,
        primary_contact_id=primary_pipeline_contact_id,
        # source=source_value,
        # source_id=source_value.id if source_value else None,
        stage=stage_value,
        stage_id=stage_value.id,
        stage_last_shifted_at=db_pipeline.stage_last_shifted_at,
        status=db_pipeline.status,
        type=type_value,
        type_id=type_value.id if type_value else None,
        closed_reason_select_list_values=closed_reason_select_list_values,
        closed_reason_select_list_value_ids=db_pipeline.closed_reason_select_list_value_ids
        if closed_reason_select_list_values
        else None,
        closed_reason_custom_detail=db_pipeline.closed_reason_custom_detail,
        updated_at=db_pipeline.updated_at,
        updated_by_user_id=db_pipeline.updated_by_user_id,
        access_status=db_pipeline.access_status,
        participant_user_id_list=[
            participant.user_id for participant in db_pipeline.participants or []
        ],
        created_source=db_pipeline.created_source,
        competition=converted_qualification_property.competition,
        decision_criteria=converted_qualification_property.decision_criteria,
        decision_process=converted_qualification_property.decision_process,
        identified_pain=converted_qualification_property.identified_pain,
        metric=converted_qualification_property.metric,
        paper_process=converted_qualification_property.paper_process,
    )


def create_pipeline_req_to_db_pipeline(
    *,
    organization_id: UUID,
    user_id: UUID,
    pipeline_stage_slvm: PipelineStageSelectListValueMetadata,
    create_req: CreatePipelineRequest,
) -> DbPipeline:
    if pipeline_stage_slvm.select_list_value_id != create_req.stage_id:
        raise ValueError(
            f"Pipeline stage select list value metadata id ({pipeline_stage_slvm.select_list_value_id}) does not "
            f"match the stage id in the request ({create_req.stage_id})"
        )
    return DbPipeline(
        organization_id=organization_id,
        account_id=create_req.account_id,
        owner_user_id=create_req.owner_user_id,
        next_step_details=create_req.next_step_details,
        display_name=create_req.display_name,
        created_at=create_req.created_at or zoned_utc_now(),
        updated_at=zoned_utc_now(),
        next_step_due_at=create_req.next_step_due_at,
        created_by_user_id=user_id,
        created_source=create_req.created_source,
        updated_by_user_id=user_id,
        state=pipeline_stage_slvm.pipeline_status,
        status=pipeline_stage_slvm.pipeline_status,
        stage_id=create_req.stage_id,
        # source_id=create_req.source_id,
        source_id=None,
        type_id=create_req.type_id,
        closed_reason_select_list_value_ids=create_req.closed_reason_select_list_value_ids,
        closed_reason_custom_detail=create_req.closed_reason_custom_detail,
        amount=create_req.amount,
        anticipated_closing_at=create_req.anticipated_closing_at,
        expires_at=create_req.expires_at,
        closed_at=create_req.closed_at
        or (zoned_utc_now() if pipeline_stage_slvm.outcome_state else None),
        closed_by_user_id=user_id if pipeline_stage_slvm.outcome_state else None,
        participants=EntityParticipant.list_from_request_field(
            create_req.participant_user_id_list
        )
        if create_req.participant_user_id_list
        else None,
    )


def create_pipeline_req_to_db_contact_pipeline_associations(
    *,
    organization_id: UUID,
    pipeline_id: UUID,
    user_id: UUID,
    create_req: CreatePipelineRequest,
) -> list[ContactPipelineAssociation]:
    association_requests = create_req.contact_pipeline_associations
    if not association_requests:
        return []
    pipeline_db_association = ContactPipelineAssociation(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        contact_id=association_requests.primary.contact_id,
        is_primary=True,
        created_by_user_id=user_id,
        updated_by_user_id=user_id,
        note=specified_or_default(association_requests.primary.note, None),
        role_types=specified_or_default(association_requests.primary.role_types, None),
    )
    additional_associations: list[ContactPipelineAssociation] = []
    for additional_contact_association_request in association_requests.additional or []:
        additional_associations.append(
            ContactPipelineAssociation(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                contact_id=additional_contact_association_request.contact_id,
                is_primary=False,
                created_by_user_id=user_id,
                updated_by_user_id=user_id,
                note=specified_or_default(
                    additional_contact_association_request.note, None
                ),
                role_types=specified_or_default(
                    additional_contact_association_request.role_types, None
                ),
            )
        )
    return [pipeline_db_association, *additional_associations]


def contact_pipeline_association_request_to_db_contact_pipeline_association(
    *,
    organization_id: UUID,
    pipeline_id: UUID,
    user_id: UUID,
    is_primary: bool,
    contact_pipeline_association_request: ContactPipelineAssociationRequest,
) -> ContactPipelineAssociation:
    return ContactPipelineAssociation(
        organization_id=organization_id,
        pipeline_id=pipeline_id,
        contact_id=contact_pipeline_association_request.contact_id,
        is_primary=is_primary,
        created_by_user_id=user_id,
        updated_by_user_id=user_id,
        note=specified_or_default(contact_pipeline_association_request.note, None),
        role_types=specified_or_default(
            contact_pipeline_association_request.role_types, None
        ),
    )


def patch_pipeline_req_to_db_pipeline_update(
    *,
    user_id: UUID,
    patch_req: InternalPatchPipelineRequest,
) -> DbPipelineUpdate:
    return DbPipelineUpdate(
        display_name=patch_req.display_name,
        amount=patch_req.amount,
        # source_id=patch_req.source_id,
        type_id=patch_req.type_id,
        owner_user_id=patch_req.owner_user_id,
        next_step_details=patch_req.next_step_details,
        next_step_due_at=patch_req.next_step_due_at,
        anticipated_closing_at=patch_req.anticipated_closing_at,
        expires_at=patch_req.expires_at,
        closed_at=patch_req.closed_at,
        updated_by_user_id=user_id,
        account_id=patch_req.account_id,
        closed_reason_select_list_value_ids=patch_req.closed_reason_select_list_value_ids,
        closed_reason_custom_detail=patch_req.closed_reason_custom_detail,
        participants=EntityParticipant.list_from_unset_aware_request_field(
            patch_req.participant_user_id_list
        ),
        status=patch_req.status,
    )
