import asyncio
from collections import defaultdict
from collections.abc import Awaitable, Mapping, Sequence
from typing import Annotated, Literal, cast
from uuid import UUID

from fastapi import Depends
from frozendict import frozendict

from salestech_be.common.exception.exception import (
    IllegalStateError,
    ResourceNotFoundError,
)
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdObjectIdentifiers,
    StdSelectListIdentifier,
)
from salestech_be.common.singleton import Singleton
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.common.domain_service import DomainQueryService
from salestech_be.core.custom_object.service.custom_object_service import (
    CustomObjectService,
)
from salestech_be.core.custom_object.type.extendable_standard_object import (
    ExtendableStandardObject,
)
from salestech_be.core.ff.feature_flag_service import (
    FeatureFlagService,
    get_feature_flag_service,
)
from salestech_be.core.metadata.converter import (
    contact_pipeline_role_from_db,
)
from salestech_be.core.metadata.dto.select_list_dto import (
    PipelineStageDto,
    PipelineStageSelectListValueDto,
    SelectListDto,
)
from salestech_be.core.metadata.service.internal_select_list_service import (
    InternalSelectListService,
    get_select_list_service,
)
from salestech_be.core.metadata.service.pipeline_stage_service import (
    PipelineStageSelectListService,
    get_pipeline_stage_select_list_service,
)
from salestech_be.core.metadata.types import ContactPipelineRole
from salestech_be.core.pipeline.converter import (
    PipelineSelectListValueContainer,
    pipeline_v2_from_db,
)
from salestech_be.core.pipeline.service.contact_pipeline_role_ai_rec_service import (
    ContactPipelineRoleAIRecService,
    get_contact_pipeline_role_ai_rec_service,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.db.dao.custom_object_repository import CustomObjectRepository
from salestech_be.db.dao.pipeline_repository import PipelineRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.dto.custom_object_data_dto import ExtensionCustomObjectDataGroupDto
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
)
from salestech_be.db.models.pipeline import Pipeline
from salestech_be.db.models.pipeline_qualification_property import (
    PipelineQualificationProperty,
)
from salestech_be.db.models.select_list import SelectListValue
from salestech_be.ree_logging import get_logger

logger = get_logger()


class PipelineQueryService(DomainQueryService[PipelineV2]):
    def __init__(
        self,
        pipeline_repository: Annotated[PipelineRepository, Depends()],
        custom_object_service: Annotated[CustomObjectService, Depends()],
        pipeline_stage_select_list_service: Annotated[
            PipelineStageSelectListService, Depends()
        ],
        select_list_service: Annotated[InternalSelectListService, Depends()],
        feature_flag_service: Annotated[
            FeatureFlagService, Depends(get_feature_flag_service)
        ],
        contact_pipeline_role_ai_rec_service: Annotated[
            ContactPipelineRoleAIRecService, Depends()
        ],
    ):
        super().__init__(feature_flag_service=feature_flag_service)
        self.pipeline_repository = pipeline_repository
        self.custom_object_service = custom_object_service
        self.pipeline_stage_select_list_service = pipeline_stage_select_list_service
        self.select_list_service = select_list_service
        self.contact_pipeline_role_ai_rec_service = contact_pipeline_role_ai_rec_service
        self.logger = get_logger()

    async def get_count_of_all_pipelines_in_organization(
        self,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> int:
        return await self.pipeline_repository.count_by_column_values(
            Pipeline,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
        )

    async def get_count_of_all_contact_pipeline_associations_in_organization(
        self,
        *,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> int:
        """Count all contact pipeline associations for the given organization."""
        return await self.pipeline_repository.count_by_column_values(
            ContactPipelineAssociation,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
        )

    async def _bulk_enrich_pipeline_v2(
        self,
        organization_id: UUID,
        exclude_archived: bool = True,
        include_custom_object: bool | None = False,
        db_pipelines: list[Pipeline] | None = None,
    ) -> list[PipelineV2]:
        # Step 1: Fetch all relevant Pipeline objects
        if db_pipelines is None:
            db_pipelines = await self.pipeline_repository.list_all(
                organization_id=organization_id,
                exclude_archived=exclude_archived,
            )

        if not db_pipelines:
            return []

        # Step 2: Fetch their contact associations
        contact_pipeline_associations_by_pipeline_id: Mapping[
            UUID, Sequence[ContactPipelineAssociation]
        ] = await self.pipeline_repository.map_contact_associations_for_org_pipelines_sql_grouped(
            organization_id=organization_id,
            exclude_archived_pipelines=exclude_archived,
            active_association_only=True,
            db_pipeline_ids={p.id for p in db_pipelines},
        )

        pipeline_ids: set[UUID] = {db_pipeline.id for db_pipeline in db_pipelines}

        tasks: list[Awaitable[object]] = [
            self._map_pipeline_stage_select_list_dtos_by_value_ids(
                organization_id=organization_id,
            ),
            self.pipeline_repository.map_pipeline_qualification_property_by_pipeline_id(
                organization_id=organization_id,
                pipeline_ids=pipeline_ids,
            ),
        ]

        custom_object_task_present = False
        if include_custom_object:
            tasks.append(
                self.custom_object_service.map_custom_object_data_by_extension_ids_if_custom_object_exists(
                    organization_id=organization_id,
                    parent_object_name=ExtendableStandardObject.pipeline,
                    extension_ids=pipeline_ids,
                )
            )
            custom_object_task_present = True

        direct_select_list_task_present = False
        if any(
            bool(
                db_pipeline.source_id
                or db_pipeline.type_id
                or db_pipeline.closed_reason_select_list_value_ids
            )
            for db_pipeline in db_pipelines
        ):
            tasks.append(
                self._map_pipeline_direct_select_list_dto_by_value_ids(
                    organization_id=organization_id,
                )
            )
            direct_select_list_task_present = True

        results = await asyncio.gather(*tasks)

        pipeline_stage_select_list_dtos_by_value_id = cast(
            frozendict[UUID, PipelineStageDto], results[0]
        )
        pipeline_qualification_properties_by_pipeline_id = cast(
            Mapping[UUID, list[PipelineQualificationProperty]], results[1]
        )

        result_idx_offset = 2
        extension_custom_object_data_group_dto: (
            ExtensionCustomObjectDataGroupDto | None
        ) = None
        if custom_object_task_present:
            extension_custom_object_data_group_dto = cast(
                ExtensionCustomObjectDataGroupDto | None, results[result_idx_offset]
            )
            result_idx_offset += 1

        pipeline_direct_select_list_dtos_by_value_id: frozendict[
            UUID, SelectListDto
        ] = frozendict()
        if direct_select_list_task_present:
            pipeline_direct_select_list_dtos_by_value_id = cast(
                frozendict[UUID, SelectListDto], results[result_idx_offset]
            )

        enriched_pipelines: list[PipelineV2] = []
        for db_pipeline in db_pipelines:
            # Ensure every pipeline_id from db_pipelines has an entry in the associations map,
            # even if it's an empty list (defaultdict behavior of the repo method helps here if a pipeline has no associations).
            associations = contact_pipeline_associations_by_pipeline_id.get(
                db_pipeline.id, []
            )
            enriched_pipelines.append(
                pipeline_v2_from_db(
                    db_pipeline=db_pipeline,
                    db_contact_pipeline_associations=associations,
                    extension_custom_object_data_group_dto=extension_custom_object_data_group_dto,
                    pipeline_select_list_value_container=self._extract_pipeline_select_list_value_container(
                        organization_id=organization_id,
                        stage_id=db_pipeline.stage_id,
                        source_id=db_pipeline.source_id,
                        type_id=db_pipeline.type_id,
                        closed_reason_select_list_value_ids=db_pipeline.closed_reason_select_list_value_ids,
                        pipeline_stage_select_list_dtos_by_value_id=pipeline_stage_select_list_dtos_by_value_id,
                        pipeline_direct_select_list_dtos_by_value_id=pipeline_direct_select_list_dtos_by_value_id,
                    ),
                    db_qualification_properties=pipeline_qualification_properties_by_pipeline_id.get(
                        db_pipeline.id, None
                    ),
                )
            )
        return enriched_pipelines

    async def _enrich_pipeline_v2(
        self,
        db_pipelines: list[Pipeline],
        organization_id: UUID,
        include_custom_object: bool | None = False,
    ) -> list[PipelineV2]:
        if not db_pipelines:
            return []
        pipeline_ids: set[UUID] = {db_pipeline.id for db_pipeline in db_pipelines}

        # Use the correct repository method that accepts a list of pipeline_ids
        contact_pipeline_associations_by_pipeline_id: Mapping[
            UUID, Sequence[ContactPipelineAssociation]
        ] = await self.pipeline_repository.map_contact_associations_for_org_pipelines_sql_grouped(
            organization_id=organization_id,
            exclude_archived_pipelines=True,
            active_association_only=True,
            db_pipeline_ids=pipeline_ids,
        )

        extension_custom_object_data_group_dto = (
            await self.custom_object_service.map_custom_object_data_by_extension_ids_if_custom_object_exists(
                organization_id=organization_id,
                parent_object_name=ExtendableStandardObject.pipeline,
                extension_ids={db_pipeline.id for db_pipeline in db_pipelines},
            )
            if include_custom_object
            else None
        )

        pipeline_stage_select_list_dtos_by_value_id: frozendict[
            UUID, PipelineStageDto
        ] = await self._map_pipeline_stage_select_list_dtos_by_value_ids(
            organization_id=organization_id,
        )

        pipeline_direct_select_list_dtos_by_value_id: frozendict[
            UUID, SelectListDto
        ] = (
            await self._map_pipeline_direct_select_list_dto_by_value_ids(
                organization_id=organization_id,
            )
            if any(
                bool(
                    db_pipeline.source_id
                    or db_pipeline.type_id
                    or db_pipeline.closed_reason_select_list_value_ids
                )
                for db_pipeline in db_pipelines
            )
            else frozendict()
        )

        pipeline_qualification_properties_by_pipeline_id: Mapping[
            UUID, list[PipelineQualificationProperty]
        ] = await self.pipeline_repository.map_pipeline_qualification_property_by_pipeline_id(
            organization_id=organization_id,
            pipeline_ids=pipeline_ids,
        )

        return [
            pipeline_v2_from_db(
                db_pipeline=db_pipeline,
                db_contact_pipeline_associations=contact_pipeline_associations_by_pipeline_id.get(
                    db_pipeline.id, ()
                ),
                extension_custom_object_data_group_dto=extension_custom_object_data_group_dto,
                pipeline_select_list_value_container=self._extract_pipeline_select_list_value_container(
                    organization_id=organization_id,
                    stage_id=db_pipeline.stage_id,
                    source_id=db_pipeline.source_id,
                    type_id=db_pipeline.type_id,
                    closed_reason_select_list_value_ids=db_pipeline.closed_reason_select_list_value_ids,
                    pipeline_stage_select_list_dtos_by_value_id=pipeline_stage_select_list_dtos_by_value_id,
                    pipeline_direct_select_list_dtos_by_value_id=pipeline_direct_select_list_dtos_by_value_id,
                ),
                db_qualification_properties=pipeline_qualification_properties_by_pipeline_id.get(
                    db_pipeline.id, None
                ),
            )
            for db_pipeline in db_pipelines
        ]

    async def list_pipelines(
        self,
        organization_id: UUID,
        only_include_pipeline_ids: UnsetAware[set[UUID]] = UNSET,
        include_custom_object: bool | None = False,
        exclude_archived: bool = True,
        limit: int | None = None,
        offset: int = 0,
    ) -> list[PipelineV2]:
        if specified(only_include_pipeline_ids):
            db_pipelines: list[Pipeline] = await self.pipeline_repository.list_by_ids(
                organization_id=organization_id,
                pipeline_ids=list(only_include_pipeline_ids),
                exclude_archived=exclude_archived,
            )
            # If specific IDs are requested, fetch raw Pipelines and then enrich them
            return await self._enrich_pipeline_v2(
                db_pipelines=db_pipelines,
                organization_id=organization_id,
                include_custom_object=include_custom_object,
            )
        elif limit:
            db_pipelines = await self.pipeline_repository.list_all_paginated(
                organization_id=organization_id,
                exclude_archived=exclude_archived,
                limit=limit,
                offset=offset,
            )
            return await self._bulk_enrich_pipeline_v2(
                organization_id=organization_id,
                exclude_archived=exclude_archived,
                include_custom_object=include_custom_object,
                db_pipelines=db_pipelines,
            )
        else:
            # If listing all (no specific IDs), _bulk_enrich_pipeline_v2 already returns the enriched V2 list
            return await self._bulk_enrich_pipeline_v2(
                organization_id=organization_id,
                exclude_archived=exclude_archived,
                include_custom_object=include_custom_object,
            )

    async def get_pipeline_by_id(
        self,
        organization_id: UUID,
        pipeline_id: UUID,
        include_custom_object: bool | None = False,
        exclude_archived: bool = True,
    ) -> PipelineV2:
        if not (
            list_result := await self.list_pipelines(
                organization_id=organization_id,
                only_include_pipeline_ids={pipeline_id},
                include_custom_object=include_custom_object,
                exclude_archived=exclude_archived,
            )
        ):
            raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")
        return list_result[0]

    async def list_contact_pipeline_roles_by_association_ids(
        self,
        *,
        organization_id: UUID,
        association_ids: list[UUID],
    ) -> list[ContactPipelineRole]:
        contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_association_ids(
            organization_id=organization_id,
            association_ids=association_ids,
        )

        return [
            contact_pipeline_role_from_db(contact_pipeline_association=association)
            for association in contact_pipeline_associations
        ]

    async def list_contact_pipeline_associations_by_pipeline_id(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        return await self.pipeline_repository.list_contact_pipeline_associations_by_pipeline_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            primary_association_only=primary_association_only,
            exclude_archived=exclude_archived,
        )

    async def list_pipelines_by_account_id(
        self,
        organization_id: UUID,
        account_id: UUID,
        exclude_archived: bool = True,
    ) -> list[Pipeline]:
        pipeline_account_id_map = (
            await self.pipeline_repository.map_pipelines_by_account_ids(
                organization_id=organization_id,
                account_ids=[account_id],
                exclude_archived=exclude_archived,
            )
        )
        return list(pipeline_account_id_map.get(account_id, ()))

    async def list_contact_pipeline_associations_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
        pipeline_id: UUID | None = None,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        return await self.pipeline_repository.list_contact_pipeline_associations_by_contact_ids(
            organization_id=organization_id,
            contact_ids={contact_id},
            account_id=account_id,
            pipeline_id=pipeline_id,
            exclude_archived=exclude_archived,
        )

    async def list_contact_pipeline_roles_by_pipeline_id(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineRole]:
        contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_pipeline_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            primary_association_only=primary_association_only,
            exclude_archived=exclude_archived,
        )
        return [
            contact_pipeline_role_from_db(contact_pipeline_association=association)
            for association in contact_pipeline_associations
        ]

    async def list_contact_pipeline_roles_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
        pipeline_id: UUID | None = None,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineRole]:
        contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_contact_ids(
            organization_id=organization_id,
            contact_ids={contact_id},
            account_id=account_id,
            pipeline_id=pipeline_id,
            exclude_archived=exclude_archived,
        )
        return [
            contact_pipeline_role_from_db(contact_pipeline_association=association)
            for association in contact_pipeline_associations
        ]

    async def list_contact_pipeline_roles_by_organization_id(
        self,
        *,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineRole]:
        contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_organization_id(
            organization_id=organization_id,
            exclude_archived=exclude_archived,
        )

        return [
            contact_pipeline_role_from_db(contact_pipeline_association=association)
            for association in contact_pipeline_associations
        ]

    async def list_contact_pipeline_roles_paginated(
        self,
        *,
        organization_id: UUID,
        exclude_archived: bool = True,
        offset: int = 0,
        limit: int = 50,
    ) -> list[ContactPipelineRole]:
        """List contact pipeline roles with offset-based pagination."""
        contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_organization_id(
            organization_id=organization_id,
            exclude_archived=exclude_archived,
        )
        all_contact_pipeline_roles = [
            contact_pipeline_role_from_db(contact_pipeline_association=association)
            for association in contact_pipeline_associations
        ]

        # Apply offset/limit pagination
        if limit:
            return all_contact_pipeline_roles[offset : offset + limit]
        return all_contact_pipeline_roles[offset:]

    async def map_contact_pipeline_roles_by_pipeline_ids(
        self,
        *,
        organization_id: UUID,
        pipeline_ids: set[UUID],
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> dict[UUID, list[ContactPipelineRole]]:
        """
        Returns a mapping of pipeline_id to a list of ContactPipelineRole.

        Args:
            organization_id: The organization ID
            pipeline_ids: List of pipeline IDs to get contact opportunity roles for
            primary_association_only: If True, only return primary contact associations
            exclude_archived: If True, exclude archived associations

        Returns:
            Dict mapping pipeline_id to list of ContactPipelineRole
        """
        contact_pipeline_associations_map = await self.pipeline_repository.map_contact_associations_for_org_pipelines_sql_grouped(
            organization_id=organization_id,
            exclude_archived_pipelines=not exclude_archived,
            active_association_only=exclude_archived,
            db_pipeline_ids=pipeline_ids,
        )

        result: dict[UUID, list[ContactPipelineRole]] = {}
        for pipeline_id, associations in contact_pipeline_associations_map.items():
            filtered_associations = associations
            if primary_association_only:
                filtered_associations = [a for a in associations if a.is_primary]

            result[pipeline_id] = [
                contact_pipeline_role_from_db(contact_pipeline_association=association)
                for association in filtered_associations
            ]

        # Ensure all requested pipeline_ids are in the result
        for pipeline_id in pipeline_ids:
            if pipeline_id not in result:
                result[pipeline_id] = []

        return result

    async def map_contact_pipeline_roles_by_contact_ids(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
        account_id: UUID | None = None,
        exclude_archived: bool = True,
    ) -> dict[UUID, list[ContactPipelineRole]]:
        """
        Returns a mapping of contact_id to a list of ContactPipelineRole.

        Args:
            organization_id: The organization ID
            contact_ids: List of contact IDs to get contact opportunity roles for
            account_id: Optional account ID to filter by
            exclude_archived: If True, exclude archived associations

        Returns:
            Dict mapping contact_id to list of ContactPipelineRole
        """
        # Make a single repository call to get all contact pipeline associations
        # for the given contact_ids
        all_contact_pipeline_associations = await self.pipeline_repository.list_contact_pipeline_associations_by_contact_ids(
            organization_id=organization_id,
            contact_ids=contact_ids,
            account_id=account_id,
            exclude_archived=exclude_archived,
        )

        # Initialize result dictionary with empty lists for all contact_ids
        result: dict[UUID, list[ContactPipelineRole]] = {
            contact_id: [] for contact_id in contact_ids
        }

        # Group associations by contact_id
        for association in all_contact_pipeline_associations:
            contact_id = association.contact_id
            if contact_id in result:  # This check is just for safety
                result[contact_id].append(
                    contact_pipeline_role_from_db(
                        contact_pipeline_association=association
                    )
                )
        return result

    async def map_pipeline_v2_by_contact_ids(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
        primary_contact_filter: Literal["primary", "additional", "all"] = "all",
        exclude_archived: bool = True,
        include_custom_object: bool = False,
    ) -> frozendict[UUID, list[PipelineV2]]:
        db_pipelines_by_contact_ids = (
            await self.pipeline_repository.map_pipelines_by_contact_ids(
                organization_id=organization_id,
                contact_ids=contact_ids,
                primary_contact_filter=primary_contact_filter,
                exclude_archived=exclude_archived,
            )
        )
        db_pipeline_by_id: dict[UUID, Pipeline] = {
            db_pipeline.id: db_pipeline
            for db_pipelines in db_pipelines_by_contact_ids.values()
            for db_pipeline in db_pipelines
        }
        db_pipelines_to_enrich = list(db_pipeline_by_id.values())
        enriched_pipelines: list[PipelineV2] = await self._enrich_pipeline_v2(
            db_pipelines=db_pipelines_to_enrich,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )
        enriched_pipelines_by_id: dict[UUID, PipelineV2] = {
            enriched_pipeline.id: enriched_pipeline
            for enriched_pipeline in enriched_pipelines
        }
        result_dict: defaultdict[UUID, list[PipelineV2]] = defaultdict(list)
        for contact_id, db_pipelines in db_pipelines_by_contact_ids.items():
            for db_pipeline in db_pipelines:
                if db_pipeline.id in enriched_pipelines_by_id:
                    result_dict[contact_id].append(
                        enriched_pipelines_by_id[db_pipeline.id]
                    )
        return frozendict[UUID, list[PipelineV2]](result_dict)

    async def map_pipeline_v2_by_account_ids(
        self,
        *,
        organization_id: UUID,
        account_ids: set[UUID],
        exclude_archived: bool = True,
        include_custom_object: bool = False,
    ) -> frozendict[UUID, list[PipelineV2]]:
        db_pipelines_by_account_ids: Mapping[
            UUID, Sequence[Pipeline]
        ] = await self.pipeline_repository.map_pipelines_by_account_ids(
            organization_id=organization_id,
            account_ids=list(account_ids),
            exclude_archived=exclude_archived,
        )
        db_pipeline_by_id: dict[UUID, Pipeline] = {
            db_pipeline.id: db_pipeline
            for db_pipelines in db_pipelines_by_account_ids.values()
            for db_pipeline in db_pipelines
        }
        db_pipelines_to_enrich = list(db_pipeline_by_id.values())
        enriched_pipelines: list[PipelineV2] = await self._enrich_pipeline_v2(
            db_pipelines=db_pipelines_to_enrich,
            organization_id=organization_id,
            include_custom_object=include_custom_object,
        )
        enriched_pipelines_by_id: dict[UUID, PipelineV2] = {
            enriched_pipeline.id: enriched_pipeline
            for enriched_pipeline in enriched_pipelines
        }
        result_dict: defaultdict[UUID, list[PipelineV2]] = defaultdict(list)
        for account_id, db_pipelines in db_pipelines_by_account_ids.items():
            for db_pipeline in db_pipelines:
                if db_pipeline.id in enriched_pipelines_by_id:
                    result_dict[account_id].append(
                        enriched_pipelines_by_id[db_pipeline.id]
                    )
        return frozendict[UUID, list[PipelineV2]](result_dict)

    async def get_contact_pipeline_association(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        pipeline_id: UUID,
        exclude_archived: bool = True,
    ) -> ContactPipelineAssociation:
        if not (
            contact_pipeline_association
            := await self.find_contact_pipeline_association(
                organization_id=organization_id,
                contact_id=contact_id,
                pipeline_id=pipeline_id,
                exclude_archived=exclude_archived,
            )
        ):
            raise ResourceNotFoundError(
                f"Contact {contact_id} not found in pipeline {pipeline_id}"
            )
        return contact_pipeline_association

    async def find_contact_pipeline_association(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        pipeline_id: UUID,
        exclude_archived: bool = True,
    ) -> ContactPipelineAssociation | None:
        contact_pipeline_associations = (
            await self.list_contact_pipeline_associations_by_contact_id(
                organization_id=organization_id,
                contact_id=contact_id,
                pipeline_id=pipeline_id,
                exclude_archived=exclude_archived,
            )
        )
        return (
            contact_pipeline_associations[0] if contact_pipeline_associations else None
        )

    # todo(xw): optimize caches

    async def _map_pipeline_stage_select_list_dtos_by_value_ids(
        self,
        organization_id: UUID,
    ) -> frozendict[UUID, PipelineStageDto]:
        pipeline_stage_select_list_dtos = await self.pipeline_stage_select_list_service.list_pipeline_stage_select_list_dtos(
            organization_id=organization_id,
        )
        result: dict[UUID, PipelineStageDto] = {}
        for dto in pipeline_stage_select_list_dtos:
            for slv in dto.select_list_value_dtos:
                result[slv.select_list_value_id] = dto
        return frozendict[UUID, PipelineStageDto](result)

    async def _map_pipeline_direct_select_list_dto_by_value_ids(
        self,
        organization_id: UUID,
    ) -> frozendict[UUID, SelectListDto]:
        select_list_dtos = await self.select_list_service.list_select_list_dtos(
            organization_id=organization_id,
            standard_select_list_ids={
                StdSelectListIdentifier.pipeline_source,
                StdSelectListIdentifier.pipeline_type,
                StdSelectListIdentifier.pipeline_closed_won_reasons,
                StdSelectListIdentifier.pipeline_closed_lost_reasons,
            },
        )
        result: dict[UUID, SelectListDto] = {}
        for dto in select_list_dtos:
            for slv in dto.select_list_values:
                result[slv.id] = dto
        return frozendict[UUID, SelectListDto](result)

    async def get_pipeline_select_list_value_container(
        self,
        *,
        organization_id: UUID,
        stage_id: UUID,
        source_id: UUID | None,
        type_id: UUID | None,
        closed_reason_select_list_value_ids: list[UUID] | None,
    ) -> PipelineSelectListValueContainer:
        stage_value_map = await self._map_pipeline_stage_select_list_dtos_by_value_ids(
            organization_id=organization_id,
        )
        direct_value_map: frozendict[UUID, SelectListDto] = frozendict()
        if source_id or type_id or closed_reason_select_list_value_ids:
            direct_value_map = (
                await self._map_pipeline_direct_select_list_dto_by_value_ids(
                    organization_id=organization_id,
                )
            )
        return self._extract_pipeline_select_list_value_container(
            organization_id=organization_id,
            stage_id=stage_id,
            source_id=source_id,
            type_id=type_id,
            closed_reason_select_list_value_ids=closed_reason_select_list_value_ids,
            pipeline_stage_select_list_dtos_by_value_id=stage_value_map,
            pipeline_direct_select_list_dtos_by_value_id=direct_value_map,
        )

    def _extract_pipeline_select_list_value_container(  # noqa: C901
        self,
        *,
        organization_id: UUID,
        stage_id: UUID,
        source_id: UUID | None,
        type_id: UUID | None,
        closed_reason_select_list_value_ids: list[UUID] | None,
        pipeline_stage_select_list_dtos_by_value_id: frozendict[UUID, PipelineStageDto],
        pipeline_direct_select_list_dtos_by_value_id: frozendict[UUID, SelectListDto],
    ) -> PipelineSelectListValueContainer:
        _effective_stage_value_dto: PipelineStageSelectListValueDto | None = None
        _effective_source_value: SelectListValue | None = None
        _effective_type_value: SelectListValue | None = None
        _effective_closed_reason_values: list[SelectListValue] = []

        if not (
            _stage_dto := pipeline_stage_select_list_dtos_by_value_id.get(stage_id)
        ):
            raise IllegalStateError(
                f"Pipeline stage select list value metadata with id {stage_id} not found"
            )
        _effective_stage_value_dto = _stage_dto.get_effective_pipeline_stage_value(
            pipeline_stage_slv_id=stage_id,
        )

        if source_id:
            if not (
                _source_dto := pipeline_direct_select_list_dtos_by_value_id.get(
                    source_id
                )
            ):
                raise IllegalStateError(
                    f"Pipeline source select list value with id {source_id} not found"
                )
            _effective_source_value = _source_dto.get_effective_value(
                slv_id=source_id,
            )

        if type_id:
            if not (
                _type_dto := pipeline_direct_select_list_dtos_by_value_id.get(type_id)
            ):
                raise IllegalStateError(
                    f"Pipeline type select list value with id {type_id} not found"
                )
            _effective_type_value = _type_dto.get_effective_value(
                slv_id=type_id,
            )

        if closed_reason_select_list_value_ids:
            for (
                closed_reason_select_list_value_id
            ) in closed_reason_select_list_value_ids:
                if not (
                    _closed_reason_dto
                    := pipeline_direct_select_list_dtos_by_value_id.get(
                        closed_reason_select_list_value_id
                    )
                ):
                    raise IllegalStateError(
                        f"Pipeline closed reason select list value with id {closed_reason_select_list_value_id} not found"
                    )
                _effective_closed_reason_values.append(
                    _closed_reason_dto.get_effective_value(
                        slv_id=closed_reason_select_list_value_id,
                    )
                )

        if (
            _effective_source_value
            and _effective_source_value.organization_id != organization_id
        ):
            raise IllegalStateError(
                f"Pipeline source select list value with id {source_id} not found"
            )
        if (
            _effective_type_value
            and _effective_type_value.organization_id != organization_id
        ):
            raise IllegalStateError(
                f"Pipeline type select list value with id {type_id} not found"
            )
        if _effective_closed_reason_values and any(
            _effective_closed_reason_value.organization_id != organization_id
            for _effective_closed_reason_value in _effective_closed_reason_values
        ):
            raise IllegalStateError(
                f"Pipeline closed reason select list value with ids {closed_reason_select_list_value_ids} not found"
            )
        if _effective_stage_value_dto.organization_id != organization_id:
            raise IllegalStateError(
                f"Pipeline stage select list value metadata with id {stage_id} not found"
            )

        return PipelineSelectListValueContainer(
            stage_select_list_value_dto=_effective_stage_value_dto,
            source_select_list_value=_effective_source_value,
            type_select_list_value=_effective_type_value,
            closed_reason_select_list_values=_effective_closed_reason_values
            if _effective_closed_reason_values
            else None,
        )

    async def list_contact_pipeline_role_by_pipeline_id(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineRole]:
        contact_pipeline_associations = (
            await self.list_contact_pipeline_associations_by_pipeline_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                primary_association_only=primary_association_only,
                exclude_archived=exclude_archived,
            )
        )

        # property_metadata
        property_metadata = await self.contact_pipeline_role_ai_rec_service.find_property_metadata_for_record(
            organization_id=organization_id,
            record_ids={
                association.id for association in contact_pipeline_associations
            },
            sobject_name=StdObjectIdentifiers.contact_pipeline_role,
        )

        # Convert from ContactPipelineAssociation to ContactPipelineRole
        return [
            contact_pipeline_role_from_db(
                contact_pipeline_association=association,
                property_metadata=property_metadata.get(association.id, None),
            )
            for association in contact_pipeline_associations
        ]


class SingletonPipelineQueryService(Singleton, PipelineQueryService):
    pass


def get_pipeline_query_service(
    db_engine: DatabaseEngine,
) -> PipelineQueryService:
    return SingletonPipelineQueryService(
        pipeline_repository=PipelineRepository(
            engine=db_engine,
        ),
        custom_object_service=CustomObjectService(
            custom_object_repo=CustomObjectRepository(engine=db_engine),
            select_list_service=get_select_list_service(engine=db_engine),
        ),
        pipeline_stage_select_list_service=get_pipeline_stage_select_list_service(
            engine=db_engine
        ),
        select_list_service=get_select_list_service(engine=db_engine),
        feature_flag_service=get_feature_flag_service(),
        contact_pipeline_role_ai_rec_service=get_contact_pipeline_role_ai_rec_service(
            db_engine=db_engine
        ),
    )
