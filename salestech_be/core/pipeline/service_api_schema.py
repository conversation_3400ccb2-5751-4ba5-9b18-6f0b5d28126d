from collections.abc import Mapping
from datetime import timed<PERSON><PERSON>
from typing import Literal, Self, TypeA<PERSON>s
from uuid import UUID

from pydantic import BaseModel, field_validator, model_validator

from salestech_be.common.core_crm.contact_pipeline_role import ContactPipelineRoleType
from salestech_be.common.type.metadata.field.field_value import FieldValueOrAny
from salestech_be.common.type.numbers import NonNegativeDecimal
from salestech_be.common.type.patch_request import (
    UNSET,
    BaseBulkPatchRequest,
    BasePatchRequest,
    UnsetAware,
    specified,
)
from salestech_be.common.type.shape_constrained_model import ShapeConstrainedModel
from salestech_be.core.approval_request.types import (
    ApprovalRequest,
    IApprovableActionResponse,
)
from salestech_be.core.common.service_api_patch_spec import PatchSpec
from salestech_be.core.metadata.types import (
    ContactPipelineRole,
    StageCriteriaEvaluateResult,
)
from salestech_be.core.opportunity_stage_criteria.standard_criteria.paper_process import (
    PaperProcess,
)
from salestech_be.core.pipeline.qualification_properties.competition import (
    Competition,
    Competitor,
)
from salestech_be.core.pipeline.qualification_properties.decision_criteria import (
    DecisionCriteria,
    DecisionCriteriaItem,
)
from salestech_be.core.pipeline.qualification_properties.decision_process import (
    DecisionProcess,
    DecisionProcessItem,
)
from salestech_be.core.pipeline.qualification_properties.identified_pain import (
    IdentifiedPain,
    IdentifiedPainItem,
)
from salestech_be.core.pipeline.qualification_properties.metric import (
    Metric,
    MetricItem,
)
from salestech_be.core.pipeline.qualification_properties.paper_process import (
    PaperProcessItem,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.stage_criteria.service_api_schema import (
    StageCriteriaEvaluationResultV2,
)
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
)
from salestech_be.db.models.core.types import CreatedSource
from salestech_be.db.models.pipeline import PipelineStatus
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now

ContactPipelineRoleRequestFields = Literal["role_types", "is_primary_contact", "note"]


class ContactPipelineAssociationRequest(BasePatchRequest):
    contact_id: UUID
    note: UnsetAware[str | None] = UNSET
    role_types: UnsetAware[list[ContactPipelineRoleType] | None] = UNSET
    is_primary: UnsetAware[bool] = UNSET


class FullContactPipelineAssociationRequests(BaseModel):
    primary: ContactPipelineAssociationRequest
    additional: list[ContactPipelineAssociationRequest] | None = None

    @model_validator(mode="after")
    def validate_contact_pipeline_association_requests(self) -> Self:
        # make sure the primary contact is not in the additional contacts
        primary_contact_id = self.primary.contact_id
        if primary_contact_id in (
            association.contact_id for association in self.additional or []
        ):
            raise ValueError("Primary contact cannot also be in additional contacts")
        # validate no duplicate contact_ids in additional contacts
        contact_ids = {association.contact_id for association in self.additional or []}
        if len(contact_ids) != len(self.additional or []):
            raise ValueError("Additional contacts cannot have duplicate contact_ids")
        return self


class UpsertContactPipelineAssociationRequests(BaseModel):
    primary: ContactPipelineAssociationRequest | None = None
    additional: list[ContactPipelineAssociationRequest] | None = None

    @model_validator(mode="after")
    def validate_contact_pipeline_association_requests(self) -> Self:
        if (not self.primary) and (not self.additional):
            raise ValueError("Must specify at least one contact")
        # make sure the primary contact is not in the additional contacts
        primary = self.primary
        additional = self.additional or []
        if primary:
            primary_contact_id = primary.contact_id
            if primary_contact_id in (
                association.contact_id for association in additional
            ):
                raise ValueError(
                    "Primary contact cannot also be in additional contacts"
                )
        # validate no duplicate contact_ids in additional contacts
        if additional:
            contact_ids = {association.contact_id for association in additional}
            if len(contact_ids) != len(additional):
                raise ValueError(
                    "Additional contacts cannot have duplicate contact_ids"
                )
        return self


class CreatePipelineRequest(BaseModel):
    display_name: str
    amount: NonNegativeDecimal | None = None
    account_id: UUID
    stage_id: UUID
    type_id: UUID | None = None
    closed_reason_select_list_value_ids: list[UUID] | None = None
    closed_reason_custom_detail: str | None = None
    owner_user_id: UUID
    next_step_details: str | None = None
    next_step_due_at: ZoneRequiredDateTime | None = None
    anticipated_closing_at: ZoneRequiredDateTime | None = None
    expires_at: ZoneRequiredDateTime | None = None
    contact_pipeline_associations: FullContactPipelineAssociationRequests | None = None
    custom_field_data: Mapping[UUID, FieldValueOrAny] | None = None
    participant_user_id_list: list[UUID] | None = None
    created_at: ZoneRequiredDateTime | None = None
    closed_at: ZoneRequiredDateTime | None = None
    created_source: CreatedSource | None = None


class PatchPipelineRequest(BasePatchRequest):
    display_name: UnsetAware[str] = UNSET
    amount: UnsetAware[NonNegativeDecimal | None] = UNSET
    # source_id: UnsetAware[UUID | None] = UNSET
    type_id: UnsetAware[UUID | None] = UNSET
    closed_reason_select_list_value_ids: UnsetAware[list[UUID] | None] = UNSET
    closed_reason_custom_detail: UnsetAware[str | None] = UNSET
    owner_user_id: UnsetAware[UUID] = UNSET
    next_step_details: UnsetAware[str | None] = UNSET
    next_step_due_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    anticipated_closing_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    expires_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    closed_at: UnsetAware[ZoneRequiredDateTime] = UNSET
    custom_field_data: UnsetAware[Mapping[UUID, FieldValueOrAny] | None] = UNSET
    participant_user_id_list: UnsetAware[list[UUID] | None] = UNSET
    created_source: CreatedSource | None = None
    patch_spec: PatchSpec | None = None
    status: UnsetAware[PipelineStatus] = UNSET

    @field_validator("closed_at")
    @classmethod
    def validate_closed_at(
        cls, value: UnsetAware[ZoneRequiredDateTime]
    ) -> UnsetAware[ZoneRequiredDateTime]:
        """
        Validate closed_at field to ensure:
        1. Cannot be None (enforced by type system)
        2. Cannot be in the future (with 60-second tolerance for clock skew)
        """
        # Skip validation if value is UNSET (not specified in request)
        if not specified(value):
            return value

        # Value is specified, validate business rules
        # Allow 60 seconds tolerance for clock skew between client and server
        now_with_tolerance = zoned_utc_now() + timedelta(seconds=60)
        if value > now_with_tolerance:
            raise ValueError("closed_at cannot be in the future")

        return value


class InternalPatchPipelineRequest(PatchPipelineRequest):
    """
    Specialized patch request for internal non-api layer use (data integrity fixing, etc.)
    Additional attributes are allowed to be patched.
    """

    account_id: UnsetAware[UUID] = UNSET

    @classmethod
    def of_public_patch_pipeline_request(
        cls, *, patch_pipeline_request: PatchPipelineRequest
    ) -> Self:
        return cls(
            display_name=patch_pipeline_request.display_name,
            amount=patch_pipeline_request.amount,
            type_id=patch_pipeline_request.type_id,
            owner_user_id=patch_pipeline_request.owner_user_id,
            next_step_details=patch_pipeline_request.next_step_details,
            next_step_due_at=patch_pipeline_request.next_step_due_at,
            anticipated_closing_at=patch_pipeline_request.anticipated_closing_at,
            expires_at=patch_pipeline_request.expires_at,
            closed_at=patch_pipeline_request.closed_at,
            custom_field_data=patch_pipeline_request.custom_field_data,
            closed_reason_select_list_value_ids=patch_pipeline_request.closed_reason_select_list_value_ids,
            closed_reason_custom_detail=patch_pipeline_request.closed_reason_custom_detail,
            participant_user_id_list=patch_pipeline_request.participant_user_id_list,
            status=patch_pipeline_request.status,
        )


class ShiftPipelineStageRequest(BaseModel):
    target_stage_id: UUID
    approval_request_id: UUID | None = None
    closed_reason_select_list_value_ids: list[UUID] | None = None
    closed_reason_custom_detail: str | None = None


class ShiftStageOverrideType(NameValueStrEnum):
    SOFT_FAILURE = "SOFT_FAILURE"
    FAILURE = "FAILURE"


class ShiftStageOverride(BaseModel):
    override_type: ShiftStageOverrideType
    override_reason: str | None = None


class ShiftPipelineStageRequestV2(BaseModel):
    target_stage_id: UUID
    closed_reason_select_list_value_ids: list[UUID] | None = None
    closed_reason_custom_detail: str | None = None
    soft_failure_override: ShiftStageOverride | None = None


class OpportunityShiftPipelineRequest(BaseModel):
    target_pipeline_stage_select_list_id: UUID

    # target_pipeline_stage_select_list_value_id is optional. If missing, shift to target pipeline default stage.
    target_pipeline_stage_select_list_value_id: UUID | None = None
    target_pipeline_closed_reason_select_list_value_ids: list[UUID] | None = None
    target_pipeline_closed_reason_custom_detail: str | None = None


class ShiftPipelineStageDefaultRule(NameValueStrEnum):
    AMOUNT_REQUIRED = "AMOUNT_REQUIRED"
    VALID_EXPIRATION = "VALID_EXPIRATION"
    CLOSED_REASON_REQUIRED = "CLOSED_REASON_REQUIRED"


class ShiftPipelineStageResponse(BaseModel, IApprovableActionResponse):
    pipeline: PipelineV2
    shifted: bool
    violated_default_rule: ShiftPipelineStageDefaultRule | None = None
    violation_description: str | None = None
    stage_criteria_evaluation_result: StageCriteriaEvaluateResult | None = None
    approval_request: ApprovalRequest | None = None

    @property
    def is_successful(self) -> bool:
        if self.violated_default_rule:
            return False
        if self.stage_criteria_evaluation_result:
            return self.stage_criteria_evaluation_result.is_success
        return True


class ShiftPipelineStageResponseV2(BaseModel):
    pipeline: PipelineV2
    shifted: bool
    violated_default_rule: ShiftPipelineStageDefaultRule | None = None
    stage_criteria_evaluation_result: StageCriteriaEvaluationResultV2 | None = None
    violation_description: str | None = None


class ArchiveContactPipelineAssociationRequest(BaseModel):
    contact_ids: set[UUID]


class ReplaceContactPipelineAssociationRequest(BaseModel):
    archiving_contact_id: UUID
    replacing_contact_association_request: ContactPipelineAssociationRequest | None = (
        None
    )

    @model_validator(mode="after")
    def validate_replacing_contact_association(self) -> Self:
        if self.replacing_contact_association_request and (
            self.replacing_contact_association_request.contact_id
            == self.archiving_contact_id
        ):
            raise ValueError("Replacing contact cannot be the archiving contact")
        return self


class PutAllContactPipelineAssociationsResult(BaseModel):
    """
    Result of putting all contact pipeline associations.
    - The pipeline object is returned for convenience.
    - The current list is the current contact pipeline associations.
    - The archived list is the contact pipeline associations that were archived.
    """

    pipeline: PipelineV2
    current: list[ContactPipelineAssociation]
    archived: list[ContactPipelineAssociation]
    demoted_primary: ContactPipelineAssociation | None


class ArchiveContactPipelineAssociationResult(BaseModel):
    """
    Result of archiving a contact pipeline association.
    - The pipeline object is returned for convenience.
    - The archived contact pipeline association is the one that was archived.
    """

    pipeline: PipelineV2
    archived: list[ContactPipelineAssociation]


class UpsertContactPipelineAssociationResult(BaseModel):
    """
    Result of upserting a contact pipeline association.
    - The pipeline object is returned for convenience.
    - The upserted contact pipeline association is the new or updated primary association.
    - The updated_siblings are the other contact pipeline associations that were updated
        because of the upsert.
    """

    pipeline: PipelineV2
    upserted: ContactPipelineAssociation
    demoted_primary: ContactPipelineAssociation | None


class ReplaceContactPipelineAssociationResult(BaseModel):
    """
    Result of replacing a contact pipeline association.
    - The pipeline object is returned for convenience.
    - The archived contact pipeline association is the one that was archived.
    - The upserted contact pipeline association is the replacement.
    - The updated_siblings are the other contact pipeline associations that were updated
        because of the replacement.
    """

    pipeline: PipelineV2
    archived: ContactPipelineAssociation
    replacement: ContactPipelineAssociation | None


class MergeContactPipelineAssociationPreviewResult(BaseModel):
    """
    Result of previewing association updates when merging two accounts/contacts.
    - to_archive is the contact pipeline associations that will be archived.
    - to_create is the contact pipeline associations that will be created.
    - to_promote is the contact pipeline associations that will be promoted to primary.

    Notes: ids in to_create might have no actual meaning, do NOT use them directly as reference.
    """

    to_archive: list[ContactPipelineAssociation]
    to_create: list[ContactPipelineAssociation]
    to_promote: list[ContactPipelineAssociation]


# ContactPipelineRole API Schema


class PatchContactPipelineRoleTypesRequest(ShapeConstrainedModel[ContactPipelineRole]):
    role_types: UnsetAware[list[ContactPipelineRoleType] | None] = UNSET


class ContactPipelineRoleRequest(PatchContactPipelineRoleTypesRequest):
    contact_id: UUID
    is_primary_contact: UnsetAware[bool] = UNSET
    note: UnsetAware[str | None] = UNSET


class UpsertContactPipelineRoleRequests(BaseModel):
    """
    Request model for upserting contact opportunity roles.
    Mirrors the structure of UpsertContactPipelineAssociationRequests.
    """

    primary: ContactPipelineRoleRequest | None = None
    additional: list[ContactPipelineRoleRequest] | None = None

    @model_validator(mode="after")
    def validate_contact_pipeline_role_requests(self) -> Self:
        if (not self.primary) and (not self.additional):
            raise ValueError("Must specify at least one contact")
        # make sure the primary contact is not in the additional contacts
        primary = self.primary
        additional = self.additional or []
        if primary:
            primary_contact_id = primary.contact_id
            if primary_contact_id in (
                association.contact_id for association in additional
            ):
                raise ValueError(
                    "Primary contact cannot also be in additional contacts"
                )
        # validate no duplicate contact_ids in additional contacts
        if additional:
            contact_ids = {association.contact_id for association in additional}
            if len(contact_ids) != len(additional):
                raise ValueError(
                    "Additional contacts cannot have duplicate contact_ids"
                )
        return self


class FullContactPipelineRoleRequests(BaseModel):
    """
    Request model for replacing all contact opportunity roles.
    Mirrors the structure of FullContactPipelineAssociationRequests.
    """

    primary: ContactPipelineRoleRequest
    additional: list[ContactPipelineRoleRequest] | None = None

    @model_validator(mode="after")
    def validate_contact_pipeline_role_requests(self) -> Self:
        # make sure the primary contact is not in the additional contacts
        primary_contact_id = self.primary.contact_id
        if primary_contact_id in (
            association.contact_id for association in self.additional or []
        ):
            raise ValueError("Primary contact cannot also be in additional contacts")
        # validate no duplicate contact_ids in additional contacts
        contact_ids = {association.contact_id for association in self.additional or []}
        if len(contact_ids) != len(self.additional or []):
            raise ValueError("Additional contacts cannot have duplicate contact_ids")
        return self


class UpsertContactPipelineRoleResult(BaseModel):
    """
    Result of upserting a contact opportunity role.
    Mirrors the structure of UpsertContactPipelineAssociationResult.
    """

    pipeline: PipelineV2
    upserted: ContactPipelineRole
    demoted_primary: ContactPipelineRole | None = None


class ArchiveContactPipelineRoleResult(BaseModel):
    """
    Result of archiving contact opportunity roles.
    Mirrors the structure of ArchiveContactPipelineAssociationResult.
    """

    pipeline: PipelineV2
    archived: list[ContactPipelineRole]


class PutAllContactPipelineRolesResult(BaseModel):
    """
    Result of replacing all contact opportunity roles.
    Mirrors the structure of PutAllContactPipelineAssociationsResult.
    """

    pipeline: PipelineV2
    current: list[ContactPipelineRole]
    archived: list[ContactPipelineRole]
    demoted_primary: ContactPipelineRole | None = None


class CreateIdentifiedPainItemRequest(ShapeConstrainedModel[IdentifiedPainItem]):
    name: str
    note: str | None = None


class PatchIdentifiedPainItemRequest(ShapeConstrainedModel[IdentifiedPainItem]):
    name: UnsetAware[str] = UNSET
    note: UnsetAware[str | None] = UNSET


class CreateIdentifiedPainRequest(ShapeConstrainedModel[IdentifiedPain]):
    pipeline_id: UUID


class CreateDecisionProcessItemRequest(ShapeConstrainedModel[DecisionProcessItem]):
    name: str
    note: str | None = None


class PatchDecisionProcessItemRequest(ShapeConstrainedModel[DecisionProcessItem]):
    name: UnsetAware[str] = UNSET
    note: UnsetAware[str | None] = UNSET


class CreateDecisionProcessRequest(ShapeConstrainedModel[DecisionProcess]):
    solution_launches_by: ZoneRequiredDateTime | None = None
    implementation_starts_by: ZoneRequiredDateTime | None = None
    decision_by: ZoneRequiredDateTime | None = None
    testing_starts_by: ZoneRequiredDateTime | None = None
    testing_ends_by: ZoneRequiredDateTime | None = None


class PatchDecisionProcessRequest(ShapeConstrainedModel[DecisionProcess]):
    solution_launches_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    implementation_starts_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    decision_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    testing_starts_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    testing_ends_by: UnsetAware[ZoneRequiredDateTime | None] = UNSET


class CreateDecisionCriteriaItemRequest(ShapeConstrainedModel[DecisionCriteriaItem]):
    name: str
    note: str | None = None


class PatchDecisionCriteriaItemRequest(ShapeConstrainedModel[DecisionCriteriaItem]):
    name: UnsetAware[str] = UNSET
    note: UnsetAware[str | None] = UNSET


class CreateDecisionCriteriaRequest(ShapeConstrainedModel[DecisionCriteria]):
    product_solution_fit_note: str | None = None
    budget_amount: NonNegativeDecimal | None = None
    budget_note: str | None = None


class PatchDecisionCriteriaRequest(ShapeConstrainedModel[DecisionCriteria]):
    product_solution_fit_note: UnsetAware[str | None] = UNSET
    budget_amount: UnsetAware[NonNegativeDecimal | None] = UNSET
    budget_note: UnsetAware[str | None] = UNSET


class CreateMetricItemRequest(ShapeConstrainedModel[MetricItem]):
    name: str
    note: str | None = None


class PatchMetricItemRequest(ShapeConstrainedModel[MetricItem]):
    name: UnsetAware[str] = UNSET
    note: UnsetAware[str | None] = UNSET


class CreateMetricRequest(ShapeConstrainedModel[Metric]):
    business_negative_consequences: str | None = None
    business_positive_outcomes: str | None = None


class PatchMetricRequest(ShapeConstrainedModel[Metric]):
    business_negative_consequences: UnsetAware[str | None] = UNSET
    business_positive_outcomes: UnsetAware[str | None] = UNSET


class CreatePaperProcessItemRequest(ShapeConstrainedModel[PaperProcessItem]):
    name: str
    note: str | None = None


class PatchPaperProcessItemRequest(ShapeConstrainedModel[PaperProcessItem]):
    name: UnsetAware[str] = UNSET
    note: UnsetAware[str | None] = UNSET


class CreatePaperProcessRequest(ShapeConstrainedModel[PaperProcess]):
    pass


class CreateCompetitorRequest(ShapeConstrainedModel[Competitor]):
    competitor_name: str
    competitor_advantage: str | None = None
    competitor_disadvantage: str | None = None
    note: str | None = None


class PatchCompetitorRequest(ShapeConstrainedModel[Competitor]):
    competitor_name: UnsetAware[str] = UNSET
    competitor_advantage: UnsetAware[str | None] = UNSET
    competitor_disadvantage: UnsetAware[str | None] = UNSET
    note: UnsetAware[str | None] = UNSET


class PatchCompetitionRequest(ShapeConstrainedModel[Competition]):
    solution_differentiation: UnsetAware[str | None] = UNSET
    note: UnsetAware[str | None] = UNSET


PatchItemRequest: TypeAlias = (
    PatchDecisionProcessItemRequest
    | PatchDecisionCriteriaItemRequest
    | PatchIdentifiedPainItemRequest
    | PatchMetricItemRequest
    | PatchPaperProcessItemRequest
    | PatchCompetitorRequest
)

PatchCriteriaRequest: TypeAlias = (
    PatchDecisionCriteriaRequest
    | PatchDecisionProcessRequest
    | PatchMetricRequest
    | PatchCompetitionRequest
)

CreateItemRequest: TypeAlias = (
    CreateDecisionProcessItemRequest
    | CreateDecisionCriteriaItemRequest
    | CreateIdentifiedPainItemRequest
    | CreateMetricItemRequest
    | CreatePaperProcessItemRequest
    | CreateCompetitorRequest
)

CreateCriteriaRequest: TypeAlias = (
    CreateDecisionCriteriaRequest
    | CreateIdentifiedPainRequest
    | CreateMetricRequest
    | CreatePaperProcessRequest
)


class BulkPatchPipelineRequest(BaseBulkPatchRequest):
    entity_ids: list[UUID]
    patch_request: PatchPipelineRequest
