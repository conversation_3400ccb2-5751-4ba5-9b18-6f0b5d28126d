import json
from datetime import datetime
from uuid import UUID, uuid4

from fastapi import HTTP<PERSON>xception
from pydantic_ai.agent import <PERSON><PERSON>unR<PERSON>ult
from pydantic_ai.messages import (
    ModelMessage,
    ModelRequest,
    ModelResponse,
    TextPart,
    ToolCallPart,
    ToolReturnPart,
)

from salestech_be.common import ree_llm
from salestech_be.common.singleton import Singleton
from salestech_be.core.ai.prompt.langfuse_prompt_service import (
    LangfusePromptService,
    get_langfuse_prompt_service,
)
from salestech_be.core.ai.prompt.schema import PromptRequest
from salestech_be.core.chat.tools import FE_TOOL_NAMES
from salestech_be.core.chat.types import Chat
from salestech_be.db.dao.chat_repository import ChatRepository, get_chat_repository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.chat import (
    ChatMessage,
    ChatMessageSender,
    MessageContentBlock,
    <PERSON><PERSON><PERSON><PERSON><PERSON>lock,
    Tool<PERSON>allContentBlock,
)
from salestech_be.ree_logging import get_logger

TITLE_GEN_SYSTEM_PROMPT_NAME = "chat.title"

logger = get_logger(__name__)


class ChatHistoryService:
    """
    Service class for managing chat history. For getting chat history data for the
    application, see ChatHistoryQueryService
    """

    def __init__(
        self,
        chat_repo: ChatRepository,
        langfuse_prompt_service: LangfusePromptService,
    ) -> None:
        self.chat_repo = chat_repo
        self.langfuse_prompt_service = langfuse_prompt_service

    async def create_chat(
        self,
        organization_id: UUID,
        user_id: UUID,
        initial_message: str,
        response: str,
        timestamp: datetime,
    ) -> Chat:
        db_chat = await self.chat_repo.create_chat(
            organization_id=organization_id,
            owner_user_id=user_id,
            title=await self._generate_chat_title(
                organization_id=organization_id,
                user_id=user_id,
                initial_message=initial_message,
                assistant_response=response,
            ),
            timestamp=timestamp,
        )
        return Chat.from_db(db_chat)

    async def _generate_chat_title(
        self,
        organization_id: UUID,
        user_id: UUID,
        initial_message: str,
        assistant_response: str,
    ) -> str:
        title_gen_prompt = await self.langfuse_prompt_service.get_prompt(
            PromptRequest(
                prompt_name=TITLE_GEN_SYSTEM_PROMPT_NAME,
                variables={
                    "chat_messages": json.dumps(
                        [
                            {"role": "user", "content": initial_message},
                            {"role": "assistant", "content": assistant_response},
                        ]
                    ),
                },
            )
        )
        response = await ree_llm.acompletion(
            model="vertex_ai/gemini-2.0-flash-001",
            messages=title_gen_prompt.messages,
            max_completion_tokens=15,
            temperature=0.2,
            metadata=ree_llm.LLMTraceMetadata(
                trace_name="chat.title",
                user_id=user_id,
                custom_fields={
                    "organization_id": str(organization_id),
                },
                prompt=title_gen_prompt.prompt_client,
            ),
        )
        return response.message_content.strip()

    async def persist_messages(
        self,
        chat_id: UUID,
        user_message_content: str,
        user_message_timestamp: datetime,
        response_message_id: UUID,
        run_result: AgentRunResult | None = None,
    ) -> list[ChatMessage]:
        if run_result is None:
            raise ValueError("run_result must be provided but was None")

        logger.bind(
            chat_id=chat_id,
            user_message_content=user_message_content,
            user_message_timestamp=user_message_timestamp,
        ).debug("Persisting user message")

        content_blocks = self._pai_to_response_content_blocks(run_result.new_messages())
        logger.bind(
            chat_id=chat_id,
            response_message_id=response_message_id,
            content=content_blocks,
        ).debug("Persisting response message")

        async with self.chat_repo.engine.begin():
            user_message = await self.chat_repo.create_message(
                message_id=uuid4(),
                chat_id=chat_id,
                content=[TextContentBlock(text=user_message_content)],
                sender=ChatMessageSender.USER,
                timestamp=user_message_timestamp,
            )
            response_message = await self.chat_repo.create_message(
                message_id=response_message_id,
                chat_id=chat_id,
                content=content_blocks,
                sender=ChatMessageSender.ASSISTANT,
            )

        return [user_message, response_message]

    @staticmethod
    def _pai_to_response_content_blocks(  # noqa: C901
        model_messages: list[ModelMessage],
    ) -> list[MessageContentBlock]:
        response_content_blocks: list[MessageContentBlock] = []
        for message in model_messages:
            if isinstance(message, ModelResponse):
                for part in message.parts:
                    if isinstance(part, TextPart):
                        response_content_blocks.append(
                            TextContentBlock(text=part.content)
                        )
                    elif isinstance(part, ToolCallPart):
                        # Keep this consistent how we handle COT text in streaming
                        response_content_blocks.append(TextContentBlock(text="\n\n"))
                        response_content_blocks.append(
                            ToolCallContentBlock(
                                tool_name=part.tool_name,
                                tool_id=part.tool_call_id,
                                args=part.args_as_dict(),
                                result=None,
                            )
                        )
                    else:
                        logger.error(
                            f"Dropping unknown message part kind: {part.part_kind}"
                        )
            elif isinstance(message, ModelRequest):
                for request_part in message.parts:
                    if isinstance(request_part, ToolReturnPart):
                        for content_block in reversed(response_content_blocks):
                            # FE tool calls currently use the result to filter unsuccessful invocations
                            # Only persisting successful calls so we don't have to check the result value
                            # in the future if we move to a cleaner approach
                            if (
                                isinstance(content_block, ToolCallContentBlock)
                                and content_block.tool_id == request_part.tool_call_id
                                and content_block.result is None
                                and request_part.tool_name in FE_TOOL_NAMES
                                and str(request_part.content).startswith("Success")
                            ):
                                content_block.result = request_part.content
                                break

        return response_content_blocks

    async def update_chat(
        self,
        chat_id: UUID,
        user_id: UUID,
        organization_id: UUID,
        title: str | None = None,
    ) -> Chat:
        db_chat = await self.chat_repo.update_chat(
            chat_id=chat_id,
            user_id=user_id,
            organization_id=organization_id,
            title=title,
        )
        if db_chat is None:
            raise HTTPException(status_code=404, detail="Chat not found")
        return Chat.from_db(db_chat)

    async def delete_chat(
        self,
        chat_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> Chat:
        db_chat = await self.chat_repo.delete_chat(
            chat_id=chat_id, user_id=user_id, organization_id=organization_id
        )
        if db_chat is None:
            raise HTTPException(status_code=404, detail="Chat not found")
        return Chat.from_db(db_chat)


class SingletonChatHistoryService(ChatHistoryService, Singleton):
    pass


def get_chat_history_service(db_engine: DatabaseEngine) -> ChatHistoryService:
    return SingletonChatHistoryService(
        chat_repo=get_chat_repository(db_engine=db_engine),
        langfuse_prompt_service=get_langfuse_prompt_service(),
    )
