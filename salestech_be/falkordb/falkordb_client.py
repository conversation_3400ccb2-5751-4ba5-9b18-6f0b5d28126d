from __future__ import annotations

import asyncio
import concurrent.futures
import contextlib
import time
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any, TypeVar
from uuid import UUID

from falkordb.asyncio import FalkorDB as AsyncFalkorDB
from falkordb.asyncio.graph import AsyncGraph
from temporalio import activity

from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.meeting.types.meeting_types_v2 import MeetingV2
from salestech_be.core.metadata.types import ContactAccountRole, ContactPipelineRole
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.falkordb.falkordb_factory import (
    create_async_falkordb_instance,
    create_falkor_connection_pool,
)
from salestech_be.falkordb.prepare_node_structure_batch import (
    NodeTypeData,
    RelationshipTypeData,
    prepare_node_structure_batch,
)
from salestech_be.falkordb.schema_registry import (
    OrganizationSchemaDescriptor,
    SchemaRegistry,
)
from salestech_be.ree_logging import get_logger
from salestech_be.settings import settings

# Define a TypeVar for property values
T = TypeVar("T")
# Update PropertyDict to allow nested dictionaries and lists
PropertyDict = dict[str, str | int | float | bool | dict[str, Any] | list[Any] | UUID]

logger = get_logger(__name__)


class FalkorDBClient:
    """Async FalkorDB client for graph operations."""

    def __init__(
        self,
        async_falkordb_client: AsyncFalkorDB | None = None,
    ) -> None:
        """Initialize FalkorDB client with settings or an existing AsyncFalkorDB instance."""
        # Graph prefix for organization-specific graphs
        self.graph_prefix = settings.falkordb_graph_prefix
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=10)

        if async_falkordb_client:
            logger.info(
                "Initializing FalkorDBClient with injected AsyncFalkorDB instance."
            )
            self.client = async_falkordb_client
            # Pool management is handled by the injected client
            self.pool = None
        else:
            logger.info(
                "Initializing FalkorDBClient by creating new AsyncFalkorDB instance."
            )
            # Initialize connection pool using the factory
            self.pool = create_falkor_connection_pool(
                host=settings.falkordb_host,
                port=settings.falkordb_port,
                username=settings.falkordb_user,
                password=settings.falkordb_password,
                blocking_connection_pool=settings.falkordb_blocking_connection_pool,
                max_connections=settings.falkordb_max_connections,
            )
            # Initialize async FalkorDB client using the factory
            self.client = create_async_falkordb_instance(
                pool=self.pool,
                username=settings.falkordb_user,
                password=settings.falkordb_password,
            )

    # Public method version of _get_graph_name
    def get_graph_name(self, organization_id: str | UUID) -> str:
        return self._get_graph_name(organization_id)

    def _get_graph_name(self, organization_id: str | UUID) -> str:
        """Get the graph name for a specific organization.

        Args:
            organization_id: The organization ID

        Returns:
            The graph name for the organization
        """
        org_id_str = str(organization_id).replace("-", "")
        return f"{self.graph_prefix}{org_id_str}"

    async def ensure_indexes(self, organization_id: str | UUID) -> None:
        """Create necessary indexes for efficient querying for a specific organization.

        Args:
            organization_id: The organization ID
        """
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)

        # Create indexes for each node type
        node_types = ["Meeting", "User", "Contact", "Account", "Pipeline"]
        for node_type in node_types:
            # Create index on id field
            try:
                await graph.query(f"CREATE INDEX FOR (n:{node_type}) ON (n.id)")
            except Exception as e:
                # Index might already exist, log and continue
                logger.debug(f"Error creating index for {node_type}: {e}")

    async def ensure_graph_exists(self, organization_id: str | UUID) -> None:
        """Ensure that a graph exists for the specified organization.

        Args:
            organization_id: The organization ID
        """
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)

        # Check if the graph exists by running a simple query
        try:
            await graph.query("MATCH (n) RETURN n LIMIT 1")
        except Exception as e:
            # If the graph doesn't exist, create it with a dummy node that we'll delete
            if "key doesn't contain a graph object" in str(e):
                await graph.query("CREATE (n:_Init {created: true}) RETURN n")
                # Delete the initialization node
                await graph.query("MATCH (n:_Init) DELETE n")
                logger.info(f"Created new graph for organization: {organization_id}")

                # Create indexes for the new graph
                await self.ensure_indexes(organization_id)
            else:
                # If it's a different error, raise it
                raise

    async def _prepare_batch_data_async(
        self,
        batch: list[dict[str, Any]],
        node_type: str,
        schema_registry: SchemaRegistry,
    ) -> tuple[list[NodeTypeData], list[RelationshipTypeData]]:
        """Async wrapper for preparing batch data in a thread pool."""
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(
            self.thread_pool,
            self._prepare_batch_data,
            batch,
            node_type,
            schema_registry,
        )

    def _prepare_batch_data(
        self,
        batch: list[dict[str, Any]],
        node_type: str,
        schema_registry: SchemaRegistry,
    ) -> tuple[list[NodeTypeData], list[RelationshipTypeData]]:
        """Parallelized synchronous function to prepare batch data in a thread pool."""
        batch_nodes_to_create: list[NodeTypeData] = []
        batch_rels_to_create: list[RelationshipTypeData] = []

        def process_item(
            item: dict[str, Any],
        ) -> tuple[list[NodeTypeData], list[RelationshipTypeData]]:
            item_id = item.get("id")
            if not item_id:
                logger.warning(
                    f"Item data missing ID, skipping: {item.get('name', 'N/A')}"
                )
                return [], []
            try:
                return prepare_node_structure_batch(
                    node_type=node_type,
                    node_id=str(item_id),
                    data=item,
                    schema_registry=schema_registry,
                )
            except Exception as e:
                logger.error(
                    f"Error preparing batch data for item {item_id}: {e}",
                    exc_info=True,
                )
                return [], []

        results = [process_item(item) for item in batch]

        for nodes, rels in results:
            batch_nodes_to_create.extend(nodes)
            batch_rels_to_create.extend(rels)

        return batch_nodes_to_create, batch_rels_to_create

    async def sync_accounts(
        self,
        accounts: list[dict[str, Any]],
        organization_id: UUID,
        schema: OrganizationSchemaDescriptor,
        batch_size: int = 5000,  # Reduced from 10000
        deleted_account_ids: set[UUID] | None = None,
    ) -> None:
        """
        Sync accounts to FalkorDB in parallel batches using batch preparation and execution.

        Args:
            accounts: List of account dictionaries to sync (upsert)
            organization_id: Organization ID for the accounts
            schema: Schema descriptor for the accounts
            batch_size: Number of accounts to process in each batch
            deleted_account_ids: Set of account IDs to delete
        """
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)
        account_node_type = AccountV2.object_id.object_name
        schema_registry = SchemaRegistry(schema)

        # 1. Handle Deletions
        if deleted_account_ids:
            logger.info(f"Processing deletion of {len(deleted_account_ids)} accounts.")
            safe_heartbeat()  # Heartbeat before delete operation
            batch_nodes_to_delete = [
                NodeTypeData(
                    label=account_node_type,
                    id=str(account_id),
                    properties={},  # Properties not needed for delete
                )
                for account_id in deleted_account_ids
            ]
            try:
                await self._execute_batch_node_deletes(graph, batch_nodes_to_delete)
                logger.info(
                    f"Successfully completed deletion of {len(deleted_account_ids)} accounts."
                )
            except Exception as e:
                logger.error(f"Failed to delete accounts: {e}", exc_info=True)
                # Decide if to raise or continue if only deletion failed
            safe_heartbeat()  # Heartbeat after delete operation

        # 2. Handle Upserts (Creations/Updates)
        if not accounts:
            logger.info("No accounts provided for syncing (upsert).")
            if deleted_account_ids:
                logger.info("Account sync finished after processing deletions.")
            else:
                logger.info("Account sync finished, no operations performed.")
            return

        logger.info(f"Starting upsert of {len(accounts)} accounts.")
        # Process accounts in batches for upsert
        for i in range(0, len(accounts), batch_size):
            safe_heartbeat()
            batch = accounts[i : i + batch_size]
            logger.info(
                f"MATTY INDEXING FRESH Processing account batch {i // batch_size + 1} for upsert with {len(batch)} items. Org ID is {organization_id}"
            )

            start_time = time.time()
            (
                batch_nodes_to_create,
                batch_rels_to_create,
            ) = await self._prepare_batch_data_async(
                batch, account_node_type, schema_registry
            )
            end_time = time.time()
            logger.info(
                f"Time taken to prepare batch data for accounts upsert: {end_time - start_time} seconds"
            )

            # Execute batch updates for nodes
            if batch_nodes_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_node_updates(graph, batch_nodes_to_create)
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute node batch upsert for accounts: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to execute node batch upsert for accounts: {e}",
                        exc_info=True,
                    )

            # Execute batch updates for relationships (after nodes)
            if batch_rels_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_relationship_updates(
                        graph, batch_rels_to_create
                    )
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute relationship batch upsert for accounts: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    error_message = str(e).replace("{", "{{").replace("}", "}}")
                    logger.error(
                        f"Failed to execute relationship batch upsert for accounts: {error_message}",
                        exc_info=True,
                    )

            logger.info(
                f"Finished processing account batch {i // batch_size + 1} for upsert."
            )
            safe_heartbeat()

        logger.info(f"Completed upserting {len(accounts)} accounts.")

    async def sync_pipelines(
        self,
        pipelines: list[dict[str, Any]],
        organization_id: UUID,
        schema: OrganizationSchemaDescriptor,
        batch_size: int = 5000,  # Reduced from 10000
        deleted_pipeline_ids: set[UUID] | None = None,
    ) -> None:
        """
        Sync pipelines to FalkorDB.

        Args:
            pipelines: List of pipeline dictionaries to sync
            organization_id: Organization ID for the pipelines
            batch_size: Number of pipelines to process in each batch
            deleted_pipeline_ids: Set of pipeline IDs to delete
        """
        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)
        pipeline_node_type = PipelineV2.object_id.object_name
        schema_registry = SchemaRegistry(schema)

        # 1. Handle Deletions
        if deleted_pipeline_ids:
            logger.info(
                f"Processing deletion of {len(deleted_pipeline_ids)} pipelines."
            )
            safe_heartbeat()  # Heartbeat before delete operation
            batch_nodes_to_delete = [
                NodeTypeData(
                    label=pipeline_node_type,
                    id=str(pipeline_id),
                    properties={},  # Properties not needed for delete
                )
                for pipeline_id in deleted_pipeline_ids
            ]
            try:
                await self._execute_batch_node_deletes(graph, batch_nodes_to_delete)
                logger.info(
                    f"Successfully completed deletion of {len(deleted_pipeline_ids)} pipelines."
                )
            except Exception as e:
                logger.error(f"Failed to delete pipelines: {e}", exc_info=True)
                # Decide if to raise or continue if only deletion failed
            safe_heartbeat()  # Heartbeat after delete operation

        # 2. Handle Upserts (Creations/Updates)
        if not pipelines:
            logger.info("No pipelines provided for syncing (upsert).")
            if deleted_pipeline_ids:
                logger.info("Pipeline sync finished after processing deletions.")
            else:
                logger.info("Pipeline sync finished, no operations performed.")
            return

        logger.info(f"Starting upsert of {len(pipelines)} pipelines.")
        # Process pipelines in batches for upsert
        for i in range(0, len(pipelines), batch_size):
            # Send heartbeat before starting each batch
            safe_heartbeat()

            batch = pipelines[i : i + batch_size]
            logger.info(
                f"MATTY INDEXING FRESH Processing pipeline batch {i // batch_size + 1} with {len(batch)} items. Org ID is {organization_id}"
            )

            # Prepare data for all pipelines in the current batch using thread pool
            start_time = time.time()
            (
                batch_nodes_to_create,
                batch_rels_to_create,
            ) = await self._prepare_batch_data_async(
                batch, pipeline_node_type, schema_registry
            )
            end_time = time.time()
            logger.info(
                f"Time taken to prepare batch data for pipelines: {end_time - start_time} seconds"
            )

            # Execute batch updates for nodes
            if batch_nodes_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_node_updates(graph, batch_nodes_to_create)
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute node batch update for pipelines: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to execute node batch update for pipelines: {e}",
                        exc_info=True,
                    )

            # Execute batch updates for relationships (after nodes)
            if batch_rels_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_relationship_updates(
                        graph, batch_rels_to_create
                    )
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute relationship batch update for pipelines: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    error_message = str(e).replace("{", "{{").replace("}", "}}")
                    logger.error(
                        f"Failed to execute relationship batch update for pipelines: {error_message}",
                        exc_info=True,
                    )

            logger.info(f"Finished processing pipeline batch {i // batch_size + 1}.")
            # Send heartbeat after completing each batch
            safe_heartbeat()

        logger.info(f"Completed syncing {len(pipelines)} pipelines.")

    async def sync_meetings(
        self,
        meetings: list[dict[str, Any]],
        organization_id: UUID,
        batch_size: int = 5000,
    ) -> None:
        """
        Sync meetings to FalkorDB.

        Args:
            meetings: List of meeting dictionaries to sync
            organization_id: Organization ID for the meetings
            batch_size: Number of meetings to process in each batch
        """
        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)

        # Process meetings in batches
        for i in range(0, len(meetings), batch_size):
            batch = meetings[i : i + batch_size]
            for meeting in batch:
                # Ensure organization_id is set
                meeting_props = self.clean_props(meeting)
                meeting_props["organization_id"] = str(organization_id)

                # Create nested structure for meeting
                logger.debug(
                    f"MATTY INDEXING FRESH attempting to index meeting: {meeting_props['id']}"
                )
                await self.create_complex_node_structure(
                    graph=graph,
                    node_type=MeetingV2.object_id.object_name,
                    node_id=meeting_props["id"],
                    data=meeting_props,
                )
                logger.debug(
                    f"MATTY INDEXING FRESH successfully indexed meeting: {meeting_props['id']}"
                )

    # TODO: Kill this function once we fully migrate to the new "create_complex_node_structure_lib"
    async def create_complex_node_structure(
        self,
        graph: AsyncGraph,
        node_type: str,
        node_id: str,
        data: dict[str, Any],
        parent_node_id: str | None = None,
        relation_name: str | None = None,
    ) -> None:
        """
        Create a complex node structure with nested objects and relationships.

        Args:
            graph: FalkorDB graph object
            node_type: Type/label of the node to create
            node_id: ID of the node
            data: Node data including nested objects
            parent_node_id: ID of the parent node (if this is a child node)
            relation_name: Name of the relation connecting to parent (if this is a child node)
        """
        # Extract primitive properties vs nested objects
        primitive_props = {}
        nested_objects = {}
        nested_lists = {}

        # Extract custom field data separately
        custom_field_data = dict[str, Any]()

        for key, value in data.items():
            if isinstance(value, dict):
                if key == "custom_field_data":
                    custom_field_data = value
                else:
                    nested_objects[key] = value
            elif (
                isinstance(value, list)
                and value
                and all(isinstance(item, dict) for item in value)
            ):
                nested_lists[key] = value
            else:
                # This is a primitive or list of primitives
                primitive_props[key] = value

        # Update primitive properties with custom field data
        primitive_props.update(custom_field_data)

        # Create the node with primitive properties
        await self._create_node(
            graph=graph,
            label=node_type,
            node_id=node_id,
            properties=primitive_props,
        )

        # Create nodes for nested objects and connect them
        for key, obj in nested_objects.items():
            # Generate a unique ID for the nested object
            sub_node_id = f"{node_id}_{key}"

            # Create the nested object node with the key as node type
            await self.create_complex_node_structure(
                graph=graph,
                node_type=key,
                node_id=sub_node_id,
                data=obj,
                parent_node_id=node_id,
                relation_name=key,
            )

            # Create relationship from parent to this object
            await self._create_relationship(
                graph=graph,
                from_label=node_type,
                from_id=node_id,
                to_label=key,
                to_id=sub_node_id,
                relationship_type=key,
            )

        # Create nodes for lists of objects and connect them
        for key, items in nested_lists.items():
            for i, item in enumerate(items):
                # Generate a unique ID for the list item
                item_node_id = f"{node_id}_{key}_{i}"
                item_node_type = f"{key}_item"

                # Create node for list item
                await self.create_complex_node_structure(
                    graph=graph,
                    node_type=item_node_type,
                    node_id=item_node_id,
                    data=item,
                    parent_node_id=node_id,
                    relation_name=key,
                )

                # Create relationship from parent to this list item
                await self._create_relationship(
                    graph=graph,
                    from_label=node_type,
                    from_id=node_id,
                    to_label=item_node_type,
                    to_id=item_node_id,
                    relationship_type=key,
                )

        # If this is a child node, connect it to the parent
        if parent_node_id and relation_name:
            # We've already created this relationship in the parent processing,
            # no need to create it again
            pass

    async def sync_contact_pipeline_roles(
        self,
        contact_pipeline_roles: list[dict[str, Any]],
        organization_id: UUID,
        schema: OrganizationSchemaDescriptor,
        batch_size: int = 5000,  # Reduced from 10000
        deleted_contact_pipeline_role_ids: set[UUID] | None = None,
    ) -> None:
        """
        Sync contact pipeline roles to FalkorDB.
        """
        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)
        contact_pipeline_role_node_type = ContactPipelineRole.object_id.object_name
        schema_registry = SchemaRegistry(schema)

        # 1. Handle Deletions
        if deleted_contact_pipeline_role_ids:
            logger.info(
                f"Processing deletion of {len(deleted_contact_pipeline_role_ids)} contact pipeline roles."
            )
            safe_heartbeat()  # Heartbeat before delete operation
            batch_nodes_to_delete = [
                NodeTypeData(
                    label=contact_pipeline_role_node_type,
                    id=str(role_id),
                    properties={},  # Properties not needed for delete
                )
                for role_id in deleted_contact_pipeline_role_ids
            ]
            try:
                await self._execute_batch_node_deletes(graph, batch_nodes_to_delete)
                logger.info(
                    f"Successfully completed deletion of {len(deleted_contact_pipeline_role_ids)} contact pipeline roles."
                )
            except Exception as e:
                logger.error(
                    f"Failed to delete contact pipeline roles: {e}", exc_info=True
                )
            safe_heartbeat()  # Heartbeat after delete operation

        # 2. Handle Upserts (Creations/Updates)
        if not contact_pipeline_roles:
            logger.info("No contact pipeline roles provided for syncing (upsert).")
            if deleted_contact_pipeline_role_ids:
                logger.info(
                    "Contact pipeline role sync finished after processing deletions."
                )
            else:
                logger.info(
                    "Contact pipeline role sync finished, no operations performed."
                )
            return

        logger.info(
            f"Starting upsert of {len(contact_pipeline_roles)} contact pipeline roles."
        )
        # Process contact_pipeline_roles in batches for upsert
        for i in range(0, len(contact_pipeline_roles), batch_size):
            # Send heartbeat before starting each batch
            safe_heartbeat()

            batch = contact_pipeline_roles[i : i + batch_size]
            logger.info(
                f"MATTY INDEXING FRESH Processing contact_pipeline_role batch {i // batch_size + 1} with {len(batch)} items. Org ID is {organization_id}"
            )

            # Prepare data for all contact_pipeline_roles in the current batch using thread pool
            start_time = time.time()
            (
                batch_nodes_to_create,
                batch_rels_to_create,
            ) = await self._prepare_batch_data_async(
                batch, contact_pipeline_role_node_type, schema_registry
            )
            end_time = time.time()
            logger.info(
                f"Time taken to prepare batch data for contact pipeline roles: {end_time - start_time} seconds"
            )

            # Execute batch updates for nodes
            if batch_nodes_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_node_updates(graph, batch_nodes_to_create)
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute node batch update for contact pipeline roles: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to execute node batch update for contact pipeline roles: {e}",
                        exc_info=True,
                    )

            # Execute batch updates for relationships (after nodes)
            if batch_rels_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_relationship_updates(
                        graph, batch_rels_to_create
                    )
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute relationship batch update for contact pipeline roles: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    error_message = str(e).replace("{", "{{").replace("}", "}}")
                    logger.error(
                        f"Failed to execute relationship batch update for contact pipeline roles: {error_message}",
                        exc_info=True,
                    )

            logger.info(
                f"Finished processing contact pipeline role batch {i // batch_size + 1}."
            )
            # Send heartbeat after completing each batch
            safe_heartbeat()

        logger.info(
            f"Completed syncing {len(contact_pipeline_roles)} contact pipeline roles."
        )

    async def sync_contact_account_roles(
        self,
        contact_account_roles: list[dict[str, Any]],
        organization_id: UUID,
        schema: OrganizationSchemaDescriptor,
        batch_size: int = 5000,  # Reduced from 10000
        deleted_contact_account_role_ids: set[UUID] | None = None,
    ) -> None:
        """
        Sync contact account roles to FalkorDB.
        """
        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)
        contact_account_role_node_type = ContactAccountRole.object_id.object_name
        schema_registry = SchemaRegistry(schema)

        # 1. Handle Deletions
        if deleted_contact_account_role_ids:
            logger.info(
                f"Processing deletion of {len(deleted_contact_account_role_ids)} contact account roles."
            )
            safe_heartbeat()  # Heartbeat before delete operation
            batch_nodes_to_delete = [
                NodeTypeData(
                    label=contact_account_role_node_type,
                    id=str(role_id),
                    properties={},  # Properties not needed for delete
                )
                for role_id in deleted_contact_account_role_ids
            ]
            try:
                await self._execute_batch_node_deletes(graph, batch_nodes_to_delete)
                logger.info(
                    f"Successfully completed deletion of {len(deleted_contact_account_role_ids)} contact account roles."
                )
            except Exception as e:
                logger.error(
                    f"Failed to delete contact account roles: {e}", exc_info=True
                )
            safe_heartbeat()  # Heartbeat after delete operation

        # 2. Handle Upserts (Creations/Updates)
        if not contact_account_roles:
            logger.info("No contact account roles provided for syncing (upsert).")
            if deleted_contact_account_role_ids:
                logger.info(
                    "Contact account role sync finished after processing deletions."
                )
            else:
                logger.info(
                    "Contact account role sync finished, no operations performed."
                )
            return

        logger.info(
            f"Starting upsert of {len(contact_account_roles)} contact account roles."
        )
        # Process contact_account_roles in batches for upsert
        for i in range(0, len(contact_account_roles), batch_size):
            # Send heartbeat before starting each batch
            safe_heartbeat()

            batch = contact_account_roles[i : i + batch_size]
            logger.info(
                f"MATTY INDEXING FRESH Processing contact_account_role batch {i // batch_size + 1} with {len(batch)} items. Org ID is {organization_id}"
            )

            # Prepare data for all contact_account_roles in the current batch using thread pool
            start_time = time.time()
            (
                batch_nodes_to_create,
                batch_rels_to_create,
            ) = await self._prepare_batch_data_async(
                batch, contact_account_role_node_type, schema_registry
            )
            end_time = time.time()
            logger.info(
                f"Time taken to prepare batch data for contact account roles: {end_time - start_time} seconds"
            )

            # Execute batch updates for nodes
            if batch_nodes_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_node_updates(graph, batch_nodes_to_create)
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute node batch update for contact account roles: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to execute node batch update for contact account roles: {e}",
                        exc_info=True,
                    )

            # Execute batch updates for relationships (after nodes)
            if batch_rels_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_relationship_updates(
                        graph, batch_rels_to_create
                    )
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute relationship batch update for contact account roles: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    error_message = str(e).replace("{", "{{").replace("}", "}}")
                    logger.error(
                        f"Failed to execute relationship batch update for contact account roles: {error_message}",
                        exc_info=True,
                    )

            logger.info(
                f"Finished processing contact account role batch {i // batch_size + 1}."
            )
            # Send heartbeat after completing each batch
            safe_heartbeat()

        logger.info(
            f"Completed syncing {len(contact_account_roles)} contact account roles."
        )

    async def sync_custom_objects(  # noqa: C901, PLR0912, PLR0915
        self,
        custom_objects: list[dict[str, Any]],
        organization_id: UUID,
        schema: OrganizationSchemaDescriptor,
        batch_size: int = 5000,  # Reduced from 10000
        deleted_custom_object_ids_by_type: dict[str, set[UUID]] | None = None,
    ) -> None:
        """
        Sync custom objects to FalkorDB.

        Args:
            custom_objects: List of custom object dictionaries to sync
            organization_id: Organization ID for the custom objects
            batch_size: Number of custom objects to process in each batch
            deleted_custom_object_ids_by_type: Dict of cobject_metadata_id to set of custom object IDs to delete
        """
        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)
        schema_registry = SchemaRegistry(schema)

        # 1. Handle Deletions
        if deleted_custom_object_ids_by_type:
            logger.info(
                f"Processing deletion of custom objects: {deleted_custom_object_ids_by_type}"
            )
            safe_heartbeat()  # Heartbeat before delete operation
            all_deleted_nodes: list[NodeTypeData] = []
            for (
                cobject_metadata_id,
                object_ids_to_delete,
            ) in deleted_custom_object_ids_by_type.items():
                all_deleted_nodes.extend(
                    [
                        NodeTypeData(
                            label=cobject_metadata_id,  # Use the metadata ID as the label
                            id=str(obj_id),
                            properties={},
                        )
                        for obj_id in object_ids_to_delete
                    ]
                )
            if all_deleted_nodes:
                try:
                    await self._execute_batch_node_deletes(graph, all_deleted_nodes)
                    logger.info(
                        f"Successfully completed deletion of {len(all_deleted_nodes)} custom objects."
                    )
                except Exception as e:
                    logger.error(f"Failed to delete custom objects: {e}", exc_info=True)
            safe_heartbeat()  # Heartbeat after delete operation

        # 2. Handle Upserts (Creations/Updates)
        if not custom_objects:
            logger.info("No custom objects provided for syncing (upsert).")
            if deleted_custom_object_ids_by_type:
                logger.info("Custom object sync finished after processing deletions.")
            else:
                logger.info("Custom object sync finished, no operations performed.")
            return

        logger.info(
            f"Starting custom object sync for {len(custom_objects)} custom objects in batches of {batch_size}."
        )

        # Process custom objects in batches for upsert
        for i in range(0, len(custom_objects), batch_size):
            # Send heartbeat before starting each batch
            safe_heartbeat()

            batch = custom_objects[i : i + batch_size]
            logger.info(
                f"MATTY INDEXING FRESH Processing custom object batch {i // batch_size + 1} with {len(batch)} items. Org ID is {organization_id}"
            )

            # Prepare data for all custom objects in the current batch using thread pool
            start_time = time.time()
            batch_nodes_to_create: list[NodeTypeData] = []
            batch_rels_to_create: list[RelationshipTypeData] = []

            def process_custom_object(
                item: dict[str, Any],
            ) -> tuple[list[NodeTypeData], list[RelationshipTypeData]]:
                cobject_metadata_id = item.get("cobject_metadata_id")
                item_id = item.get("id")
                if not cobject_metadata_id or not item_id:
                    logger.warning(
                        f"Custom object missing cobject_metadata_id or id, skipping: {item.get('name', 'N/A')}"
                    )
                    return [], []
                try:
                    return prepare_node_structure_batch(
                        node_type=str(cobject_metadata_id),
                        node_id=str(item_id),
                        data=item,
                        schema_registry=schema_registry,
                    )
                except Exception as e:
                    logger.error(
                        f"Error preparing batch data for custom object {item_id}: {e}",
                        exc_info=True,
                    )
                    return [], []

            results = [process_custom_object(item) for item in batch]

            for nodes, rels in results:
                batch_nodes_to_create.extend(nodes)
                batch_rels_to_create.extend(rels)

            end_time = time.time()
            logger.info(
                f"Time taken to prepare batch data for custom objects: {end_time - start_time} seconds"
            )

            # Execute batch updates for nodes
            if batch_nodes_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_node_updates(graph, batch_nodes_to_create)
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute node batch update for custom objects: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to execute node batch update for custom objects: {e}",
                        exc_info=True,
                    )

            # Execute batch updates for relationships (after nodes)
            if batch_rels_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_relationship_updates(
                        graph, batch_rels_to_create
                    )
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute relationship batch update for custom objects: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    error_message = str(e).replace("{", "{{").replace("}", "}}")
                    logger.error(
                        f"Failed to execute relationship batch update for custom objects: {error_message}",
                        exc_info=True,
                    )

            logger.info(
                f"Finished processing custom object batch {i // batch_size + 1}."
            )
            # Send heartbeat after completing each batch
            safe_heartbeat()

        logger.info(f"Completed syncing {len(custom_objects)} custom objects.")

    async def sync_users(
        self,
        users: list[dict[str, Any]],
        organization_id: UUID,
        schema: OrganizationSchemaDescriptor,
        batch_size: int = 5000,  # Reduced from 10000
        deleted_user_ids: set[UUID] | None = None,
    ) -> None:
        """Sync users to FalkorDB."""

        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)
        user_node_type = OrganizationUserV2.object_id.object_name
        schema_registry = SchemaRegistry(schema)

        # 1. Handle Deletions
        if deleted_user_ids:
            logger.info(f"Processing deletion of {len(deleted_user_ids)} users.")
            safe_heartbeat()  # Heartbeat before delete operation
            batch_nodes_to_delete = [
                NodeTypeData(
                    label=user_node_type,
                    id=str(user_id),
                    properties={},  # Properties not needed for delete
                )
                for user_id in deleted_user_ids
            ]
            try:
                await self._execute_batch_node_deletes(graph, batch_nodes_to_delete)
                logger.info(
                    f"Successfully completed deletion of {len(deleted_user_ids)} users."
                )
            except Exception as e:
                logger.error(f"Failed to delete users: {e}", exc_info=True)
            safe_heartbeat()  # Heartbeat after delete operation

        # 2. Handle Upserts (Creations/Updates)
        if not users:
            logger.info("No users provided for syncing (upsert).")
            if deleted_user_ids:
                logger.info("User sync finished after processing deletions.")
            else:
                logger.info("User sync finished, no operations performed.")
            return

        logger.info(
            f"Starting user sync for {len(users)} users in batches of {batch_size}."
        )

        # Process users in batches for upsert
        for i in range(0, len(users), batch_size):
            # Send heartbeat before starting each batch
            safe_heartbeat()

            batch = users[i : i + batch_size]
            logger.info(
                f"MATTY INDEXING FRESH Processing user batch {i // batch_size + 1} with {len(batch)} items. Org ID is {organization_id}"
            )

            # Prepare data for all users in the current batch using thread pool
            start_time = time.time()
            (
                batch_nodes_to_create,
                batch_rels_to_create,
            ) = await self._prepare_batch_data_async(
                batch, user_node_type, schema_registry
            )
            end_time = time.time()
            logger.info(
                f"Time taken to prepare batch data for users: {end_time - start_time} seconds"
            )

            # Execute batch updates for nodes
            if batch_nodes_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_node_updates(graph, batch_nodes_to_create)
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute node batch update for users: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to execute node batch update for users: {e}",
                        exc_info=True,
                    )

            # Execute batch updates for relationships (after nodes)
            if batch_rels_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_relationship_updates(
                        graph, batch_rels_to_create
                    )
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute relationship batch update for users: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    error_message = str(e).replace("{", "{{").replace("}", "}}")
                    logger.error(
                        f"Failed to execute relationship batch update for users: {error_message}",
                        exc_info=True,
                    )

            logger.info(f"Finished processing user batch {i // batch_size + 1}.")
            # Send heartbeat after completing each batch
            safe_heartbeat()

        logger.info(f"Completed syncing {len(users)} users.")

    async def get_node_by_id(
        self,
        graph: AsyncGraph,
        node_type: str,
        node_id: UUID,
        id_field_name: str = "id",
    ) -> dict[str, Any] | None:
        # Escape label if needed (e.g., contains special chars or starts with number)
        escaped_node_type = (
            f"`{node_type}`"
            if not node_type.isalnum() or node_type[0].isdigit()
            else node_type
        )
        query_text = (
            f"MATCH (n:{escaped_node_type} {{`{id_field_name}`: $id}}) RETURN n"
        )

        logger.info(f"query_text: {query_text}")
        logger.info(f"node_type: {node_type}")
        logger.info(f"node_id: {node_id}")
        logger.info(f"id_field_name: {id_field_name}")
        result = await graph.query(query_text, params={"id": str(node_id)})
        logger.info(f"result: {result}")
        logger.info(f"vars(result): {vars(result)}")
        logger.info(f"result.result_set: {result.result_set}")

        return result.result_set[0][0].properties if result.result_set else None

    async def _create_node(
        self,
        graph: AsyncGraph,
        label: str,
        node_id: str,
        properties: dict[str, Any],
    ) -> None:
        """
        Create or update a node in FalkorDB.

        Args:
            graph: FalkorDB graph object
            label: Node label (type). Assumed to be potentially needing escaping.
            node_id: Node ID
            properties: Node properties
        """
        # This is an explicit step to ensure that the id field is set.
        properties["id"] = node_id

        # Escape label if needed
        escaped_label = (
            f"`{label}`"  # Always escape for safety, Cypher handles valid labels fine.
        )

        # Create or merge node with properties
        query = f"""MERGE (n:{escaped_label} {{id: $node_id}})
                    SET n = $props
                RETURN n"""

        # Add debug logging
        logger.info(f"Query: {query}")
        logger.info(f"Properties: {properties}")

        # Pass id separately and properties as a whole
        params: dict[str, Any] = {"node_id": node_id, "props": properties}

        await graph.query(query, params=params)

    async def _create_relationship(
        self,
        graph: AsyncGraph,
        from_label: str,
        from_id: str,
        to_label: str,
        to_id: str,
        relationship_type: str,
        properties: dict[str, Any] | None = None,
        from_id_field_name: str = "id",
        to_id_field_name: str = "id",
    ) -> None:
        """
        Create a relationship between two nodes in FalkorDB.

        Args:
            graph: FalkorDB graph object
            from_label: Source node label
            from_id: Source node ID
            to_label: Target node label
            to_id: Target node ID
            relationship_type: Type of relationship
            properties: Relationship properties
        """
        # Default properties to empty dict if None
        properties = properties or {}

        # Escape labels and relationship type if needed
        escaped_from_label = (
            from_label if from_label.startswith("`") else f"`{from_label}`"
        )
        escaped_to_label = to_label if to_label.startswith("`") else f"`{to_label}`"
        escaped_rel_type = (
            relationship_type
            if relationship_type.startswith("`")
            else f"`{relationship_type}`"
        )

        # Create relationship with properties
        query = f"""MATCH (a:{escaped_from_label} {{`{from_id_field_name}`: $from_id}}), (b:{escaped_to_label} {{`{to_id_field_name}`: $to_id}})
                        MERGE (a)-[r:{escaped_rel_type}]->(b)
                        SET r = $props
                    RETURN r"""

        logger.info(f"query: {query}")
        logger.info(f"from_id: {from_id}")
        logger.info(f"to_id: {to_id}")
        logger.info(f"props: {properties}")

        # Pass parameters directly
        params = {"from_id": from_id, "to_id": to_id, "props": properties}

        await graph.query(query, params=params)

    async def create_relationship_between_entities(
        self,
        organization_id: UUID,
        from_type: str,
        from_id: UUID,
        to_type: str,
        to_id: UUID,
        relationship_type: str,
        from_id_field_name: str = "id",
        to_id_field_name: str = "id",
        properties: dict[str, Any] | None = None,
    ) -> None:
        """
        Create a relationship between two entities in FalkorDB.

        Args:
            organization_id: Organization ID
            from_type: Source entity type
            from_id: Source entity ID
            to_type: Target entity type
            to_id: Target entity ID
            relationship_type: Type of relationship
            properties: Relationship properties
        """
        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)

        # Verify source node exists
        source_node = await self.get_node_by_id(
            graph,
            from_type,
            from_id,
            from_id_field_name,
        )
        if not source_node:
            logger.warning(
                f"Source node with type '{from_type}' and ID '{from_id}' (field: '{from_id_field_name}') not found in FalkorDB"
            )
            return
        logger.info(f"source_node found: {source_node}")

        # Verify target node exists
        target_node = await self.get_node_by_id(
            graph,
            to_type,
            to_id,
            to_id_field_name,
        )
        if not target_node:
            logger.warning(
                f"Target node with type '{to_type}' and ID '{to_id}' (field: '{to_id_field_name}') not found in FalkorDB"
            )
            return
        logger.info(f"target_node found: {target_node}")

        # Create relationship
        logger.debug(
            f"MATTY INDEXING FRESH attempting to create relationship between {from_type} and {to_type}"
        )
        await self._create_relationship(
            graph,
            from_type,
            str(from_id),  # Ensure IDs are strings for the query
            to_type,
            str(to_id),
            relationship_type,
            properties,
            from_id_field_name,
            to_id_field_name,
        )
        logger.debug(
            f"MATTY INDEXING FRESH successfully created relationship between {from_type} and {to_type}"
        )

    async def _delete_relationship(
        self,
        graph: AsyncGraph,
        from_label: str,
        from_id: str,
        to_label: str,
        to_id: str,
        relationship_type: str,
        from_id_field_name: str = "id",
        to_id_field_name: str = "id",
    ) -> None:
        """
        Deletes a relationship between two nodes in FalkorDB.

        Args:
            graph: FalkorDB graph object
            from_label: Source node label
            from_id: Source node ID
            to_label: Target node label
            to_id: Target node ID
            relationship_type: Type of relationship
            from_id_field_name: Name of the field to use as the source entity ID
            to_id_field_name: Name of the field to use as the target entity ID
        """

        # Escape labels and relationship type if needed
        escaped_from_label = (
            from_label if from_label.startswith("`") else f"`{from_label}`"
        )
        escaped_to_label = to_label if to_label.startswith("`") else f"`{to_label}`"
        escaped_rel_type = (
            relationship_type
            if relationship_type.startswith("`")
            else f"`{relationship_type}`"
        )

        # Create relationship with properties
        query = f"""MATCH (a:{escaped_from_label} {{`{from_id_field_name}`: $from_id}})-[r:{escaped_rel_type}]->(b:{escaped_to_label} {{`{to_id_field_name}`: $to_id}})
                    DELETE r"""

        logger.info(f"query: {query}")
        logger.info(f"from_id: {from_id}")
        logger.info(f"to_id: {to_id}")

        # Pass parameters directly with correct type annotation
        params: dict[str, object] = {"from_id": from_id, "to_id": to_id}

        await graph.query(query, params=params)

    async def delete_relationship_between_entities(
        self,
        organization_id: UUID,
        from_type: str,
        from_id: UUID,
        to_type: str,
        to_id: UUID,
        relationship_type: str,
        from_id_field_name: str = "id",
        to_id_field_name: str = "id",
    ) -> None:
        """
        Delete a relationship between two entities in FalkorDB.

        Args:
            organization_id: Organization ID
            from_type: Source entity type
            from_id: Source entity ID
            to_type: Target entity type
            to_id: Target entity ID
            relationship_type: Type of relationship
        """

        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)

        # Verify source node exists
        source_node = await self.get_node_by_id(
            graph,
            from_type,
            from_id,
            from_id_field_name,
        )
        if not source_node:
            logger.error(
                f"Source node with type '{from_type}' and ID '{from_id}' (field: '{from_id_field_name}') not found in FalkorDB"
            )
            return
        logger.info(f"source_node found: {source_node}")

        # Verify target node exists
        target_node = await self.get_node_by_id(
            graph,
            to_type,
            to_id,
            to_id_field_name,
        )
        if not target_node:
            logger.error(
                f"Target node with type '{to_type}' and ID '{to_id}' (field: '{to_id_field_name}') not found in FalkorDB"
            )
            return
        logger.info(f"target_node found: {target_node}")

        # Delete relationship
        logger.debug(
            f"MATTY INDEXING FRESH attempting to delete relationship between {from_type} and {to_type}"
        )
        await self._delete_relationship(
            graph=graph,
            from_label=from_type,
            from_id=str(from_id),
            to_label=to_type,
            to_id=str(to_id),
            relationship_type=relationship_type,
            from_id_field_name=from_id_field_name,
            to_id_field_name=to_id_field_name,
        )
        logger.debug(
            f"MATTY INDEXING FRESH successfully deleted relationship between {from_type} and {to_type}"
        )

    @staticmethod
    def clean_props(props: dict[str, Any]) -> dict[str, Any]:  # noqa: C901, PLR0912
        """
        Clean properties for FalkorDB compatibility. Converts complex types to strings or appropriate primitives.
        Also escapes property keys if they are not valid Cypher identifiers.

        Args:
            props: Properties to clean

        Returns:
            Cleaned properties
        """
        result: dict[str, Any] = {}
        for key, value in props.items():
            # Skip None values
            if value is None:
                continue

            # Convert key to string if it's not already
            key_str = str(key)

            # Escape key if it's not a valid Cypher identifier (e.g., contains hyphens, starts with a digit)
            # A simple check: if it contains non-alphanumeric chars (excluding '_') or starts with a digit.
            needs_escaping = (
                not key_str.replace("_", "").isalnum() or key_str[0].isdigit()
            )
            escaped_key = f"`{key_str}`" if needs_escaping else key_str

            # Convert known types explicitly
            if isinstance(value, UUID):
                result[escaped_key] = str(value)
            elif isinstance(value, datetime):
                # Convert datetime to milliseconds since epoch (integer)
                result[escaped_key] = int(value.timestamp() * 1000)
            elif isinstance(value, Decimal):
                # Convert Decimal to string to avoid precision issues with float
                result[escaped_key] = str(value)
            elif (
                hasattr(value, "value")
                and isinstance(value, Enum)
                and not isinstance(value, bool | int | float | str)
            ):
                # Convert Enums (excluding standard types like bool) to their value, then to string
                result[escaped_key] = str(value.value)
            elif isinstance(value, list):
                # Recursively clean items in the list
                cleaned_list: list[
                    str | int | float | bool | dict[str, Any]
                ] = []  # Allow dicts temporarily
                for item in value:
                    if item is None:
                        continue  # Skip None values within lists
                    if isinstance(item, UUID):
                        cleaned_list.append(str(item))
                    elif isinstance(item, datetime):
                        cleaned_list.append(
                            str(int(item.timestamp() * 1000))
                        )  # Keep as string for list homogeneity
                    elif isinstance(item, Decimal):
                        cleaned_list.append(str(item))
                    elif (
                        hasattr(item, "value")
                        and isinstance(item, Enum)
                        and not isinstance(item, bool | int | float | str)
                    ):
                        cleaned_list.append(str(item.value))
                    elif isinstance(item, dict):
                        # Recursively clean dictionaries within lists
                        cleaned_dict = FalkorDBClient.clean_props(item)
                        cleaned_list.append(cleaned_dict)  # Keep as cleaned dictionary
                    elif isinstance(item, bool | int | float | str):
                        cleaned_list.append(
                            str(item)
                        )  # Convert all primitives in list to string
                    else:
                        # Convert other list items to string as a fallback
                        cleaned_list.append(str(item))
                result[escaped_key] = cleaned_list
            elif isinstance(value, dict):
                # Recursively clean nested dictionaries
                result[escaped_key] = FalkorDBClient.clean_props(value)
            elif isinstance(value, bool | int | float | str):
                # Keep allowed primitive types as is
                result[escaped_key] = value
            else:
                # Convert any other types to string as a fallback
                result[escaped_key] = str(value)
        return result

    async def _create_custom_object_node(
        self,
        graph: AsyncGraph,
        label: str,  # Assumed to be already escaped (e.g., `metadata_id`)
        node_id: str,
        properties: dict[str, Any],  # Base properties, already cleaned
        field_values: dict[str, Any],  # Raw field values
    ) -> None:
        """
        Create or update a custom object node in FalkorDB with field values merged into properties.

        Args:
            graph: FalkorDB graph object
            label: Node label (escaped metadata ID)
            node_id: Node ID (data record ID)
            properties: Base node properties (already cleaned)
            field_values: Raw field values dictionary
        """
        # Create a copy of the base properties
        node_props = properties.copy()

        # Clean the field_values dictionary separately
        cleaned_field_values = self.clean_props(field_values)

        # Merge cleaned field values into the node properties
        # This overwrites any base properties with the same key from field_values
        node_props.update(cleaned_field_values)

        # Ensure the primary ID is present and correct
        node_props["id"] = node_id

        logger.info("Creating/updating custom object node:")
        logger.info(f"  Label: {label}")
        logger.info(f"  Node ID: {node_id}")
        logger.info(f"  Final Properties: {node_props}")

        # Create or merge node using the already escaped label
        query = f"""MERGE (n:{label} {{id: $node_id}})
                    SET n = $props
                RETURN n"""

        params = {"node_id": node_id, "props": node_props}

        await graph.query(query, params=params)

    # --- Batch Execution Methods (using UNWIND) ---
    async def _execute_batch_node_deletes(
        self, graph: AsyncGraph, node_batch: list[NodeTypeData]
    ) -> None:
        """
        Executes a batch delete operation for nodes using UNWIND,
        grouping by node label to handle Cypher's lack of label parameterization.
        """
        if not node_batch:
            logger.debug("Node batch is empty, skipping execution.")
            return

        logger.info(f"Executing batch node delete for {len(node_batch)} nodes.")

        nodes_by_label: dict[str, list[NodeTypeData]] = {}
        for node_data in node_batch:
            label = node_data["label"]
            # Escape label here before using as key/in query
            # Simple types likely don't need backticks, but custom objects might
            escaped_label = (
                f"`{label}`" if not label.isalnum() or label[0].isdigit() else label
            )

            if escaped_label not in nodes_by_label:
                nodes_by_label[escaped_label] = []
            # Store the original label within the data if needed, but use escaped for grouping/query
            nodes_by_label[escaped_label].append(node_data)

        for escaped_label, batch_for_label in nodes_by_label.items():
            if not batch_for_label:
                continue
            query = f"""
            UNWIND $batch AS node_data
            MATCH (n:{escaped_label} {{id: node_data.id}})
            DELETE n
            """
            try:
                await graph.query(query, params={"batch": batch_for_label})
                logger.info(
                    f"Successfully executed batch node delete for label '{escaped_label}'."
                )
            except Exception as e:
                logger.error(
                    f"Error executing batch node delete for label '{escaped_label}': {e}",
                    exc_info=True,
                )
                raise

        logger.info(
            f"Completed batch node deletes for {len(node_batch)} total nodes across {len(nodes_by_label)} labels."
        )

    async def execute_batch_node_deletes(  # Public method for use in indexing_lib.py
        self, graph: AsyncGraph, node_batch: list[NodeTypeData]
    ) -> None:
        """
        Executes a batch delete operation for nodes using UNWIND,
        grouping by node label to handle Cypher's lack of label parameterization.
        """
        await self._execute_batch_node_deletes(graph, node_batch)

    async def _execute_batch_node_updates(
        self, graph: AsyncGraph, node_batch: list[NodeTypeData]
    ) -> None:
        """
        Executes a batch update/create operation for nodes using UNWIND,
        grouping by node label to handle Cypher's lack of label parameterization.

        Args:
            graph: The AsyncGraph instance to use.
            node_batch: A list of node data dictionaries, each conforming to NodeTypeData.
                      Expected format: {'label': str, 'id': str, 'properties': dict}
        """
        if not node_batch:
            logger.debug("Node batch is empty, skipping execution.")
            return

        logger.info(f"Executing batch node update for {len(node_batch)} nodes.")

        # Group nodes by label
        nodes_by_label: dict[str, list[NodeTypeData]] = {}
        for node_data in node_batch:
            label = node_data["label"]
            # Escape label here before using as key/in query
            # Simple types likely don't need backticks, but custom objects might
            escaped_label = (
                f"`{label}`" if not label.isalnum() or label[0].isdigit() else label
            )

            if escaped_label not in nodes_by_label:
                nodes_by_label[escaped_label] = []
            # Store the original label within the data if needed, but use escaped for grouping/query
            nodes_by_label[escaped_label].append(node_data)

        # Execute a separate batch query for each label
        for escaped_label, batch_for_label in nodes_by_label.items():
            if not batch_for_label:
                logger.info(
                    f"Skipping label '{escaped_label}' as it has no nodes to update."
                )
                continue

            # Ensure an index exists on the id property for the current label
            try:
                index_query = f"CREATE INDEX ON :{escaped_label}(id)"
                await graph.query(index_query)
                logger.info(
                    f"Successfully created or ensured index exists for :{escaped_label}(id)."
                )
            except Exception as e:
                logger.warning(
                    f"Could not create index for :{escaped_label}(id) (it might already exist or another error occurred): {e}"
                )

            query = f"""
            UNWIND $batch AS node_data
            MERGE (n:{escaped_label} {{id: node_data.id}})
            SET n = node_data.properties
            RETURN count(n)
            """
            try:
                result = await graph.query(query, params={"batch": batch_for_label})
                if result and result.result_set and result.result_set[0][0] > 0:
                    logger.info(
                        f"Successfully merged/updated {result.result_set[0][0]} nodes for label '{escaped_label}'"
                    )
                else:
                    logger.warning(
                        f"No nodes reported as merged/updated for label '{escaped_label}' by query. Result: {result.result_set if result else 'No result'}"
                    )
            except Exception as e:
                logger.error(
                    f"Error executing batch node update for label '{escaped_label}': {e}",
                    exc_info=True,
                    query=query,
                    batch_sample=batch_for_label[:5],
                )
                raise

        logger.info(
            f"Completed batch node updates for {len(node_batch)} total nodes across {len(nodes_by_label)} labels."
        )

    async def _execute_batch_relationship_updates(
        self,
        graph: AsyncGraph,
        rel_batch: list[RelationshipTypeData],
        is_domain_relationship: bool = False,
    ) -> None:
        """
        Executes a batch create/update operation for relationships using UNWIND,
        grouping by relationship type, from_label, and to_label.

        Args:
            graph: The AsyncGraph instance to use.
            rel_batch: A list of relationship data dictionaries with field name info
            is_domain_relationship: Whether the relationships are domain relationships
        """
        if not rel_batch:
            logger.debug("Relationship batch is empty, skipping execution.")
            return

        logger.info(
            f"Executing batch relationship update for {len(rel_batch)} relationships."
        )

        # Group relationships by (from_label, to_label, type, from_id_field_name, to_id_field_name)
        rels_by_group: dict[
            tuple[str, str, str, str | None, str | None], list[RelationshipTypeData]
        ] = {}
        for rel_data in rel_batch:
            group_key = (
                rel_data["from_label"],
                rel_data["to_label"],
                rel_data["type"],
                rel_data.get("from_id_field_name", "id"),  # Default to "id" if None
                rel_data.get("to_id_field_name", "id"),  # Default to "id" if None
            )
            if group_key not in rels_by_group:
                rels_by_group[group_key] = []
            rels_by_group[group_key].append(rel_data)

        # Execute a separate batch query for each relationship group
        group_count = 0
        semaphore = asyncio.Semaphore(10)  # Limit to 10 concurrent queries
        tasks = []
        for group_key, batch_for_group in rels_by_group.items():
            group_count += 1
            if not batch_for_group:
                continue

            from_label, to_label, rel_type, from_id_field_name, to_id_field_name = (
                group_key
            )

            # Escape labels and type for embedding in the query string
            # Assume simple labels don't need escaping, but types might
            escaped_from_label = (
                from_label
                if from_label.startswith("`") and from_label.endswith("`")
                else f"`{from_label}`"
            )
            escaped_to_label = (
                to_label
                if to_label.startswith("`") and to_label.endswith("`")
                else f"`{to_label}`"
            )
            escaped_rel_type = (
                rel_type
                if rel_type.startswith("`") and rel_type.endswith("`")
                else f"`{rel_type}`"
            )
            logger.info(
                f"Executing batch rel update {group_count}/{len(rels_by_group)} for {len(batch_for_group)} rels: "
                f"{escaped_from_label}({from_id_field_name}) -[{escaped_rel_type}]-> {escaped_to_label}({to_id_field_name})."
            )

            if is_domain_relationship:
                query = f"""
                    MATCH (a:{escaped_from_label})
                    MATCH (b:{escaped_to_label})
                    where a.{from_id_field_name} = b.{to_id_field_name}
                    MERGE (a)-[r:{escaped_rel_type}]->(b)
                    return count(r)
                """  # SET r = overwrites, use SET r += if additive properties are needed
            else:  # Sub-node edge creation and Custom Associations creation
                query = f"""
                    UNWIND $batch AS rel_data
                    MATCH (a:{escaped_from_label} {{{from_id_field_name}: rel_data.from_id}})
                    MATCH (b:{escaped_to_label} {{{to_id_field_name}: rel_data.to_id}})
                    MERGE (a)-[r:{escaped_rel_type}]->(b)
                    SET r = rel_data.properties
                    RETURN count(r)
                """  # SET r = overwrites, use SET r += if additive properties are needed

            async def run_query(
                group_key: tuple[str, str, str, str | None, str | None] = group_key,
                batch_for_group: list[RelationshipTypeData] = batch_for_group,
                query: str = query,
            ) -> None:
                async with semaphore:
                    try:
                        if is_domain_relationship:
                            await graph.query(query)
                        else:
                            await graph.query(query, params={"batch": batch_for_group})
                        logger.info(
                            f"Successfully executed batch relationship update for group: {group_key}"
                        )
                    except Exception as e:
                        error_message = str(e).replace("{", "{{").replace("}", "}}")
                        logger.error(
                            f"Error executing batch relationship update for group {group_key}: {error_message}",
                            exc_info=True,
                        )
                        raise

            tasks.append(run_query())

        await asyncio.gather(*tasks)

        logger.info(
            f"Completed batch relationship updates for {len(rel_batch)} total relationships across {len(rels_by_group)} groups."
        )

    # --- End Batch Execution Methods ---

    async def sync_relationships(
        self,
        relationships: list[dict[str, Any]],
        organization_id: UUID,
        schema: OrganizationSchemaDescriptor,
        batch_size: int = 50000,
        is_domain_relationship: bool = False,
    ) -> None:
        """
        Sync relationships to FalkorDB in parallel batches using UNWIND.

        Args:
            relationships: List of relationship dictionaries containing from_node, to_node, and type info
            organization_id: Organization ID
            schema: Schema descriptor
            batch_size: Number of relationships to process in each batch
        """
        if not relationships:
            logger.info("No relationships provided for syncing.")
            return

        # Ensure graph exists
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)

        # Process relationships in batches
        for i in range(0, len(relationships), batch_size):
            batch = relationships[i : i + batch_size]
            logger.info(
                f"MATTY INDEXING FRESH Processing relationship batch {i // batch_size + 1} with {len(batch)} items. Org ID is {organization_id}"
            )

            # Convert relationships to RelationshipTypeData format
            rel_batch: list[RelationshipTypeData] = []
            start_time = time.time()

            for rel_data in batch:
                from_id = rel_data.get("from_id")
                to_id = rel_data.get("to_id")
                rel_type = rel_data.get("type")

                if not all([from_id, to_id, rel_type]):
                    logger.warning(
                        f"Relationship data missing required fields, skipping: {rel_data}"
                    )
                    continue

                try:
                    # Ensure rel_type is a string
                    relationship_type = str(rel_type)

                    # Default to "id" if field names aren't provided
                    rel_batch.append(
                        RelationshipTypeData(
                            from_label=rel_data["from_type"],
                            from_id=str(from_id),
                            from_id_field_name=rel_data.get(
                                "from_id_field_name", "id"
                            ),  # Default to "id"
                            to_label=rel_data["to_type"],
                            to_id=str(to_id),
                            to_id_field_name=rel_data.get(
                                "to_id_field_name", "id"
                            ),  # Default to "id"
                            type=relationship_type,
                            properties=rel_data.get("properties", {}),
                        )
                    )
                except Exception as e:
                    logger.error(
                        f"Error preparing relationship data for {from_id}->{to_id}: {e}",
                        exc_info=True,
                    )
                    continue

            time_to_prepare = time.time() - start_time
            logger.info(
                f"Time to prepare relationship batch: {time_to_prepare} seconds"
            )

            # Execute batch update
            if rel_batch:
                try:
                    start_time = time.time()
                    await self._execute_batch_relationship_updates(
                        graph, rel_batch, is_domain_relationship
                    )
                    time_to_execute = time.time() - start_time
                    logger.info(
                        f"Time to execute relationship batch: {time_to_execute} seconds"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to execute relationship batch: {e}", exc_info=True
                    )

            logger.info(
                f"Finished processing relationship batch {i // batch_size + 1}."
            )

        logger.info(f"Completed syncing {len(relationships)} relationships.")

    async def sync_contacts(
        self,
        contacts: list[dict[str, Any]],
        organization_id: UUID,
        schema: OrganizationSchemaDescriptor,
        batch_size: int = 5000,  # Reduced from 10000
        deleted_contact_ids: set[UUID] | None = None,
    ) -> None:
        """
        Sync contacts to FalkorDB using batch preparation and execution.

        Args:
            contacts: List of contact dictionaries to sync
            organization_id: Organization ID for the contacts
            schema: Schema descriptor for the organization
            batch_size: Number of contacts to process in each database batch
            deleted_contact_ids: Set of contact IDs to delete
        """
        # Ensure the graph exists for this organization
        await self.ensure_graph_exists(organization_id)
        graph_name = self._get_graph_name(organization_id)
        graph = self.client.select_graph(graph_name)
        contact_node_type = ContactV2.object_id.object_name
        schema_registry = SchemaRegistry(schema)

        # 1. Handle Deletions
        if deleted_contact_ids:
            logger.info(f"Processing deletion of {len(deleted_contact_ids)} contacts.")
            safe_heartbeat()  # Heartbeat before delete operation
            batch_nodes_to_delete = [
                NodeTypeData(
                    label=contact_node_type,
                    id=str(contact_id),
                    properties={},  # Properties not needed for delete
                )
                for contact_id in deleted_contact_ids
            ]
            try:
                await self._execute_batch_node_deletes(graph, batch_nodes_to_delete)
                logger.info(
                    f"Successfully completed deletion of {len(deleted_contact_ids)} contacts."
                )
            except Exception as e:
                logger.error(f"Failed to delete contacts: {e}", exc_info=True)
            safe_heartbeat()  # Heartbeat after delete operation

        # 2. Handle Upserts (Creations/Updates)
        if not contacts:
            logger.info("No contacts provided for syncing (upsert).")
            if deleted_contact_ids:
                logger.info("Contact sync finished after processing deletions.")
            else:
                logger.info("Contact sync finished, no operations performed.")
            return

        logger.info(
            f"Starting contact sync for {len(contacts)} contacts in batches of {batch_size}."
        )

        # Process contacts in batches for upsert
        for i in range(0, len(contacts), batch_size):
            # Send heartbeat before starting each batch
            safe_heartbeat()

            batch = contacts[i : i + batch_size]
            logger.info(
                f"MATTY INDEXING FRESH Processing contact batch {i // batch_size + 1} with {len(batch)} items. Org ID is {organization_id}"
            )

            # Prepare data for all contacts in the current batch using thread pool
            start_time = time.time()
            (
                batch_nodes_to_create,
                batch_rels_to_create,
            ) = await self._prepare_batch_data_async(
                batch, contact_node_type, schema_registry
            )
            end_time = time.time()
            logger.info(
                f"Time taken to prepare batch data for contacts: {end_time - start_time} seconds"
            )

            # Execute batch updates for nodes
            if batch_nodes_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_node_updates(graph, batch_nodes_to_create)
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute node batch update for contacts: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to execute node batch update for contacts: {e}",
                        exc_info=True,
                    )

            # Execute batch updates for relationships (after nodes)
            if batch_rels_to_create:
                try:
                    start_time = time.time()
                    await self._execute_batch_relationship_updates(
                        graph, batch_rels_to_create
                    )
                    end_time = time.time()
                    logger.info(
                        f"Time taken to execute relationship batch update for contacts: {end_time - start_time} seconds"
                    )
                except Exception as e:
                    error_message = str(e).replace("{", "{{").replace("}", "}}")
                    logger.error(
                        f"Failed to execute relationship batch update for contacts: {error_message}",
                        exc_info=True,
                    )

            logger.info(f"Finished processing contact batch {i // batch_size + 1}.")
            # Send heartbeat after completing each batch
            safe_heartbeat()

        logger.info(f"Completed syncing {len(contacts)} contacts.")


# Using an internal class to hold the singleton state.
# This avoids metaclass conflicts and issues with global variables or function attributes.
class _FalkorDBState:
    instance: FalkorDBClient | None = None


def get_falkordb_client() -> FalkorDBClient:
    """
    Get a FalkorDB client instance (Singleton pattern).

    Uses an internal class `_FalkorDBState` to lazily initialize and store
    the single instance.

    Returns:
        FalkorDB client instance
    """
    if _FalkorDBState.instance is None:
        _FalkorDBState.instance = FalkorDBClient()
    return _FalkorDBState.instance


# Add a safe heartbeat function for use in both Temporal and non-Temporal contexts


def safe_heartbeat() -> None:
    with contextlib.suppress(Exception):
        activity.heartbeat()
