import asyncio
import time
from collections.abc import Coroutine
from datetime import datetime
from typing import Any, TypeVar
from uuid import UUID

from falkordb.asyncio.graph import AsyncGraph, QueryResult
from pydantic import BaseModel

from salestech_be.common.exception import IllegalStateError
from salestech_be.common.type.metadata.common import (
    CustomObjectIdentifier,
    StandardFieldIdentifier,
    StandardObjectIdentifier,
)
from salestech_be.common.type.metadata.schema import (
    OrganizationSchemaDescriptor,
)
from salestech_be.core.account.types_v2 import AccountV2
from salestech_be.core.common.types import CustomizableDomainModel, DomainModel
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.metadata.types import ContactA<PERSON>untRole, ContactPipelineRole
from salestech_be.core.organization.service.organization_service_v2 import (
    get_organization_service_v2_from_engine,
)
from salestech_be.core.pipeline.types_v2 import PipelineV2
from salestech_be.core.user.types_v2 import OrganizationUserV2
from salestech_be.db.dao.custom_object_association_repository import (
    CustomObjectAssociationRecordRepository,
)
from salestech_be.db.models.user_organization_association import (
    UserOrganizationAssociationStatus,
)
from salestech_be.falkordb.cdc_events.types import Params, Query
from salestech_be.falkordb.create_complex_node_structure import (
    _convert_param_value,
    _process_fields_by_schema,  # Reuse helper for param sanitization
)
from salestech_be.falkordb.prepare_node_structure_batch import (
    NodeTypeData,
)
from salestech_be.falkordb.schema_registry import (
    SchemaRegistry,
)
from salestech_be.falkordb.types import RelationshipDef
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)

T = TypeVar("T", bound=BaseModel)


async def run_in_parallel_and_log_exceptions(
    message: str,
    jobs: list[Coroutine[Any, Any, Any]],
) -> None:
    results = await asyncio.gather(*jobs, return_exceptions=True)
    for result in results:
        if isinstance(result, Exception):
            logger.opt(exception=result).error(f"{message}: {result}")


class FalkorDBIndexingLib:
    """Library for indexing data to FalkorDB."""

    from pydantic import BaseModel

    from salestech_be.common.type.metadata.field.field_type import (
        FieldType,
    )
    from salestech_be.db.dbengine.core import DatabaseEngine
    from salestech_be.falkordb.falkordb_client import (
        FalkorDBClient,
    )

    def __init__(
        self,
        db_engine: DatabaseEngine,
        falkordb_client: FalkorDBClient | None = None,
    ) -> None:
        from salestech_be.core.account.service.account_query_service import (
            get_account_query_service,
        )
        from salestech_be.core.account.service.account_service import (
            get_account_service,
        )
        from salestech_be.core.contact.service.contact_query_service import (
            get_contact_query_service,
        )
        from salestech_be.core.contact.service.contact_service import (
            get_contact_service,
        )
        from salestech_be.core.custom_object.service.custom_object_service import (
            get_custom_object_service,
        )
        from salestech_be.core.data.service.query_service import (
            get_domain_object_query_service,
        )
        from salestech_be.core.meeting.meeting_service import (
            meeting_service_factory_general,
        )
        from salestech_be.core.meeting.service.meeting_query_service import (
            get_meeting_query_service,
        )
        from salestech_be.core.metadata.service.metadata_service import (
            get_metadata_service,
        )
        from salestech_be.core.pipeline.service.pipeline_query_service import (
            get_pipeline_query_service,
        )
        from salestech_be.core.pipeline.service.pipeline_service import (
            get_pipeline_service,
        )
        from salestech_be.core.user.service.user_service import get_user_service_general
        from salestech_be.falkordb.falkordb_client import (
            get_falkordb_client,
        )
        from salestech_be.falkordb.schema_registry import SchemaRegistry

        """Initialize the FalkorDB indexing library.

        Args:
            db_engine: Database engine for accessing data
            falkordb_client: Optional FalkorDB client, will create one if not provided
        """
        self.db_engine = db_engine
        self.falkordb_client = falkordb_client or get_falkordb_client()

        # Initialize services with proper parameters
        self.account_service = get_account_service(db_engine=db_engine)
        self.account_query_service = get_account_query_service(db_engine)
        self.pipeline_service = get_pipeline_service(db_engine)
        self.pipeline_query_service = get_pipeline_query_service(db_engine)
        self.meeting_service = meeting_service_factory_general(db_engine)
        self.meeting_query_service = get_meeting_query_service(db_engine)
        self.contact_service = get_contact_service(db_engine)
        self.contact_query_service = get_contact_query_service(db_engine)
        self.custom_object_service = get_custom_object_service(db_engine)
        self.user_service = get_user_service_general(db_engine)
        self.organization_service_v2 = get_organization_service_v2_from_engine(
            db_engine
        )
        self.domain_object_query_service = get_domain_object_query_service(
            db_engine=db_engine
        )
        self.schema_registry = SchemaRegistry()
        self.metadata_service = get_metadata_service(db_engine=db_engine)
        self.association_record_repository = CustomObjectAssociationRecordRepository(
            engine=db_engine
        )

    def _convert_model_to_dict(self, model: BaseModel) -> dict[str, Any]:
        """Convert a Pydantic model to a dictionary.

        Args:
            model: Pydantic model to convert

        Returns:
            Dictionary representation of the model
        """
        return model.model_dump()

    # Helper functions
    async def get_list_of_all_user_and_org_ids_in_instance(
        self,
    ) -> list[tuple[UUID, UUID]]:
        return await self.organization_service_v2.get_all_user_org_ids()

    async def group_users_by_org(
        self,
        user_and_org_ids: list[tuple[UUID, UUID]],
    ) -> dict[UUID, list[UUID]]:
        org_id_to_user_ids: dict[UUID, list[UUID]] = {}
        for user_id, org_id in user_and_org_ids:
            if org_id not in org_id_to_user_ids:
                org_id_to_user_ids[org_id] = []
            org_id_to_user_ids[org_id].append(user_id)
        return org_id_to_user_ids

    async def index_all_organizations(
        self,
    ) -> list[AsyncGraph]:
        graphs: list[AsyncGraph] = []
        user_and_org_ids = await self.get_list_of_all_user_and_org_ids_in_instance()
        org_id_to_user_ids = await self.group_users_by_org(user_and_org_ids)
        for org_id in org_id_to_user_ids:
            graph = await self.index_organization(
                org_id=org_id, org_id_to_user_ids=org_id_to_user_ids
            )
            graphs.append(graph)
        return graphs

    async def index_organization(
        self,
        org_id: UUID,
        org_id_to_user_ids: dict[UUID, list[UUID]],
        run_batching: bool = False,
    ) -> AsyncGraph:
        # Create a graph for the org
        await self.falkordb_client.ensure_graph_exists(org_id)
        graph_name = self.falkordb_client.get_graph_name(org_id)
        logger.info(f"graph_name: {graph_name}")
        graph = self.falkordb_client.client.select_graph(graph_name)
        logger.info(f"graph: {graph}")
        logger.info(f"vars(graph): {vars(graph)}\n")

        if not run_batching:
            # Index users belonging to the org
            logger.info(f"org_id_to_user_ids[org_id]: {org_id_to_user_ids[org_id]}")
            logger.info(f"org_id: {org_id}")
            await self.index_users(org_id_to_user_ids[org_id], org_id)

        # Index standard objects
        # --- Parallelize initial object indexing ---
        logger.info(
            f"Starting parallel indexing for users and objects for org_id: {org_id}"
        )
        if (
            run_batching
        ):  # Account, Contact, and Pipeline indexing is handled separately
            initial_indexing_tasks = [
                # TODO(raj): Write a batch version of meeting indexing and move this upstream.
                # self.index_all_meetings_for_organization(org_id),
                self.index_all_custom_objects_for_organization(org_id),
            ]
        else:  # Handle all domain objects in parallel
            initial_indexing_tasks = [
                self.index_all_accounts_for_organization(org_id),
                self.index_all_contacts_for_organization(org_id),
                self.index_all_pipelines_for_organization(org_id),
                self.index_all_meetings_for_organization(org_id),
                self.index_all_custom_objects_for_organization(org_id),
            ]
        # Run these tasks concurrently and wait for all to complete
        await asyncio.gather(*initial_indexing_tasks)
        logger.info(
            f"Completed parallel indexing for users and objects for org_id: {org_id}"
        )
        # --- End Parallelization ---

        # --- Schema retrieval (Sequential Step - needed before relationship indexing) ---
        logger.info(f"Retrieving schema for org_id: {org_id}")
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=org_id,
        )
        # logger.info(f"org_schema: {org_schema}")
        self.schema_registry.load_schema(org_schema)
        logger.info(f"Schema loaded for org_id: {org_id}")
        # --- End Schema retrieval ---

        # Index contact account roles
        contacts = await self.contact_service.list_contacts_v2(
            organization_id=org_id, include_custom_object=True
        )
        contact_ids = {contact.id for contact in contacts}

        # Index contact account roles is handled separately in batches if run_batching is True
        if not run_batching:
            await self.index_all_contact_account_roles_for_organization(
                organization_id=org_id, contact_ids=contact_ids
            )

        # Index contact pipeline roles is handled separately in batches if run_batching is True
        if not run_batching:
            for contact_id in contact_ids:
                await self.index_all_contact_pipeline_roles_for_organization(
                    organization_id=org_id,
                    contact_id=contact_id,
                )
        # --- End contact role indexing ---

        # --- Parallelize relationship and association indexing ---
        if not run_batching:
            logger.info(
                f"Starting parallel indexing for relationships and associations for org_id: {org_id}"
            )
            relationship_tasks = [
                self.index_all_relationships_for_organization(org_id),
                self.index_all_custom_associations_for_organization(org_id),
            ]
            try:
                await asyncio.gather(*relationship_tasks)
            except Exception as e:
                logger.exception(
                    f"Error during parallel relationship/association indexing for org {org_id}",
                    exc_info=e,
                )
                # Depending on desired behavior, you might want to raise e here
        else:
            logger.info(
                "We are running batching, so we will not index relationships and associations in this function."
            )

        logger.info(
            f"Completed parallel indexing for relationships and associations for org_id: {org_id}"
        )
        # --- End Parallelization ---

        return graph

    async def index_all_accounts_for_organization_in_batches(
        self,
        organization_id: UUID,
        offset: int,
        batch_size: int,
    ) -> None:
        """Index a batch of accounts using offset pagination."""
        start_time = time.time()
        accounts = await self.account_service.list_accounts_v2_paginated(
            organization_id=organization_id,
            include_custom_object=True,
            offset=offset,
            limit=batch_size,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to list accounts: {end_time - start_time} seconds"
        )
        if not accounts:
            return

        start_time = time.time()
        await self.index_accounts(
            organization_id,
            [account.id for account in accounts],
            accounts=accounts,
            batch_size=batch_size,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to index accounts: {end_time - start_time} seconds"
        )

    async def index_all_accounts_for_organization(
        self,
        organization_id: UUID,
    ) -> None:
        """
        Index all accounts for an organization.

        Args:
            organization_id: The ID of the organization to index accounts for
        """
        accounts = await self.account_service.list_accounts_v2(
            organization_id=organization_id,
            include_custom_object=True,
        )
        logger.info(f"MATTY INDEXING FRESH accounts: length: {len(accounts)}")

        if not accounts:
            logger.info("No accounts to index")
            return

        # Convert accounts to dictionaries
        account_dicts = [self._convert_model_to_dict(account) for account in accounts]

        # Get the org scehma
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )
        # logger.info(f"org_schema: {org_schema}")

        # Index accounts to FalkorDB
        await self.falkordb_client.sync_accounts(
            accounts=account_dicts,
            organization_id=organization_id,
            schema=org_schema,
        )
        logger.info(f"Indexed {len(account_dicts)} accounts to FalkorDB")

    async def index_accounts(
        self,
        organization_id: UUID,
        account_ids: list[UUID],
        accounts: list[AccountV2] | None = None,
        batch_size: int = 10000,
        update_edges: bool = False,
    ) -> None:
        """Index accounts to FalkorDB.

        Args:
            account_ids: List of account IDs to index
        """
        if not account_ids:
            logger.info("No accounts to index")
            return

        logger.info(f"Indexing {len(account_ids)} accounts to FalkorDB")

        # Get accounts from database, including potentially archived ones, to correctly determine deletions
        accounts_from_db = await self.account_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids=set(account_ids),
            include_custom_object=True,
            # Assuming list_accounts_v2 with only_include_account_ids fetches regardless of archive status,
            # or if it has an exclude_archived flag, it should be set to False here.
            # For now, we rely on the subsequent loop to filter.
        )

        account_dicts = []
        output_account_ids = set()
        if accounts_from_db:
            for account in accounts_from_db:
                if account.archived_at is None:  # Filter for active accounts
                    account_dicts.append(self._convert_model_to_dict(account))
                    output_account_ids.add(account.id)

        deleted_account_ids = set(account_ids) - output_account_ids

        if not account_dicts and not deleted_account_ids:
            logger.info("No active accounts to index and no accounts to delete.")
            return

        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        if (
            update_edges and account_dicts
        ):  # Only update edges if there are active nodes
            graph_name = self.falkordb_client.get_graph_name(organization_id)
            graph = self.falkordb_client.client.select_graph(graph_name)
            registry = SchemaRegistry(org_schema)

            jobs = [
                self.update_complex_node_structure(
                    graph=graph,
                    node_type=AccountV2.object_id.object_name,
                    node_id=str(account_dict["id"]),
                    data=account_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=True,
                )
                for account_dict in account_dicts
            ]
            await run_in_parallel_and_log_exceptions(
                "Error in index_accounts during update_complex_node_structure", jobs
            )

        # Index accounts to FalkorDB (upsert active, delete removed ones)
        await self.falkordb_client.sync_accounts(
            accounts=account_dicts if not (update_edges and account_dicts) else [],
            organization_id=organization_id,
            schema=org_schema,
            batch_size=batch_size,
            deleted_account_ids=deleted_account_ids,
        )

        logger.info(
            f"Processed {len(account_dicts)} accounts for upsert and {len(deleted_account_ids)} for deletion to FalkorDB"
        )

    async def cdc_index_accounts_by_ids(
        self,
        organization_id: UUID,
        account_ids: list[UUID],
    ) -> list[tuple[Query, Params]]:
        """Index accounts to FalkorDB for CDC events by IDs.

        Args:
            organization_id: The ID of the organization to index accounts for
            account_ids: List of account IDs to index

        Returns:
            list[tuple[Query, Params]]: List of (query, params) tuples that were generated
        """
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        graph = self.falkordb_client.client.select_graph(graph_name)

        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )
        registry = SchemaRegistry(org_schema)

        accounts = await self.account_service.list_accounts_v2(
            organization_id=organization_id,
            only_include_account_ids=set(account_ids),
            include_custom_object=True,
        )

        active_accounts = [a for a in accounts if a.archived_at is None]
        missing_account_ids = set(account_ids) - {a.id for a in active_accounts}

        await self._delete_accounts(graph, list(missing_account_ids))
        return await self._index_accounts(graph, org_schema, registry, active_accounts)

    async def _delete_accounts(
        self,
        graph: AsyncGraph,
        account_ids: list[UUID],
    ) -> None:
        """Delete accounts from FalkorDB for CDC events by IDs."""
        if not account_ids:
            return

        logger.info(f"Processing deletion of {len(account_ids)} accounts.")
        batch_nodes_to_delete = [
            NodeTypeData(
                label=AccountV2.object_id.object_name,
                id=str(account_id),
                properties={},  # Properties not needed for delete
            )
            for account_id in account_ids
        ]
        try:
            await self.falkordb_client.execute_batch_node_deletes(
                graph, batch_nodes_to_delete
            )
        except Exception as e:
            logger.error(f"Failed to delete accounts: {e}", exc_info=True)
            # Decide if to raise or continue if only deletion failed

    async def _index_accounts(
        self,
        graph: AsyncGraph,
        org_schema: OrganizationSchemaDescriptor,
        registry: SchemaRegistry,
        accounts: list[AccountV2],
    ) -> list[tuple[Query, Params]]:
        """Index accounts to FalkorDB for CDC events, skipping archived/deleted accounts.

        Args:
            organization_id: The ID of the organization to index accounts for
            accounts: List of accounts to index

        Returns:
            list[tuple[Query, Params]]: List of (query, params) tuples that were generated
        """

        active_accounts = [a for a in accounts if a.archived_at is None]
        archived_accounts = [a for a in accounts if a.archived_at is not None]

        if archived_accounts:
            logger.warning(
                "Some accounts are archived, they will not be indexed.",
                archived_accounts=[a.id for a in archived_accounts],
            )

        if not active_accounts:
            logger.warning(
                "No active accounts provided. Please provide a list of active accounts to index."
            )
            return []

        # Update the accounts
        all_queries: list[tuple[str, dict[str, Any]]] = []
        for account in active_accounts:
            account_dict = self._convert_model_to_dict(account)
            try:
                queries = await self.update_complex_node_structure(
                    graph=graph,
                    node_type=AccountV2.object_id.object_name,
                    node_id=str(account_dict["id"]),
                    data=account_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=False,
                )
                all_queries.extend(queries)
            except Exception as e:
                logger.error(
                    f"Error generating queries for account {account_dict['id']}: {e!s}"
                )

        logger.info(
            f"Generated {len(all_queries)} queries for {len(active_accounts)} accounts"
        )

        return all_queries

    def _get_property_from_result(
        self, result: QueryResult, property_name: str
    ) -> UUID | None:
        """Safely extract a property from a FalkorDB query result and convert to UUID.

        Args:
            result: FalkorDB query result
            property_name: Name of the property to extract

        Returns:
            UUID if property found and valid, None otherwise
        """
        try:
            value = result.result_set[0][0].properties[property_name]
            return UUID(value) if value else None
        except (IndexError, AttributeError, KeyError, ValueError) as e:
            logger.warning(
                f"Failed to get property '{property_name}' from result or convert to UUID",
                exc_info=e,
            )
            return None

    async def index_relationships_for_object_name_for_organization(  # noqa: C901, PLR0912, PLR0915
        self,
        object_name: str,
        object_id: str,
        organization_id: UUID,
    ) -> None:
        """Index all domain object level relationships given an object name and an organization."""
        logger.info(
            f"MATTY INDEXING FRESH Indexing all domain object level relationships for organization {organization_id}"
        )

        # Ensure the graph exists for this organization
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        graph = self.falkordb_client.client.select_graph(graph_name)

        standard_object_count = 0
        custom_object_count = 0
        other_object_count = 0
        # TODO: This can probably be done in schema registry itself, and just save the `from_id` and `from_type`` there.
        # Iterate through all domain object relationships and index their outbound relationships
        logger.info(f"object_name: {object_name}")
        logger.info(
            f"MATTY UPDATE FRESH self.schema_registry: {self.schema_registry.domain_object_relationships_by_object_name}"
        )
        logger.info(
            f"MATTY UPDATE FRESH self.schema_registry.domain_object_relationships[{object_name}]: {self.schema_registry.get_domain_relationships(object_name)}"
        )

        # Get the first relationship to check the object type
        relationships = self.schema_registry.get_domain_relationships(object_name)
        if not relationships:
            logger.info(
                f"MATTY UPDATE FRESH No relationships found for object: {object_name}, skipping"
            )
            return

        # Get the node_label
        first_relationship = relationships[0]

        # Proceed if standard object, skip if custom object
        if isinstance(
            first_relationship.self_object_identifier, StandardObjectIdentifier
        ):
            logger.info(
                "MATTY UPDATE FRESH Found StandardObjectIdentifier, proceeding as expected."
            )
            standard_object_count += 1
        elif isinstance(
            first_relationship.self_object_identifier, CustomObjectIdentifier
        ):
            logger.info("MATTY UPDATE FRESH Found CustomObjectIdentifier, skipping.")
            custom_object_count += 1
            return
        else:
            logger.warning(
                f"MATTY UPDATE FRESH Unexpected object identifier type: {type(first_relationship.self_object_identifier)}"
            )
            other_object_count += 1
            return

        try:
            result = await graph.query(
                f"MATCH (n:{object_name} {{id: $object_id}}) RETURN n",
                params={"object_id": object_id},
            )

            logger.info(f"MATTY UPDATE FRESH result: {result}")
            logger.info(f"MATTY UPDATE FRESH vars(result): {vars(result)}")
            logger.info(f"MATTY UPDATE FRESH result.result_set: {result.result_set}")
            logger.info(
                f"MATTY UPDATE FRESH result.result_set[0]: {result.result_set[0]}"
            )
            logger.info(
                f"MATTY UPDATE FRESH result.result_set[0][0]: {result.result_set[0][0]}"
            )
            logger.info(
                f"MATTY UPDATE FRESH result.result_set[0][0].properties: {result.result_set[0][0].properties}"
            )
        except Exception as e:
            logger.warning(
                f'MATTY UPDATE FRESH Unable to get result from graph.query(f"MATCH (n:{object_name}) RETURN n"). Skipping this object.'
            )
            logger.warning(f"MATTY UPDATE FRESH Error: {e}")
            return

        from_id = self._get_property_from_result(result, "id")
        if not from_id:
            logger.warning(
                f"MATTY UPDATE FRESH No valid from_id UUID found for {object_name}"
            )
            return

        # Index outbound relationships
        for outbound_rel in self.schema_registry.get_domain_relationships(object_name):
            logger.info(f"MATTY UPDATE FRESH outbound_rel: {outbound_rel}")
            logger.info(
                f"MATTY UPDATE FRESH outbound_rel.relationship_name: {outbound_rel.relationship_name}"
            )

            if isinstance(
                outbound_rel.self_object_identifier, StandardObjectIdentifier
            ):
                from_type = outbound_rel.self_object_identifier.object_name

            # Note: from_id is already set from above

            if isinstance(
                outbound_rel.related_object_identifier, StandardObjectIdentifier
            ):
                to_type = outbound_rel.related_object_identifier.object_name

            # Get the field name associated to the "to_id"
            if isinstance(
                outbound_rel.ordered_self_field_identifiers[0],
                StandardFieldIdentifier,
            ):
                to_id_field_name = outbound_rel.ordered_self_field_identifiers[
                    0
                ].field_name
                logger.info(f"to_id_field_name: {to_id_field_name}")

                to_id = self._get_property_from_result(result, to_id_field_name)
                if not to_id:
                    logger.warning(
                        f"No valid to_id UUID found for field {to_id_field_name}"
                    )
                    continue
                relationship_type = str(outbound_rel.id)

                logger.info(f"MATTY UPDATE FRESH from_type: {from_type}")
                logger.info(f"MATTY UPDATE FRESH from_id: {from_id}")
                logger.info(f"MATTY UPDATE FRESH to_type: {to_type}")
                logger.info(f"MATTY UPDATE FRESH to_id_field_name: {to_id_field_name}")
                logger.info(f"MATTY UPDATE FRESH to_id: {to_id}")
                logger.info(
                    f"MATTY UPDATE FRESH relationship_type: {relationship_type}"
                )
                try:
                    await self.create_relationship(
                        organization_id=organization_id,
                        from_type=from_type,
                        from_id=from_id,
                        to_type=to_type,
                        to_id=to_id,  # Now guaranteed to be UUID
                        relationship_type=relationship_type,
                    )
                    logger.info(
                        f"MATTY UPDATE FRESH created relationship between {from_type} and {to_type}"
                    )
                except Exception as e:
                    logger.warning(
                        f"MATTY UPDATE FRESH Error creating relationship type {relationship_type} between {from_type} {from_id} and {to_type} {to_id}: {e}"
                    )

        for inbound_rel in self.schema_registry.get_inbound_domain_relationships(
            object_name
        ):
            logger.info(f"MATTY UPDATE FRESH inbound_rel: {inbound_rel}")
            logger.info(
                f"MATTY UPDATE FRESH inbound_rel.relationship_name: {inbound_rel.relationship_name}"
            )

            if isinstance(inbound_rel.self_object_identifier, StandardObjectIdentifier):
                to_type = inbound_rel.self_object_identifier.object_name

            # Note: from_id is already set from above

            if isinstance(
                inbound_rel.related_object_identifier, StandardObjectIdentifier
            ):
                from_type = inbound_rel.related_object_identifier.object_name

            # Get the field name associated to the "to_id"
            if isinstance(
                inbound_rel.ordered_self_field_identifiers[0],
                StandardFieldIdentifier,
            ):
                to_id_field_name = inbound_rel.ordered_self_field_identifiers[
                    0
                ].field_name
                logger.info(f"to_id_field_name: {to_id_field_name}")

                to_id = self._get_property_from_result(result, to_id_field_name)
                if not to_id:
                    logger.warning(
                        f"No valid to_id UUID found for field {to_id_field_name}"
                    )
                    continue

            if isinstance(
                inbound_rel.ordered_related_field_identifiers[0],
                StandardFieldIdentifier,
            ):
                from_id_field_name = inbound_rel.ordered_related_field_identifiers[
                    0
                ].field_name
                logger.info(
                    f"MATTY UPDATE FRESH from_id_field_name: {from_id_field_name}"
                )

                from_id = to_id
                if not from_id:
                    logger.warning(
                        f"No valid from_id UUID found for field {from_id_field_name}"
                    )
                    continue

            if not from_id or not to_id:
                logger.warning(
                    f"No valid from_id or to_id UUID found for {object_name}"
                )
                continue

            relationship_type = str(inbound_rel.id)

            logger.info(
                "MATTY UPDATE FRESH Attributes for cypher query:",
                from_type=from_type,
                from_id=from_id,
                to_type=to_type,
                to_id=to_id,
                to_id_field_name=to_id_field_name,
                from_id_field_name=from_id_field_name,
                relationship_type=relationship_type,
            )

            try:
                await self.create_relationship(
                    organization_id=organization_id,
                    from_type=from_type,
                    from_id=from_id,
                    to_type=to_type,
                    to_id=to_id,  # Now guaranteed to be UUID
                    relationship_type=relationship_type,
                    from_id_field_name=from_id_field_name,
                    to_id_field_name=to_id_field_name,
                )
                logger.info(
                    f"MATTY UPDATE FRESH created relationship between {from_type} and {to_type}"
                )
            except Exception as e:
                logger.warning(
                    f"MATTY UPDATE FRESH Error creating relationship type {relationship_type} between {from_type} {from_id} and {to_type} {to_id}: {e}"
                )

        logger.info(
            f"MATTY UPDATE FRESH Standard object count: {standard_object_count}"
        )
        logger.info(f"MATTY UPDATE FRESH Custom object count: {custom_object_count}")
        logger.info(f"MATTY UPDATE FRESH Other object count: {other_object_count}")
        logger.info(
            "MATTY UPDATE FRESH Finished syncing all domain object level relationships to FalkorDB"
        )

    async def index_all_relationships_for_organization_in_batches(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        batch_size: int = 100,
        start_after: UUID | None = None,
    ) -> None:
        """Index all domain object level relationships for an organization in batches."""
        logger.info(
            f"Starting batch indexing of domain relationships for org {organization_id}"
        )

        # Ensure the graph exists for this organization
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        self.falkordb_client.client.select_graph(graph_name)

        relationships_to_process: list[dict[str, Any]] = []
        standard_object_count = 0
        custom_object_count = 0
        other_object_count = 0

        # Iterate through all domain object relationships
        for (
            object_name,
            relationships,
        ) in self.schema_registry.domain_object_relationships_by_object_name.items():
            if object_name not in [
                "account",
                "user",
                "contact",
                "pipeline",
                "contact_account_role",
                "contact_pipeline_role",
            ]:  # TODO: Add to this as we index more domain objects
                logger.info(f"MATTY INDEXING FRESH skipping object_name: {object_name}")
                continue

            if not relationships:
                logger.info(
                    f"No relationships found for object: {object_name}, skipping"
                )
                continue

            # Get the node_label
            first_relationship = relationships[0]

            # Proceed if standard object, skip if custom object
            if isinstance(
                first_relationship.self_object_identifier, StandardObjectIdentifier
            ):
                logger.info("Found StandardObjectIdentifier, proceeding as expected.")
                standard_object_count += 1
            elif isinstance(
                first_relationship.self_object_identifier, CustomObjectIdentifier
            ):
                logger.info("Found CustomObjectIdentifier, skipping.")
                custom_object_count += 1
                continue
            else:
                logger.warning(
                    f"Unexpected object identifier type: {type(first_relationship.self_object_identifier)}"
                )
                other_object_count += 1
                continue

            # Process outbound relationships
            for outbound_rel in self.schema_registry.get_domain_relationships(
                object_name
            ):
                try:
                    if not isinstance(
                        outbound_rel.self_object_identifier,
                        StandardObjectIdentifier,
                    ):
                        continue

                    # Initialize variables
                    from_type = None
                    to_type = None
                    relationship_type = None

                    # Get from_type (must be standard object as checked above)
                    from_type = outbound_rel.self_object_identifier.object_name
                    if isinstance(
                        outbound_rel.ordered_self_field_identifiers[0],
                        StandardFieldIdentifier,
                    ):
                        from_id_field_name = (
                            outbound_rel.ordered_self_field_identifiers[0].field_name
                        )
                    # Get to_type based on identifier type
                    if isinstance(
                        outbound_rel.related_object_identifier,
                        StandardObjectIdentifier,
                    ):
                        to_type = outbound_rel.related_object_identifier.object_name
                        relationship_type = str(outbound_rel.id)

                        # Only process if we have a valid field identifier
                        if isinstance(
                            outbound_rel.ordered_related_field_identifiers[0],
                            StandardFieldIdentifier,
                        ):
                            # Get to_id from the appropriate field
                            to_id_field_name = (
                                outbound_rel.ordered_related_field_identifiers[
                                    0
                                ].field_name
                            )

                            logger.info(
                                f"MATTY TESTING FRESH to_id_field_name: {to_id_field_name}"
                            )
                            logger.info(f"MATTY TESTING FRESH from_type: {from_type}")
                            logger.info(f"MATTY TESTING FRESH to_type: {to_type}")
                            logger.info(
                                f"MATTY TESTING FRESH relationship_type: {relationship_type}"
                            )
                            # Only create relationship if we have all required data
                            if all(
                                [
                                    from_type,
                                    to_type,
                                    relationship_type,
                                ]
                            ):
                                relationships_to_process.append(
                                    {
                                        "from_type": from_type,
                                        "from_id": "xyz",  # TODO: Fix this later
                                        "from_id_field_name": from_id_field_name,
                                        "to_type": to_type,
                                        "to_id": "abc",  # TODO: Fix this later
                                        "to_id_field_name": to_id_field_name,
                                        "type": relationship_type,
                                        "properties": {},
                                    }
                                )

                except Exception as e:
                    logger.error(
                        f"Error processing outbound relationship for {object_name}: {e}",
                        exc_info=True,
                    )
                    continue

            logger.info(
                f"MATTY TESTING FRESH inbound_domain_object_relationships: {self.schema_registry.get_inbound_domain_relationships(object_name)}"
            )

            # Process inbound relationships
            for inbound_rel in self.schema_registry.get_inbound_domain_relationships(
                object_name
            ):
                logger.info(f"MATTY TESTING FRESH inbound_rel: {inbound_rel}")
                try:
                    if not isinstance(
                        inbound_rel.self_object_identifier,
                        StandardObjectIdentifier,
                    ):
                        continue

                    to_type = inbound_rel.self_object_identifier.object_name
                    if isinstance(
                        inbound_rel.related_object_identifier,
                        StandardObjectIdentifier,
                    ):
                        from_type = inbound_rel.related_object_identifier.object_name
                    relationship_type = str(inbound_rel.id)

                    # Get IDs from the appropriate fields
                    if isinstance(
                        inbound_rel.ordered_self_field_identifiers[0],
                        StandardFieldIdentifier,
                    ):
                        to_id_field_name = inbound_rel.ordered_self_field_identifiers[
                            0
                        ].field_name

                    if isinstance(
                        inbound_rel.ordered_related_field_identifiers[0],
                        StandardFieldIdentifier,
                    ):
                        from_id_field_name = (
                            inbound_rel.ordered_related_field_identifiers[0].field_name
                        )

                    logger.info(
                        f"MATTY TESTING FRESH to_id_field_name: {to_id_field_name}"
                    )
                    logger.info(f"MATTY TESTING FRESH from_type: {from_type}")
                    logger.info(f"MATTY TESTING FRESH to_type: {to_type}")
                    logger.info(
                        f"MATTY TESTING FRESH relationship_type: {relationship_type}"
                    )
                    relationships_to_process.append(
                        {
                            "from_type": from_type,
                            "from_id": "xyz",  # TODO: Fix this later
                            "from_id_field_name": from_id_field_name,
                            "to_type": to_type,
                            "to_id": "abc",  # TODO: Fix this later
                            "to_id_field_name": to_id_field_name,
                            "type": relationship_type,
                            "properties": {},
                        }
                    )

                except Exception as e:
                    logger.error(
                        f"Error processing inbound relationship for {object_name}: {e}",
                        exc_info=True,
                    )
                    continue

            # Process relationships in batches when we reach batch_size
            if len(relationships_to_process) >= batch_size:
                try:
                    await self.falkordb_client.sync_relationships(
                        relationships=relationships_to_process[:batch_size],
                        organization_id=organization_id,
                        schema=await self.metadata_service.get_organization_domain_object_schema(
                            organization_id
                        ),
                        batch_size=batch_size,
                        is_domain_relationship=True,
                    )
                    relationships_to_process = relationships_to_process[batch_size:]
                except Exception as e:
                    logger.error(
                        f"Error during batch relationship indexing: {e}", exc_info=True
                    )
                    raise

        # Process any remaining relationships
        if relationships_to_process:
            try:
                await self.falkordb_client.sync_relationships(
                    relationships=relationships_to_process,
                    organization_id=organization_id,
                    schema=await self.metadata_service.get_organization_domain_object_schema(
                        organization_id
                    ),
                    batch_size=batch_size,
                    is_domain_relationship=True,
                )
            except Exception as e:
                logger.error(
                    f"Error during final batch relationship indexing: {e}",
                    exc_info=True,
                )
                raise

        logger.info(f"Standard object count: {standard_object_count}")
        logger.info(f"Custom object count: {custom_object_count}")
        logger.info(f"Other object count: {other_object_count}")
        logger.info(
            f"Completed batch indexing of domain relationships for org {organization_id}"
        )

    async def index_all_relationships_for_organization(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
    ) -> None:
        """Index all domain object level relationships for an organization."""
        logger.info(
            f"MATTY INDEXING FRESH Indexing all domain object level relationships for organization {organization_id}"
        )

        # Ensure the graph exists for this organization
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        graph = self.falkordb_client.client.select_graph(graph_name)

        standard_object_count = 0
        custom_object_count = 0
        other_object_count = 0
        # TODO: This can probably be done in schema registry itself, and just save the `from_id` and `from_type`` there.
        # Iterate through all domain object relationships and index their outbound relationships
        for (
            object_name,
            relationships,
        ) in self.schema_registry.domain_object_relationships_by_object_name.items():
            if not relationships:
                logger.info(
                    f"No relationships found for object: {object_name}, skipping"
                )
                continue
            logger.info(f"object_name: {object_name}")
            # Get the node_label
            first_relationship = relationships[0]

            # Proceed if standard object, skip if custom object
            if isinstance(
                first_relationship.self_object_identifier, StandardObjectIdentifier
            ):
                logger.info("Found StandardObjectIdentifier, proceeding as expected.")
                standard_object_count += 1
            elif isinstance(
                first_relationship.self_object_identifier, CustomObjectIdentifier
            ):
                logger.info("Found CustomObjectIdentifier, skipping.")
                custom_object_count += 1
                continue
            else:
                logger.warning(
                    f"Unexpected object identifier type: {type(first_relationship.self_object_identifier)}"
                )
                other_object_count += 1
                continue

            all_results = await graph.query(
                f"MATCH (n:{object_name}) RETURN n",
            )

            for result in all_results.result_set:
                # --- Process each result ---
                try:
                    logger.info(f"result: {result}")
                    # logger.info(f"vars(result): {vars(result)}") # Often too verbose
                    if not result:
                        logger.warning(
                            f"Empty result found for object {object_name}, skipping."
                        )
                        continue

                    # Ensure result_set[0] and result_set[0][0] exist before accessing properties
                    if (
                        not result
                        or not result[0]
                        or not hasattr(result[0], "properties")
                    ):
                        logger.warning(
                            f"Unexpected result structure for object {object_name}: {result}, skipping."
                        )
                        continue

                    logger.info(f"result[0].properties: {result[0].properties}")

                    # Index outbound relationships
                    for outbound_rel in self.schema_registry.get_domain_relationships(
                        object_name
                    ):
                        from_id = result[0].properties["id"]
                        if not from_id:
                            logger.warning(
                                f"No valid from_id UUID found for {object_name} in result: {result[0].properties}"
                            )
                            continue
                        logger.info(f"outbound_rel: {outbound_rel}")
                        logger.info(
                            f"outbound_rel.relationship_name: {outbound_rel.relationship_name}"
                        )

                        if isinstance(
                            outbound_rel.self_object_identifier,
                            StandardObjectIdentifier,
                        ):
                            from_type = outbound_rel.self_object_identifier.object_name

                        # Note: from_id is already set from above

                        if isinstance(
                            outbound_rel.related_object_identifier,
                            StandardObjectIdentifier,
                        ):
                            to_type = outbound_rel.related_object_identifier.object_name

                        # Get the field name associated to the "to_id"
                        if isinstance(
                            outbound_rel.ordered_self_field_identifiers[0],
                            StandardFieldIdentifier,
                        ):
                            to_id_field_name = (
                                outbound_rel.ordered_self_field_identifiers[
                                    0
                                ].field_name
                            )
                            logger.info(f"to_id_field_name: {to_id_field_name}")
                            if to_id_field_name not in result[0].properties:
                                logger.warning(
                                    f"to_id_field_name {to_id_field_name} not found in result: {result[0].properties}"
                                )
                                continue
                            to_id = result[0].properties[to_id_field_name]
                            if not to_id:
                                logger.warning(
                                    f"No valid to_id UUID found for field {to_id_field_name} in result: {result[0].properties}"
                                )
                                continue

                            relationship_type = str(outbound_rel.id)

                            logger.info(f"from_type: {from_type}")
                            logger.info(f"from_id: {from_id}")
                            logger.info(f"to_type: {to_type}")
                            logger.info(f"to_id_field_name: {to_id_field_name}")
                            logger.info(f"to_id: {to_id}")
                            logger.info(f"relationship_type: {relationship_type}")

                            await self.create_relationship(
                                organization_id=organization_id,
                                from_type=from_type,
                                from_id=from_id,
                                to_type=to_type,
                                to_id=to_id,  # Now guaranteed to be UUID
                                relationship_type=relationship_type,
                            )
                            logger.info(
                                f"created relationship between {from_type} and {to_type}"
                            )

                    # Index inbound relationships
                    logger.info(
                        f"self.schema_registry.inbound_domain_object_relationships[{object_name}]: {self.schema_registry.get_inbound_domain_relationships(object_name)}"
                    )
                    for (
                        inbound_rel
                    ) in self.schema_registry.get_inbound_domain_relationships(
                        object_name
                    ):
                        logger.info(f"inbound_rel: {inbound_rel}")
                        logger.info(
                            f"inbound_rel.relationship_name: {inbound_rel.relationship_name}"
                        )

                        if isinstance(
                            inbound_rel.self_object_identifier, StandardObjectIdentifier
                        ):
                            to_type = inbound_rel.self_object_identifier.object_name

                        if isinstance(
                            inbound_rel.related_object_identifier,
                            StandardObjectIdentifier,
                        ):
                            from_type = (
                                inbound_rel.related_object_identifier.object_name
                            )

                        to_id_field_name = "id"
                        if isinstance(
                            inbound_rel.ordered_self_field_identifiers[0],
                            StandardFieldIdentifier,
                        ):
                            to_id_field_name = (
                                inbound_rel.ordered_self_field_identifiers[0].field_name
                            )
                            logger.info(f"to_id_field_name: {to_id_field_name}")
                            if to_id_field_name not in result[0].properties:
                                logger.warning(
                                    f"to_id_field_name {to_id_field_name} not found in result: {result[0].properties}"
                                )
                                continue
                            to_id = result[0].properties[to_id_field_name]
                            if not to_id:
                                logger.warning(
                                    f"No valid to_id UUID found for field {to_id_field_name} in result: {result[0].properties}"
                                )
                                continue

                        from_id_field_name = None
                        if isinstance(
                            inbound_rel.ordered_related_field_identifiers[0],
                            StandardFieldIdentifier,
                        ):
                            from_id_field_name = (
                                inbound_rel.ordered_related_field_identifiers[
                                    0
                                ].field_name
                            )
                            logger.info(f"from_id_field_name: {from_id_field_name}")
                            if to_id_field_name not in result[0].properties:
                                logger.warning(
                                    f"to_id_field_name {to_id_field_name} not found in result: {result[0].properties}"
                                )
                                continue
                            from_id = result[0].properties[to_id_field_name]

                            if not from_id:
                                logger.warning(
                                    f"No valid from_id UUID derived for field {from_id_field_name} in result: {result[0].properties}"
                                )
                                continue

                        if not from_id or not to_id:
                            logger.warning(
                                f"No valid from_id or to_id UUID found for {object_name} relationship in result: {result[0].properties}"
                            )
                            continue

                        relationship_type = str(inbound_rel.id)

                        logger.info(f"from_type: {from_type}")
                        logger.info(f"from_id: {from_id}")
                        logger.info(f"to_type: {to_type}")
                        logger.info(f"to_id: {to_id}")
                        logger.info(f"from_id_field_name: {from_id_field_name}")
                        logger.info(f"to_id_field_name: {to_id_field_name}")
                        logger.info(f"relationship_type: {relationship_type}")

                        await self.create_relationship(
                            organization_id=organization_id,
                            from_type=from_type,
                            from_id=from_id,  # Note: Revisit this logic for inbound relationships
                            to_type=to_type,
                            to_id=to_id,
                            relationship_type=relationship_type,
                            from_id_field_name=from_id_field_name
                            if from_id_field_name
                            else "id",  # Pass field names if available
                            to_id_field_name=to_id_field_name
                            if to_id_field_name
                            else "id",
                        )
                        logger.info(
                            f"created relationship between {from_type} and {to_type}"
                        )

                except Exception as e:
                    logger.error(
                        f"Error processing result for {object_name}",
                        exc_info=e,
                        result_data=result,
                    )
                    # Continue processing other results even if one fails
                    continue
        # --- End Process each result ---

        logger.info(f"Standard object count: {standard_object_count}")
        logger.info(f"Custom object count: {custom_object_count}")
        logger.info(f"Other object count: {other_object_count}")
        logger.info(
            "MATTY INDEXING FRESH Finished syncing all domain object level relationships to FalkorDB"
        )

    async def index_all_custom_associations_for_organization_in_batches(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
        batch_size: int = 50,
        start_after: UUID | None = None,
    ) -> None:
        """Index all custom associations in batches for an organization."""
        logger.info(
            f"Starting batch indexing of custom associations for org {organization_id}"
        )

        # Get schema first since we need it for relationship processing
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id
        )
        self.schema_registry.load_schema(org_schema)

        relationships_to_process: list[dict[str, Any]] = []
        standard_object_count = 0
        custom_object_count = 0
        other_object_count = 0

        # Iterate through all custom associations
        for object_name in self.schema_registry.custom_associations_by_object_name:
            logger.info(f"Processing custom associations for object: {object_name}")

            relationships = self.schema_registry.get_domain_relationships(object_name)
            if not relationships:
                logger.info(
                    f"No relationships found for object: {object_name}, skipping"
                )
                continue

            first_relationship = relationships[0]
            node_label = object_name

            # Handle object type
            if isinstance(
                first_relationship.self_object_identifier, StandardObjectIdentifier
            ):
                standard_object_count += 1
            elif isinstance(
                first_relationship.self_object_identifier, CustomObjectIdentifier
            ):
                custom_object_count += 1
                node_label = f"`{object_name}`"
            else:
                logger.warning(
                    f"Unexpected object identifier type: {type(first_relationship.self_object_identifier)}"
                )
                other_object_count += 1
                continue

            # Process outbound relationships
            for outbound_rel in self.schema_registry.get_custom_associations(
                object_name
            ):
                if not UUID(str(outbound_rel.id)):
                    logger.warning(
                        f"No valid association_id UUID found for {object_name}"
                    )
                    continue

                # Determine from_type
                if isinstance(
                    outbound_rel.self_object_identifier, StandardObjectIdentifier
                ):
                    from_type = outbound_rel.self_object_identifier.object_name
                elif isinstance(
                    outbound_rel.self_object_identifier, CustomObjectIdentifier
                ):
                    from_type = node_label
                else:
                    logger.warning("Unexpected source object identifier type")
                    continue

                # Determine to_type
                if isinstance(
                    outbound_rel.related_object_identifier, StandardObjectIdentifier
                ):
                    to_type = outbound_rel.related_object_identifier.object_name
                elif isinstance(
                    outbound_rel.related_object_identifier, CustomObjectIdentifier
                ):
                    to_node_label = str(
                        outbound_rel.related_object_identifier.object_id
                    )
                    to_type = f"`{to_node_label}`"
                else:
                    logger.warning("Unexpected target object identifier type")
                    continue

                # Get association records
                try:
                    records = await self.association_record_repository.get_records_from_org_assoc_id(
                        organization_id=organization_id,
                        association_id=UUID(str(outbound_rel.id)),
                    )

                    # Process each record
                    for record in records:
                        # Skip if using pagination and haven't reached start point
                        if start_after and str(record.source_record_id) < str(
                            start_after
                        ):
                            continue

                        relationships_to_process.append(
                            {
                                "from_type": from_type,
                                "from_id": str(record.source_record_id),
                                "to_type": to_type,
                                "to_id": str(record.target_record_id),
                                "type": str(
                                    outbound_rel.id
                                ),  # Maintain the original relationship ID
                                "properties": {},
                            }
                        )

                        # Process batch if we've reached batch_size
                        if len(relationships_to_process) >= batch_size:
                            await self.falkordb_client.sync_relationships(
                                relationships=relationships_to_process[:batch_size],
                                organization_id=organization_id,
                                schema=org_schema,
                                batch_size=batch_size,
                                is_domain_relationship=False,
                            )
                            relationships_to_process = relationships_to_process[
                                batch_size:
                            ]
                            logger.info(
                                f"Processed batch of {batch_size} relationships"
                            )

                except Exception as e:
                    logger.error(
                        f"Error processing association records: {e}", exc_info=True
                    )
                    continue

        # Process any remaining relationships
        if relationships_to_process:
            try:
                await self.falkordb_client.sync_relationships(
                    relationships=relationships_to_process,
                    organization_id=organization_id,
                    schema=org_schema,
                    batch_size=batch_size,
                    is_domain_relationship=False,
                )
                logger.info(
                    f"Processed final batch of {len(relationships_to_process)} relationships"
                )
            except Exception as e:
                logger.error(
                    f"Error during final batch custom association indexing: {e}",
                    exc_info=True,
                )
                raise

        logger.info(f"Standard object count: {standard_object_count}")
        logger.info(f"Custom object count: {custom_object_count}")
        logger.info(f"Other object count: {other_object_count}")
        logger.info("Completed batch indexing of custom associations")

    async def index_all_custom_associations_for_organization(  # noqa: C901, PLR0912, PLR0915
        self,
        organization_id: UUID,
    ) -> None:
        """Index all custom associations for an organization."""
        logger.info(
            f"MATTY INDEXING FRESH Indexing all custom associations for organization {organization_id}"
        )

        # Ensure the graph exists for this organization
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        graph = self.falkordb_client.client.select_graph(graph_name)

        logger.info(
            f"self.schema_registry.custom_associations: {self.schema_registry.custom_associations_by_object_name}"
        )

        standard_object_count = 0
        custom_object_count = 0
        other_object_count = 0
        # Iterate through all custom associations and index their outbound relationships
        for object_name in self.schema_registry.custom_associations_by_object_name:
            logger.info(f"object_name: {object_name}")
            logger.info(
                f"self.schema_registry.custom_associations[{object_name}]: {self.schema_registry.get_custom_associations(object_name)}"
            )

            # Get the first relationship to check the object type
            relationships = self.schema_registry.get_domain_relationships(object_name)
            if not relationships:
                logger.info(
                    f"No relationships found for object: {object_name}, skipping"
                )
                continue

            # Get the node_label
            first_relationship = relationships[0]
            node_label = object_name
            # Proceed if standard object, skip if custom object
            if isinstance(
                first_relationship.self_object_identifier, StandardObjectIdentifier
            ):
                logger.info("Found StandardObjectIdentifier, proceeding as expected.")
                standard_object_count += 1
            elif isinstance(
                first_relationship.self_object_identifier, CustomObjectIdentifier
            ):
                logger.info(
                    "Found CustomObjectIdentifier, wrapping object name in backticks."
                )
                custom_object_count += 1
                node_label = f"`{object_name}`"
                logger.info(f"node_label: {node_label}")
            else:
                logger.warning(
                    f"Unexpected object identifier type: {type(first_relationship.self_object_identifier)}"
                )
                other_object_count += 1
                continue

            try:
                result = await graph.query(
                    f"MATCH (n:{node_label}) RETURN n",
                )
                logger.info(f"result: {result}")
                logger.info(f"vars(result): {vars(result)}")
                logger.info(f"result.result_set: {result.result_set}")
            except Exception as e:
                logger.warning(
                    f'Unable to get result from graph.query(f"MATCH (n:{node_label}) RETURN n"). Skipping this object.'
                )
                logger.warning(f"Error: {e}")
                continue

            from_id = self._get_property_from_result(result, "id")
            if not from_id:
                logger.warning(f"No valid from_id UUID found for {object_name}")
                continue

            # Index outbound relationships
            for outbound_rel in self.schema_registry.get_custom_associations(
                object_name
            ):
                if not UUID(str(outbound_rel.id)):
                    logger.warning(
                        f"No valid association_id UUID found for {object_name}"
                    )
                    continue

                logger.info(f"outbound_rel: {outbound_rel}")
                logger.info(
                    f"outbound_rel.relationship_name: {outbound_rel.relationship_name}"
                )
                relationship_type = str(outbound_rel.id)

                if isinstance(
                    outbound_rel.self_object_identifier, StandardObjectIdentifier
                ):
                    from_type = outbound_rel.self_object_identifier.object_name
                elif isinstance(
                    outbound_rel.self_object_identifier, CustomObjectIdentifier
                ):
                    logger.info(
                        "Found CustomObjectIdentifier, setting from_type to node_label."
                    )
                    logger.info(f"node_label: {node_label}")
                    from_type = node_label
                else:
                    logger.warning(
                        f"Unexpected object identifier type: {type(outbound_rel.self_object_identifier)}"
                    )
                    continue
                # Note: from_id is already set from above

                if isinstance(
                    outbound_rel.related_object_identifier, StandardObjectIdentifier
                ):
                    to_type = outbound_rel.related_object_identifier.object_name
                elif isinstance(
                    outbound_rel.related_object_identifier, CustomObjectIdentifier
                ):
                    to_node_label = str(
                        outbound_rel.related_object_identifier.object_id
                    )
                    to_type = f"`{to_node_label}`"
                    logger.info(
                        f"Found CustomObjectIdentifier, setting to_type to {to_type}."
                    )
                else:
                    logger.warning(
                        f"Unexpected object identifier type: {type(outbound_rel.related_object_identifier)}"
                    )
                records = await self.association_record_repository.get_records_from_org_assoc_id(
                    organization_id=organization_id,
                    association_id=UUID(str(outbound_rel.id)),
                )
                logger.info(f"length of records: {len(records)}")
                try:
                    for record in records:
                        logger.info(f"from_type: {from_type}")
                        logger.info(f"from_id: {record.source_record_id}")
                        logger.info(f"to_type: {to_type}")
                        logger.info(f"to_id: {record.target_record_id}")
                        logger.info(f"relationship_type: {relationship_type}")

                        await self.create_relationship(
                            organization_id=organization_id,
                            from_type=from_type,
                            from_id=record.source_record_id,
                            to_type=to_type,
                            to_id=record.target_record_id,  # Now guaranteed to be UUID
                            relationship_type=relationship_type,
                        )
                        logger.info(
                            f"created relationship between {from_type} and {to_type}"
                        )
                except Exception as e:
                    logger.warning(
                        f"Unable to create relationship between {from_type} and {to_type}."
                    )
                    logger.warning(f"Error: {e}")
                    continue

        logger.info(f"Standard object count: {standard_object_count}")
        logger.info(f"Custom object count: {custom_object_count}")
        logger.info(f"Other object count: {other_object_count}")
        logger.info(
            "MATTY INDEXING FRESH Finished syncing all custom associations to FalkorDB"
        )

    async def index_relationships_for_organization_in_batches(
        self,
        organization_id: UUID,
        batch_size: int = 100,
        start_after: UUID | None = None,
    ) -> None:
        """Indexes only the relationships and associations for an organization in batches, assuming nodes already exist."""
        logger.info(
            f"Starting relationship-only indexing in batches for organization {organization_id}"
        )
        logger.info(f"batch_size: {batch_size}")
        logger.info(f"start_after: {start_after}")

        # Ensure the graph exists for this organization
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        self.falkordb_client.client.select_graph(graph_name)
        logger.info(
            "MATTY INDEXING FRESH. Read graph successfully. Continuing to schema retrieval."
        )

        # --- Schema retrieval (Required before relationship indexing) ---
        logger.info(f"Retrieving schema for org_id: {organization_id}")
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )
        self.schema_registry.load_schema(org_schema)
        logger.info(f"Schema loaded for org_id: {organization_id}")
        # --- End Schema retrieval ---

        # --- Relationship Indexing ---
        logger.info(
            f"Starting relationship and association indexing for org_id: {organization_id}"
        )

        # Index standard object relationships in batches
        await self.index_all_relationships_for_organization_in_batches(
            organization_id, batch_size, start_after
        )

        # Index custom object associations in batches
        await self.index_all_custom_associations_for_organization_in_batches(
            organization_id, batch_size, start_after
        )

        logger.info(
            f"Completed relationship-only indexing for organization {organization_id}"
        )

    async def index_relationships_for_organization(
        self,
        organization_id: UUID,
    ) -> None:
        """Indexes only the relationships and associations for an organization, assuming nodes already exist."""
        logger.info(
            f"Starting relationship-only indexing for organization {organization_id}"
        )

        # Ensure the graph exists for this organization
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        graph = self.falkordb_client.client.select_graph(graph_name)  # noqa F841

        # --- Schema retrieval (Required before relationship indexing) ---
        logger.info(f"Retrieving schema for org_id: {organization_id}")
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )
        self.schema_registry.load_schema(org_schema)
        logger.info(f"Schema loaded for org_id: {organization_id}")
        # --- End Schema retrieval ---

        # --- Relationship Indexing ---
        logger.info(
            f"Starting relationship and association indexing for org_id: {organization_id}"
        )

        # Index standard object relationships
        await self.index_all_relationships_for_organization_in_batches(organization_id)

        # Index custom object associations
        await self.index_all_custom_associations_for_organization(organization_id)

        logger.info(
            f"Completed relationship-only indexing for organization {organization_id}"
        )
        # --- End Relationship Indexing ---

    async def index_all_pipelines_for_organization_in_batches(
        self,
        organization_id: UUID,
        offset: int,
        batch_size: int,
    ) -> None:
        """Index a batch of pipelines using offset pagination."""
        start_time = time.time()
        pipelines = await self.pipeline_service.list_pipelines_paginated(
            organization_id=organization_id,
            include_custom_object=True,
            offset=offset,
            limit=batch_size,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to list pipelines: {end_time - start_time} seconds"
        )

        if not pipelines:
            return

        start_time = time.time()
        await self.index_pipelines(
            organization_id,
            [pipeline.id for pipeline in pipelines],
            pipelines=pipelines,
            batch_size=batch_size,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to index pipelines: {end_time - start_time} seconds"
        )

    async def index_all_pipelines_for_organization(
        self,
        organization_id: UUID,
    ) -> None:
        """Index all pipelines for an organization.

        Args:
            organization_id: The ID of the organization to index pipelines for
        """
        pipelines = await self.pipeline_service.list_pipelines(
            organization_id=organization_id,
            include_custom_object=True,
        )
        logger.info(f"MATTY INDEXING FRESH pipelines: length: {len(pipelines)}")

        # Convert pipelines to dictionaries
        pipeline_dicts = [
            self._convert_model_to_dict(pipeline) for pipeline in pipelines
        ]

        # Get org schema
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        # Index pipelines to FalkorDB
        await self.falkordb_client.sync_pipelines(
            pipelines=pipeline_dicts,
            organization_id=organization_id,
            schema=org_schema,
        )

        logger.info("MATTY INDEXING FRESH Finished syncing all pipelines to FalkorDB")
        logger.info(f"Indexed {len(pipeline_dicts)} pipelines to FalkorDB")

    async def index_pipelines(
        self,
        organization_id: UUID,
        pipeline_ids: list[UUID],
        batch_size: int = 10000,
        update_edges: bool = False,
        pipelines: list[PipelineV2]
        | None = None,  # TODO: We should standardize the placement of this arg, or do * so order doesn't matter
    ) -> None:
        """Index pipelines to FalkorDB.

        Args:
            pipeline_ids: List of pipeline IDs to index
        """
        if not pipeline_ids:
            logger.info("No pipelines to index")
            return

        logger.info(f"Indexing {len(pipeline_ids)} pipelines to FalkorDB")

        if pipelines is None:
            pipelines_from_db = await self.pipeline_service.list_pipelines(
                organization_id=organization_id,
                only_include_pipeline_ids=set(pipeline_ids),
                include_custom_object=True,
                # ensure archived pipelines are also fetched to correctly identify deletions
            )
        else:
            pipelines_from_db = pipelines

        pipeline_dicts = []
        output_pipeline_ids = set()
        if pipelines_from_db:
            for p in pipelines_from_db:
                if p.archived_at is None:  # Check if pipeline is not archived
                    pipeline_dicts.append(self._convert_model_to_dict(p))
                    output_pipeline_ids.add(p.id)

        deleted_pipeline_ids = set(pipeline_ids) - output_pipeline_ids

        if not pipeline_dicts and not deleted_pipeline_ids:
            logger.info("No active pipelines to index and no pipelines to delete.")
            return

        # Get org schema
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        if (
            update_edges and pipeline_dicts
        ):  # Only update edges if there are nodes to update/create
            graph_name = self.falkordb_client.get_graph_name(organization_id)
            graph = self.falkordb_client.client.select_graph(graph_name)
            registry = SchemaRegistry(org_schema)

            jobs = [
                self.update_complex_node_structure(
                    graph=graph,
                    node_type=PipelineV2.object_id.object_name,
                    node_id=str(pipeline_dict["id"]),
                    data=pipeline_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=True,
                )
                for pipeline_dict in pipeline_dicts
            ]
            await run_in_parallel_and_log_exceptions(
                "Error in index_pipelines during update_complex_node_structure", jobs
            )

        # Index pipelines to FalkorDB
        await self.falkordb_client.sync_pipelines(
            pipelines=pipeline_dicts if not (update_edges and pipeline_dicts) else [],
            organization_id=organization_id,
            schema=org_schema,
            batch_size=batch_size,
            deleted_pipeline_ids=deleted_pipeline_ids,
        )

        logger.info(
            f"Processed {len(pipeline_dicts)} pipelines for upsert and {len(deleted_pipeline_ids)} for deletion to FalkorDB"
        )

    async def cdc_index_pipelines_by_ids(
        self,
        organization_id: UUID,
        pipeline_ids: list[UUID],
    ) -> list[tuple[Query, Params]]:
        """Index pipelines to FalkorDB for CDC events by IDs.

        Args:
            organization_id: The ID of the organization to index pipelines for
            pipeline_ids: List of pipeline IDs to index

        Returns:
            list[tuple[Query, Params]]: List of (query, params) tuples that were generated
        """
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        graph = self.falkordb_client.client.select_graph(graph_name)

        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )
        registry = SchemaRegistry(org_schema)

        pipelines = await self.pipeline_service.list_pipelines(
            organization_id=organization_id,
            only_include_pipeline_ids=set(pipeline_ids),
            include_custom_object=True,
        )

        active_pipelines = [p for p in pipelines if p.archived_at is None]
        missing_pipeline_ids = set(pipeline_ids) - {p.id for p in active_pipelines}

        await self._delete_pipelines(graph, list(missing_pipeline_ids))
        return await self._index_pipelines(
            graph, org_schema, registry, active_pipelines
        )

    async def _delete_pipelines(
        self,
        graph: AsyncGraph,
        pipeline_ids: list[UUID],
    ) -> None:
        """Delete pipelines from FalkorDB for CDC events by IDs.

        Args:
            graph: The graph to delete the pipelines from
            pipeline_ids: List of pipeline IDs to delete
        """
        if not pipeline_ids:
            return

        logger.info(f"Processing deletion of {len(pipeline_ids)} pipelines.")
        batch_nodes_to_delete = [
            NodeTypeData(
                label=PipelineV2.object_id.object_name,
                id=str(pipeline_id),
                properties={},  # Properties not needed for delete
            )
            for pipeline_id in pipeline_ids
        ]
        try:
            await self.falkordb_client.execute_batch_node_deletes(
                graph, batch_nodes_to_delete
            )
        except Exception as e:
            logger.error(f"Failed to delete pipelines: {e}", exc_info=True)
            # Decide if to raise or continue if only deletion failed

    async def _index_pipelines(
        self,
        graph: AsyncGraph,
        org_schema: OrganizationSchemaDescriptor,
        registry: SchemaRegistry,
        pipelines: list[PipelineV2],
    ) -> list[tuple[Query, Params]]:
        """Index pipelines to FalkorDB for CDC events, skipping archived/deleted pipelines.

        Args:
            graph: The graph to index the pipelines to
            org_schema: The organization schema descriptor
            registry: The schema registry
            pipelines: The pipelines to index

        Returns:
            list[tuple[Query, Params]]: List of (query, params) tuples that were generated
        """

        active_pipelines = [p for p in pipelines if p.archived_at is None]
        archived_pipelines = [p for p in pipelines if p.archived_at is not None]

        if archived_pipelines:
            logger.warning(
                "Some pipelines are archived, they will not be indexed.",
                archived_pipelines=[p.id for p in archived_pipelines],
            )

        if not active_pipelines:
            logger.warning(
                "No active pipelines provided. Please provide a list of active pipelines to index."
            )
            return []

        # Update the pipelines
        all_queries: list[tuple[str, dict[str, Any]]] = []
        for pipeline in active_pipelines:
            pipeline_dict = self._convert_model_to_dict(pipeline)
            try:
                queries = await self.update_complex_node_structure(
                    graph=graph,
                    node_type=PipelineV2.object_id.object_name,
                    node_id=str(pipeline_dict["id"]),
                    data=pipeline_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=False,
                )
                all_queries.extend(queries)
            except Exception as e:
                logger.error(
                    f"Error generating queries for pipeline {pipeline_dict['id']}: {e!s}"
                )

        logger.info(
            f"Generated {len(all_queries)} queries for {len(active_pipelines)} pipelines"
        )

        return all_queries

    async def index_all_meetings_for_organization(
        self,
        organization_id: UUID,
    ) -> None:
        """Index all meetings for an organization.

        Args:
            organization_id: The ID of the organization to index meetings for
        """
        meetings = await self.meeting_query_service.list_meeting_v2(
            organization_id=organization_id,
            user_id=None,
            include_custom_object=True,
        )
        logger.info(f"MATTY INDEXING FRESH indexing meetings: length: {len(meetings)}")

        # Convert meetings to dictionaries
        meeting_dicts = [self._convert_model_to_dict(meeting) for meeting in meetings]

        # Index meetings to FalkorDB
        await self.falkordb_client.sync_meetings(meeting_dicts, organization_id)

        logger.info("MATTY INDEXING FRESH Finished syncing all meetings to FalkorDB")
        logger.info(f"Indexed {len(meeting_dicts)} meetings to FalkorDB")

    async def index_meetings(self, meeting_ids: list[UUID]) -> None:
        """Index meetings to FalkorDB.

        Args:
            meeting_ids: List of meeting IDs to index
        """
        if not meeting_ids:
            logger.info("No meetings to index")
            return

        logger.info(f"Indexing {len(meeting_ids)} meetings to FalkorDB")

        # Get meetings from database - use the correct method name
        meetings = await self.meeting_service.list_meetings_by_ids_untenanted(
            meeting_ids, exclude_deleted_or_archived=False
        )
        # Get organization ID from first meeting
        organization_id = meetings[0].organization_id

        # Convert meetings to dictionaries
        meeting_dicts = [self._convert_model_to_dict(meeting) for meeting in meetings]

        # Index meetings to FalkorDB
        await self.falkordb_client.sync_meetings(meeting_dicts, organization_id)

        logger.info(f"Indexed {len(meeting_dicts)} meetings to FalkorDB")

    async def index_all_contact_pipeline_roles_for_organization_in_batches(
        self,
        organization_id: UUID,
        offset: int,
        batch_size: int,
    ) -> None:
        """Index a batch of contact pipeline roles using offset pagination."""
        start_time = time.time()
        contact_pipeline_roles = (
            await self.pipeline_query_service.list_contact_pipeline_roles_paginated(
                organization_id=organization_id,
                exclude_archived=True,
                offset=offset,
                limit=batch_size,
            )
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to list contact pipeline roles: {end_time - start_time} seconds"
        )

        # Extract association IDs from the contact pipeline roles
        association_ids = [role.id for role in contact_pipeline_roles]

        # Call index_contact_pipeline_roles with the association IDs
        await self.index_contact_pipeline_roles(
            organization_id=organization_id,
            association_ids=association_ids,
            contact_pipeline_roles=contact_pipeline_roles,
        )

        logger.info(
            f"Indexed {len(association_ids)} contact pipeline roles at offset {offset}"
        )

    async def index_contact_pipeline_roles(
        self,
        organization_id: UUID,
        association_ids: list[UUID],
        contact_pipeline_roles: list[ContactPipelineRole] | None = None,
        update_edges: bool = False,
    ) -> None:
        """Index contact pipeline roles for an organization."""
        if not association_ids:
            logger.info("No contact pipeline role association IDs provided to index.")
            return

        logger.info(
            f"Indexing {len(association_ids)} contact pipeline roles for organization {organization_id}"
        )

        if contact_pipeline_roles is None:
            contact_pipeline_roles_from_db = await self.pipeline_query_service.list_contact_pipeline_roles_by_association_ids(
                organization_id=organization_id,
                association_ids=association_ids,
                # Assuming this fetches roles regardless of archived status for correct deletion handling.
            )
        else:
            contact_pipeline_roles_from_db = contact_pipeline_roles

        contact_pipeline_role_dicts = [
            self._convert_model_to_dict(role)
            for role in contact_pipeline_roles_from_db
            # Add filtering here if list_..._by_association_ids doesn't already filter archived,
            # and archived roles should not be synced with update_edges or sync_contact_pipeline_roles
            # e.g. if role.archived_at is None
        ]

        output_role_ids = {role.id for role in contact_pipeline_roles_from_db}
        deleted_contact_pipeline_role_ids = set(association_ids) - output_role_ids

        if not contact_pipeline_role_dicts and not deleted_contact_pipeline_role_ids:
            logger.info(
                "No contact pipeline roles to update/create and no roles to delete."
            )
            return

        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        if (
            update_edges and contact_pipeline_role_dicts
        ):  # Only update edges if there are nodes to process
            graph_name = self.falkordb_client.get_graph_name(organization_id)
            graph = self.falkordb_client.client.select_graph(graph_name)
            registry = SchemaRegistry(org_schema)

            jobs = [
                self.update_complex_node_structure(
                    graph=graph,
                    node_type=ContactPipelineRole.object_id.object_name,
                    node_id=str(contact_pipeline_role_dict["id"]),
                    data=contact_pipeline_role_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=True,
                )
                for contact_pipeline_role_dict in contact_pipeline_role_dicts
            ]
            await run_in_parallel_and_log_exceptions(
                "Error in index_contact_pipeline_roles during update_complex_node_structure",
                jobs,
            )

        await self.falkordb_client.sync_contact_pipeline_roles(
            contact_pipeline_roles=contact_pipeline_role_dicts
            if not (update_edges and contact_pipeline_role_dicts)
            else [],
            organization_id=organization_id,
            schema=org_schema,
            deleted_contact_pipeline_role_ids=deleted_contact_pipeline_role_ids,
        )
        logger.info(
            f"Processed {len(contact_pipeline_role_dicts)} contact pipeline roles for upsert and {len(deleted_contact_pipeline_role_ids)} for deletion."
        )

    # TODO: Can probably deprecate this
    async def index_all_contact_pipeline_roles_for_organization(
        self,
        organization_id: UUID,
        contact_id: UUID,
        pipeline_id: UUID | None = None,
    ) -> UUID | None:
        """Index all contact pipeline roles for an organization."""
        contact_pipeline_roles = (
            await self.pipeline_query_service.list_contact_pipeline_roles_by_contact_id(
                organization_id=organization_id,
                contact_id=contact_id,
                pipeline_id=pipeline_id,
            )
        )

        if not contact_pipeline_roles:
            logger.info(
                f"MATTY INDEXING FRESH No contact pipeline roles found for organization {organization_id}, contact {contact_id}, pipeline {pipeline_id}"
            )
            return None

        logger.info(
            f"MATTY INDEXING FRESH contact_pipeline_roles: {contact_pipeline_roles}"
        )

        # Convert contact pipeline roles to dictionaries
        contact_pipeline_role_dicts = [
            self._convert_model_to_dict(contact_pipeline_role)
            for contact_pipeline_role in contact_pipeline_roles
        ]

        # Get org schema
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        # Index contact account roles to FalkorDB
        await self.falkordb_client.sync_contact_pipeline_roles(
            contact_pipeline_roles=contact_pipeline_role_dicts,
            organization_id=organization_id,
            schema=org_schema,
        )
        logger.info(
            f"Indexed {len(contact_pipeline_role_dicts)} contact pipeline roles to FalkorDB"
        )

        return contact_pipeline_roles[-1].id

    async def index_all_contact_account_roles_for_organization_in_batches(
        self,
        organization_id: UUID,
        offset: int,
        batch_size: int,
    ) -> None:
        """Index a batch of contact account roles using offset pagination."""
        start_time = time.time()
        contact_account_roles = await self.contact_query_service.list_contact_account_roles_by_organization_id_in_batches(
            organization_id=organization_id,
            offset=offset,
            limit=batch_size,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to list contact account roles: {end_time - start_time} seconds"
        )

        if not contact_account_roles:
            logger.info(f"No contact account roles found at offset {offset}")
            return

        # Extract association IDs from the contact account roles
        association_ids = [role.id for role in contact_account_roles]

        # Call index_contact_account_roles with the association IDs
        await self.index_contact_account_roles(
            organization_id=organization_id,
            association_ids=association_ids,
            contact_account_roles=contact_account_roles,
        )

        logger.info(
            f"Indexed {len(association_ids)} contact account roles at offset {offset}"
        )

    async def index_contact_account_roles(
        self,
        organization_id: UUID,
        association_ids: list[UUID],
        contact_account_roles: list[ContactAccountRole] | None = None,
        update_edges: bool = False,
    ) -> None:
        """Index contact account roles for an organization."""
        if not association_ids:
            logger.info("No contact account role association IDs provided to index.")
            return

        logger.info(
            f"Indexing {len(association_ids)} contact account roles for organization {organization_id}"
        )

        if contact_account_roles is None:
            contact_account_roles_from_db = await self.contact_query_service.list_contact_account_roles_by_association_ids(
                organization_id=organization_id,
                association_ids=association_ids,
                # Assuming this fetches roles regardless of archived status for correct deletion handling.
                # If it has an exclude_archived flag, it should be effectively false here or handled appropriately.
            )
        else:
            contact_account_roles_from_db = contact_account_roles

        contact_account_role_dicts = [
            self._convert_model_to_dict(role)
            for role in contact_account_roles_from_db
            # Add filtering here if `list_contact_account_roles_by_association_ids` doesn't already filter archived,
            # and archived roles should not be synced with `update_edges` or `sync_contact_account_roles`
            # e.g. if role.archived_at is None
        ]

        output_role_ids = {role.id for role in contact_account_roles_from_db}
        deleted_contact_account_role_ids = set(association_ids) - output_role_ids

        if not contact_account_role_dicts and not deleted_contact_account_role_ids:
            logger.info(
                "No contact account roles to update/create and no roles to delete."
            )
            return

        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        if (
            update_edges and contact_account_role_dicts
        ):  # Only update edges if there are nodes to process
            graph_name = self.falkordb_client.get_graph_name(organization_id)
            graph = self.falkordb_client.client.select_graph(graph_name)
            registry = SchemaRegistry(org_schema)

            jobs = [
                self.update_complex_node_structure(
                    graph=graph,
                    node_type=ContactAccountRole.object_id.object_name,
                    node_id=str(contact_account_role_dict["id"]),
                    data=contact_account_role_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=True,
                )
                for contact_account_role_dict in contact_account_role_dicts
            ]
            await run_in_parallel_and_log_exceptions(
                "Error in index_contact_account_roles during update_complex_node_structure",
                jobs,
            )

        await self.falkordb_client.sync_contact_account_roles(
            contact_account_roles=contact_account_role_dicts
            if not (update_edges and contact_account_role_dicts)
            else [],
            organization_id=organization_id,
            schema=org_schema,
            deleted_contact_account_role_ids=deleted_contact_account_role_ids,
        )
        logger.info(
            f"Processed {len(contact_account_role_dicts)} contact account roles for upsert and {len(deleted_contact_account_role_ids)} for deletion."
        )

    # TODO: Can probably deprecate this
    async def index_all_contact_account_roles_for_organization(
        self,
        organization_id: UUID,
        contact_ids: set[UUID],
    ) -> UUID | None:
        """Index all contact account roles for an organization."""
        contact_account_roles = (
            await self.contact_query_service.list_contact_account_roles_by_contact_id(
                organization_id=organization_id,
                contact_ids=contact_ids,
            )
        )
        if not contact_account_roles:
            logger.info(
                f"MATTY INDEXING FRESH No contact account roles found for organization {organization_id}, contact {contact_ids}"
            )
            return None
        logger.info(
            f"MATTY INDEXING FRESH contact_account_roles length: {len(contact_account_roles)}"
        )

        # Convert contact account roles to dictionaries
        contact_account_role_dicts = [
            self._convert_model_to_dict(contact_account_role)
            for contact_account_role in contact_account_roles
        ]

        # Get org schema
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        # Index contact account roles to FalkorDB
        await self.falkordb_client.sync_contact_account_roles(
            contact_account_roles=contact_account_role_dicts,
            organization_id=organization_id,
            schema=org_schema,
        )
        logger.info(
            f"Indexed {len(contact_account_role_dicts)} contact account roles to FalkorDB"
        )

        return contact_account_roles[-1].id

    async def index_all_contacts_for_organization_in_batches(
        self,
        organization_id: UUID,
        offset: int,
        batch_size: int,
    ) -> None:
        """Index a batch of contacts using offset pagination."""
        start_time = time.time()
        contacts = await self.contact_service.list_contacts_v2_paginated(
            organization_id=organization_id,
            include_custom_object=True,
            offset=offset,
            limit=batch_size,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to list contacts: {end_time - start_time} seconds"
        )
        if not contacts:
            return
        start_time = time.time()
        await self.index_contacts(
            organization_id,
            [contact.id for contact in contacts],
            contacts=contacts,
            batch_size=batch_size,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to index contacts: {end_time - start_time} seconds"
        )

    async def index_all_contacts_for_organization(
        self,
        organization_id: UUID,
    ) -> None:
        """
        Index all contacts for an organization.

        Args:
            organization_id: The ID of the organization to index contacts for
        """

        try:
            contacts = await self.contact_service.list_contacts_v2(
                organization_id=organization_id,
                include_custom_object=True,
            )
        except IllegalStateError as e:
            logger.exception(
                f"IllegalStateError encountered while listing contacts for org {organization_id}. Skipping contact indexing.",
                exc_info=e,
            )
            return  # Skip indexing contacts for this org if state is illegal
        except Exception as e:
            logger.exception(
                f"Unexpected error while listing contacts for org {organization_id}. Skipping contact indexing.",
                exc_info=e,
            )
            return  # Skip indexing for other unexpected errors too

        # Convert contacts to dictionaries
        contact_dicts = [self._convert_model_to_dict(contact) for contact in contacts]

        # Get org schema
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        # Index contacts to FalkorDB
        await self.falkordb_client.sync_contacts(
            contacts=contact_dicts,
            organization_id=organization_id,
            schema=org_schema,
        )

        logger.info(f"Indexed {len(contact_dicts)} contacts to FalkorDB")

    async def index_contacts(
        self,
        organization_id: UUID,
        contact_ids: list[UUID],
        contacts: list[ContactV2] | None = None,
        batch_size: int = 10000,
        update_edges: bool = False,
    ) -> None:
        """Index contacts to FalkorDB.

        Args:
            contact_ids: List of contact IDs to index
        """
        if not contact_ids:
            logger.info("No contacts to index")
            return

        logger.info(f"Indexing {len(contact_ids)} contacts to FalkorDB")

        if contacts is None:
            contacts_from_db = await self.contact_service.list_contacts_v2(
                organization_id=organization_id,
                only_include_contact_ids=set(contact_ids),
                include_custom_object=True,
                # ensure archived contacts are also fetched to correctly identify deletions
            )
        else:
            contacts_from_db = contacts

        contact_dicts = []
        output_contact_ids = set()
        if contacts_from_db:
            for c in contacts_from_db:
                if c.archived_at is None:  # Filter for active contacts
                    contact_dicts.append(self._convert_model_to_dict(c))
                    output_contact_ids.add(c.id)

        deleted_contact_ids = set(contact_ids) - output_contact_ids

        if not contact_dicts and not deleted_contact_ids:
            logger.info("No active contacts to index and no contacts to delete.")
            return

        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        if (
            update_edges and contact_dicts
        ):  # Only update edges if there are nodes to update/create
            graph_name = self.falkordb_client.get_graph_name(organization_id)
            graph = self.falkordb_client.client.select_graph(graph_name)
            registry = SchemaRegistry(org_schema)

            jobs = [
                self.update_complex_node_structure(
                    graph=graph,
                    node_type=ContactV2.object_id.object_name,
                    node_id=str(contact_dict["id"]),
                    data=contact_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=True,
                )
                for contact_dict in contact_dicts
            ]
            await run_in_parallel_and_log_exceptions(
                "Error in index_contacts during update_complex_node_structure", jobs
            )
        # Index contacts to FalkorDB (upsert active, delete removed ones)
        await self.falkordb_client.sync_contacts(
            contacts=contact_dicts if not (update_edges and contact_dicts) else [],
            organization_id=organization_id,
            schema=org_schema,
            batch_size=batch_size,
            deleted_contact_ids=deleted_contact_ids,
        )

        logger.info(
            f"Processed {len(contact_dicts)} contacts for upsert and {len(deleted_contact_ids)} for deletion to FalkorDB"
        )

    async def cdc_index_contacts_by_ids(
        self,
        organization_id: UUID,
        contact_ids: list[UUID],
    ) -> list[tuple[Query, Params]]:
        """Index contacts to FalkorDB for CDC events by IDs.

        Args:
            organization_id: The ID of the organization to index contacts for
            contact_ids: List of contact IDs to index

        Returns:
            list[tuple[Query, Params]]: List of (query, params) tuples that were generated
        """
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        graph = self.falkordb_client.client.select_graph(graph_name)

        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )
        registry = SchemaRegistry(org_schema)

        contacts = await self.contact_service.list_contacts_v2(
            organization_id=organization_id,
            only_include_contact_ids=set(contact_ids),
            include_custom_object=True,
        )

        active_contacts = [c for c in contacts if c.archived_at is None]
        missing_contact_ids = set(contact_ids) - {c.id for c in active_contacts}

        await self._delete_contacts(graph, list(missing_contact_ids))
        return await self._index_contacts(graph, org_schema, registry, active_contacts)

    async def _delete_contacts(
        self,
        graph: AsyncGraph,
        contact_ids: list[UUID],
    ) -> None:
        """Delete contacts from FalkorDB for CDC events by IDs.

        Args:
            graph: The graph to delete the contacts from
            contact_ids: List of contact IDs to delete
        """
        if not contact_ids:
            return

        logger.info(f"Processing deletion of {len(contact_ids)} contacts.")
        batch_nodes_to_delete = [
            NodeTypeData(
                label=ContactV2.object_id.object_name,
                id=str(contact_id),
                properties={},  # Properties not needed for delete
            )
            for contact_id in contact_ids
        ]
        try:
            await self.falkordb_client.execute_batch_node_deletes(
                graph, batch_nodes_to_delete
            )
        except Exception as e:
            logger.error(f"Failed to delete contacts: {e}", exc_info=True)
            # Decide if to raise or continue if only deletion failed

    async def _index_contacts(
        self,
        graph: AsyncGraph,
        org_schema: OrganizationSchemaDescriptor,
        registry: SchemaRegistry,
        contacts: list[ContactV2],
    ) -> list[tuple[Query, Params]]:
        """Index contacts to FalkorDB for CDC events, skipping archived/deleted contacts.

        Args:
            graph: The graph to index the pipelines to
            org_schema: The organization schema descriptor
            registry: The schema registry
            contacts: The contacts to index

        Returns:
            list[tuple[Query, Params]]: List of (query, params) tuples that were generated
        """

        active_contacts = [c for c in contacts if c.archived_at is None]
        archived_contacts = [c for c in contacts if c.archived_at is not None]

        if archived_contacts:
            logger.warning(
                "Some contacts are archived, they will not be indexed.",
                archived_contacts=[c.id for c in archived_contacts],
            )

        if not active_contacts:
            logger.warning(
                "No active contacts provided. Please provide a list of active contacts to index."
            )
            return []

        # Update the contacts
        all_queries: list[tuple[str, dict[str, Any]]] = []
        for contact in active_contacts:
            contact_dict = self._convert_model_to_dict(contact)
            try:
                queries = await self.update_complex_node_structure(
                    graph=graph,
                    node_type=ContactV2.object_id.object_name,
                    node_id=str(contact_dict["id"]),
                    data=contact_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=False,
                )
                all_queries.extend(queries)
            except Exception as e:
                logger.error(
                    f"Error generating queries for contact {contact_dict['id']}: {e!s}"
                )

        logger.info(
            f"Generated {len(all_queries)} queries for {len(active_contacts)} contacts"
        )

        return all_queries

    async def index_all_custom_objects_for_organization_in_batches(
        self,
        organization_id: UUID,
        offset: int,
        batch_size: int,
    ) -> None:
        """Index a batch of custom objects using offset pagination."""
        start_time = time.time()
        objects = await self.custom_object_service.list_custom_object_data_dto_by_organization_id(
            organization_id=organization_id,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to list custom objects: {end_time - start_time} seconds"
        )
        if not objects:
            return
        start_time = time.time()

        # Apply offset/limit in memory since the service doesn't support pagination
        batch_objects = objects[offset : offset + batch_size]
        if not batch_objects:
            return

        start_time = time.time()
        await self.index_custom_objects(
            organization_id=organization_id,
            cobject_data_ids=[obj.custom_object_data.id for obj in batch_objects],
            batch_size=batch_size,
        )
        end_time = time.time()
        logger.info(
            f"MATTY INDEXING FRESH Time taken to index custom objects: {end_time - start_time} seconds"
        )

    async def index_all_custom_objects_for_organization(
        self,
        organization_id: UUID,
    ) -> None:
        """Index all custom objects for an organization.
        Args:
            organization_id: The ID of the organization to index custom objects for
        """

        custom_object_data_dto_list = await self.custom_object_service.list_custom_object_data_dto_by_organization_id(
            organization_id=organization_id,
        )
        logger.info(
            f"MATTY INDEXING FRESH custom_object_data_dto_list: {custom_object_data_dto_list}"
        )
        # Get organization ID from first custom object data
        if not custom_object_data_dto_list:
            logger.info("Found 0 custom object data to index")
            return

        # Get org schema
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        # Convert custom object data to dictionaries
        custom_object_dicts = []
        for custom_object_data_dto in custom_object_data_dto_list:
            logger.info(f"custom_object_data_dto: {custom_object_data_dto}")
            logger.info(f"vars(custom_object_data_dto): {vars(custom_object_data_dto)}")

            # Get custom object definition from the DTO
            custom_object = custom_object_data_dto.custom_object_dto.custom_object
            if not custom_object:
                logger.warning(
                    f"Custom object definition not found for custom object data {custom_object_data_dto.custom_object_data.id}",
                    custom_object_definition_id=custom_object_data_dto.custom_object_data.cobject_metadata_id,
                )
                continue

            # Process field values
            processed_field_values = {}
            for field in custom_object_data_dto.custom_object_dto.custom_fields:
                if field.deleted_at:
                    # Skip deleted fields
                    continue

                field_data = custom_object_data_dto.custom_object_data.get_custom_field_data_from_value_slot(
                    slot_number=field.slot_number
                )

                if field_data and (
                    field_value := field_data.value_by_field_id.get(field.id)
                ):
                    value = field_value.to_generic_value()

                    # Process field value based on field type
                    processed_value = self._process_custom_object_field_value(
                        field.field_type, value
                    )
                    processed_field_values[str(field.id)] = processed_value

            # Create custom object dictionary
            custom_object_dict: dict[str, Any] = {
                "id": str(custom_object_data_dto.custom_object_data.id),
                "organization_id": str(organization_id),
                "cobject_metadata_id": str(
                    custom_object_data_dto.custom_object_data.cobject_metadata_id
                ),
                "object_display_name": custom_object.object_display_name,
                "field_values": processed_field_values,
                "created_at": custom_object_data_dto.custom_object_data.created_at
                if custom_object_data_dto.custom_object_data.created_at
                else None,
                "updated_at": custom_object_data_dto.custom_object_data.updated_at
                if custom_object_data_dto.custom_object_data.updated_at
                else None,
                "display_name": custom_object_data_dto.custom_object_data.display_name,
            }
            custom_object_dicts.append(custom_object_dict)

        if not custom_object_dicts:
            logger.info("No custom objects to index")
            return

        # Index custom objects to FalkorDB
        await self.falkordb_client.sync_custom_objects(
            custom_objects=custom_object_dicts,
            organization_id=organization_id,
            schema=org_schema,
        )

        logger.info(f"Indexed {len(custom_object_dicts)} custom objects to FalkorDB")

    async def index_custom_objects(
        self,
        organization_id: UUID,
        cobject_data_ids: list[UUID],
        batch_size: int = 10000,
        update_edges: bool = False,
    ) -> None:
        """Index custom objects to FalkorDB.

        Args:
            cobject_data_ids: List of custom object data IDs to index
        """
        if not cobject_data_ids:
            logger.info("No custom objects to index")
            return
        logger.info(f"cobject_data_ids: {cobject_data_ids}")

        logger.info(
            "Retrieving custom object data to index",
            number_of_custom_object_data_ids=len(cobject_data_ids),
        )

        # Get custom object data from database - use the correct method name
        custom_object_data_dto_list = await self.custom_object_service.list_custom_object_data_dto_by_organization_id(
            organization_id=organization_id,
            custom_object_data_ids=cobject_data_ids,
        )
        if not custom_object_data_dto_list:
            logger.info("Found 0 custom object data to index")
            return

        # Get organization ID from first custom object data
        organization_id = custom_object_data_dto_list[
            0
        ].custom_object_data.organization_id

        # Get org schema
        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        # Convert custom object data to dictionaries
        custom_object_dicts = []
        for custom_object_data_dto in custom_object_data_dto_list:
            # Get custom object definition from the DTO
            custom_object = custom_object_data_dto.custom_object_dto.custom_object
            if not custom_object:
                logger.warning(
                    f"Custom object definition not found for custom object data {custom_object_data_dto.custom_object_data.id}",
                    custom_object_definition_id=custom_object_data_dto.custom_object_data.cobject_metadata_id,
                )
                continue

            # Process field values
            processed_field_values = {}
            for field in custom_object_data_dto.custom_object_dto.custom_fields:
                if field.deleted_at:
                    # Skip deleted fields
                    continue

                field_data = custom_object_data_dto.custom_object_data.get_custom_field_data_from_value_slot(
                    slot_number=field.slot_number
                )

                if field_data and (
                    field_value := field_data.value_by_field_id.get(field.id)
                ):
                    value = field_value.to_generic_value()

                    # Process field value based on field type
                    processed_value = self._process_custom_object_field_value(
                        field.field_type, value
                    )
                    processed_field_values[str(field.id)] = processed_value

            # Create custom object dictionary
            custom_object_dict: dict[str, Any] = {
                "id": str(custom_object_data_dto.custom_object_data.id),
                "organization_id": str(organization_id),
                "cobject_metadata_id": str(
                    custom_object_data_dto.custom_object_data.cobject_metadata_id
                ),
                "object_display_name": custom_object.object_display_name,
                "field_values": processed_field_values,
                "created_at": custom_object_data_dto.custom_object_data.created_at
                if custom_object_data_dto.custom_object_data.created_at
                else None,
                "updated_at": custom_object_data_dto.custom_object_data.updated_at
                if custom_object_data_dto.custom_object_data.updated_at
                else None,
                "created_by_user_id": custom_object_data_dto.custom_object_data.created_by_user_id
                if custom_object_data_dto.custom_object_data.created_by_user_id
                else None,
                "updated_by_user_id": custom_object_data_dto.custom_object_data.updated_by_user_id
                if custom_object_data_dto.custom_object_data.updated_by_user_id
                else None,
            }
            custom_object_dicts.append(custom_object_dict)

        if not custom_object_dicts:
            logger.info("No custom objects to index")
            return

        # Index custom objects to FalkorDB
        await self.falkordb_client.sync_custom_objects(
            custom_objects=custom_object_dicts,
            organization_id=organization_id,
            schema=org_schema,
            batch_size=batch_size,
        )

        logger.info(f"Indexed {len(custom_object_dicts)} custom objects to FalkorDB")

        if update_edges:
            graph_name = self.falkordb_client.get_graph_name(organization_id)
            graph = self.falkordb_client.client.select_graph(graph_name)
            registry = SchemaRegistry(org_schema)

            jobs = [
                self.update_complex_node_structure(
                    graph=graph,
                    node_type=custom_object_dict["cobject_metadata_id"],
                    node_id=custom_object_dict["id"],
                    data=custom_object_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=True,
                )
                for custom_object_dict in custom_object_dicts
            ]
            await run_in_parallel_and_log_exceptions(
                "Error in index_custom_objects during update_complex_node_structure",
                jobs,
            )

    async def index_users(
        self,
        user_ids: list[UUID],
        organization_id: UUID,
        update_edges: bool = False,
        batch_size: int = 10000,
    ) -> None:
        """Index users to FalkorDB.

        Args:
            user_ids: List of user IDs to index
            organization_id: The organization ID these users belong to
            update_edges: Whether to update complex node structures (edges)
            batch_size: Size of batches for syncing to FalkorDB
        """
        if not user_ids:
            logger.info("No users to index")
            return

        logger.info(
            f"Indexing {len(user_ids)} users for organization {organization_id} to FalkorDB"
        )

        users_from_db = await self.user_service.list_users_v2(
            organization_id=organization_id,
            only_include_user_ids=set(user_ids),
            active_users_only=False,  # Fetch all to correctly identify deleted/inactive
        )

        user_dicts = []
        output_user_ids = set()
        if users_from_db:
            for u in users_from_db:
                if (
                    u.organization_association_status
                    == UserOrganizationAssociationStatus.ACTIVE
                ):  # Filter for active users
                    user_dicts.append(self._convert_model_to_dict(u))
                    output_user_ids.add(u.id)

        deleted_user_ids = set(user_ids) - output_user_ids

        if not user_dicts and not deleted_user_ids:
            logger.info("No active users to index and no users to delete.")
            return

        org_schema = await self.metadata_service.get_organization_domain_object_schema(
            organization_id=organization_id,
        )

        # Only update edges if there are nodes to update/create
        if update_edges and user_dicts:
            graph_name = self.falkordb_client.get_graph_name(organization_id)
            graph = self.falkordb_client.client.select_graph(graph_name)
            registry = SchemaRegistry(org_schema)

            jobs = [
                self.update_complex_node_structure(
                    graph=graph,
                    node_type=OrganizationUserV2.object_id.object_name,
                    node_id=str(user_dict["id"]),
                    data=user_dict,
                    schema=org_schema,
                    registry=registry,
                    execute_queries=True,
                )
                for user_dict in user_dicts
            ]
            await run_in_parallel_and_log_exceptions(
                "Error in index_users during update_complex_node_structure", jobs
            )

        # Index users to FalkorDB (upsert active, delete removed ones)
        await self.falkordb_client.sync_users(
            users=user_dicts if not (update_edges and user_dicts) else [],
            organization_id=organization_id,
            schema=org_schema,
            batch_size=batch_size,
            deleted_user_ids=deleted_user_ids,
        )

        logger.info(
            f"Processed {len(user_dicts)} users for upsert and {len(deleted_user_ids)} for deletion to FalkorDB"
        )

    async def create_relationship(
        self,
        organization_id: UUID,
        from_type: str,
        from_id: UUID,
        to_type: str,
        to_id: UUID,
        relationship_type: str,
        from_id_field_name: str = "id",
        to_id_field_name: str = "id",
        properties: dict[str, Any] | None = None,
    ) -> None:
        """Create a relationship between two entities in FalkorDB.

        Args:
            organization_id: Organization ID
            from_type: Source entity type
            from_id: Source entity ID
            to_type: Target entity type
            to_id: Target entity ID
            relationship_type: Type of relationship
            from_id_field_name: Name of the field to use as the source entity ID
            to_id_field_name: Name of the field to use as the target entity ID
            properties: Optional relationship properties
        """
        await self.falkordb_client.create_relationship_between_entities(
            organization_id,
            from_type,
            from_id,
            to_type,
            to_id,
            relationship_type,
            from_id_field_name,
            to_id_field_name,
            properties,
        )

    async def delete_relationship(
        self,
        organization_id: UUID,
        from_type: str,
        from_id: UUID,
        to_type: str,
        to_id: UUID,
        relationship_type: str,
    ) -> None:
        """Delete a relationship between two entities in FalkorDB."""
        await self.falkordb_client.delete_relationship_between_entities(
            organization_id,
            from_type,
            from_id,
            to_type,
            to_id,
            relationship_type,
        )

    def _process_custom_object_field_value(  # noqa: C901, PLR0911, PLR0912
        self,
        field_type: FieldType,
        value: Any,
    ) -> Any:
        from salestech_be.common.type.metadata.field.field_type import (
            FieldType,
            is_custom_field_type,
        )

        """Process a custom object field value based on its type.

        Args:
            field_type: Field type
            value: Field value

        Returns:
            Processed field value
        """
        if value is None:
            return None

        # Handle different field types using the correct enum values
        if field_type == FieldType.TEXT:
            return str(value)
        elif field_type in (FieldType.NUMERIC, FieldType.CURRENCY, FieldType.PERCENT):
            return float(value)
        elif field_type == FieldType.BOOLEAN_CHECKBOX:
            return bool(value)
        elif field_type in (
            FieldType.TIMESTAMP,
            FieldType.LOCAL_TIME_OF_DAY,
            FieldType.TIME_OF_DAY,
            FieldType.LOCAL_DATE,
        ):
            if isinstance(value, datetime):
                return value.isoformat()
            return value
        elif (
            field_type == FieldType.EMAIL
            or field_type in (FieldType.PHONE_NUMBER, FieldType.URL)
            or field_type == FieldType.SINGLE_SELECT
        ):
            return str(value)
        elif field_type == FieldType.MULTI_SELECT:
            if isinstance(value, list):
                return [str(item) for item in value]
            return [str(value)]
        elif field_type == FieldType.UUID:
            if isinstance(value, UUID):
                return str(value)
            return value
        elif is_custom_field_type(field_type):
            # Handle custom field types
            return str(value)
        else:
            # Default handling for unknown field types
            return str(value)

    """ Postgres counts """

    async def get_accounts_count(self, organization_id: UUID) -> int:
        """Get total count of accounts for an organization."""
        logger.info("MATTY ACCOUNTS FRESH. Trying to get accounts count")
        count = (
            await self.account_query_service.get_count_of_all_accounts_in_organization(
                organization_id=organization_id,
                exclude_archived=True,
            )
        )
        logger.info(f"MATTY ACCOUNTS FRESH. Got accounts count: {count}")
        return count

    async def get_contacts_count(self, organization_id: UUID) -> int:
        """Get total count of contacts for an organization."""
        logger.info("MATTY CONTACTS FRESH. Trying to get contacts count")
        count = (
            await self.contact_query_service.get_count_of_all_contacts_in_organization(
                organization_id=organization_id,
                exclude_archived=True,
            )
        )
        logger.info(f"MATTY CONTACTS FRESH. Got contacts count: {count}")
        return count

    async def get_pipelines_count(self, organization_id: UUID) -> int:
        """Get total count of pipelines for an organization."""
        logger.info("MATTY PIPELINE FRESH. Trying to get pipelines count")
        count = await self.pipeline_query_service.get_count_of_all_pipelines_in_organization(
            organization_id=organization_id,
            exclude_archived=True,
        )
        logger.info(f"MATTY PIPELINE FRESH. Got pipelines count: {count}")
        return count

    async def get_contact_pipeline_roles_count(self, organization_id: UUID) -> int:
        """Get total count of contact pipeline roles for an organization."""
        logger.info(
            "MATTY CONTACT PIPELINE ROLES FRESH. Trying to get contact pipeline roles count"
        )
        count = await self.pipeline_query_service.get_count_of_all_contact_pipeline_associations_in_organization(
            organization_id=organization_id,
            exclude_archived=True,
        )
        logger.info(
            f"MATTY CONTACT PIPELINE ROLES FRESH. Got contact pipeline roles count: {count}"
        )
        return count

    async def get_contact_account_roles_count(self, organization_id: UUID) -> int:
        """Get total count of contact account roles for an organization."""
        logger.info(
            "MATTY CONTACT ACCOUNT ROLES FRESH. Trying to get contact account roles count"
        )
        count = await self.contact_query_service.get_count_of_all_contact_account_associations_in_organization(
            organization_id=organization_id,
            exclude_archived=True,
        )
        logger.info(
            f"MATTY CONTACT ACCOUNT ROLES FRESH. Got contact account roles count: {count}"
        )
        return count

    # TODO: implement "get_count_of_all_custom_objects_in_organization"
    async def get_custom_objects_count(self, organization_id: UUID) -> int:
        """Get total count of custom objects for an organization."""
        objects = await self.custom_object_service.list_custom_object_data_dto_by_organization_id(
            organization_id=organization_id,
        )
        return len(objects)

    """ End of Postgres counts """

    """ FalkorDB counts """

    async def get_domain_object_count_falkor(
        self,
        organization_id: UUID,
        domain_object: type[CustomizableDomainModel] | type[DomainModel],
    ) -> int:
        """Get total count for a domain object from an organization in Falkor."""

        # Ensure the graph exists
        await self.falkordb_client.ensure_graph_exists(organization_id)
        graph_name = self.falkordb_client.get_graph_name(organization_id)
        graph = self.falkordb_client.client.select_graph(graph_name)
        # Query for the domain object
        if issubclass(domain_object, CustomizableDomainModel) or issubclass(
            domain_object, DomainModel
        ):
            result = await graph.query(
                f"MATCH (n:{domain_object.object_id.object_name}) RETURN COUNT(n)",
            )

            return int(result.result_set[0][0])
        else:
            raise ValueError(f"Invalid domain object type: {type(domain_object)}")

    """ End of Falkor counts """

    async def update_relationship(
        self,
        *,
        organization_id: UUID,
        relationship_type: str,
        old_relationship: RelationshipDef | None = None,
        new_relationship: RelationshipDef | None = None,
        properties: dict[str, Any] | None = None,
    ) -> None:
        """Update a relationship between two entities in FalkorDB.

        Args:
            organization_id: Organization ID
            relationship_type: Type of relationship
            old_relationship: Current relationship definition to delete
            new_relationship: New relationship definition to create
            properties: Optional properties to update on the relationship

        Raises:
            ValueError: If an invalid combination of relationships is provided
        """

        # Validate relationship combinations
        if not (old_relationship or new_relationship):
            logger.error("Must provide either old_relationship or new_relationship")
            raise ValueError("Must provide either old_relationship or new_relationship")

        if old_relationship and not new_relationship:
            # Delete operation
            logger.info("Deleting relationship")
        elif not old_relationship and new_relationship:
            # Create operation
            logger.info("Creating relationship")
        elif old_relationship and new_relationship:
            # Update operation
            logger.info("Updating relationship")
        else:
            raise ValueError("Invalid combination of relationships provided")

        if (
            old_relationship
            and isinstance(old_relationship.from_id, UUID)
            and isinstance(old_relationship.to_id, UUID)
        ):
            # First delete the old relationship
            await self.delete_relationship(
                organization_id=organization_id,
                from_type=old_relationship.from_type,
                from_id=old_relationship.from_id,
                to_type=old_relationship.to_type,
                to_id=old_relationship.to_id,
                relationship_type=relationship_type,
            )

        if (
            new_relationship
            and isinstance(new_relationship.from_id, UUID)
            and isinstance(new_relationship.to_id, UUID)
        ):
            # Then create the new relationship
            await self.create_relationship(
                organization_id=organization_id,
                from_type=new_relationship.from_type,
                from_id=new_relationship.from_id,
                to_type=new_relationship.to_type,
                to_id=new_relationship.to_id,
                relationship_type=relationship_type,
                from_id_field_name=new_relationship.from_id_field_name,
                to_id_field_name=new_relationship.to_id_field_name,
                properties=properties,
            )

    async def update_complex_node_structure(  # noqa: C901, PLR0912, PLR0915
        self,
        graph: AsyncGraph | None,
        node_type: str,
        node_id: str,
        data: dict[str, Any],
        schema: OrganizationSchemaDescriptor,
        registry: SchemaRegistry,
        execute_queries: bool = True,
    ) -> list[tuple[Query, Params]]:
        """Updates a complex node structure following these steps:
        1. Merge core node
        2. Delete existing relationships to nested structures
        3. Delete existing outbound structures
        4. Merge nested structures
        5. Create nested structure relationships
        6. Create outbound domain relationships
        """
        if not schema:
            raise ValueError(
                "Schema is required for schema-driven node structure update"
            )

        source_descriptor = registry.get_object_descriptor(node_type)
        all_queries: list[tuple[str, dict[str, Any]]] = []

        if not source_descriptor:
            logger.warning(f"No schema descriptor found for type {node_type}")
            return []

        # 0. Identify nested and domain relationship types
        nested_rel_types: list[tuple[str, str]] = []
        custom_fields: list[str] = []
        primitive_fields: list[str] = []
        for field in source_descriptor.fields:
            field_name = registry.extract_field_name(field.field_identifier)

            # Get relationship type for the field
            relationship_type = registry.get_field_relationship_type(
                source_descriptor, field_name
            )

            if relationship_type == "nested_structure":
                field_info = registry.analyze_field_type(source_descriptor, field)
                if field_info.nested_descriptor is None:
                    logger.warning(
                        f"Skipping field {field_name} - no nested descriptor found"
                    )
                    continue
                target_type = registry.get_object_name(field_info.nested_descriptor)

                nested_structure_data = (field_name, target_type)
                nested_rel_types.append(nested_structure_data)
            elif relationship_type == "custom_field":
                custom_fields.append(field_name)
            elif relationship_type == "primitive":
                primitive_fields.append(field_name)
            else:
                primitive_fields.append(field_name)

        outbound_domain_object_relationships = registry.get_domain_relationships(
            node_type
        )
        outbound_domain_object_rel_types: list[str] = []
        for outbound_rel in outbound_domain_object_relationships:
            outbound_domain_object_rel_types.append(str(outbound_rel.id))

        inbound_domain_object_relationships = registry.get_inbound_domain_relationships(
            node_type
        )
        inbound_domain_object_rel_types: list[str] = []
        for inbound_rel in inbound_domain_object_relationships:
            inbound_domain_object_rel_types.append(str(inbound_rel.id))

        # 1. Merge core node with primitive fields and custom fields
        primitive_props = {k: v for k, v in data.items() if k in primitive_fields}

        # Handle custom fields from the nested custom_field_data dictionary
        custom_props = {}
        if "custom_field_data" in data and data["custom_field_data"] is not None:
            custom_props = {
                f"`{k!s}`": v
                for k, v in data[
                    "custom_field_data"
                ].items()  # Use backticks to treat key as literal property name
                if str(k) in custom_fields  # Convert UUID to string for comparison
            }

        core_node_props = {**primitive_props, **custom_props}

        query = f"""MERGE (n:{node_type} {{id: $node_id}})
                SET n = $properties"""
        params = {"node_id": node_id, "properties": core_node_props}
        all_queries.append((query, params))

        # 2. Delete existing relationships to nested structures
        if nested_rel_types:
            query = f"""MATCH (n:{node_type} {{id: $node_id}})-[r]->()
                    WHERE type(r) IN $nested_rel_types
                    DELETE r"""
            params = {
                "node_id": node_id,
                "nested_rel_types": [t[0] for t in nested_rel_types],
            }
            all_queries.append((query, params))

        # 3. Delete existing outbound domain relationships and inbound domain relationships
        if outbound_domain_object_rel_types:
            query = f"""MATCH (n:{node_type} {{id: $node_id}})-[r]->(m)
                    WHERE type(r) IN $outbound_domain_object_rel_types
                    DELETE r"""
            params = {
                "node_id": node_id,
                "outbound_domain_object_rel_types": outbound_domain_object_rel_types,
            }
            all_queries.append((query, params))
        if inbound_domain_object_rel_types:
            query = f"""MATCH (n:{node_type} {{id: $node_id}})<-[r]-(m)
                    WHERE type(r) IN $inbound_domain_object_rel_types
                    DELETE r"""
            params = {
                "node_id": node_id,
                "inbound_domain_object_rel_types": inbound_domain_object_rel_types,
            }
            all_queries.append((query, params))

        # 4. Process fields according to schema definition
        if source_descriptor:
            # Process fields according to schema definition
            node_queries, edge_queries = await _process_fields_by_schema(
                graph=graph,
                node_type=node_type,
                node_id=node_id,
                data=data,
                primitive_props=core_node_props,
                schema=schema,
                descriptor=source_descriptor,
                registry=registry,
                execute_queries=execute_queries,
            )
            all_queries.extend(node_queries)
            all_queries.extend(edge_queries)
        else:
            logger.warning(
                f"No schema descriptor found for type {node_type}. Node will be created with primitive properties only."
            )

        # 5. Execute if requested
        organization_id = schema.organization_id
        if execute_queries and graph:
            if not all_queries:
                logger.info(f"No queries generated for update of {node_type}:{node_id}")
                return []

            for i, (query, params) in enumerate(all_queries):
                try:
                    sanitized_params = {
                        k: _convert_param_value(v) for k, v in params.items()
                    }
                    await graph.query(query, params=sanitized_params)
                except Exception as e:
                    logger.error(
                        f"Failed to execute query {i + 1}/{len(all_queries)}: {query}. "
                        f"Exception: {e}"
                    )
                    raise

            # 6: Index domain object relationships
            # Load schema into schema registry
            self.schema_registry.load_schema(schema)

            # Create relationships for outbound domain object relationships and inbound domain object relationships
            await self.index_relationships_for_object_name_for_organization(
                object_name=node_type,
                object_id=node_id,
                organization_id=organization_id,
            )

            return []
        else:
            return all_queries  # Return queries if not executing


from salestech_be.db.dbengine.core import DatabaseEngine  # noqa: E402
from salestech_be.falkordb.falkordb_client import (  # noqa: E402
    FalkorDBClient,
)
from salestech_be.ree_logging import get_logger  # noqa: E402


def get_falkordb_indexing_lib(
    db_engine: DatabaseEngine,
    falkordb_client: FalkorDBClient | None = None,
) -> FalkorDBIndexingLib:
    """Get a FalkorDB indexing library instance.

    Args:
        db_engine: Database engine for accessing data
        falkordb_client: Optional FalkorDB client, will create one if not provided

    Returns:
        FalkorDB indexing library instance
    """
    return FalkorDBIndexingLib(db_engine, falkordb_client)
