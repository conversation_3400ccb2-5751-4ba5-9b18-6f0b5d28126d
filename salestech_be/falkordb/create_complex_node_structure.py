"""
Module for handling the creation of complex node structures with schema awareness.
This provides enhanced support for creating and querying nested object relationships
by using a schema-driven approach.
"""

import datetime
import uuid
from enum import Enum
from typing import Any

from falkordb.asyncio.graph import AsyncGraph

from salestech_be.common.type.metadata.schema import (
    ObjectDescriptor,
    OrganizationSchemaDescriptor,
)
from salestech_be.falkordb.schema_registry import (
    SchemaRegistry,
)
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)


async def create_complex_node_structure(
    graph: AsyncGraph | None,
    node_type: str,
    node_id: str,
    data: dict[str, Any],
    schema: OrganizationSchemaDescriptor,
    registry: SchemaRegistry | None = None,
    parent_node_id: str | None = None,  # TODO: What does this do?
    relation_name: str | None = None,  # TODO: What does this do?
    execute_queries: bool = True,
) -> list[tuple[str, dict[str, Any]]]:
    """
    Create a complex node structure with nested objects and relationships.
    Uses schema information to identify and index only nested fields that are
    defined in the schema. Domain relationships are NOT handled by this function.

    Args:
        graph: FalkorDB graph object (optional if execute_queries is False)
        node_type: Type/label of the node to create
        node_id: ID of the node
        data: Node data including nested objects
        schema: Schema information for relationship handling (required for schema-driven approach)
        parent_node_id: ID of the parent node (if this is a child node)
        relation_name: Name of the relation connecting to parent (if this is a child node)
        execute_queries: Whether to execute the queries (True) or just return them (False)

    Returns:
        List of (query, params) tuples if execute_queries is False, otherwise empty list
    """
    if not schema:
        raise ValueError("Schema is required for schema-driven node structure creation")
    if not data:
        logger.warning(f"Empty data provided for node {node_id} of type {node_type}")
        return []

    # Initialize schema registry
    if registry is None:
        registry = SchemaRegistry(schema)

    # Extract all properties to be stored directly on this node
    primitive_props: dict[str, Any] = {}
    node_queries: list[tuple[str, dict[str, Any]]] = []
    edge_queries: list[tuple[str, dict[str, Any]]] = []

    # Get object descriptor for this node type
    source_descriptor = registry.get_object_descriptor(node_type)

    if source_descriptor:
        # Process fields according to schema definition
        node_queries, edge_queries = await _process_fields_by_schema(
            graph=graph,
            node_type=node_type,
            node_id=node_id,
            data=data,
            primitive_props=primitive_props,
            schema=schema,
            descriptor=source_descriptor,
            registry=registry,
            execute_queries=execute_queries,
        )
    else:
        logger.warning(
            f"No schema descriptor found for type {node_type}. Node will be created with primitive properties only."
        )

    # Create the node with primitive properties
    initial_node_query = _create_node_query(
        label=node_type,
        node_id=node_id,
        properties=primitive_props,
    )
    # Prepend it to the start of the list since we execute the queries in order
    node_queries.insert(0, initial_node_query)

    # Gather the list of all queries, with node creation first, then edges
    all_queries = node_queries + edge_queries
    sanitized_all_queries: list[tuple[str, dict[str, Any]]] = []
    for query, params in all_queries:
        sanitized_params = {k: _convert_param_value(v) for k, v in params.items()}
        sanitized_all_queries.append((query, sanitized_params))
    # Execute the queries if requested
    if execute_queries and graph:
        for query, params in sanitized_all_queries:
            try:
                logger.debug("Attempting to execute FalkorDB query", query=query)
                await graph.query(query, params=params)
            except Exception as e:
                logger.exception(
                    f"Failed to execute FalkorDB query; exception: {e}",
                    query=query,
                    params=params,
                )
                raise e

    return sanitized_all_queries


def _convert_param_value(value: Any) -> Any:  # noqa: PLR0911
    """Recursively converts parameter values to FalkorDB-compatible types."""
    if isinstance(value, uuid.UUID):
        return str(value)
    if isinstance(value, datetime.datetime):
        # Convert to UTC timestamp in milliseconds
        if value.tzinfo is None:
            # Assume UTC if naive, or handle as needed
            timestamp_ms = int(value.replace(tzinfo=datetime.UTC).timestamp() * 1000)
        else:
            timestamp_ms = int(value.astimezone(datetime.UTC).timestamp() * 1000)
        return timestamp_ms
    if isinstance(value, Enum):
        return value.value
    if isinstance(value, (list, tuple, set)):
        # Convert elements within sequences
        return [_convert_param_value(item) for item in value]
    if isinstance(value, dict):
        # Convert values within dictionaries
        return {k: _convert_param_value(v) for k, v in value.items()}
    # Handle other basic types (int, float, str, bool, None) - they are generally fine
    if isinstance(value, (str, int, float, bool)) or value is None:
        return value

    # Fallback for unknown types - convert to string, might need adjustment
    # Log a warning for unexpected types
    # logger.warning(f"Unexpected parameter type encountered: {type(value)}. Converting to string.")
    return str(value)


async def _process_fields_by_schema(  # noqa: C901, PLR0912
    graph: AsyncGraph | None,
    node_type: str,
    node_id: str,
    data: dict[str, Any],
    primitive_props: dict[str, Any],
    schema: OrganizationSchemaDescriptor,
    descriptor: ObjectDescriptor,
    registry: SchemaRegistry,
    execute_queries: bool = True,
) -> tuple[list[tuple[str, dict[str, Any]]], list[tuple[str, dict[str, Any]]]]:
    """
    Process fields based on schema descriptor.
    Only processes fields that are explicitly defined in the schema descriptor.
    Nested structures are turned into separate nodes with relationships,
    but domain relationships are not handled here.

    Args:
        graph: FalkorDB graph object
        node_type: Type of the node
        node_id: ID of the node
        data: Node data
        primitive_props: Dictionary to collect primitive properties
        schema: Schema information
        descriptor: Object descriptor for the node type
        registry: Schema registry instance
        execute_queries: Whether to execute the queries or just return them

    Returns:
        Tuple of node_queries and edge_queries. Both represent list of (query, params) tuples if execute_queries is False, otherwise empty list
    """
    node_queries: list[tuple[str, dict[str, Any]]] = []
    edge_queries: list[tuple[str, dict[str, Any]]] = []

    # Create a set of field names defined in the schema
    schema_field_names = {
        registry.extract_field_name(field.field_identifier)
        for field in descriptor.fields
    }

    # Process only fields that are defined in the schema
    for field_name in schema_field_names:
        # Check if the field is a custom field
        if (
            "custom_field_data" in data
            and data["custom_field_data"] is not None
            and f"`{field_name}`" in data["custom_field_data"]
        ):
            field_value = data["custom_field_data"][f"`{field_name}`"]
        # Skip if field is not in the data
        elif field_name not in data:
            continue
        else:
            field_value = data[field_name]

        if not field_value:
            continue

        # Use SchemaRegistry to determine the relationship type
        relationship_type = registry.get_field_relationship_type(descriptor, field_name)
        # Handle based on relationship type
        if relationship_type == "domain_relationship":
            # Skip domain relationships - they should be handled separately
            continue

        if relationship_type == "nested_structure":
            # Get field descriptor to analyze its type
            field_descriptor = None
            for field in descriptor.fields:
                if registry.extract_field_name(field.field_identifier) == field_name:
                    field_descriptor = field
                    break

            if not field_descriptor:
                continue

            # Convert field_descriptor.field_identifier to its string key representation
            field_info = registry.analyze_field_type(descriptor, field_descriptor)

            # Handle nested structure (not a domain relationship)
            if (
                field_info.is_nested
                and not field_info.is_collection
                and field_info.nested_descriptor is not None
            ):
                if not field_value:
                    continue
                target_type = registry.get_object_name(field_info.nested_descriptor)
                sub_node_id = field_value.get("id", f"{node_id}_{field_name}")

                # Create the nested object node
                nested_queries = await create_complex_node_structure(
                    graph=graph,
                    node_type=target_type,
                    node_id=sub_node_id,
                    data=field_value,
                    schema=schema,
                    registry=registry,
                    parent_node_id=node_id,
                    relation_name=field_name,
                    execute_queries=execute_queries,
                )
                node_queries.extend(nested_queries)

                # Create relationship from parent to this nested structure
                rel_query = _create_relationship_query(
                    from_label=node_type,
                    from_id=node_id,
                    to_label=target_type,
                    to_id=sub_node_id,
                    relationship_type=field_name,
                )
                edge_queries.append(rel_query)
            # Handle collection of nested structures
            elif (
                field_info.is_collection
                and field_value
                and field_info.nested_descriptor is not None
            ):
                target_type = registry.get_object_name(field_info.nested_descriptor)

                for i, item in enumerate(field_value):
                    item_node_id = item.get("id", f"{node_id}_{field_name}_{i}")
                    # Create node for list item
                    item_queries = await create_complex_node_structure(
                        graph=graph,
                        node_type=target_type,
                        node_id=item_node_id,
                        data=item,
                        schema=schema,
                        registry=registry,
                        parent_node_id=node_id,
                        relation_name=field_name,
                        execute_queries=execute_queries,
                    )
                    node_queries.extend(item_queries)

                    # Create relationship from parent to this list item
                    rel_query = _create_relationship_query(
                        from_label=node_type,
                        from_id=node_id,
                        to_label=target_type,
                        to_id=item_node_id,
                        relationship_type=field_name,
                    )
                    edge_queries.append(rel_query)
            else:
                # If the field value doesn't match the expected type, store as primitive
                primitive_props[field_name] = field_value

        elif relationship_type == "primitive":
            # Store primitive fields directly
            primitive_props[field_name] = field_value
        elif relationship_type == "custom_field":
            # Store custom fields directly, wrapped in backticks
            primitive_props[f"`{field_name}`"] = field_value
        else:
            # Unknown type, store as primitive
            primitive_props[field_name] = field_value

    return (node_queries, edge_queries)


def _create_node_query(
    label: str,
    node_id: str,
    properties: dict[str, Any],
) -> tuple[str, dict[str, Any]]:
    """
    Create a Cypher query for creating or updating a node.

    Args:
        label: Node label (type)
        node_id: Node ID
        properties: Node properties

    Returns:
        Tuple of (query, params)
    """
    try:
        # This is an explicit step to ensure that the id field is set.
        properties["id"] = node_id
        # Create or merge node with properties
        query = f"""MERGE (n:{label} {{id: '{properties["id"]}'}})
                    SET n = $props
                RETURN n"""

        # Pass id separately and properties as a whole
        params: dict[str, Any] = {"props": properties}
    except Exception as e:
        logger.exception(
            f"Error creating node query; error: {e}",
            label=label,
            node_id=node_id,
            properties=properties,
        )
        raise e

    return (query, params)


def _create_relationship_query(
    from_label: str,
    from_id: str,
    to_label: str,
    to_id: str,
    relationship_type: str,
    properties: dict[str, Any] | None = None,
) -> tuple[str, dict[str, Any]]:
    """
    Create a Cypher query for creating a relationship between two nodes.

    Args:
        from_label: Source node label
        from_id: Source node ID
        to_label: Target node label
        to_id: Target node ID
        relationship_type: Type of relationship
        properties: Relationship properties

    Returns:
        Tuple of (query, params)
    """
    try:
        # Default properties to empty dict if None
        properties = properties or {}

        # Create relationship with properties
        query = f"""MATCH (a:{from_label} {{id: $from_id}}), (b:{to_label} {{id: $to_id}})
                        MERGE (a)-[r:{relationship_type}]->(b)
                        SET r = $props
                    RETURN r"""

        # Pass parameters directly
        params = {"from_id": from_id, "to_id": to_id, "props": properties}
    except Exception as e:
        logger.exception(
            f"Error creating relationship query with from_label: {from_label}, from_id: {from_id}, to_label: {to_label}, to_id: {to_id}, relationship_type: {relationship_type}, properties: {properties}. Error: {e}"
        )
        raise e

    return (query, params)


async def _create_node(
    graph: AsyncGraph,
    label: str,
    node_id: str,
    properties: dict[str, Any],
) -> None:
    """
    Create or update a node in FalkorDB.

    Args:
        graph: FalkorDB graph object
        label: Node label (type)
        node_id: Node ID
        properties: Node properties
    """
    query, params = _create_node_query(label, node_id, properties)
    await graph.query(query, params=params)


async def _create_relationship(
    graph: AsyncGraph,
    from_label: str,
    from_id: str,
    to_label: str,
    to_id: str,
    relationship_type: str,
    properties: dict[str, Any] | None = None,
) -> None:
    """
    Create a relationship between two nodes in FalkorDB.

    Args:
        graph: FalkorDB graph object
        from_label: Source node label
        from_id: Source node ID
        to_label: Target node label
        to_id: Target node ID
        relationship_type: Type of relationship
        properties: Relationship properties
    """
    query, params = _create_relationship_query(
        from_label, from_id, to_label, to_id, relationship_type, properties
    )
    await graph.query(query, params=params)
