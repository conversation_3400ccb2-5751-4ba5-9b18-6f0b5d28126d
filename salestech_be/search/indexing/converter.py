from decimal import Decimal
from typing import Any, TypeVar, assert_never, overload
from uuid import UUID

from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.transcript.types import TranscriptContainer
from salestech_be.db.models.account import Account
from salestech_be.db.models.meeting import Meeting, MeetingAttendee, MeetingInvitee
from salestech_be.db.models.pipeline import Pipeline
from salestech_be.search.common.type import DocumentType
from salestech_be.search.es.indexing.mapping.account import ESAccount
from salestech_be.search.es.indexing.mapping.common import Indexable
from salestech_be.search.es.indexing.mapping.contact import ESContact
from salestech_be.search.es.indexing.mapping.custom_object import ESCustomObjectData
from salestech_be.search.es.indexing.mapping.meeting import (
    ESMeeting,
    InnerDocMeetingAttendee,
    InnerDocMeetingInvitee,
)
from salestech_be.search.es.indexing.mapping.pipeline import ESPipeline
from salestech_be.search.indexing.transcript_embeddings import pre_processing
from salestech_be.settings import settings

T = TypeVar("T")


@overload
def convert_field_to_es(field: UUID) -> str: ...


@overload
def convert_field_to_es(field: Decimal) -> float: ...


@overload
def convert_field_to_es(field: MeetingAttendee) -> InnerDocMeetingAttendee: ...


@overload
def convert_field_to_es(field: MeetingInvitee) -> InnerDocMeetingInvitee: ...


@overload
def convert_field_to_es(
    field: list[MeetingInvitee],
) -> list[InnerDocMeetingInvitee]: ...


@overload
def convert_field_to_es(
    field: list[MeetingAttendee],
) -> list[InnerDocMeetingAttendee]: ...


@overload
def convert_field_to_es(field: T) -> T: ...


def convert_field_to_es(field: Any) -> Any:  # type: ignore[explicit-any]
    """Handles conversion of fields that are not supported by the Elasticsearch DSL."""
    match field:
        case UUID():
            return str(field)
        case Decimal():
            return float(field)
        case MeetingAttendee():
            return convert_meeting_attendee_to_es(field)
        case MeetingInvitee():
            return convert_meeting_invitee_to_es(field)
        case list():
            return [convert_field_to_es(item) for item in field]
        case _:
            # This also handles None values in optional fields
            return field


def convert_meeting_invitee_to_es(invitee: MeetingInvitee) -> InnerDocMeetingInvitee:
    return InnerDocMeetingInvitee(
        user_id=convert_field_to_es(invitee.user_id),
        contact_id=convert_field_to_es(invitee.contact_id),
        contact_email=convert_field_to_es(invitee.contact_email),
        is_organizer=convert_field_to_es(invitee.is_organizer),
        account_id=convert_field_to_es(invitee.account_id),
    )


def convert_meeting_attendee_to_es(
    attendee: MeetingAttendee,
) -> InnerDocMeetingAttendee:
    return InnerDocMeetingAttendee(
        user_id=convert_field_to_es(attendee.user_id),
        contact_id=convert_field_to_es(attendee.contact_id),
        contact_email=convert_field_to_es(attendee.contact_email),
        transcript_speaker_name=convert_field_to_es(attendee.transcript_speaker_name),
        is_organizer=convert_field_to_es(attendee.is_organizer),
        account_id=convert_field_to_es(attendee.account_id),
    )


def convert_account_to_es(account: Account) -> ESAccount:
    return ESAccount(
        id=convert_field_to_es(account.id),
        organization_id=convert_field_to_es(account.organization_id),
        display_name=convert_field_to_es(account.display_name),
        status=convert_field_to_es(account.status),
        owner_user_id=convert_field_to_es(account.owner_user_id),
        official_website=convert_field_to_es(account.official_website),
        domain_name=convert_field_to_es(account.domain_name),
        linkedin_url=convert_field_to_es(account.linkedin_url),
        facebook_url=convert_field_to_es(account.facebook_url),
        zoominfo_url=convert_field_to_es(account.zoominfo_url),
        x_url=convert_field_to_es(account.x_url),
        address_id=convert_field_to_es(account.address_id),
        description=convert_field_to_es(account.description),
        created_at=convert_field_to_es(account.created_at),
        created_by_user_id=convert_field_to_es(account.created_by_user_id),
        updated_at=convert_field_to_es(account.updated_at),
        updated_by_user_id=convert_field_to_es(account.updated_by_user_id),
        archived_at=convert_field_to_es(account.archived_at),
        archived_by_user_id=convert_field_to_es(account.archived_by_user_id),
        # stage=convert_field_to_es(account.stage), # doesn't exist anymore
        # state=convert_field_to_es(account.state), # doesn't exist anymore
        estimated_annual_revenue=convert_field_to_es(account.estimated_annual_revenue),
        estimated_employee_count=convert_field_to_es(account.estimated_employee_count),
        technology_list=convert_field_to_es(account.technology_list),
        category_list=convert_field_to_es(account.category_list),
        keyword_list=convert_field_to_es(account.keyword_list),
        research_tldr=convert_field_to_es(account.research_tldr),
        research_content=convert_field_to_es(account.research_content),
        research_reference_urls=convert_field_to_es(account.research_reference_urls),
    )


def convert_pipeline_to_es(pipeline: Pipeline) -> ESPipeline:
    return ESPipeline(
        id=convert_field_to_es(pipeline.id),
        organization_id=convert_field_to_es(pipeline.organization_id),
        display_name=convert_field_to_es(pipeline.display_name),
        next_step_details=convert_field_to_es(pipeline.next_step_details),
        state=convert_field_to_es(pipeline.state),
        stage_id=convert_field_to_es(pipeline.stage_id),
        source_id=convert_field_to_es(pipeline.source_id),
        type_id=convert_field_to_es(pipeline.type_id),
        amount=convert_field_to_es(pipeline.amount),
        owner_user_id=convert_field_to_es(pipeline.owner_user_id),
        created_at=convert_field_to_es(pipeline.created_at),
        created_by_user_id=convert_field_to_es(pipeline.created_by_user_id),
        updated_at=convert_field_to_es(pipeline.updated_at),
        updated_by_user_id=convert_field_to_es(pipeline.updated_by_user_id),
        next_step_due_at=convert_field_to_es(pipeline.next_step_due_at),
        anticipated_closing_at=convert_field_to_es(pipeline.anticipated_closing_at),
        expires_at=convert_field_to_es(pipeline.expires_at),
        closed_at=convert_field_to_es(pipeline.closed_at),
        closed_by_user_id=convert_field_to_es(pipeline.closed_by_user_id),
        archived_at=convert_field_to_es(pipeline.archived_at),
        archived_by_user_id=convert_field_to_es(pipeline.archived_by_user_id),
    )


def convert_contact_v2_to_es(contact: ContactV2) -> ESContact:
    primary_email = next(
        (
            contact_email.email
            for contact_email in contact.contact_emails
            if contact_email.is_contact_primary
        ),
        contact.primary_email,
    )
    return ESContact(
        id=convert_field_to_es(contact.id),
        organization_id=convert_field_to_es(contact.organization_id),
        display_name=convert_field_to_es(contact.display_name),
        # status=convert_field_to_es(contact.status), # no longer exists?
        created_at=convert_field_to_es(contact.created_at),
        created_by_user_id=convert_field_to_es(contact.created_by_user_id),
        owner_user_id=convert_field_to_es(contact.owner_user_id),
        first_name=convert_field_to_es(contact.first_name),
        last_name=convert_field_to_es(contact.last_name),
        middle_name=convert_field_to_es(contact.middle_name),
        primary_email=convert_field_to_es(primary_email),
        primary_phone_number=convert_field_to_es(contact.primary_phone_number),
        linkedin_url=convert_field_to_es(contact.linkedin_url),
        zoominfo_url=convert_field_to_es(contact.zoominfo_url),
        facebook_url=convert_field_to_es(contact.facebook_url),
        x_url=convert_field_to_es(contact.x_url),
        title=convert_field_to_es(contact.title),
        department=convert_field_to_es(contact.department),
        updated_at=convert_field_to_es(contact.updated_at),
        updated_by_user_id=convert_field_to_es(contact.updated_by_user_id),
        archived_at=convert_field_to_es(contact.archived_at),
        archived_by_user_id=convert_field_to_es(contact.archived_by_user_id),
        primary_account_id=convert_field_to_es(contact.primary_account_id),
        person_id=convert_field_to_es(contact.person_id),
        stage_id=convert_field_to_es(contact.stage_id),
        created_source=convert_field_to_es(contact.created_source),
        emails=[
            contact_email.email
            for contact_email in contact.contact_emails
            if contact_email.email
        ],
    )


def convert_meeting_and_transcript_to_es(
    meeting: Meeting,
    transcript_id: UUID | None,
    transcript_container: TranscriptContainer | None,
) -> ESMeeting:
    es_meeting = ESMeeting(
        id=convert_field_to_es(meeting.id),
        organization_id=convert_field_to_es(meeting.organization_id),
        title=convert_field_to_es(meeting.title),
        description=convert_field_to_es(meeting.description),
        agenda=convert_field_to_es(meeting.agenda),
        key_talking_points=convert_field_to_es(meeting.key_talking_points),
        status=convert_field_to_es(meeting.status),
        started_at=convert_field_to_es(meeting.started_at),
        ended_at=convert_field_to_es(meeting.ended_at),
        is_no_show=convert_field_to_es(meeting.is_no_show),
        meeting_url=convert_field_to_es(meeting.meeting_url),
        meeting_platform=convert_field_to_es(meeting.meeting_platform),
        metadata=convert_field_to_es(meeting.metadata),
        conferencing_details=convert_field_to_es(meeting.conferencing_details),
        starts_at=convert_field_to_es(meeting.starts_at),
        ends_at=convert_field_to_es(meeting.ends_at),
        location=convert_field_to_es(meeting.location),
        organizer_user_id=convert_field_to_es(meeting.organizer_user_id),
        invitees=convert_field_to_es(meeting.invitees),
        attendees=convert_field_to_es(meeting.attendees),
        consent_id=convert_field_to_es(meeting.consent_id),
        verbal_consent_at=convert_field_to_es(meeting.verbal_consent_at),
        event_schedule_id=convert_field_to_es(meeting.event_schedule_id),
        created_by_user_id=convert_field_to_es(meeting.created_by_user_id),
        cancel_reason=convert_field_to_es(meeting.cancel_reason),
        is_rescheduled=convert_field_to_es(meeting.is_rescheduled),
        rescheduled_from_id=convert_field_to_es(meeting.rescheduled_from_id),
        meeting_stats_id=convert_field_to_es(meeting.meeting_stats_id),
        pipeline_id=convert_field_to_es(meeting.pipeline_id),
        pipeline_select_list_value_id=convert_field_to_es(
            meeting.pipeline_select_list_value_id
        ),
        account_id=convert_field_to_es(meeting.account_id),
        created_at=convert_field_to_es(meeting.created_at),
        updated_at=convert_field_to_es(meeting.updated_at),
        canceled_at=convert_field_to_es(meeting.canceled_at),
        deleted_at=convert_field_to_es(meeting.deleted_at),
        transcript_id=None,
        sentences_embedding=[],
    )

    if transcript_id is not None:
        es_meeting.transcript_id = convert_field_to_es(transcript_id)
    if transcript_container is not None:
        es_meeting.sentences_embedding = pre_processing(transcript_container)
    return es_meeting


def doc_type_to_index_name(doc_type: DocumentType) -> str:
    return f"{settings.search_index_prefix}_{doc_type.value.lower()}_idx"


def doc_type_to_read_alias(doc_type: DocumentType) -> str:
    return f"{doc_type_to_index_name(doc_type)}_read"


def doc_type_to_write_alias(doc_type: DocumentType) -> str:
    return f"{doc_type_to_index_name(doc_type)}_write"


def doc_type_to_indexable_cls(doc_type: DocumentType) -> type[Indexable]:
    match doc_type:
        case DocumentType.ACCOUNT:
            return ESAccount
        case DocumentType.CONTACT:
            return ESContact
        case DocumentType.MEETING:
            return ESMeeting
        case DocumentType.PIPELINE:
            return ESPipeline
        case DocumentType.CUSTOM_OBJECT_DATA:
            return ESCustomObjectData
        case _ as unreachable:
            assert_never(unreachable)
