import contextlib
import time
from collections import defaultdict
from datetime import timedelta
from typing import Any, Literal, assert_never

from elastic_transport import ObjectApiResponse
from elasticsearch import AsyncElasticsearch

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.stats.metric import custom_metric
from salestech_be.common.util import is_dev_env, is_prod_env
from salestech_be.core.contact.types_v2 import ContactV2
from salestech_be.core.meeting.service.meeting_reference_type_strategy import (
    get_meeting_reference_type_strategy_factory_db_engine,
)
from salestech_be.core.transcript.transcript_service import (
    transcript_service_from_engine,
)
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.account import Account
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.custom_object_data import CustomObjectData
from salestech_be.db.models.meeting import Meeting
from salestech_be.db.models.pipeline import Pipeline
from salestech_be.ree_logging import get_logger
from salestech_be.search.common.type import DocumentType
from salestech_be.search.es.indexing.client_utils import get_es_indexing_client
from salestech_be.search.es.indexing.mapping.common import (
    Indexable,
    OrgTenantedIndexable,
)
from salestech_be.search.indexing.converter import (
    convert_account_to_es,
    convert_contact_v2_to_es,
    convert_meeting_and_transcript_to_es,
    convert_pipeline_to_es,
    doc_type_to_write_alias,
)
from salestech_be.search.indexing.types import (
    BulkIndexingRequest,
    PutDocRequest,
    UpsertDocRequest,
)

logger = get_logger(__name__)

ESEnabledModel = Account | Pipeline | ContactV2 | Meeting | CustomObjectData


class ESIndexService:
    def __init__(self, es: AsyncElasticsearch, db_engine: DatabaseEngine):
        self.es = es
        self.transcript_service = transcript_service_from_engine(db_engine)
        self.meeting_reference_type_strategy_factory = (
            get_meeting_reference_type_strategy_factory_db_engine(db_engine=db_engine)
        )

    async def create_index(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *,
        indexable_cls: type[Indexable],
        index_name: str,
        read_aliases: list[str] | None = None,
        write_alias: str | None = None,
        if_not_exists: bool = False,
    ) -> ObjectApiResponse[Any] | None:
        if if_not_exists and await self.es.indices.exists(index=index_name):
            logger.info(f"Index {index_name} already exists")
            return None
        aliases: dict[str, dict[str, Any]] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if write_alias:
            aliases[write_alias] = {"is_write_index": True}
        for ra in read_aliases or []:
            if ra == write_alias:
                continue
            aliases[ra] = {}

        # todo(xw): this is a hack to set number of replicas to 0 for non-prod envs, will need to make this actually driven by env-dependent settings
        index_settings = indexable_cls.settings_dict()

        if not (is_prod_env() or is_dev_env()):
            index_settings["number_of_replicas"] = 0

        result = await self.es.indices.create(
            index=index_name,
            mappings=indexable_cls.mapping_dict(),
            settings=index_settings,
            aliases=aliases,
        )
        logger.info("es index created", response=result)
        return result

    async def bulk_index(self, request: BulkIndexingRequest) -> Any:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        start_time = time.perf_counter()
        indexing_api_state = "success"
        put_indices: dict[str, int] = defaultdict(lambda: 0)
        upsert_indices: dict[str, int] = defaultdict(lambda: 0)
        try:
            operations: list[dict[str, Any]] = []  # type: ignore[explicit-any] # TODO: fix-any-annotation
            for upsert in request.upserts or []:
                operations.append(upsert.as_operation())
                operations.append(upsert.as_doc_data())
                upsert_indices[request.index] += 1
            for put in request.puts or []:
                operations.append(put.as_operation())
                operations.append(put.as_doc_data())
                put_indices[request.index] += 1
            if not operations:
                return None
            bulk_result = await self.es.bulk(
                index=request.index,
                operations=operations,
                wait_for_active_shards=1,  # wait until at least 1 shard is available,
                # this might need to be removed once we up volume
            )
            with contextlib.suppress(Exception):
                logger.info(
                    "es bulk index result",
                    response=bulk_result,
                    response_body=bulk_result.body,
                    index=request.index,
                    upsert_ids=[upsert.doc.id for upsert in request.upserts or []],
                    put_ids=[put.doc.id for put in request.puts or []],
                )
            return bulk_result
        except Exception as e:
            indexing_api_state = "failure"
            logger.exception(
                "Error bulk indexing",
                index_name=request.index,
                document_ids=[upsert.doc.id for upsert in request.upserts or []],
                time_passed=(time.perf_counter() - start_time) * 1000,
                exception_type=type(e),
                exception_details=str(e),
                exception_details_repr=repr(e),
            )
            raise
        finally:
            custom_metric.timing(
                metric_name="es_index_service_bulk_index",
                value=(time.perf_counter() - start_time) * 1000,
                tags=[
                    f"index:{request.index}",
                    f"api_state:{indexing_api_state}",
                ],
            )
            for index, count in put_indices.items():
                custom_metric.increment(
                    metric_name="es_index_service_bulk_index_by_action",
                    value=count,
                    tags=[
                        f"index:{index}",
                        "action:put",
                        f"api_state:{indexing_api_state}",
                    ],
                )
            for index, count in upsert_indices.items():
                custom_metric.increment(
                    metric_name="es_index_service_bulk_index_by_action",
                    value=count,
                    tags=[
                        f"index:{index}",
                        "action:upsert",
                        f"api_state:{indexing_api_state}",
                    ],
                )

    async def bulk_index_docs(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        *,
        index: str,
        upsert_indexables: list[Indexable],
        put_indexables: list[Indexable] | None = None,
        # do not set to true in production code!!!!
        _refresh_index_immediately: Literal[False] = False,
    ) -> Any:
        result = await self.bulk_index(
            BulkIndexingRequest(
                index=index,
                upserts=[UpsertDocRequest(doc=d) for d in upsert_indexables],
                puts=[PutDocRequest(doc=d) for d in put_indexables]
                if put_indexables
                else [],
            )
        )
        if _refresh_index_immediately:
            await self.es.indices.refresh(index=index)
        return result

    async def convert_object_to_es(
        self, instance: ESEnabledModel
    ) -> OrgTenantedIndexable:
        start_time = time.perf_counter()
        try:
            match instance:
                case Account():
                    return convert_account_to_es(instance)
                case Pipeline():
                    return convert_pipeline_to_es(instance)
                case ContactV2():
                    return convert_contact_v2_to_es(instance)
                case Meeting():
                    transcript_container = None
                    db_transcript = None
                    if (
                        instance.starts_at
                        and instance.ends_at
                        and (instance.ends_at - instance.starts_at) > timedelta(hours=2)
                    ):
                        logger.info(
                            "Meeting is too long for transcript and embedding, only index meeting metadata",
                            meeting_id=instance.id,
                            starts_at=instance.starts_at,
                            ends_at=instance.ends_at,
                            organization_id=instance.organization_id,
                        )
                        return convert_meeting_and_transcript_to_es(
                            meeting=instance,
                            transcript_id=None,
                            transcript_container=None,
                        )
                    try:
                        request = await self.meeting_reference_type_strategy_factory.get_instance(
                            reference_id_type=instance.reference_id_type
                        ).get_load_transcript_request(meeting=instance)
                        (
                            db_transcript,
                            transcript_container,
                        ) = await self.transcript_service.get_processed_transcript(
                            organization_id=instance.organization_id, request=request
                        )
                    except ResourceNotFoundError:
                        logger.info(f"No transcript for meeting {instance.id}")

                    transcript_id = db_transcript.id if db_transcript else None
                    return convert_meeting_and_transcript_to_es(
                        instance, transcript_id, transcript_container
                    )
                case CustomObjectData():
                    raise NotImplementedError("CustomObjectData not implemented")
                    # return convert_custom_object_data_to_es(instance)
                case _ as unreachable:
                    assert_never(unreachable)
        finally:
            custom_metric.timing(
                metric_name="es_index_service_convert_object_to_es",
                value=(time.perf_counter() - start_time) * 1000,
                tags=[
                    f"document_type:{type(instance).__name__}",
                ],
            )

    def map_object_to_index(self, instance: ESEnabledModel) -> str:
        match instance:
            case Account():
                return doc_type_to_write_alias(DocumentType.ACCOUNT)
            case Pipeline():
                return doc_type_to_write_alias(DocumentType.PIPELINE)
            case Contact():
                return doc_type_to_write_alias(DocumentType.CONTACT)
            case ContactV2():
                return doc_type_to_write_alias(DocumentType.CONTACT)
            case Meeting():
                return doc_type_to_write_alias(DocumentType.MEETING)
            case CustomObjectData():
                return doc_type_to_write_alias(DocumentType.CUSTOM_OBJECT_DATA)
            case _ as unreachable:
                assert_never(unreachable)


_es_index_service: ESIndexService | None = None


def get_es_index_service(db_engine: DatabaseEngine) -> ESIndexService:
    global _es_index_service  # noqa: PLW0603
    if _es_index_service is None:
        _es_index_service = ESIndexService(get_es_indexing_client(), db_engine)
    return _es_index_service
