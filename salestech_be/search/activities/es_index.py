from datetime import datetime
from typing import assert_never
from uuid import UUID

from temporalio import activity

from salestech_be.common.type.metadata.field.field_type import (
    FieldType,
    is_custom_field_type,
)
from salestech_be.core.account.service.account_service import get_account_service
from salestech_be.core.contact.service.contact_query_service import (
    get_contact_query_service,
)
from salestech_be.core.custom_object.service.custom_object_service import (
    get_custom_object_service,
)
from salestech_be.core.meeting.meeting_service import (
    meeting_service_factory_general,
)
from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.db.dto.custom_object_data_dto import CustomObjectDataDto
from salestech_be.ree_logging import get_logger
from salestech_be.search.common.type import DocumentType
from salestech_be.search.es.indexing.mapping.custom_object import (
    CustomObjectArrayKeywordFieldInnerDoc,
    CustomObjectBooleanFieldInnerDoc,
    CustomObjectDateFieldInnerDoc,
    CustomObjectKeywordFieldInnerDoc,
    CustomObjectNumericFieldInnerDoc,
    CustomObjectTextFieldInnerDoc,
    ESCustomObjectData,
)
from salestech_be.search.indexing.converter import (
    doc_type_to_write_alias,
)
from salestech_be.search.indexing.index_service import get_es_index_service
from salestech_be.search.indexing.types import (
    BulkIndexingRequest,
    PutDocRequest,
)
from salestech_be.temporal.database import get_or_init_db_engine

logger = get_logger(__name__)


class ElasticsearchIndexActivities:
    @activity.defn
    async def index_accounts(self, account_ids: list[UUID]) -> None:
        db_engine = await get_or_init_db_engine()
        es_service = get_es_index_service(db_engine)
        account_service = get_account_service(db_engine)
        index = doc_type_to_write_alias(DocumentType.ACCOUNT)

        logger.info(f"Indexing {len(account_ids)} accounts")
        accounts = await account_service.list_accounts_by_ids_untenanted(
            account_ids=account_ids,
            exclude_deleted_or_archived=False,
        )
        logger.info(f"Found {len(accounts)} accounts to index")

        docs = [await es_service.convert_object_to_es(account) for account in accounts]
        logger.info(f"Converting {len(docs)} accounts to ES documents")

        await es_service.bulk_index(
            BulkIndexingRequest(
                index=index,
                puts=[PutDocRequest(doc=doc) for doc in docs],
            )
        )
        logger.info(f"Successfully indexed {len(docs)} accounts")

    @activity.defn
    async def index_pipelines(self, pipeline_ids: list[UUID]) -> None:
        db_engine = await get_or_init_db_engine()
        es_service = get_es_index_service(db_engine)
        pipeline_service = get_pipeline_service(db_engine)
        index = doc_type_to_write_alias(DocumentType.PIPELINE)

        logger.info(f"Indexing {len(pipeline_ids)} pipelines")
        pipelines = await pipeline_service.list_by_ids_untenanted(
            pipeline_ids, exclude_deleted_or_archived=False
        )
        logger.info(f"Found {len(pipelines)} pipelines to index")
        if not pipelines:
            logger.info("No pipelines to index")
            return

        docs = [
            await es_service.convert_object_to_es(pipeline) for pipeline in pipelines
        ]
        logger.info(f"Converting {len(docs)} pipelines to ES documents")

        await es_service.bulk_index(
            BulkIndexingRequest(
                index=index,
                puts=[PutDocRequest(doc=doc) for doc in docs],
            )
        )
        logger.info(f"Successfully indexed {len(docs)} pipelines")

    @activity.defn
    async def index_meetings(self, meeting_ids: list[UUID]) -> None:
        db_engine = await get_or_init_db_engine()
        es_service = get_es_index_service(db_engine)
        meeting_service = meeting_service_factory_general(db_engine)
        index = doc_type_to_write_alias(DocumentType.MEETING)

        logger.info(f"Indexing {len(meeting_ids)} meetings")
        meetings = await meeting_service.list_meetings_by_ids_untenanted(
            meeting_ids, exclude_deleted_or_archived=False
        )
        logger.info(f"Found {len(meetings)} meetings to index")
        if not meetings:
            logger.info("No meetings to index")
            return

        docs = [await es_service.convert_object_to_es(meeting) for meeting in meetings]
        logger.info(f"Converting {len(docs)} meetings to ES documents")

        await es_service.bulk_index(
            BulkIndexingRequest(
                index=index,
                puts=[PutDocRequest(doc=doc) for doc in docs],
            )
        )
        logger.info(f"Successfully indexed {len(docs)} meetings")

    @activity.defn
    async def index_contacts(self, contact_ids: list[UUID]) -> None:
        db_engine = await get_or_init_db_engine()
        es_service = get_es_index_service(db_engine)
        contact_query_service = get_contact_query_service(db_engine)
        index = doc_type_to_write_alias(DocumentType.CONTACT)

        logger.info(f"Indexing {len(contact_ids)} contacts")
        contacts = (
            await contact_query_service.list_contact_v2_untenanted_by_contact_ids(
                contact_ids=set(contact_ids),
            )
        )
        logger.info(f"Found {len(contacts)} contacts to index")
        if not contacts:
            logger.info("No contacts to index")
            return

        docs = [await es_service.convert_object_to_es(contact) for contact in contacts]
        logger.info(f"Converting {len(docs)} contacts to ES documents")

        await es_service.bulk_index(
            BulkIndexingRequest(
                index=index,
                puts=[PutDocRequest(doc=doc) for doc in docs],
            )
        )
        logger.info(f"Successfully indexed {len(docs)} contacts")

    @activity.defn
    async def index_custom_objects(self, cobject_data_ids: list[UUID]) -> None:  # noqa: C901, PLR0912
        db_engine = await get_or_init_db_engine()
        es_service = get_es_index_service(db_engine)
        cobject_service = get_custom_object_service(db_engine)
        index = doc_type_to_write_alias(DocumentType.CUSTOM_OBJECT_DATA)

        logger.info(
            "Retrieving custom object data to index",
            number_of_custom_object_data_ids=len(cobject_data_ids),
        )
        cobject_data_dto_list: list[
            CustomObjectDataDto
        ] = await cobject_service.list_custom_object_data_dto_by_data_ids_untenanted(
            custom_object_data_ids=cobject_data_ids,
            # we want to exclude deleted custom object data here, since we should updated the deleted_at field in the ES document as well
            exclude_deleted_or_archived=False,
        )
        for cobject_data_dto in cobject_data_dto_list:
            logger.debug(
                "Custom object data dto",
                cobject_data_dto=cobject_data_dto,
            )
        logger.debug(
            "Found custom objects to index",
            number_of_custom_object_data_dtos=len(cobject_data_dto_list),
        )

        docs: list[ESCustomObjectData] = []
        for cobject_data_dto in cobject_data_dto_list:
            # Initialize field lists
            text_fields: list[CustomObjectTextFieldInnerDoc] = []
            numeric_fields: list[CustomObjectNumericFieldInnerDoc] = []
            keyword_fields: list[CustomObjectKeywordFieldInnerDoc] = []
            date_fields: list[CustomObjectDateFieldInnerDoc] = []
            bool_fields: list[CustomObjectBooleanFieldInnerDoc] = []
            array_keyword_fields: list[CustomObjectArrayKeywordFieldInnerDoc] = []

            logger.debug(
                "fields",
                fields=cobject_data_dto.custom_object_dto.custom_fields,
            )

            # Process each field based on its defined type
            for field in cobject_data_dto.custom_object_dto.custom_fields:
                if field.deleted_at:
                    # skip deleted fields
                    logger.debug(
                        "Skipping deleted field",
                        field_id=field.id,
                        field_name=field.field_name,
                    )
                    continue
                field_data = cobject_data_dto.custom_object_data.get_custom_field_data_from_value_slot(
                    slot_number=field.slot_number
                )
                logger.debug(
                    "field_data",
                    field_data=field_data,
                )

                if field_data and (
                    field_value := field_data.value_by_field_id.get(field.id)
                ):
                    value = field_value.to_generic_value()
                    logger.debug(
                        "value",
                        value=value,
                    )
                    if not is_custom_field_type(field_type=field.field_type):
                        # for now: just log the error message, since the downstream code does a string cast on these kinda fields
                        # we will need to fix the custom field type narrowing in the upstream.
                        logger.error(
                            "Unexpected, during custom object indexing, found a field that is not a custom field type",
                            organization_id=cobject_data_dto.custom_object_data.organization_id,
                            custom_object_id=cobject_data_dto.custom_object_data.id,
                            field_id=field.id,
                            field_name=field.field_name,
                            field_type=field.field_type,
                        )
                    if value is not None:
                        logger.debug(
                            "value is not None",
                            value=value,
                        )
                        match field.field_type:
                            case (
                                FieldType.NUMERIC
                                | FieldType.CURRENCY
                                | FieldType.PERCENT
                            ):
                                numeric_fields.append(
                                    CustomObjectNumericFieldInnerDoc(
                                        id=str(field.id), value=float(value)
                                    )
                                )
                            case (
                                FieldType.UUID
                                | FieldType.SINGLE_SELECT
                                | FieldType.PHONE_NUMBER
                                | FieldType.URL
                            ):
                                keyword_fields.append(
                                    CustomObjectKeywordFieldInnerDoc(
                                        id=str(field.id), value=str(value)
                                    )
                                )
                            case FieldType.BOOLEAN_CHECKBOX:
                                bool_fields.append(
                                    CustomObjectBooleanFieldInnerDoc(
                                        id=str(field.id), value=bool(value)
                                    )
                                )
                            case (
                                FieldType.TIMESTAMP
                                | FieldType.LOCAL_TIME_OF_DAY
                                | FieldType.TIME_OF_DAY
                                | FieldType.LOCAL_DATE
                            ):
                                date_fields.append(
                                    CustomObjectDateFieldInnerDoc(
                                        id=field.field_name,
                                        value=value
                                        if isinstance(value, datetime)
                                        else datetime.fromisoformat(
                                            str(value).replace("Z", "+00:00")
                                        ),
                                    )
                                )
                            case (
                                FieldType.EMAIL  # email here, just in case we need to deal with casing / partial domain matches
                                | FieldType.TEXT
                                | FieldType.TEXT_AREA
                                | FieldType.LONG_TEXT_AREA
                                | FieldType.RICH_TEXT_AREA
                                | FieldType.GEO_LOCATION
                                | FieldType.LIST
                                | FieldType.DICT
                                | FieldType.NESTED_OBJECT
                                | FieldType.DEFAULT_ENUM
                            ):
                                text_value = (
                                    ", ".join(map(str, value))
                                    if isinstance(value, list | tuple)
                                    else str(value)
                                )
                                text_fields.append(
                                    CustomObjectTextFieldInnerDoc(
                                        id=str(field.id), value=text_value
                                    )
                                )
                            case FieldType.MULTI_SELECT:
                                array_keyword_fields.append(
                                    CustomObjectArrayKeywordFieldInnerDoc(
                                        id=str(field.id),
                                        value=[str(v) for v in value]
                                        if isinstance(value, (list, tuple))
                                        else [str(value)],
                                    )
                                )
                            case _ as unreachable:
                                assert_never(unreachable)

            # Create ES document
            es_doc = ESCustomObjectData(
                id=str(cobject_data_dto.custom_object_data.id),
                extension_id=str(cobject_data_dto.custom_object_data.extension_id)
                if cobject_data_dto.custom_object_data.extension_id
                else None,
                display_name=cobject_data_dto.custom_object_data.display_name,
                deleted_at=cobject_data_dto.custom_object_data.deleted_at,
                deleted_by_user_id=str(
                    cobject_data_dto.custom_object_data.deleted_by_user_id
                )
                if cobject_data_dto.custom_object_data.deleted_by_user_id
                else None,
                organization_id=str(
                    cobject_data_dto.custom_object_data.organization_id
                ),
                custom_object_id=str(
                    cobject_data_dto.custom_object_dto.custom_object_id
                ),
                parent_object_name=cobject_data_dto.custom_object_dto.custom_object.parent_object_name,
                created_at=cobject_data_dto.custom_object_data.created_at,
                updated_at=cobject_data_dto.custom_object_data.updated_at
                or cobject_data_dto.custom_object_data.created_at,
                created_by_user_id=str(
                    cobject_data_dto.custom_object_data.created_by_user_id
                ),
                updated_by_user_id=str(
                    cobject_data_dto.custom_object_data.updated_by_user_id
                    or cobject_data_dto.custom_object_data.created_by_user_id
                ),
                text_fields=text_fields if text_fields else None,
                numeric_fields=numeric_fields if numeric_fields else None,
                keyword_fields=keyword_fields if keyword_fields else None,
                date_fields=date_fields if date_fields else None,
                bool_fields=bool_fields if bool_fields else None,
                array_keyword_fields=array_keyword_fields
                if array_keyword_fields
                else None,
            )
            logger.debug(
                "ES document inside loop",
                es_doc_id=es_doc.id,
                es_doc_display_name=es_doc.display_name,
                es_doc_text_fields=es_doc.text_fields,
                es_doc_numeric_fields=es_doc.numeric_fields,
                es_doc_keyword_fields=es_doc.keyword_fields,
                es_doc_date_fields=es_doc.date_fields,
                es_doc_bool_fields=es_doc.bool_fields,
                es_doc_array_keyword_fields=es_doc.array_keyword_fields,
            )
            docs.append(es_doc)

        logger.info(
            "Converting custom objects to ES documents",
            number_of_custom_objects=len(docs),
        )

        await es_service.bulk_index(
            BulkIndexingRequest(
                index=index,
                puts=[PutDocRequest(doc=doc) for doc in docs],
            )
        )
        logger.info(
            "Successfully indexed custom objects",
            number_of_custom_objects=len(docs),
        )
