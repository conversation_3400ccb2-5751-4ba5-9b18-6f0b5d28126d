from enum import StrEnum
from uuid import UUID

from pydantic import BaseModel


class EvalTag(StrEnum):
    SINGLE_MEETING = "single_meeting"
    MULTI_MEETING = "multi_meeting"


class EvalQuery(BaseModel):
    query: str
    criteria: list[str]


class EvalConversation(BaseModel):
    user_id: UUID
    organization_id: UUID
    meeting_id: UUID | None = None
    tags: list[EvalTag] | None = None
    queries: list[EvalQuery]


jerry_user_id = UUID("4258be3d-642e-4fd7-814e-b92e29ce312a")  # <EMAIL>
hugh_user_id = UUID("5cfd355a-f65b-4f1b-be3c-550ac2970db8")  # Hugh GTM
ross_user_id = UUID("9ab23c4a-63a4-477d-b148-11beb687bca4")  # Ross GTM
test_organization_id = UUID("4d29f892-7e25-4efa-ad0b-f348bd0fc0fc")  # Reevo.ai - GTM

eval_dataset = [
    EvalConversation(
        # This may be better suited with <PERSON>'s user_id
        user_id=hugh_user_id,
        organization_id=test_organization_id,
        meeting_id=UUID("e1368bdd-8ebe-4a14-9745-0698a9153d51"),
        tags=[EvalTag.SINGLE_MEETING],
        queries=[
            EvalQuery(
                query="What repetitive tasks did I handle post-call that can be automated?",
                criteria=[],
            ),
            EvalQuery(
                query="What is my average talk time for this weeks meetings",
                criteria=[],
            ),
            EvalQuery(
                query="What calls do I have internal action items for? List them out",
                criteria=[],
            ),
        ],
    ),
    EvalConversation(
        user_id=hugh_user_id,
        organization_id=test_organization_id,
        meeting_id=UUID("e1368bdd-8ebe-4a14-9745-0698a9153d51"),
        tags=[EvalTag.SINGLE_MEETING],
        queries=[
            EvalQuery(
                query="How did Ross align customer pain points to value props?",
                criteria=[],
            ),
            EvalQuery(
                query="How did Ross position Reevo on the CostCuts call?", criteria=[]
            ),
            EvalQuery(
                query="Did Ross ask for introduction on the call with CostCuts?",
                criteria=[],
            ),
        ],
    ),
    EvalConversation(
        user_id=jerry_user_id,
        organization_id=test_organization_id,
        meeting_id=UUID("e1368bdd-8ebe-4a14-9745-0698a9153d51"),
        tags=[EvalTag.SINGLE_MEETING],
        queries=[
            EvalQuery(
                query="Identify the strengths and weaknesses in Ross's call with CostCuts",
                criteria=[],
            ),
            EvalQuery(
                query="Was the call with Luis successful? Provide reasons based on the conversation",
                criteria=[],
            ),
            EvalQuery(
                query="What commitments were made during the call that need to be tracked?",
                criteria=[],
            ),
        ],
    ),
    EvalConversation(
        user_id=hugh_user_id,
        organization_id=test_organization_id,
        meeting_id=None,
        tags=[EvalTag.MULTI_MEETING],
        queries=[
            EvalQuery(
                query="Which competitors were mentioned the most?",
                criteria=[],
            ),
            EvalQuery(
                query="What are the most common objections from my meetings?",
                criteria=[],
            ),
            EvalQuery(
                query="What are the most requested features from my meetings?",
                criteria=[],
            ),
        ],
    ),
    EvalConversation(
        user_id=hugh_user_id,
        organization_id=test_organization_id,
        meeting_id=None,
        tags=[EvalTag.MULTI_MEETING],
        queries=[
            EvalQuery(
                query="Which calls have outstanding tasks with a expired due date",
                criteria=[],
            ),
            EvalQuery(
                query="Which meetings require urgent attention?",
                criteria=[],
            ),
            EvalQuery(
                query="List all follow-ups from the calls",
                criteria=[],
            ),
        ],
    ),
    EvalConversation(
        user_id=hugh_user_id,
        organization_id=test_organization_id,
        meeting_id=None,
        tags=[EvalTag.MULTI_MEETING],
        queries=[
            EvalQuery(
                query='Show me all calls with keyword "Reevo"',
                criteria=[],
            ),
            EvalQuery(
                query="What area of our product did sentiment increase?",
                criteria=[],
            ),
            EvalQuery(
                query="Compare this call with previous ones-are we progressing?",
                criteria=[],
            ),
        ],
    ),
    EvalConversation(
        user_id=jerry_user_id,
        organization_id=test_organization_id,
        meeting_id=None,
        tags=[EvalTag.MULTI_MEETING],
        queries=[
            EvalQuery(
                query="Show me Hugh's last 4 calls",
                criteria=[],
            )
        ],
    ),
    EvalConversation(
        user_id=ross_user_id,
        organization_id=test_organization_id,
        meeting_id=UUID("1eed55e3-da15-4db2-8d76-e68c6081774a"),
        tags=[EvalTag.SINGLE_MEETING],
        queries=[
            EvalQuery(
                query="write me a post call follow up email for my call with Kristi May. I want action items and DRI's for each outstanding action item in the email",
                criteria=[],
            )
        ],
    ),
    EvalConversation(
        user_id=jerry_user_id,
        organization_id=test_organization_id,
        meeting_id=None,
        tags=[],
        queries=[
            EvalQuery(
                query="how do i mass import contacts",
                criteria=[
                    "Assistant should state it does not know and direct user to the application documentation"
                ],
            )
        ],
    ),
]
