from typing import Self
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, PrivateAttr, model_validator

from salestech_be.common.type.formatted_string import EmailStrLower
from salestech_be.db.models.contact import (
    Contact,
    ContactAccountAssociation,
)
from salestech_be.db.models.contact_email import (
    ContactEmail,
    ContactEmailAccountAssociation,
)
from salestech_be.db.models.contact_phone_number import (
    ContactPhoneNumber,
    ContactPhoneNumberAccountAssociation,
)


class ContactAccountAssociationJourney(BaseModel):
    model_config = ConfigDict(frozen=True)

    contact_id: UUID
    account_id: UUID
    associations_ordered_by_created_at_desc: tuple[ContactAccountAssociation, ...]

    @model_validator(mode="after")
    def _validate_contact_account_associations(self) -> Self:
        for association in self.associations_ordered_by_created_at_desc:
            if association.contact_id != self.contact_id:
                raise ValueError("All associations must belong to the same contact")
            if association.account_id != self.account_id:
                raise ValueError("All associations must belong to the same account")
        # validate ordering of associations
        for i in range(len(self.associations_ordered_by_created_at_desc) - 1):
            if (
                self.associations_ordered_by_created_at_desc[i].created_at
                < self.associations_ordered_by_created_at_desc[i + 1].created_at
            ):
                raise ValueError(
                    "Associations are not ordered by created_at in descending order"
                )
        return self

    @property
    def latest_association(self) -> ContactAccountAssociation | None:
        return (
            self.associations_ordered_by_created_at_desc[0]
            if self.associations_ordered_by_created_at_desc
            else None
        )

    @property
    def active_association(self) -> ContactAccountAssociation | None:
        latest_association = self.latest_association
        return (
            latest_association
            if (latest_association and not latest_association.archived_at)
            else None
        )


class ContactDto(BaseModel):
    model_config = ConfigDict(frozen=True)

    contact: Contact
    emails: list[ContactEmail] = Field(default_factory=list)
    email_account_associations: list[ContactEmailAccountAssociation] = Field(
        default_factory=list
    )
    phone_numbers: list[ContactPhoneNumber] = Field(default_factory=list)
    phone_number_account_associations: list[ContactPhoneNumberAccountAssociation] = (
        Field(default_factory=list)
    )
    active_account_associations: list[ContactAccountAssociation] = Field(
        default_factory=list
    )
    _email_by_email: dict[EmailStrLower, ContactEmail] = PrivateAttr(
        default_factory=dict
    )
    _email_by_id: dict[UUID, ContactEmail] = PrivateAttr(default_factory=dict)
    _active_account_association_by_account_id: dict[UUID, ContactAccountAssociation] = (
        PrivateAttr(default_factory=dict)
    )

    @model_validator(mode="after")
    def _validate_create_contact_request(self) -> Self:
        self._populate_private_attributes()
        self._validate_contact_emails()
        self._validate_contact_account_associations()
        return self

    def _populate_private_attributes(self) -> None:
        self._active_account_association_by_account_id = {
            caa.account_id: caa for caa in self.active_account_associations
        }
        self._email_by_email = {ce.email: ce for ce in self.emails}
        self._email_by_id = {ce.id: ce for ce in self.emails}

    def _validate_contact_emails(self) -> Self:  # noqa: C901, PLR0912
        _contact_primary: ContactEmail | None = None
        _account_primary_by_account_id_set: set[UUID] = set()
        for contact_email in self.emails:
            if contact_email.deleted_at:
                raise ValueError("contact_email.deleted_at must be None")
            if contact_email.archived_at:
                raise ValueError("contact_email.archived_at must be None")
            if contact_email.contact_id != self.contact.id:
                raise ValueError("contact_email.contact_id must match contact.id")
            if contact_email.organization_id != self.contact.organization_id:
                raise ValueError(
                    "contact_email.organization_id must match contact.organization_id"
                )
            if contact_email.is_contact_primary:
                if _contact_primary:
                    raise ValueError("only one contact_email can be primary")
                _contact_primary = contact_email

        for contact_email_account_association in self.email_account_associations:
            if (
                contact_email_account_association.contact_email_id
                not in self._email_by_id
            ):
                raise ValueError(
                    "contact_email_account_association.contact_email_id must be in contact_emails"
                )

            if (
                contact_email_account_association.account_id
                not in self._active_account_association_by_account_id
            ):
                raise ValueError(
                    "contact_email_account_association.account_id must be in contact_account_associations"
                )

            if contact_email_account_association.is_contact_account_primary:
                if (
                    contact_email_account_association.account_id
                    in _account_primary_by_account_id_set
                ):
                    raise ValueError(
                        f"only one contact_email can be contact account primary for account {contact_email_account_association.account_id}"
                    )
                _account_primary_by_account_id_set.add(
                    contact_email_account_association.account_id
                )

        if len(self._email_by_email) != len(self.emails):
            _duplicates = [
                ce.email for ce in self.emails if self._email_by_email[ce.email] != ce
            ]
            raise ValueError(
                f"contact_emails must contain unique emails, but found duplicates: {_duplicates}"
            )
        return self

    def _validate_contact_account_associations(self) -> Self:
        _primary_account_association: ContactAccountAssociation | None = None
        for contact_account_association in self.active_account_associations:
            if contact_account_association.archived_at:
                raise ValueError("contact_account_association.archived_at must be None")
            if contact_account_association.contact_id != self.contact.id:
                raise ValueError(
                    "contact_account_association.contact_id must match contact.id"
                )
            if (
                contact_account_association.organization_id
                != self.contact.organization_id
            ):
                raise ValueError(
                    "contact_account_association.organization_id must match contact.organization_id"
                )
            if contact_account_association.is_primary:
                if _primary_account_association:
                    raise ValueError(
                        "only one contact_account_association can be primary"
                    )
                _primary_account_association = contact_account_association
        if len(self._active_account_association_by_account_id) != len(
            self.active_account_associations
        ):
            raise ValueError(
                "contact_account_associations must contain unique account_ids"
            )
        return self
