"""add ready at date

Revision ID: 8020cc8e73f2
Revises: 36b173c24d49
Create Date: 2025-06-02 23:43:36.637798+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "8020cc8e73f2"
down_revision: str | tuple[str, ...] | None = "36b173c24d49"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    # Add the last_started_at column
    op.execute(
        "ALTER TABLE email_account_warm_up_campaign ADD COLUMN last_started_at TIMESTAMPTZ"
    )

    # Backfill last_started_at with created_at date
    op.execute(
        "UPDATE email_account_warm_up_campaign SET last_started_at = created_at WHERE last_started_at IS NULL"
    )


def downgrade() -> None:
    pass
