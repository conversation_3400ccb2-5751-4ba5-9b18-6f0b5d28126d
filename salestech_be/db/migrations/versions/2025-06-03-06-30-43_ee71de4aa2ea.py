"""create people_data_labs_overlap_tracking table

Revision ID: ee71de4aa2ea
Revises: 8020cc8e73f2
Create Date: 2025-06-03 06:30:43.633259+00:00

"""

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "ee71de4aa2ea"
down_revision: str | tuple[str, ...] | None = "8020cc8e73f2"
branch_labels: set[str] | str | None = None
depends_on: str | None = None


def upgrade() -> None:
    op.execute(
        """
        CREATE TABLE if not exists people_data_labs_overlap_tracking (
            id UUID PRIMARY KEY,
            organization_id UUID NOT NULL,
            entity_ids UUID[] NOT NULL,
            entity_type VARCHAR NOT NULL,
            action VARCHAR NOT NULL,
            request_data JSONB NULL,
            created_at TIMESTAMPTZ NOT NULL,
            updated_at TIMESTAMPTZ NULL
        )
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP TABLE if exists people_data_labs_overlap_tracking
        """
    )
