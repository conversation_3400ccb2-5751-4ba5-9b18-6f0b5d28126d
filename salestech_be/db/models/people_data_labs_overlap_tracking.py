from typing import Any
from uuid import UUID

from salestech_be.db.models.core.base import (
    Column,
    JsonColumn,
    TableModel,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.enum_util import NameValueStrEnum
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class PeopleDataLabsOverlapEntityType(NameValueStrEnum):
    person = "person"
    company = "company"


class PeopleDataLabsOverlapAction(NameValueStrEnum):
    search = "search"
    enrich = "enrich"


class PeopleDataLabsOverlapTracking(TableModel):  # type: ignore[explicit-any]
    table_name = TableName.people_data_labs_overlap_tracking
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    organization_id: Column[UUID]
    entity_ids: Column[list[UUID]]
    entity_type: Column[PeopleDataLabsOverlapEntityType]
    action: Column[PeopleDataLabsOverlapAction]
    request_data: <PERSON>sonColumn[dict[str, Any] | None]  # type: ignore[explicit-any]

    created_at: Column[ZoneRequiredDateTime]
    updated_at: Column[ZoneRequiredDateTime | None] = None
