from collections.abc import Callable, Mapping
from datetime import UTC, date, datetime
from decimal import Decimal
from enum import StrEnum
from types import NoneType
from typing import Any, Literal, NamedTuple, assert_never, cast
from uuid import UUID

import orjson
from pydantic import AwareDatetime

from salestech_be.common.query_util.filter_schema import (
    FilterValue,
    RelativeTime,
    SingularFilterValue,
    ValueFilter,
)
from salestech_be.common.query_util.legacy.sort_schema import OrderEnum
from salestech_be.common.query_util.operator import MatchOperator
from salestech_be.common.query_util.sort_schema import (
    NonRelationalSorter,
    NonRelationalSortingSpec,
)
from salestech_be.common.results import Cursor
from salestech_be.common.type.metadata.schema import QualifiedField
from salestech_be.db.models.core.base import ColumnFieldInfo, TableModel
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.parser import (
    ParsedType,
    parse_pydantic_field_type,
)

__all__ = [
    "SqlComparator",
    "SqlSelection",
    "SqlSort",
    "sort_spec_to_sql_sort_clause",
    "value_filter_to_sql_selection",
]


logger = get_logger(__name__)


FieldPathDBColumnNameMappingProvider = Callable[[tuple[str, ...]], str | None]


class SqlComparator(StrEnum):
    EQ = "="
    NE = "!="
    LT = "<"
    GT = ">"
    LTE = "<="
    GTE = ">="
    IN = "IN"
    NOT_IN = "NOT IN"
    ILIKE = "ILIKE"
    NOT_ILIKE = "NOT ILIKE"
    IS_NULL = "IS NULL"
    IS_NOT_NULL = "IS NOT NULL"
    TRUE = "TRUE"
    FALSE = "FALSE"
    JSON_CONTAINS = "@>"

    def as_condition_clause(self, *, fq_column_name: str, param_name: str) -> str:  # noqa: C901, PLR0911, PLR0912
        match self:
            case SqlComparator.EQ:
                return f"{fq_column_name} = :{param_name}"
            case SqlComparator.NE:
                return f"{fq_column_name} != :{param_name}"
            case SqlComparator.LT:
                return f"{fq_column_name} < :{param_name}"
            case SqlComparator.GT:
                return f"{fq_column_name} > :{param_name}"
            case SqlComparator.LTE:
                return f"{fq_column_name} <= :{param_name}"
            case SqlComparator.GTE:
                return f"{fq_column_name} >= :{param_name}"
            case SqlComparator.IN:
                return f"{fq_column_name} = ANY(:{param_name})"
            case SqlComparator.NOT_IN:
                return f"{fq_column_name} != ANY(:{param_name})"
            case SqlComparator.ILIKE:
                return f"{fq_column_name} ILIKE :{param_name}"
            case SqlComparator.NOT_ILIKE:
                return f"{fq_column_name} NOT ILIKE :{param_name}"
            case SqlComparator.IS_NULL:
                return f"{fq_column_name} IS NULL"
            case SqlComparator.IS_NOT_NULL:
                return f"{fq_column_name} IS NOT NULL"
            case SqlComparator.TRUE:
                return "TRUE"
            case SqlComparator.FALSE:
                return "FALSE"
            case SqlComparator.JSON_CONTAINS:
                return f"{fq_column_name} @> :{param_name} ::jsonb"
            case _ as unreachable:
                assert_never(unreachable)


ComparableSingularValueType = (
    int | float | str | bool | datetime | Decimal | UUID | date
)

ComparableValueType = ComparableSingularValueType | list[ComparableSingularValueType]


class SqlSelection(NamedTuple):
    column_name: str
    sql_comparator: SqlComparator
    comparable_value: ComparableValueType | None

    def _as_param_name(self, *, param_prefix: str | None) -> str:
        if not self.column_name:
            raise ValueError("column_name is blank")
        return (
            f"{param_prefix}_{self.column_name}" if param_prefix else self.column_name
        )

    def _as_fq_column_name(self, *, tb_prefix: str | None) -> str:
        if not self.column_name:
            raise ValueError("column_name is blank")
        return f"{tb_prefix}.{self.column_name}" if tb_prefix else self.column_name

    def as_where_clause(
        self, *, tb_prefix: str | None, param_prefix: str | None
    ) -> str:
        fq_column_name = self._as_fq_column_name(tb_prefix=tb_prefix)
        param_name = self._as_param_name(param_prefix=param_prefix)
        return self.sql_comparator.as_condition_clause(
            fq_column_name=fq_column_name,
            param_name=param_name,
        )

    def as_params(self, *, param_prefix: str | None) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if self.comparable_value is None:
            # Return if none - this allows false as a valid value (for bool columns)
            return {}
        param_name = self._as_param_name(param_prefix=param_prefix)
        if not param_name:
            return {}
        return {param_name: self.comparable_value}


class SqlSortRankMapping(NamedTuple):
    rank_map: Mapping[ComparableSingularValueType, int | Decimal]
    rank_default: int | Decimal | None = None


SortRankMappingProvider = Callable[[tuple[str, ...]], SqlSortRankMapping | None]


class SqlSort(NamedTuple):
    column_name: str
    order_str: Literal["ASC", "DESC"]
    nulls_order_str: Literal["NULLS FIRST", "NULLS LAST"]
    rank_mapping: SqlSortRankMapping | None = None

    def _as_param_name(self, *, param_prefix: str | None) -> str:
        if not self.column_name:
            raise ValueError("column_name is blank")
        return (
            f"{param_prefix}_{self.column_name}" if param_prefix else self.column_name
        )

    def as_sort_clause_and_param(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, *, tb_prefix: str | None, param_prefix: str | None = None
    ) -> tuple[str, Mapping[str, Any]]:
        fq_column_name = (
            f"{tb_prefix}.{self.column_name}" if tb_prefix else self.column_name
        )
        if not self.rank_mapping or not self.rank_mapping.rank_map:
            return (
                f"{fq_column_name.strip()} {self.order_str.strip()} {self.nulls_order_str.strip()}",
                {},
            )

        params: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        case_clause = ""
        _last_idx = 0
        for idx, (value, rank) in enumerate(self.rank_mapping.rank_map.items()):
            _last_idx = idx
            condition_param_name = self._as_param_name(
                param_prefix=f"{param_prefix}_c_{idx}"
            )
            params[condition_param_name] = value
            rank_param_name = self._as_param_name(
                param_prefix=f"{param_prefix}_r_{idx}"
            )
            params[rank_param_name] = rank
            case_clause += f"WHEN :{condition_param_name} THEN :{rank_param_name} "
        if self.rank_mapping.rank_default is not None:
            default_rank_param_name = self._as_param_name(
                param_prefix=f"{param_prefix}_r_{_last_idx + 1}"
            )
            params[default_rank_param_name] = self.rank_mapping.rank_default
            case_clause += f"ELSE :{default_rank_param_name}"
        return (
            f"CASE {fq_column_name} {case_clause} END {self.order_str.strip()} {self.nulls_order_str.strip()}",
            params,
        )


def cursor_to_offset_limit_with_overflow(cursor: Cursor) -> tuple[int, int]:
    """
    Returns (offset, limit)
    @param cursor:
    @return:
    """
    return ((cursor.page_size * (cursor.page_index - 1)), cursor.page_size + 1)


def value_filter_to_sql_selection(  # noqa: C901, PLR0912
    *,
    value_filter: ValueFilter,
    table_model_tp: type[TableModel],
    param_name_prefix: str,
    field_path_db_column_mapping_provider: FieldPathDBColumnNameMappingProvider,
    enable_json_spec: bool = False,
) -> SqlSelection | None:
    filter_field = value_filter.field
    if not isinstance(filter_field, QualifiedField):
        # don't support relationship field sql translation (since we are not joining)
        logger.debug(
            "Skipping filter - field is not a QualifiedField", field=filter_field
        )
        return None
    if any(not isinstance(field_name, str) for field_name in filter_field.path):
        # don't support relationship field sql translation (since we are not joining)
        logger.debug(
            "Skipping filter - field path contains non-string values",
            field_path=filter_field.path,
        )
        return None

    target_field_path = cast(tuple[str, ...], filter_field.path)
    if not (
        target_column_name := field_path_db_column_mapping_provider(target_field_path)
    ):
        logger.debug(
            "Skipping filter - field path not found in domain_field_name_to_table_columns",
            field_path=target_field_path,
        )
        return None
    if target_column_name not in table_model_tp.column_fields:
        logger.debug(
            "Skipping filter - column not found in table model",
            column=target_column_name,
            table=table_model_tp.__name__,
        )
        return None

    column_info: ColumnFieldInfo = table_model_tp.column_fields[target_column_name]

    if column_info.mapped_column.is_array or (
        column_info.mapped_column.is_json and not enable_json_spec
    ):
        # when the column is an array or json field, it's very tricky to apply the sql translation here (and we likely shouldn't do it)
        logger.debug(
            "Skipping filter - column is an array or json field",
            column=target_column_name,
        )
        return None

    fq_column_name = target_column_name
    param_name = (
        f"{param_name_prefix}_{target_column_name}"
        if param_name_prefix
        else target_column_name
    )

    sql_selection: SqlSelection | None
    match_operator: MatchOperator = value_filter.operator

    column_parsed_type: ParsedType = parse_pydantic_field_type(
        table_model_tp, field_name=target_column_name
    )

    if column_info.mapped_column.is_json:
        match match_operator:
            case MatchOperator.CONTAINS if value_filter.value:
                try:
                    json_filter_value = orjson.dumps([str(value_filter.value)]).decode(
                        "utf-8"
                    )
                    sql_selection = SqlSelection(
                        column_name=fq_column_name,
                        sql_comparator=SqlComparator.JSON_CONTAINS,
                        comparable_value=json_filter_value,
                    )
                except Exception:
                    logger.warning(
                        "Skipping filter - failed to convert value to json",
                        column=target_column_name,
                        value=value_filter.value,
                    )
    else:
        match match_operator:
            case MatchOperator.EQ | MatchOperator.NE:
                sql_selection = parsed_type_to_eq_operator(
                    pt=column_parsed_type,
                    match_operator=match_operator,
                    filter_value=value_filter.value,
                    fq_column_name=fq_column_name,
                    param_name=param_name,
                )
            case (
                MatchOperator.LT
                | MatchOperator.GT
                | MatchOperator.LTE
                | MatchOperator.GTE
            ):
                sql_selection = parsed_type_to_comparison_operator(
                    pt=column_parsed_type,
                    match_operator=match_operator,
                    filter_value=value_filter.value,
                    fq_column_name=fq_column_name,
                    param_name=param_name,
                )
            case MatchOperator.IN | MatchOperator.NIN:
                sql_selection = parsed_type_to_in_operator(
                    pt=column_parsed_type,
                    match_operator=match_operator,
                    filter_value=value_filter.value,
                    fq_column_name=fq_column_name,
                    param_name=param_name,
                )
            case MatchOperator.CONTAINS | MatchOperator.NCONTAINS:
                sql_selection = parsed_type_to_contains_operator(
                    pt=column_parsed_type,
                    match_operator=match_operator,
                    filter_value=value_filter.value,
                    fq_column_name=fq_column_name,
                    param_name=param_name,
                )
            case MatchOperator.STARTS_WITH | MatchOperator.ENDS_WITH:
                sql_selection = parsed_type_to_starts_or_ends_with_operator(
                    pt=column_parsed_type,
                    match_operator=match_operator,
                    filter_value=value_filter.value,
                    fq_column_name=fq_column_name,
                    param_name=param_name,
                )
            case MatchOperator.BLANK | MatchOperator.NBLANK:
                sql_selection = parsed_type_to_blank_operator(
                    pt=column_parsed_type,
                    match_operator=match_operator,
                    fq_column_name=fq_column_name,
                )
            case _ as unreachable:
                assert_never(unreachable)
    return sql_selection


def parsed_type_to_eq_operator(
    *,
    pt: ParsedType,
    match_operator: Literal[MatchOperator.EQ, MatchOperator.NE],
    filter_value: FilterValue,
    fq_column_name: str,
    param_name: str,
) -> SqlSelection | None:
    if filter_value is None:
        return SqlSelection(
            column_name=fq_column_name,
            sql_comparator=SqlComparator.IS_NULL
            if match_operator == MatchOperator.EQ
            else SqlComparator.IS_NOT_NULL,
            comparable_value=None,
        )
    if not (column_base_type := pt.type_if_singular_or_nullable_singular):
        # when there is no base type, means the column is modeled as a json or array field
        # in this case, we can't do sql level equality check, so we skip this filter
        logger.debug(
            "Skipping equality filter - no base type for column",
            column=fq_column_name,
            parsed_type=pt,
        )
        return None
    if not isinstance(filter_value, SingularFilterValue | RelativeTime):
        # when the filter value is not a singular value or relative time, we can't do sql level equality check, so we skip this filter
        logger.debug(
            "Skipping equality filter - value is not a singular value or relative time",
            value=filter_value,
        )
        return None
    _filter_value = (
        filter_value.to_zoned_datetime(tz=UTC)
        if isinstance(filter_value, RelativeTime)
        else filter_value
    )
    _is_comparable, _comparable_value = _to_comparable_value_if_comparable(
        tp=column_base_type, value=_filter_value
    )
    if not _is_comparable:
        logger.debug(
            "Skipping equality filter - value is not comparable with type",
            value=_filter_value,
            type=column_base_type,
        )
        return None
    return SqlSelection(
        column_name=fq_column_name,
        sql_comparator=SqlComparator.EQ
        if match_operator == MatchOperator.EQ
        else SqlComparator.NE,
        comparable_value=_comparable_value,
    )


def parsed_type_to_comparison_operator(
    *,
    pt: ParsedType,
    match_operator: Literal[
        MatchOperator.LT, MatchOperator.GT, MatchOperator.LTE, MatchOperator.GTE
    ],
    filter_value: FilterValue,
    fq_column_name: str,
    param_name: str,
) -> SqlSelection | None:
    if filter_value is None:
        # when the filter value is None, we can't do sql level comparison, so we skip this filter
        logger.debug(
            "Skipping comparison filter - value is None", column=fq_column_name
        )
        return None
    if not (column_base_type := pt.type_if_singular_or_nullable_singular):
        # when there is no base type, means the column is modeled as a json or array field
        # in this case, we can't do sql level comparison, so we skip this filter
        logger.debug(
            "Skipping comparison filter - no base type for column",
            column=fq_column_name,
            parsed_type=pt,
        )
        return None
    if not isinstance(filter_value, SingularFilterValue | RelativeTime):
        # when the filter value is not a singular value or relative time, we can't do sql level comparison, so we skip this filter
        logger.debug(
            "Skipping comparison filter - value is not a singular value or relative time",
            value=filter_value,
        )
        return None
    _filter_value = (
        filter_value.to_zoned_datetime(tz=UTC)
        if isinstance(filter_value, RelativeTime)
        else filter_value
    )
    _is_comparable, _comparable_value = _to_comparable_value_if_comparable(
        tp=column_base_type, value=_filter_value
    )
    if not _is_comparable:
        logger.debug(
            "Skipping comparison filter - value is not comparable with type",
            value=_filter_value,
            type=column_base_type,
        )
        return None
    comparison_operator: SqlComparator
    match match_operator:
        case MatchOperator.LT:
            comparison_operator = SqlComparator.LT
        case MatchOperator.GT:
            comparison_operator = SqlComparator.GT
        case MatchOperator.LTE:
            comparison_operator = SqlComparator.LTE
        case MatchOperator.GTE:
            comparison_operator = SqlComparator.GTE
        case _ as unreachable:
            assert_never(unreachable)
    return SqlSelection(
        column_name=fq_column_name,
        sql_comparator=comparison_operator,
        comparable_value=_comparable_value,
    )


def parsed_type_to_in_operator(
    *,
    pt: ParsedType,
    match_operator: Literal[MatchOperator.IN, MatchOperator.NIN],
    filter_value: FilterValue,
    fq_column_name: str,
    param_name: str,
) -> SqlSelection | None:
    if filter_value is None:
        # when the filter value is None, we can't do sql level in check, so we skip this filter
        logger.debug("Skipping IN filter - value is None", column=fq_column_name)
        return None
    if not (column_base_type := pt.type_if_singular_or_nullable_singular):
        # when there is no base type, means the column is modeled as a json or array field
        # in this case, we can't do sql level in check, so we skip this filter
        logger.debug(
            "Skipping IN filter - no base type for column",
            column=fq_column_name,
            parsed_type=pt,
        )
        return None
    if not isinstance(filter_value, set):
        # when the filter value is not a set, there is nothing to check contains against, so we skip this filter
        logger.debug("Skipping IN filter - value is not a set", value=filter_value)
        return None
    if not filter_value:
        # when the filter value is an empty set, we simply return true for NIN and false for IN
        return SqlSelection(
            column_name=fq_column_name,
            sql_comparator=SqlComparator.TRUE
            if match_operator == MatchOperator.NIN
            else SqlComparator.FALSE,
            comparable_value=None,
        )
    _comparable_values = []
    for set_value in filter_value:
        _is_comparable, _comparable_value = _to_comparable_value_if_comparable(
            tp=column_base_type, value=set_value
        )
        if not _is_comparable:
            logger.debug(
                "Skipping IN filter - value is not comparable with type",
                value=set_value,
                type=column_base_type,
            )
            return None
        _comparable_values.append(_comparable_value)
    return SqlSelection(
        column_name=fq_column_name,
        sql_comparator=SqlComparator.IN
        if match_operator == MatchOperator.IN
        else SqlComparator.NOT_IN,
        comparable_value=_comparable_values,
    )


def parsed_type_to_contains_operator(
    *,
    pt: ParsedType,
    match_operator: Literal[MatchOperator.CONTAINS, MatchOperator.NCONTAINS],
    filter_value: FilterValue,
    fq_column_name: str,
    param_name: str,
) -> SqlSelection | None:
    if filter_value is None:
        # when the filter value is None, nothing will contains it, so we skip this filter
        logger.debug("Skipping CONTAINS filter - value is None", column=fq_column_name)
        return None
    if not (column_base_type := pt.type_if_singular_or_nullable_singular):
        # when there is no base type, means the column is modeled as a json or array field
        # in this case, we can't do sql level contains check, so we skip this filter
        logger.debug(
            "Skipping CONTAINS filter - no base type for column",
            column=fq_column_name,
            parsed_type=pt,
        )
        return None
    if not isinstance(filter_value, str):
        # when the filter value is not a string, we can't do sql level contains check, so we skip this filter
        logger.debug(
            "Skipping CONTAINS filter - value is not a string", value=filter_value
        )
        return None
    _is_comparable, _comparable_value = _to_comparable_value_if_comparable(
        tp=column_base_type, value=filter_value
    )
    if not _is_comparable:
        # when the filter value is not of the same type as the column, we can't do sql level contains check, so we skip this filter
        logger.debug(
            "Skipping CONTAINS filter - value is not comparable with type",
            value=filter_value,
            type=column_base_type,
        )
        return None
    return SqlSelection(
        column_name=fq_column_name,
        sql_comparator=SqlComparator.ILIKE
        if match_operator == MatchOperator.CONTAINS
        else SqlComparator.NOT_ILIKE,
        comparable_value=f"%{_comparable_value}%",
    )


def parsed_type_to_starts_or_ends_with_operator(
    *,
    pt: ParsedType,
    match_operator: Literal[MatchOperator.STARTS_WITH, MatchOperator.ENDS_WITH],
    filter_value: FilterValue,
    fq_column_name: str,
    param_name: str,
) -> SqlSelection | None:
    if filter_value is None:
        # when the filter value is None, nothing will start or end with it, so we skip this filter
        logger.debug(
            "Skipping STARTS/ENDS WITH filter - value is None", column=fq_column_name
        )
        return None
    if not (column_base_type := pt.type_if_singular_or_nullable_singular):
        # when there is no base type, means the column is modeled as a json or array field
        # in this case, we can't do sql level starts or ends with check, so we skip this filter
        logger.debug(
            "Skipping STARTS/ENDS WITH filter - no base type for column",
            column=fq_column_name,
            parsed_type=pt,
        )
        return None
    if not isinstance(filter_value, str):
        # when the filter value is not a string, we can't do sql level starts or ends with check, so we skip this filter
        logger.debug(
            "Skipping STARTS/ENDS WITH filter - value is not a string",
            value=filter_value,
        )
        return None
    _is_comparable, _comparable_value = _to_comparable_value_if_comparable(
        tp=column_base_type, value=filter_value
    )
    if not _is_comparable:
        # when the filter value is not of the same type as the column, we can't do sql level starts or ends with check, so we skip this filter
        logger.debug(
            "Skipping STARTS/ENDS WITH filter - value is not comparable with type",
            value=filter_value,
            type=column_base_type,
        )
        return None
    return SqlSelection(
        column_name=fq_column_name,
        sql_comparator=SqlComparator.ILIKE,
        comparable_value=(
            f"{_comparable_value}%"
            if match_operator == MatchOperator.STARTS_WITH
            else f"%{_comparable_value}"
        ),
    )


def parsed_type_to_blank_operator(
    *,
    pt: ParsedType,
    match_operator: Literal[MatchOperator.BLANK, MatchOperator.NBLANK],
    fq_column_name: str,
) -> SqlSelection | None:
    if not (pt.type_if_singular_or_nullable_singular):
        # when there is no base type, means the column is modeled as a json or array field
        # in this case, we can't do sql level blank check, so we skip this filter
        logger.debug(
            "Skipping BLANK filter - no base type for column",
            column=fq_column_name,
            parsed_type=pt,
        )
        return None
    match match_operator:
        case MatchOperator.BLANK:
            return SqlSelection(
                column_name=fq_column_name,
                sql_comparator=SqlComparator.IS_NULL,
                comparable_value=None,
            )
        case MatchOperator.NBLANK:
            return SqlSelection(
                column_name=fq_column_name,
                sql_comparator=SqlComparator.IS_NOT_NULL,
                comparable_value=None,
            )
        case _ as unreachable:
            assert_never(unreachable)


def sort_spec_to_sql_sort_clause(
    *,
    sort_spec: NonRelationalSortingSpec,
    table_model_tp: type[TableModel],
    field_path_db_column_mapping_provider: FieldPathDBColumnNameMappingProvider,
    sort_rank_mapping_provider: SortRankMappingProvider | None = None,
) -> tuple[SqlSort, ...] | None:
    sort_clauses: list[SqlSort] = []
    for sorter in sort_spec.ordered_sorters:
        if sort_clause := sort_to_sql_sort_clause(
            sorter=sorter,
            table_model_tp=table_model_tp,
            field_path_db_column_mapping_provider=field_path_db_column_mapping_provider,
            sort_rank_mapping_provider=sort_rank_mapping_provider,
        ):
            sort_clauses.append(sort_clause)
    return tuple(sort_clauses) if sort_clauses else None


def sort_to_sql_sort_clause(  # noqa: PLR0911
    *,
    sorter: NonRelationalSorter,
    table_model_tp: type[TableModel],
    field_path_db_column_mapping_provider: FieldPathDBColumnNameMappingProvider,
    sort_rank_mapping_provider: SortRankMappingProvider | None = None,
) -> SqlSort | None:
    filter_field = sorter.field
    if any(not isinstance(field_name, str) for field_name in filter_field.path):
        # don't support relationship field sql translation (since we are not joining)
        return None

    target_field_path = cast(tuple[str, ...], filter_field.path)
    if not (
        target_column_name := field_path_db_column_mapping_provider(target_field_path)
    ):
        return None
    if target_column_name not in table_model_tp.column_fields:
        return None

    column_info: ColumnFieldInfo = table_model_tp.column_fields[target_column_name]
    if column_info.mapped_column.is_array or column_info.mapped_column.is_json:
        # when the column is an array or json field, it's very tricky to apply the sql translation here (and we likely shouldn't do it)
        return None
    column_parsed_type: ParsedType = parse_pydantic_field_type(
        table_model_tp, field_name=target_column_name
    )
    if not (
        column_base_type := column_parsed_type.type_if_singular_or_nullable_singular
    ):
        # when there is no base type, means the column is modeled as a json or array field
        # in this case, we can't do sql level sorting, so we skip this filter
        return None

    if column_base_type not in _sortable_types:
        return None

    sort_rank_mapping = (
        sort_rank_mapping_provider(target_field_path)
        if sort_rank_mapping_provider
        else None
    )

    fq_column_name = target_column_name
    order_str: Literal["ASC", "DESC"]
    match sorter.order:
        case OrderEnum.ASC:
            order_str = "ASC"
        case OrderEnum.DESC:
            order_str = "DESC"
        case _ as unreachable:
            assert_never(unreachable)
    return SqlSort(
        column_name=fq_column_name,
        order_str=order_str,
        nulls_order_str="NULLS FIRST" if sorter.null_first else "NULLS LAST",
        rank_mapping=sort_rank_mapping,
    )


# must include UUID here to support sorting by id fields such as stage_id.
# todo(xw): think of a better way to do it.
_sortable_types = {int, float, str, bool, datetime, Decimal, date, AwareDatetime, UUID}


def _is_convertible_to_decimal(s: str) -> bool:
    try:
        Decimal(s.strip())  # Handles leading/trailing whitespace
        return True
    except Exception:
        return False


def _to_comparable_value_if_comparable(  # type: ignore[explicit-any] # TODO: fix-any-annotation # noqa: PLR0911
    *, tp: type | None, value: Any
) -> tuple[bool, Any]:
    if tp is None:
        return False, value
    if isinstance(value, tp):
        return True, value
    if isinstance(value, date) and (
        tp is datetime or tp is date or tp is AwareDatetime
    ):
        return True, value
    if isinstance(value, datetime) and (tp is datetime or tp is AwareDatetime):
        return True, value
    if tp is Decimal:
        if isinstance(value, int | float):
            return True, Decimal(str(value))
        if isinstance(value, str) and _is_convertible_to_decimal(value):
            return True, Decimal(value)
    return isinstance(value, SingularFilterValue | NoneType), value
