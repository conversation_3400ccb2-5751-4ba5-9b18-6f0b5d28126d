import asyncio
import inspect
import types
import typing
from collections.abc import Sequence
from dataclasses import dataclass
from typing import Annotated, Any, ClassVar, Final, Self, TypeVar
from uuid import UUID

from more_itertools import chunked
from pydantic import BaseModel, ConfigDict, SecretStr, TypeAdapter
from pydantic.fields import Field, FieldInfo
from sqlalchemy import Row

from salestech_be.common.type.patch_request import (
    AbstractUnsetAwareModel,
    AbstractUnsetAwareT,
    is_unset_type,
    specified,
)
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now

DBModelT = TypeVar("DBModelT", bound="DBModel")
AnyT = TypeVar("AnyT", bound=Any)  # type: ignore[explicit-any] # TODO: fix-any-annotation

_default_parallelism_chunk_size: Final[int] = 100


def from_row(adapter: TypeAdapter[AnyT], row: Row[Any]) -> AnyT:  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """
    Convert a row object to an arbitrary object that can be
    deserialized via pydantic TypeAdapter.

    See https://docs.pydantic.dev/latest/concepts/type_adapter/
    """
    return adapter.validate_python(row._asdict())


class DBModel(BaseModel):
    """Base model to interact directly with DB IO."""

    model_config = ConfigDict(
        frozen=True,
        # recent version of pydantic has changed behavior of use_enum_values
        # when set to True, enum fields will be deserlized as strings
        # instead of the enum value. we have to explicitly set it to False
        # to get the old behavior.
        use_enum_values=False,
        validate_default=True,
    )
    _type_adapter: ClassVar[TypeAdapter[list[Self]]]

    @classmethod
    def __pydantic_init_subclass__(cls: type[Self], **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__pydantic_init_subclass__(**kwargs)
        cls._type_adapter = TypeAdapter(list[cls])  # type: ignore[valid-type]

    @classmethod
    def model_required_fields(cls) -> set[str]:
        return {
            f_name
            for f_name, f_info in cls.model_fields.items()
            if f_info.is_required()
        }

    @classmethod
    def from_row(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cls: type[DBModelT],
        r: Row[Any],
        *,
        column_alias_prefix: str | None = None,
        additional_cols: dict[str, Any] | None = None,
    ) -> DBModelT:
        result_dict: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        d = r._asdict()
        if not column_alias_prefix:
            result_dict = d
        else:
            for k, v in d.items():
                if k.startswith(column_alias_prefix):
                    result_dict[k[len(column_alias_prefix) :]] = v
        if additional_cols:
            result_dict.update(additional_cols)
        return cls.model_validate(result_dict)

    @classmethod
    def from_row_or_none(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cls: type[DBModelT],
        r: Row[Any] | None,
        *,
        column_alias_prefix: str | None = None,
        additional_cols: dict[str, Any] | None = None,
    ) -> DBModelT | None:
        if not r:
            return None
        return cls.from_row(
            r,
            column_alias_prefix=column_alias_prefix,
            additional_cols=additional_cols,
        )

    @classmethod
    def from_row_if_exists(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cls: type[DBModelT], r: Row[Any], *, column_alias_prefix: str | None = None
    ) -> DBModelT | None:
        result_dict: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        for k, v in r._asdict().items():
            if column_alias_prefix:
                if k.startswith(column_alias_prefix):
                    result_dict[k[len(column_alias_prefix) :]] = v
            else:
                result_dict[k] = v
        if all(v is None for v in result_dict.values()):
            # todo: this isn't 100% accurate, since someone could really
            # create a pydantic model with all None values acceptable.
            return None
        return cls.model_validate(result_dict)

    @classmethod
    def _bulk_from_rows_sync(cls, rows: Sequence[Row[Any]]) -> list[Self]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return cls._type_adapter.validate_python(rows, from_attributes=True)

    @classmethod
    async def bulk_from_rows(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cls,
        *,
        rows: Sequence[Row[Any]],
        _parallelism_chunk_size: int = _default_parallelism_chunk_size,
    ) -> list[Self]:
        if not rows:
            return []

        results: list[Self] = []
        for chunk in chunked(rows, _parallelism_chunk_size):
            results.extend(await asyncio.to_thread(cls._bulk_from_rows_sync, chunk))
            await asyncio.sleep(0)
        return results


@dataclass
class MappedColumn:
    """Indicate if a field is mapped to a column.
    In the future, this may also contain additional column metadata.
    """

    is_json: bool = False
    # https://www.postgresql.org/docs/current/arrays.html
    is_array: bool = False


TypeT = TypeVar("TypeT", bound=Any)  # type: ignore[explicit-any] # TODO: fix-any-annotation
Column = Annotated[TypeT, MappedColumn()]  # type: ignore[explicit-any] # TODO: fix-any-annotation
JsonColumn = Annotated[TypeT, MappedColumn(is_json=True)]  # type: ignore[explicit-any] # TODO: fix-any-annotation
ArrayColumn = Annotated[TypeT, MappedColumn(is_array=True)]  # type: ignore[explicit-any] # TODO: fix-any-annotation


class ColumnFieldInfo(BaseModel):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    mapped_column: MappedColumn
    field_info: FieldInfo


class TableModel(DBModel):
    table_name: ClassVar[TableName] = TableName.unknown
    schema_name: ClassVar[str] = "public"
    fq_table_name: ClassVar[str] = ""
    column_fields: ClassVar[dict[str, ColumnFieldInfo]] = {}
    ordered_column_names: ClassVar[tuple[str, ...]] = ()
    ordered_primary_keys: ClassVar[tuple[str, ...]] = ()
    is_base_table: ClassVar[bool] = False

    # def model_post_init(self, __context: Any) -> None:
    #     if self.is_base_table:
    #         raise ValueError(f"Cannot instantiate base table model {self.__class__}")

    def insert_bind_params(self) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return {
            field_name: (
                getattr(self, field_name).get_secret_value()
                if isinstance(getattr(self, field_name), SecretStr)
                else getattr(self, field_name)
            )
            for field_name in self.column_fields
        }

    def primary_key_to_value(self) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        return {k: getattr(self, k) for k in self.ordered_primary_keys}

    @classmethod
    def validate_contains_all_primary_keys(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cls, columns: list[str] | dict[str, Any]
    ) -> None:
        if set(cls.ordered_primary_keys) != set(columns):
            raise ValueError(
                f"primary keys {cls.ordered_primary_keys} "
                f"must all be provided in {columns}",
            )

    @classmethod
    def validate_in_columns(cls, columns: list[str] | dict[str, Any]) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        for col in columns:
            if col not in cls.column_fields:
                raise ValueError(f"{col} is not a column for {cls.__name__}")

    @classmethod
    def get_json_columns(cls) -> list[str]:
        return [f for f, v in cls.column_fields.items() if v.mapped_column.is_json]

    @classmethod
    def array_columns(cls) -> list[str]:
        return [f for f, v in cls.column_fields.items() if v.mapped_column.is_array]

    @classmethod
    def get_column_names_with_alias(
        cls, *, table_alias: str, column_alias_prefix: str
    ) -> list[str]:
        return [
            f"{table_alias}.{col} AS {column_alias_prefix}{col}"
            for col in cls.ordered_column_names
        ]

    @classmethod
    def get_table_name(cls) -> TableName:
        table_name = getattr(cls, "table_name", TableName.unknown)
        if table_name == TableName.unknown:
            raise ValueError(f"Table name is not set for {cls.__class__}")
        return table_name

    @classmethod
    def __pydantic_init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__pydantic_init_subclass__(**kwargs)
        if cls.is_base_table:
            return
        if (not cls.table_name) or cls.table_name == TableName.unknown:
            raise TypeError(f"table_name of class {cls.__name__} must be set!")
        cls.fq_table_name = f"{cls.schema_name}.{cls.table_name}"
        cls.column_fields = cls.__get_column_fields()
        cls.__validate_primary_keys()
        cls.ordered_column_names = tuple(sorted(cls.column_fields.keys()))

    @classmethod
    def __validate_primary_keys(cls) -> None:
        if not cls.ordered_primary_keys:
            raise TypeError(
                f"ordered_primary_keys of class {cls.__name__} must be set!",
            )
        for pk in cls.ordered_primary_keys:
            if pk not in cls.column_fields:
                raise TypeError(
                    f"specified primary key ({pk}) doesn't have a matched "
                    f"column field defined!",
                )

    @classmethod
    def __get_column_fields(cls) -> dict[str, ColumnFieldInfo]:
        found: dict[str, ColumnFieldInfo] = {}
        for fn, info in cls.model_fields.items():
            column_metadata_list = [
                m for m in info.metadata if isinstance(m, MappedColumn)
            ]
            if len(column_metadata_list) > 1:
                raise TypeError(
                    f"cannot provide more than 1 MappedColumn metadata "
                    f"to a field: {cls.__name__}.{fn}",
                )
            elif len(column_metadata_list) == 1:
                found[fn] = ColumnFieldInfo(
                    mapped_column=column_metadata_list[0],
                    field_info=info,
                )

        return found

    @classmethod
    def __to_sql_column_list(
        cls,
        column_names: tuple[str, ...] | list[str],
    ) -> tuple[str, ...]:
        return column_names if isinstance(column_names, tuple) else tuple(column_names)

    @classmethod
    def __to_sql_column_param_list(
        cls,
        column_names: tuple[str, ...] | list[str],
    ) -> tuple[str, ...]:
        return tuple(f":{col}" for col in column_names)

    @classmethod
    def insert_sql_column_list(cls) -> tuple[str, ...]:
        return cls.__to_sql_column_list(cls.ordered_column_names)

    @classmethod
    def insert_sql_column_param_list(cls) -> tuple[str, ...]:
        return cls.__to_sql_column_param_list(cls.ordered_column_names)

    @classmethod
    def primary_key_column_list(cls) -> tuple[str, ...]:
        return cls.__to_sql_column_list(cls.ordered_primary_keys)


class SysTableModel(TableModel):
    is_base_table = True

    id: Column[UUID]
    # you don't have to set this field when inserting / updating to DB
    # DB will automatically populate it.
    # when test equality, you will have to exclude it.
    sys_updated_at: Column[ZoneRequiredDateTime] = Field(default_factory=zoned_utc_now)

    @classmethod
    def __pydantic_init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        cls.is_base_table = False
        super().__pydantic_init_subclass__(**kwargs)


class ColumnParity(BaseModel):
    model_config = ConfigDict(frozen=True)
    excluded_columns: frozenset[str] = Field(default_factory=frozenset)


class TableBoundedModel(
    AbstractUnsetAwareModel[AbstractUnsetAwareT], typing.Generic[AbstractUnsetAwareT]
):
    __table_model__: type[AbstractUnsetAwareT] = TableModel  # type: ignore[assignment]

    # If column_parity is set, then
    # 1. subclass must declare all the columns excluding the ones in
    #   'excluded_columns' from source table model.
    # 2. subclass must not declare any columns that are in the 'excluded_columns'.
    column_parity: ClassVar[ColumnParity | None] = None

    @classmethod
    def table_model(cls) -> type[AbstractUnsetAwareT]:
        return cls.__table_model__

    def __init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__init_subclass__(**kwargs)
        cls._validate_same_module_with_db_module()

    @classmethod
    def __pydantic_init_subclass__(cls, **kwargs: Any) -> None:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        super().__pydantic_init_subclass__(**kwargs)
        cls._populate_table_model()
        cls._validate_model_fields_against_table_model()

    @classmethod
    def _populate_table_model(cls) -> None:
        generic_origin = cls.__pydantic_generic_metadata__.get("origin", None)
        if not generic_origin:
            return
        if not issubclass(cls, generic_origin):
            raise TypeError(f"{cls.__name__} must be a subclass of {generic_origin})")
        generic_args = cls.__pydantic_generic_metadata__.get("args", ())
        if not generic_args:
            raise TypeError(f"{cls.__name__} must have generic args")
        first_generic_arg = generic_args[0]
        if inspect.isclass(first_generic_arg):
            if not issubclass(first_generic_arg, TableModel):
                raise TypeError(
                    f"{cls.__name__} must have a generic arg that is a subclass of TableModel",
                )
        elif not isinstance(first_generic_arg, TypeVar):
            raise TypeError(
                f"{cls.__name__} the generic arg can only be a TypeVar or a subclass of TableModel"
            )
        cls.__table_model__ = first_generic_arg

    @classmethod
    def _validate_same_module_with_db_module(cls) -> None:
        if isinstance(cls.__table_model__, TypeVar):
            return
        if cls.__table_model__.__module__ != cls.__module__:
            raise TypeError(
                f"table_model of class {cls.__name__} must be in the same module with "
                f"the class. This is to ensure code is organized properly.",
            )

    @classmethod
    def _validate_model_fields_against_table_model(cls) -> None:  # noqa: C901
        if isinstance(cls.__table_model__, TypeVar):
            return
        if cls.__table_model__ is TableModel:
            raise TypeError(
                f"table_model of class {cls.__name__} must be overridden to a subclass of "
                f"DBModel",
            )
        if not issubclass(cls.__table_model__, TableModel):
            raise TypeError(
                f"table_model of class {cls.__name__} must be a subclass of DBModel",
            )
        table_column_fields: dict[str, ColumnFieldInfo] = (
            cls.__table_model__.column_fields
        )

        # we don't need to validate excluded columns against source table
        if cls.column_parity and (
            missing_columns := table_column_fields.keys()
            - cls.model_fields.keys()
            - cls.column_parity.excluded_columns
        ):
            raise TypeError(
                f"{missing_columns} are not declared to be in parity with source table"
                f" model {cls.__table_model__.__name__}"
            )
        if cls.column_parity and (
            extra_columns := {
                extra_column
                for extra_column in cls.model_fields
                if extra_column in cls.column_parity.excluded_columns
            }
        ):
            raise TypeError(
                f"source table model {cls.__table_model__.__name__} has extra columns "
                f"that are declared in cls.column_parity.extra_columns: {extra_columns}"
            )

        table_model_type_hints = typing.get_type_hints(cls.__table_model__)
        my_class_type_hints = typing.get_type_hints(cls)
        for f_name in cls.model_fields:
            # step 1, validate field name
            if f_name not in table_column_fields:
                raise TypeError(
                    f"field {f_name} in {cls.__name__} is not found in "
                    f"{cls.__table_model__.__name__}",
                )
            # step 2, validate field type
            my_field_type_hints = my_class_type_hints.get(f_name)

            if (
                typing.get_origin(my_field_type_hints) == typing.Union
                or type(my_field_type_hints) is types.UnionType
            ):
                my_field_type_hints = typing.get_args(my_field_type_hints) or (
                    my_field_type_hints,
                )
            else:
                my_field_type_hints = (my_field_type_hints,)
            my_field_type_hints = tuple(
                th for th in my_field_type_hints if not is_unset_type(th)
            )
            db_field_type_hints = table_model_type_hints.get(f_name)

            if (
                typing.get_origin(db_field_type_hints) == typing.Union
                or type(db_field_type_hints) is types.UnionType
            ):
                db_field_type_hints = typing.get_args(db_field_type_hints) or (
                    db_field_type_hints,
                )
            else:
                db_field_type_hints = (db_field_type_hints,)
            if set(my_field_type_hints) - set(db_field_type_hints):
                raise TypeError(
                    f"field {f_name} in {cls.__name__} has different type "
                    f"from {cls.__table_model__.__name__}",
                    f"expected within {db_field_type_hints}, got {my_field_type_hints}",
                )

    def flatten_specified_values(self) -> dict[str, Any]:  # type: ignore[explicit-any] # TODO: fix-any-annotation
        """
        Flatten the specified values of the model into a dictionary.
        !!!NOTE!!! This is different from model_dump. This function WILL NOT dump all fields
        recursively into dictionary. By design, it will:
        1. return fields <> value pairs as long as they are not UNSET
        2. this means, if a field has a default value set, then it will be included as well.
        """
        result: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        for f_name in self.model_fields:
            if specified(f_value := getattr(self, f_name)):
                result[f_name] = f_value
        for cf_name in self.model_computed_fields:
            if specified(cf_value := getattr(self, cf_name)):
                result[cf_name] = cf_value
        return result


class ConditionalUpdateResult(BaseModel, typing.Generic[DBModelT]):
    record: DBModelT
    is_updated: bool
