from enum import StrEnum
from typing import ClassVar
from uuid import UUID

from salestech_be.common.type.patch_request import UNSET, UnsetAware
from salestech_be.db.models.core.base import Column, TableBoundedModel, TableModel
from salestech_be.db.models.core.constants import TableName
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime


class MailboxWarmUpService(StrEnum):
    MAILIVERY = "MAILIVERY"


class MailboxWarmUpStatus(StrEnum):
    CANCELLED = "CANCELLED"
    IN_PROGRESS = "IN_PROGRESS"
    PAUSED = "PAUSED"
    COMPLETED = "COMPLETED"


class MailboxWarmUpSpeed(StrEnum):
    SLOW = "slow"  # 60 days
    NORMAL = "normal"  # 30 days
    FAST = "fast"  # 14 days


class MailboxWarmUpSpeedDays:
    """Maps warmup speed to number of days."""

    SPEED_TO_DAYS: ClassVar[dict[MailboxWarmUpSpeed, int]] = {
        MailboxWarmUpSpeed.SLOW: 60,
        MailboxWarmUpSpeed.NORMAL: 30,
        MailboxWarmUpSpeed.FAST: 14,
    }

    @classmethod
    def get_days(cls, speed: MailboxWarmUpSpeed) -> int:
        return cls.SPEED_TO_DAYS[speed]


class EmailAccountWarmUpCampaign(TableModel):
    """EmailAccountWarmUpCampaign table model."""

    table_name = TableName.email_account_warm_up_campaign
    ordered_primary_keys = ("id",)

    id: Column[UUID]
    domain_id: Column[UUID | None]
    email_per_day: Column[int]
    response_rate: Column[int]
    email_account_id: Column[UUID]  # The email account to be warmuped up
    requested_by_user_id: Column[UUID]
    requested_at: Column[ZoneRequiredDateTime]  # When the request happened
    warm_up_service: Column[MailboxWarmUpService] = MailboxWarmUpService.MAILIVERY
    external_id: Column[str | None]  # Mailvery campaign id
    status: Column[MailboxWarmUpStatus]
    with_rampup: Column[bool] = False
    rampup_speed: Column[MailboxWarmUpSpeed]
    organization_id: Column[UUID]

    created_at: Column[ZoneRequiredDateTime]
    created_by_user_id: Column[UUID]
    updated_at: Column[ZoneRequiredDateTime | None] = None
    updated_by_user_id: Column[UUID | None] = None
    deleted_at: Column[ZoneRequiredDateTime | None] = None
    deleted_by_user_id: Column[UUID | None] = None
    is_mock_record: Column[bool] = False
    last_started_at: Column[ZoneRequiredDateTime | None] = None


class EmailAccountWarmUpCampaignUpdate(TableBoundedModel[EmailAccountWarmUpCampaign]):
    status: UnsetAware[MailboxWarmUpStatus] = UNSET
    email_per_day: UnsetAware[int] = UNSET
    response_rate: UnsetAware[int] = UNSET
    with_rampup: UnsetAware[bool] = UNSET
    rampup_speed: UnsetAware[MailboxWarmUpSpeed] = UNSET

    last_started_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    updated_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    updated_by_user_id: UnsetAware[UUID | None] = UNSET
    deleted_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    deleted_by_user_id: UnsetAware[UUID | None] = UNSET
