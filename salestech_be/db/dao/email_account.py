from collections import defaultdict
from typing import Any
from uuid import UUID

from pydantic import EmailStr
from sqlalchemy import bindparam, text

from salestech_be.common.exception.exception import ResourceNotFoundError
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.core.common.accounts_receivable import AccountsReceivable
from salestech_be.core.common.types import UserAuthContext
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.dto.email_dto import EmailAccountPoolWithMembershipsDTO
from salestech_be.db.models.email_account import (
    EmailAccount,
    EmailAccountHealth,
    EmailAccountPool,
    EmailAccountPoolMembership,
    EmailAccountSlotAllocation,
    EmailAccountType,
    EmailProvider,
    WarmupHealth,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

logger = get_logger(__name__)


class EmailAccountRepository(GenericRepository):
    async def find_accounts_by_ids(
        self,
        organization_id: UUID,
        email_account_ids: list[UUID],
        include_archived: bool = False,
    ) -> list[EmailAccount]:
        if not email_account_ids:
            return []

        # find active and archived email accounts, but not deleted
        if include_archived:
            stmt = text(
                """
                select * from email_account where id in :email_account_ids
                and organization_id = :organization_id
                and deleted_at is null
                """
            ).bindparams(
                bindparam("email_account_ids", email_account_ids, expanding=True),
                organization_id=organization_id,
            )
        else:
            stmt = text(
                """
                select * from email_account where id in :email_account_ids
                and organization_id = :organization_id
                and deleted_at is null
                and archived_at is null
                """
            ).bindparams(
                bindparam("email_account_ids", email_account_ids, expanding=True),
                organization_id=organization_id,
            )

        rows = await self.engine.all(stmt)
        return await EmailAccount.bulk_from_rows(rows=rows)

    async def find_accounts_by_emails(
        self,
        organization_id: UUID,
        email_list: list[EmailStr],
    ) -> list[EmailAccount]:
        stmt = text(
            """
            select * from email_account where email in :email_list
            and organization_id = :organization_id
            and deleted_at is null
            """
        ).bindparams(
            bindparam("email_list", email_list, expanding=True),
            organization_id=organization_id,
        )

        rows = await self.engine.all(stmt)
        return await EmailAccount.bulk_from_rows(rows=rows)

    async def find_accounts_by_domain_id(
        self,
        organization_id: UUID,
        outbound_domain_id: UUID,
    ) -> list[EmailAccount]:
        return await self._find_by_column_values(
            EmailAccount,
            organization_id=organization_id,
            outbound_domain_id=outbound_domain_id,
        )

    async def find_account_by_id_including_archived(
        self, email_account_id: UUID
    ) -> EmailAccount | None:
        return await self._find_unique_by_column_values(
            EmailAccount, id=email_account_id, exclude_deleted_or_archived=False
        )

    async def find_accounts_by_organization_id(
        self,
        organization_id: UUID,
        include_archived: bool = False,
    ) -> list[EmailAccount]:
        # find active and archived email accounts, but not deleted
        if include_archived:
            stmt = text(
                """
                select * from email_account where organization_id = :organization_id
                and deleted_at is null
                """
            ).bindparams(
                organization_id=organization_id,
            )
            rows = await self.engine.all(stmt)
            return await EmailAccount.bulk_from_rows(rows=rows)
        else:
            return await self._find_by_column_values(
                EmailAccount,
                organization_id=organization_id,
                deleted_at=None,
            )

    async def get_number_of_outbound_accounts_by_organization_id(
        self,
        organization_id: UUID,
    ) -> int:
        """
        Get the number of outbound mailboxes for an organization.
        Transaction type does not matter since we're finding ALL outbound accounts.
        TODO: @Benson if/when we pivot off of Infraforge, we may need to change vendor='INFRAFORGE'.
        """
        stmt = text(
            """
            SELECT COUNT(*)
            FROM email_account
            WHERE organization_id = :organization_id
                AND type = :type
                AND vendor = :vendor
                AND deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
            type=EmailAccountType.OUTBOUND,
            vendor=EmailProvider.INFRAFORGE,
        )
        rows = await self.engine.one(stmt)
        return int(rows[0])

    async def find_outbound_account_by_email_and_domain_id(
        self,
        organization_id: UUID,
        outbound_domain_id: UUID,
        email: EmailStr,
    ) -> EmailAccount | None:
        return await self._find_unique_by_column_values(
            EmailAccount,
            organization_id=organization_id,
            outbound_domain_id=outbound_domain_id,
            email=email,
        )

    async def find_pending_email_accounts_by_domain_id(
        self,
        outbound_domain_id: UUID,
        organization_id: UUID,
    ) -> list[EmailAccount]:
        return await self._find_by_column_values(
            EmailAccount,
            organization_id=organization_id,
            outbound_domain_id=outbound_domain_id,
            external_id=None,
        )

    async def get_number_of_plan_included_outbound_accounts_by_organization_id(
        self,
        organization_id: UUID,
    ) -> int:
        """
        Get the number of plan included outbound mailboxes for an organization.
        Should be up to 2 for the standard plan.
        TODO: @Benson if/when we pivot off of Infraforge, we may need to change vendor='INFRAFORGE'.
        """
        stmt = text(
            """
            SELECT COUNT(*)
            FROM email_account
            WHERE organization_id = :organization_id
                AND type = :type
                AND vendor = :vendor
                AND transaction_type = :transaction_type
                AND deleted_at IS NULL
            """
        ).bindparams(
            organization_id=organization_id,
            type=EmailAccountType.OUTBOUND,
            vendor=EmailProvider.INFRAFORGE,
            transaction_type=AccountsReceivable.INCLUDED_IN_PLAN,
        )
        rows = await self.engine.one(stmt)
        return int(rows[0])

    async def create_email_account(self, email_account: EmailAccount) -> EmailAccount:
        return await self.insert(email_account)

    async def update_email_account(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, email_account_id: UUID, column_to_update: dict[str, Any]
    ) -> EmailAccount | None:
        return await self.update_by_primary_key(
            EmailAccount,
            primary_key_to_value={"id": email_account_id},
            column_to_update=column_to_update,
        )

    async def find_imap_account_ids(self) -> list[UUID]:
        stmt = text(
            """
            select distinct id from email_account
            where imap_username is not null
            and imap_password is not null
            and deleted_at is null
            """
        )

        rows = await self.engine.all(stmt)
        return [UUID(str(row[0])) for row in rows] if rows else []

    async def find_accounts_by_owner_user_id(
        self,
        organization_id: UUID,
        owner_user_id: UUID,
        include_archived: bool = False,
    ) -> list[EmailAccount]:
        if include_archived:
            stmt = text(
                """
                select * from email_account
                where owner_user_id = :owner_user_id
                and organization_id = :organization_id
                and deleted_at is null
                """
            ).bindparams(
                owner_user_id=owner_user_id,
                organization_id=organization_id,
            )
            rows = await self.engine.all(stmt)
            return await EmailAccount.bulk_from_rows(rows=rows)
        return await self._find_by_column_values(
            EmailAccount,
            owner_user_id=owner_user_id,
            organization_id=organization_id,
        )

    async def find_signature_email_account_associations_by_signature_ids(
        self,
        organization_id: UUID,
        signature_ids: list[UUID],
    ) -> tuple[dict[UUID, set[UUID]], set[UUID]]:
        stmt = text(
            """
            SELECT DISTINCT signature_id, id
            FROM email_account
            WHERE signature_id = ANY(:signature_ids)
            AND organization_id = :organization_id
            AND deleted_at IS NULL
            AND archived_at IS NULL
            """
        ).bindparams(
            signature_ids=signature_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        signature_id_to_email_account_ids: dict[UUID, set[UUID]] = defaultdict(set)
        all_email_account_ids: set[UUID] = set()
        for row in rows:
            signature_id_to_email_account_ids[row.signature_id].add(row.id)
            all_email_account_ids.add(row.id)
        return signature_id_to_email_account_ids, all_email_account_ids

    async def find_archived_email_accounts_by_ids(
        self,
        organization_id: UUID,
        email_account_ids: list[UUID],
    ) -> list[EmailAccount]:
        stmt = text(
            """
            select * from email_account where id = any(:email_account_ids)
            and organization_id = :organization_id
            and archived_at is not null
            and deleted_at is null
            """
        ).bindparams(
            email_account_ids=email_account_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await EmailAccount.bulk_from_rows(rows=rows)

    async def archive_email_accounts_by_ids(
        self,
        email_account_ids: list[UUID],
        user_auth_context: UserAuthContext,
    ) -> None:
        stmt = text(
            """
            update email_account
            set
                archived_at = now(),
                archived_by_user_id = :archived_by_user_id,
                active = false
            where id = any(:email_account_ids)
            and organization_id = :organization_id
            and deleted_at is null
            and archived_at is null
            """
        ).bindparams(
            email_account_ids=email_account_ids,
            organization_id=user_auth_context.organization_id,
            archived_by_user_id=user_auth_context.user_id,
        )
        await self.engine.execute(stmt)

    async def unarchive_email_accounts_by_ids(
        self,
        email_account_ids: list[UUID],
        user_auth_context: UserAuthContext,
    ) -> None:
        stmt = text(
            """
            update email_account
            set
                archived_at = null,
                archived_by_user_id = null,
                active = true
            where id = any(:email_account_ids)
            and organization_id = :organization_id
            and deleted_at is null
            and archived_at is not null
            """
        ).bindparams(
            email_account_ids=email_account_ids,
            organization_id=user_auth_context.organization_id,
        )
        await self.engine.execute(stmt)

    async def find_email_accounts_with_permission_filter(
        self,
        organization_id: UUID,
        email_account_ids: list[UUID] | None = None,
        owner_user_id: UUID | None = None,
        include_archived: bool = False,
    ) -> list[EmailAccount]:
        """
        Unified method to find email accounts with optional filtering by IDs and owner.

        Args:
            organization_id: Organization to filter by
            email_account_ids: Optional list of specific email account IDs to include
            owner_user_id: Optional owner user ID to filter by (for permission enforcement)
            include_archived: Whether to include archived accounts
        """
        base_query = """
            select * from email_account
            where organization_id = :organization_id
            and deleted_at is null
        """
        params: dict[str, Any] = {"organization_id": organization_id}  # type: ignore[explicit-any] # TODO: fix-any-annotation

        if email_account_ids:
            base_query += " and id = any(:email_account_ids)"
            params["email_account_ids"] = email_account_ids

        if owner_user_id:
            base_query += " and owner_user_id = :owner_user_id"
            params["owner_user_id"] = owner_user_id

        if not include_archived:
            base_query += " and archived_at is null"

        stmt = text(base_query).bindparams(**params)
        rows = await self.engine.all(stmt)
        return await EmailAccount.bulk_from_rows(rows=rows)


class EmailAccountPoolRepository(GenericRepository):
    async def list_email_account_pools(
        self,
        user_auth_context: UserAuthContext,
        only_include_pool_ids: UnsetAware[set[UUID]] = UNSET,
    ) -> list[EmailAccountPool]:
        column_to_query = {
            "organization_id": user_auth_context.organization_id,
        }

        # Admins can see all pools, non-admins can only see their own pools.
        if not user_auth_context.is_admin and user_auth_context.user_id:
            column_to_query["created_by_user_id"] = user_auth_context.user_id

        if specified(only_include_pool_ids):
            return await self._find_by_column_values(
                EmailAccountPool,
                id=only_include_pool_ids,
                unique=False,
                exclude_deleted_or_archived=True,
                exclude_locked_by_integrity_jobs=True,
                **column_to_query,
            )
        else:
            return await self._find_by_column_values(
                EmailAccountPool,
                exclude_deleted_or_archived=True,
                exclude_locked_by_integrity_jobs=True,
                unique=False,
                **column_to_query,
            )

    async def get_email_account_pool_members(
        self,
        organization_id: UUID,
        email_account_pool_id: UUID,
    ) -> list[EmailAccountPoolMembership]:
        return await self._find_by_column_values(
            EmailAccountPoolMembership,
            organization_id=organization_id,
            email_account_pool_id=email_account_pool_id,
        )

    async def get_default_pool(
        self, organization_id: UUID, owner_user_id: UUID
    ) -> EmailAccountPool | None:
        return await self._find_unique_by_column_values(
            EmailAccountPool,
            organization_id=organization_id,
            owner_user_id=owner_user_id,
            is_default=True,
        )

    async def list_default_pools_by_owner_ids(
        self,
        organization_id: UUID,
        owner_user_ids: list[UUID],
    ) -> list[EmailAccountPool]:
        return await self._find_by_column_values(
            EmailAccountPool,
            organization_id=organization_id,
            owner_user_id=owner_user_ids,
            is_default=True,
        )

    async def get_email_account_pool_with_members(
        self,
        organization_id: UUID,
        email_account_pool_id: UUID,
    ) -> EmailAccountPoolWithMembershipsDTO:
        email_account_pool = await self._find_unique_by_column_values(
            EmailAccountPool,
            organization_id=organization_id,
            id=email_account_pool_id,
        )
        if not email_account_pool:
            raise ResourceNotFoundError(
                f"Email account pool with id {email_account_pool_id} not found"
            )

        stmt = text(
            """
            select * from email_account where id in (
                select email_account_id from email_account_pool_membership
                where email_account_pool_id = :email_account_pool_id and organization_id = :organization_id
                and deleted_at is null
            ) and archived_at is null
            """
        ).bindparams(
            email_account_pool_id=email_account_pool_id, organization_id=organization_id
        )
        rows = await self.engine.all(stmt)
        return EmailAccountPoolWithMembershipsDTO(
            email_account_pool=email_account_pool,
            email_accounts=await EmailAccount.bulk_from_rows(rows=rows) if rows else [],
        )

    async def delete_email_account_pool_members(
        self,
        organization_id: UUID,
        email_account_pool_id: UUID,
        email_account_ids: list[UUID],
    ) -> None:
        stmt = text(
            """
            delete from email_account_pool_membership where email_account_pool_id = :email_account_pool_id
            and email_account_id = any(:email_account_ids) and organization_id = :organization_id
            """
        ).bindparams(
            email_account_pool_id=email_account_pool_id,
            email_account_ids=email_account_ids,
            organization_id=organization_id,
        )

        await self.engine.execute(stmt)

    async def get_active_email_account_pool_memberships_by_email_account_id(
        self,
        organization_id: UUID,
        email_account_id: UUID,
    ) -> list[EmailAccountPoolMembership]:
        return await self._find_by_column_values(
            EmailAccountPoolMembership,
            organization_id=organization_id,
            email_account_id=email_account_id,
        )

    async def get_email_account_pool_membership_from_default_pool_by_email_account_id(
        self,
        organization_id: UUID,
        email_account_id: UUID,
        user_id: UUID,
    ) -> EmailAccountPoolMembership | None:
        default_pool = await self.get_default_pool(
            organization_id=organization_id,
            owner_user_id=user_id,
        )
        if not default_pool:
            return None
        return await self._find_unique_by_column_values(
            EmailAccountPoolMembership,
            organization_id=organization_id,
            email_account_id=email_account_id,
            email_account_pool_id=default_pool.id,
        )

    async def soft_delete_email_account_pool_memberships_by_email_account_ids(
        self,
        email_account_ids: list[UUID],
        user_id: UUID,
        organization_id: UUID,
    ) -> None:
        stmt = text(
            """
            update email_account_pool_membership
            set
                deleted_at = now(),
                deleted_by_user_id = :deleted_by_user_id
            where email_account_id = any(:email_account_ids)
            and organization_id = :organization_id
            and deleted_at is null
            """
        ).bindparams(
            email_account_ids=email_account_ids,
            organization_id=organization_id,
            deleted_by_user_id=user_id,
        )

        await self.engine.execute(stmt)


class EmailAccountSlotAllocationRepository(GenericRepository):
    async def get_last_allocation_slot(
        self, email_account_id: UUID, organization_id: UUID
    ) -> EmailAccountSlotAllocation | None:
        stmt = text(
            """
            select * from email_account_slot_allocation
            where email_account_id = :email_account_id
            and organization_id = :organization_id
            order by allocated_time desc limit 1
            """
        ).bindparams(
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

        row = await self.engine.first_or_none(stmt)
        return EmailAccountSlotAllocation.from_row(row) if row else None

    async def find_allocation_slots_in_period(
        self,
        email_account_id: UUID,
        organization_id: UUID,
        period_start: ZoneRequiredDateTime,
        period_end: ZoneRequiredDateTime,
    ) -> list[EmailAccountSlotAllocation]:
        stmt = text(
            """
            select * from email_account_slot_allocation
            where email_account_id = :email_account_id
            and organization_id = :organization_id
            and allocated_time >= :period_start
            and allocated_time <= :period_end
            order by allocated_time asc
            """
        ).bindparams(
            email_account_id=email_account_id,
            organization_id=organization_id,
            period_start=period_start,
            period_end=period_end,
        )

        rows = await self.engine.all(stmt)
        return await EmailAccountSlotAllocation.bulk_from_rows(rows=rows)


class EmailAccountHealthRepository(GenericRepository):
    async def get_email_account_health(
        self, email_account_id: UUID, organization_id: UUID
    ) -> EmailAccountHealth | None:
        return await self._find_unique_by_column_values(
            EmailAccountHealth,
            email_account_id=email_account_id,
            organization_id=organization_id,
        )

    async def get_warmup_health(
        self, email_account_id: UUID, organization_id: UUID
    ) -> WarmupHealth | None:
        return await self._find_unique_by_column_values(
            WarmupHealth,
            email_account_id=email_account_id,
            organization_id=organization_id,
        )
