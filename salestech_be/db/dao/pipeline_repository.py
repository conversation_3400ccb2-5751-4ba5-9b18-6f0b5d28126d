from collections import defaultdict
from collections.abc import Mapping, Sequence
from contextlib import nullcontext
from typing import Literal, NamedTuple, assert_never
from uuid import UUID

from frozendict import frozendict
from sqlalchemy import TextClause, bindparam, text

from salestech_be.common.exception.exception import (
    ConcurrentModificationError,
    ConflictResourceError,
    IllegalStateError,
    InvalidArgumentError,
    ResourceNotFoundError,
)
from salestech_be.common.type.patch_request import UNSET, UnsetAware, specified
from salestech_be.db.dao.pipeline_qualification_property_repository import (
    PipelineQualificationPropertyRepository,
)
from salestech_be.db.models.account import (
    Account,
    AccountStatus,
    AccountUpdate,
    AccountUpdateCondition,
)
from salestech_be.db.models.contact import Contact
from salestech_be.db.models.contact_account_association import ContactAccountAssociation
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
    ContactPipelineAssociationUpdate,
    ContactPipelineAssociationUpdateCondition,
)
from salestech_be.db.models.pipeline import <PERSON><PERSON><PERSON>, PipelineStatus, PipelineUpdate
from salestech_be.db.models.pipeline_qualification_property import (
    PipelineQualificationProperty,
)
from salestech_be.db.models.pipeline_tracking import PipelineTracking
from salestech_be.ree_logging import get_logger
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
from salestech_be.util.time import zoned_utc_now
from salestech_be.util.validation import not_none

logger = get_logger(__name__)


class UpdateOrPutAllContactPipelineAssociationsResult(NamedTuple):
    archived: list[ContactPipelineAssociation]
    upserted: list[ContactPipelineAssociation]
    demoted_primary: ContactPipelineAssociation | None


class AccountStatusUpdateForPipelineStageShift(NamedTuple):
    to_account_status: AccountStatus
    from_account_status: UnsetAware[AccountStatus]


class AccountStatusUpdateForPipelineCreationOrUpdate(NamedTuple):
    update_account_status_from: AccountStatus
    new_account_status: AccountStatus | None


class CreatePipelineResult(NamedTuple):
    pipeline: Pipeline
    contact_pipeline_associations: list[ContactPipelineAssociation]
    qualification_properties: list[PipelineQualificationProperty]


class PipelineRepository(PipelineQualificationPropertyRepository):
    async def list_all(
        self, *, organization_id: UUID, exclude_archived: bool = True
    ) -> list[Pipeline]:
        return await self._find_by_column_values(
            Pipeline,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
        )

    async def list_all_paginated(
        self,
        *,
        organization_id: UUID,
        exclude_archived: bool = True,
        limit: int | None = None,
        offset: int = 0,
    ) -> list[Pipeline]:
        return await self._find_by_column_values_paginated(
            Pipeline,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_archived,
            limit=limit,
            offset=offset,
        )

    async def get_pipeline_by_id(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        exclude_archived: bool,
    ) -> Pipeline:
        if not (
            found := await self.find_by_tenanted_primary_key(
                Pipeline,
                organization_id=organization_id,
                id=pipeline_id,
                exclude_deleted_or_archived=exclude_archived,
            )
        ):
            raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")
        return found

    async def create_pipeline(
        self,
        *,
        pipeline: Pipeline,
        contact_pipeline_associations: list[ContactPipelineAssociation],
        account_state_update_for_pipeline_creation: AccountStatusUpdateForPipelineCreationOrUpdate,
        qualification_properties: list[PipelineQualificationProperty],
    ) -> CreatePipelineResult:
        # validate the pipeline is not archived
        if pipeline.archived_at:
            raise InvalidArgumentError("Pipeline must not be archived")

        self._validate_adding_contact_pipeline_associations(
            organization_id=pipeline.organization_id,
            pipeline_id=pipeline.id,
            contact_pipeline_associations=contact_pipeline_associations,
            archive_unspecified_existing_associations=True,
        )

        organization_id = pipeline.organization_id
        contact_ids: set[UUID] = {a.contact_id for a in contact_pipeline_associations}

        async with self.engine.begin():
            inserted_pipeline = await self.insert(pipeline)
            inserted_contact_pipeline_associations = [
                await self.insert(contact_association)
                for contact_association in contact_pipeline_associations
            ]
            # make sure account is still in correct stage and not archived
            await self._validate_and_conditional_update_account_for_pipeline_creation_or_update(
                organization_id=organization_id,
                account_id=pipeline.account_id,
                update_account_status_from=account_state_update_for_pipeline_creation.update_account_status_from,
                new_account_status=account_state_update_for_pipeline_creation.new_account_status,
                user_id=pipeline.updated_by_user_id,
            )
            # make sure contacts are still valid
            await self._touch_and_validate_contacts(
                organization_id=organization_id, contact_ids=contact_ids
            )
            # make sure contact <> account associations are still valid
            await self._touch_and_validate_contact_account_associations(
                organization_id=organization_id,
                account_id=pipeline.account_id,
                contact_ids=contact_ids,
            )
            created_props: list[PipelineQualificationProperty] = []
            for prop in qualification_properties:
                created_props.append(
                    await self.create_pipeline_qualification_property(
                        create=prop,
                    )
                )
            return CreatePipelineResult(
                pipeline=inserted_pipeline,
                contact_pipeline_associations=inserted_contact_pipeline_associations,
                qualification_properties=created_props,
            )

    async def update_pipeline_stages_in_order(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        user_id: UUID,
        ordered_stage_ids: list[UUID],
        new_status: PipelineStatus,
        new_closed_reason_custom_detail: str | None = None,
        new_closed_reason_select_list_value_ids: list[UUID] | None = None,
        account_status_update: AccountStatusUpdateForPipelineStageShift | None,
        is_closed_before_update: bool,
        is_closed_after_update: bool,
    ) -> Pipeline:
        is_newly_closed = (not is_closed_before_update) and is_closed_after_update
        is_reopened = is_closed_before_update and (not is_closed_after_update)

        _closed_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
        _closed_by_user_id: UnsetAware[UUID | None] = UNSET
        _closed_reason_custom_detail: UnsetAware[str | None] = UNSET
        _closed_reason_select_list_value_ids: UnsetAware[list[UUID] | None] = UNSET

        if is_closed_after_update:
            _closed_reason_custom_detail = new_closed_reason_custom_detail
            _closed_reason_select_list_value_ids = (
                new_closed_reason_select_list_value_ids
            )
        if is_newly_closed:
            _closed_at = zoned_utc_now()
            _closed_by_user_id = user_id
        if is_reopened:
            _closed_at = None
            _closed_by_user_id = None
            _closed_reason_custom_detail = None
            _closed_reason_select_list_value_ids = None

        async with self.engine.begin():
            # db updates triggers the report, so need to update the ordered stage in a row
            for stage_id in ordered_stage_ids:
                updated_pipeline = await self.update_by_tenanted_primary_key(
                    Pipeline,
                    organization_id=organization_id,
                    exclude_deleted_or_archived=False,
                    primary_key_to_value={
                        "id": pipeline_id,
                    },
                    column_to_update=PipelineUpdate(
                        stage_id=stage_id,
                    ),
                )
                if not updated_pipeline:
                    raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")
            # update other fields in last step.
            updated_pipeline = await self.update_by_tenanted_primary_key(
                Pipeline,
                organization_id=organization_id,
                exclude_deleted_or_archived=False,
                primary_key_to_value={
                    "id": pipeline_id,
                },
                column_to_update=PipelineUpdate(
                    state=new_status,
                    status=new_status,
                    updated_by_user_id=user_id,
                    closed_at=_closed_at,
                    closed_by_user_id=_closed_by_user_id,
                    closed_reason_custom_detail=_closed_reason_custom_detail,
                    closed_reason_select_list_value_ids=_closed_reason_select_list_value_ids,
                    stage_last_shifted_at=zoned_utc_now(),
                ),
            )
            if not updated_pipeline:
                raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")

            if account_status_update:
                _update_account_result = (
                    await self.conditionally_update_by_tenanted_primary_key_or_get(
                        Account,
                        organization_id=organization_id,
                        exclude_deleted_or_archived=False,
                        primary_key_to_value={
                            "id": updated_pipeline.account_id,
                        },
                        column_to_update=AccountUpdate(
                            status=account_status_update.to_account_status,
                            updated_by_user_id=user_id,
                        ),
                        column_condition=AccountUpdateCondition(
                            status=account_status_update.from_account_status,
                        )
                        if specified(account_status_update.from_account_status)
                        else None,
                    )
                )
                if not _update_account_result.is_updated:
                    raise ConcurrentModificationError(
                        f"Account {updated_pipeline.account_id} state was concurrently updated, "
                        f"expected {account_status_update.from_account_status} but got {_update_account_result.record.status}"
                        "Please try again."
                    )

            return updated_pipeline

    async def archive_pipeline(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        user_id: UUID,
        account_status_update: AccountStatusUpdateForPipelineStageShift | None,
    ) -> Pipeline:
        async with self.engine.begin() if account_status_update else nullcontext():
            updated_pipeline = not_none(
                await self.update_by_tenanted_primary_key(
                    Pipeline,
                    organization_id=organization_id,
                    primary_key_to_value={
                        "id": pipeline_id,
                    },
                    column_to_update=PipelineUpdate(
                        archived_at=zoned_utc_now(),
                        archived_by_user_id=user_id,
                        updated_by_user_id=user_id,
                    ),
                )
            )
            if account_status_update:
                _update_account_result = (
                    await self.conditionally_update_by_tenanted_primary_key_or_get(
                        Account,
                        organization_id=organization_id,
                        exclude_deleted_or_archived=False,
                        primary_key_to_value={
                            "id": updated_pipeline.account_id,
                        },
                        column_to_update=AccountUpdate(
                            status=account_status_update.to_account_status,
                            updated_by_user_id=user_id,
                        ),
                        column_condition=AccountUpdateCondition(
                            status=account_status_update.from_account_status,
                        ),
                    )
                )
                if not _update_account_result.is_updated:
                    raise ConcurrentModificationError(
                        f"Account {updated_pipeline.account_id} state was concurrently updated, "
                        f"expected {account_status_update.from_account_status} but got {_update_account_result.record.status}"
                        "Please try again."
                    )

            return updated_pipeline

    async def cleanup_closed_reasons(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
    ) -> None:
        await self.update_by_tenanted_primary_key(
            Pipeline,
            organization_id=organization_id,
            primary_key_to_value={"id": pipeline_id},
            column_to_update=PipelineUpdate(
                closed_reason_select_list_value_ids=None,
                closed_reason_custom_detail=None,
            ),
        )

    async def upsert_or_put_all_contact_pipeline_associations(
        self,
        *,
        organization_id: UUID,
        requesting_user_id: UUID,
        pipeline_id: UUID,
        adding_contact_pipeline_associations: list[ContactPipelineAssociation],
        archive_unspecified_existing_associations: bool = False,
    ) -> UpdateOrPutAllContactPipelineAssociationsResult:
        self._validate_adding_contact_pipeline_associations(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            contact_pipeline_associations=adding_contact_pipeline_associations,
            archive_unspecified_existing_associations=archive_unspecified_existing_associations,
        )

        adding_associations_by_contact_id: dict[UUID, ContactPipelineAssociation] = {
            association.contact_id: association
            for association in adding_contact_pipeline_associations
        }
        adding_contact_ids = set(adding_associations_by_contact_id.keys())

        adding_primary_association = next(
            (
                association
                for association in adding_contact_pipeline_associations
                if association.is_primary
            ),
            None,
        )

        pipeline = await self.get_pipeline_by_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            exclude_archived=True,
        )

        pre_update_active_associations: list[
            ContactPipelineAssociation
        ] = await self.list_contact_pipeline_associations_by_pipeline_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            exclude_archived=True,
        )

        pre_update_active_primary_association: ContactPipelineAssociation | None = next(
            (
                association
                for association in pre_update_active_associations
                if association.is_primary
            ),
            None,
        )

        # pre-flight check: ensure the resulting associations have a primary association
        # so we don't have to open a transaction just to rollback
        # we are doing the second round of validation in the transaction as well, to avoid possible race conditions
        if archive_unspecified_existing_associations:
            _final_primary_association = adding_primary_association
        elif (
            pre_update_active_primary_association
            and (
                _updating_pre_update_active_primary_association := (
                    adding_associations_by_contact_id.get(
                        pre_update_active_primary_association.contact_id
                    )
                )
            )
            and (not _updating_pre_update_active_primary_association.is_primary)
        ):
            # if the pre update active primary association is demoted by the request,
            # then the final primary association is the new primary association (if it's specified)
            _final_primary_association = adding_primary_association
        else:
            # then, if a new primary association is specified, it becomes the primary
            # otherwise, the pre update active primary association remains the primary
            _final_primary_association = (
                adding_primary_association or pre_update_active_primary_association
            )

        if not _final_primary_association:
            raise InvalidArgumentError(
                "There must be exactly one primary association after association update"
            )

        archiving_contact_ids = (
            {
                association.contact_id
                for association in pre_update_active_associations
                if association.contact_id not in adding_contact_ids
            }
            if archive_unspecified_existing_associations
            else set[UUID]()
        )

        archived_associations: list[ContactPipelineAssociation] = []
        upserted_associations: list[ContactPipelineAssociation] = []
        demoted_primary: ContactPipelineAssociation | None = None
        async with self.engine.begin():
            # remove primary contact association
            # if a different primary association is specified in the request
            if adding_primary_association and (
                pre_update_active_primary_association
                and (
                    adding_primary_association.contact_id
                    != pre_update_active_primary_association.contact_id
                )
            ):
                demoted_primary = await self.update_by_tenanted_primary_key(
                    ContactPipelineAssociation,
                    organization_id=organization_id,
                    primary_key_to_value=pre_update_active_primary_association.primary_key_to_value(),
                    column_to_update=ContactPipelineAssociationUpdate(
                        updated_by_user_id=requesting_user_id,
                        is_primary=False,
                    ),
                    column_condition=ContactPipelineAssociationUpdateCondition(
                        is_primary=True, archived_at=None
                    ),
                )

            # archive contact associations
            if archiving_contact_ids:
                archived_rows = await self.engine.all(
                    self._get_archive_contact_association_stmt(
                        pipeline_id=pipeline_id,
                        organization_id=organization_id,
                        contact_ids_to_archive=archiving_contact_ids,
                        archive_by_user_id=requesting_user_id,
                    )
                )
                archived_associations.extend(
                    ContactPipelineAssociation.from_row(row) for row in archived_rows
                )

            for adding_asso in adding_contact_pipeline_associations:
                row = await self.engine.one(
                    self._get_upsert_contact_association_stmt(
                        contact_pipeline_association=adding_asso,
                    )
                )
                _added_asso = ContactPipelineAssociation.from_row(row)
                if not _added_asso.archived_at:
                    upserted_associations.append(_added_asso)
                else:
                    # since we already verified there is no "archived" association at beginning,
                    # this is unexpected
                    raise IllegalStateError(
                        f"Contact {_added_asso.contact_id} is unexpectedly archived"
                    )
            # make sure the end result has a primary association
            if not await self.list_contact_pipeline_associations_by_pipeline_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                primary_association_only=True,
                exclude_archived=True,
            ):
                raise ConflictResourceError(
                    f"No active primary contact pipeline association found for pipeline {pipeline_id} after association update, rolling back"
                )

            # ensure new / edited contact associations are still alive
            await self._touch_and_validate_contacts(
                organization_id=organization_id,
                contact_ids=adding_contact_ids,
            )
            # ensure new / edited contact <> account associations are still alive
            await self._touch_and_validate_contact_account_associations(
                organization_id=organization_id,
                account_id=pipeline.account_id,
                contact_ids=adding_contact_ids,
            )
            return UpdateOrPutAllContactPipelineAssociationsResult(
                archived=archived_associations,
                upserted=upserted_associations,
                demoted_primary=demoted_primary,
            )

    async def archive_contact_pipeline_association(
        self,
        *,
        organization_id: UUID,
        user_id: UUID,
        pipeline_id: UUID,
        contact_id: UUID,
    ) -> ContactPipelineAssociation:
        """
        Removes a contact pipeline association for a pipeline.
        - Any non-primary associations can be removed freely.
        - The primary association can only be archived when the pipeline is also archived.
        """
        existing_association: (
            ContactPipelineAssociation | None
        ) = await self._find_unique_by_column_values(
            table_model=ContactPipelineAssociation,
            exclude_deleted_or_archived=False,
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            contact_id=contact_id,
        )
        if not existing_association:
            raise ResourceNotFoundError(
                f"Contact pipeline association ({pipeline_id}, {contact_id}) not found"
            )
        if existing_association.archived_at:
            return existing_association

        # Directly archive non-primary associations without checking pipeline status
        if not existing_association.is_primary:
            conditional_update_result = await self.conditionally_update_by_tenanted_primary_key_or_get(
                table_model=ContactPipelineAssociation,
                organization_id=organization_id,
                primary_key_to_value=existing_association.primary_key_to_value(),
                column_to_update=ContactPipelineAssociationUpdate(
                    archived_at=zoned_utc_now(),
                    archived_by_user_id=user_id,
                    updated_by_user_id=user_id,
                ),
                # ensure while archiving, the association is not becoming the primary association
                column_condition=ContactPipelineAssociationUpdateCondition(
                    is_primary=False,
                ),
            )
            if not conditional_update_result.is_updated:
                raise ConflictResourceError(
                    "Contact pipeline association become a primary association before being archived"
                )
            return conditional_update_result.record

        # Now handle archival when contact is the primary contact

        # find pipeline
        pipeline = await self.get_pipeline_by_id(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            exclude_archived=False,
        )

        if not pipeline.archived_at:
            raise InvalidArgumentError(
                f"Cannot archive primary contact pipeline association ({pipeline_id}, {contact_id})"
            )
        async with self.engine.begin():
            archived_association = not_none(
                await self.update_by_tenanted_primary_key(
                    ContactPipelineAssociation,
                    organization_id=organization_id,
                    primary_key_to_value=existing_association.primary_key_to_value(),
                    column_to_update=ContactPipelineAssociationUpdate(
                        archived_at=zoned_utc_now(),
                        archived_by_user_id=user_id,
                        updated_by_user_id=user_id,
                    ),
                )
            )
            post_archival_pipeline = await self.get_pipeline_by_id(
                organization_id=organization_id,
                pipeline_id=pipeline_id,
                exclude_archived=False,
            )

            # Only allow primary association to be archived when the pipeline is archived
            if not post_archival_pipeline.archived_at:
                raise ConflictResourceError(
                    f"Another user has un-archived pipeline {pipeline_id} before you could archive "
                    f"the primary contact pipeline association ({pipeline_id}, {contact_id})"
                )
            return archived_association

    async def patch_pipeline(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        pipeline_update: PipelineUpdate,
        account_state_update_for_pipeline_update: AccountStatusUpdateForPipelineCreationOrUpdate
        | None = None,
        exclude_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> Pipeline:
        async with self.engine.begin():
            updated_pipeline = await self.update_by_tenanted_primary_key(
                Pipeline,
                exclude_deleted_or_archived=exclude_archived,
                exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
                organization_id=organization_id,
                primary_key_to_value={"id": pipeline_id},
                column_to_update=pipeline_update,
            )

            if not updated_pipeline:
                raise ResourceNotFoundError(f"Pipeline {pipeline_id} not found")

            if account_state_update_for_pipeline_update:
                await self._validate_and_conditional_update_account_for_pipeline_creation_or_update(
                    organization_id=organization_id,
                    account_id=updated_pipeline.account_id,
                    update_account_status_from=account_state_update_for_pipeline_update.update_account_status_from,
                    new_account_status=account_state_update_for_pipeline_update.new_account_status,
                    user_id=updated_pipeline.updated_by_user_id,
                )

            return updated_pipeline

    async def list_by_ids(
        self,
        *,
        pipeline_ids: list[UUID],
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> list[Pipeline]:
        if not pipeline_ids:
            return []
        where_filter_archived = "and (archived_at is null)" if exclude_archived else ""
        stmt = text(
            f"""
            select *
            from pipeline as p
            where p.id = any(:pipeline_ids) and organization_id = :organization_id
            {where_filter_archived}
            """,  # noqa: S608
        ).bindparams(pipeline_ids=pipeline_ids, organization_id=organization_id)

        rows = await self.engine.all(stmt)
        return await Pipeline.bulk_from_rows(rows=rows)

    async def list_by_ids_untenanted(
        self,
        *,
        pipeline_ids: list[UUID],
        exclude_deleted_or_archived: bool = True,
    ) -> list[Pipeline]:
        if not pipeline_ids:
            return []

        return await self._find_by_column_values(
            Pipeline,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            id=pipeline_ids,
        )

    async def find_pipelines_by_display_name_account(
        self,
        *,
        organization_id: UUID,
        display_name: str,
        account_id: UUID,
        exclude_archived: bool = True,
    ) -> list[Pipeline]:
        """
        Finds pipelines by organization_id, display_name, and account_id.

        Args:
            organization_id: The organization ID
            display_name: The display name to search for
            account_id: The account ID
            exclude_archived: Whether to exclude archived pipelines

        Returns:
            A list of Pipeline objects that match the criteria
        """
        where_filter_archived = "and (archived_at is null)" if exclude_archived else ""
        stmt = text(
            f"""
            select *
            from pipeline
            where organization_id = :organization_id
              and display_name = :display_name
              and account_id = :account_id
              {where_filter_archived}
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            display_name=display_name,
            account_id=account_id,
        )

        rows = await self.engine.all(stmt)
        return await Pipeline.bulk_from_rows(rows=rows)

    async def map_pipelines_by_account_ids(
        self,
        *,
        account_ids: list[UUID],
        organization_id: UUID,
        exclude_archived: bool | None = True,
    ) -> Mapping[UUID, Sequence[Pipeline]]:
        if not account_ids:
            return frozendict[UUID, Sequence[Pipeline]]({})
        filter_archived = "and (archived_at is null)" if exclude_archived else ""
        stmt = text(
            f"""
            select *
            from pipeline
            where account_id = any(:account_ids) and organization_id=:organization_id {filter_archived}
            """,  # noqa: S608
        ).bindparams(account_ids=account_ids, organization_id=organization_id)

        rows = await self.engine.all(stmt)
        all_pipelines = await Pipeline.bulk_from_rows(rows=rows)
        result_dict: defaultdict[UUID, list[Pipeline]] = defaultdict(list)
        for pipeline in all_pipelines:
            result_dict[pipeline.account_id].append(pipeline)
        return frozendict[UUID, Sequence[Pipeline]](result_dict)

    async def map_pipelines_by_contact_ids(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
        primary_contact_filter: Literal["primary", "additional", "all"] = "all",
        exclude_archived: bool = True,
    ) -> frozendict[UUID, Sequence[Pipeline]]:
        if not contact_ids:
            return frozendict[UUID, Sequence[Pipeline]]()
        where_filter_archived = (
            "and (p.archived_at is null)" if exclude_archived else ""
        )
        where_filter_primary = ""
        match primary_contact_filter:
            case "primary":
                where_filter_primary = "and cpa.is_primary"
            case "additional":
                where_filter_primary = "and not cpa.is_primary"
            case "all":
                pass
            case _ as unreachable:
                assert_never(unreachable)
        stmt = text(f"""
        select cpa.contact_id as _association_contact_id, p.* from pipeline p
        join contact_pipeline_association cpa
            on    cpa.organization_id = p.organization_id
              and p.id = cpa.pipeline_id
              and cpa.contact_id = any(:contact_ids)
        where p.organization_id = :organization_id
        {where_filter_primary}
        {where_filter_archived}
        """).bindparams(  # noqa: S608
            organization_id=organization_id,
            contact_ids=list(contact_ids),
        )
        rows = await self.engine.all(stmt)
        result_dict: defaultdict[UUID, list[Pipeline]] = defaultdict(list)
        for row in rows:
            contact_id = (
                row._association_contact_id
                if isinstance(row._association_contact_id, UUID)
                else UUID(row._association_contact_id)
            )
            result_dict[contact_id].append(Pipeline.from_row(row))
        return frozendict[UUID, Sequence[Pipeline]](result_dict)

    async def list_contact_pipeline_associations_by_association_ids(
        self,
        *,
        organization_id: UUID,
        association_ids: list[UUID],
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        where_filter_archived = "and (archived_at is null)" if exclude_archived else ""
        where_filter_primary = (
            "and is_primary = true" if primary_association_only else ""
        )
        stmt = text(
            f"""
            select *
            from contact_pipeline_association
            where organization_id = :organization_id and id = any(:association_ids)
            {where_filter_archived}
            {where_filter_primary}
            """  # noqa: S608
        ).bindparams(organization_id=organization_id, association_ids=association_ids)
        rows = await self.engine.all(stmt)
        return await ContactPipelineAssociation.bulk_from_rows(rows=rows)

    async def list_contact_pipeline_associations_by_pipeline_id(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        primary_association_only: bool = False,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        where_filter_archived = "and (archived_at is null)" if exclude_archived else ""
        where_filter_primary = (
            "and is_primary = true" if primary_association_only else ""
        )
        stmt = text(
            f"""
            select *
            from contact_pipeline_association
            where organization_id = :organization_id and pipeline_id = :pipeline_id
            {where_filter_archived}
            {where_filter_primary}
            """  # noqa: S608
        ).bindparams(organization_id=organization_id, pipeline_id=pipeline_id)
        rows = await self.engine.all(stmt)
        return await ContactPipelineAssociation.bulk_from_rows(rows=rows)

    async def list_contact_pipeline_associations_by_organization_id(
        self,
        *,
        organization_id: UUID,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        """
        List all contact pipeline associations for the given contact IDs.

        Args:
            organization_id: The organization ID
            exclude_archived: If True, exclude archived associations

        Returns:
            List of ContactPipelineAssociation
        """

        where_filter_archived = (
            "and (cpa.archived_at is null)" if exclude_archived else ""
        )
        stmt = text(
            f"""
            select cpa.*
            from contact_pipeline_association as cpa
            where cpa.organization_id = :organization_id
            {where_filter_archived}
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
        )

        rows = await self.engine.all(stmt)
        return await ContactPipelineAssociation.bulk_from_rows(rows=rows)

    async def list_contact_pipeline_associations_by_contact_ids(
        self,
        *,
        organization_id: UUID,
        contact_ids: set[UUID],
        account_id: UUID | None = None,
        pipeline_id: UUID | None = None,
        exclude_archived: bool = True,
    ) -> list[ContactPipelineAssociation]:
        """
        List all contact pipeline associations for the given contact IDs.

        Args:
            organization_id: The organization ID
            contact_ids: Set of contact IDs to get associations for
            account_id: Optional account ID to filter by
            exclude_archived: If True, exclude archived associations

        Returns:
            List of ContactPipelineAssociation
        """
        if not contact_ids:
            return []

        where_filter_archived = (
            "and (cpa.archived_at is null)" if exclude_archived else ""
        )
        join_statement_account = (
            "join pipeline as p on cpa.pipeline_id = p.id" if account_id else ""
        )
        where_filter_account = "and p.account_id = :account_id" if account_id else ""
        where_filter_pipeline = (
            "and cpa.pipeline_id = :pipeline_id" if pipeline_id else ""
        )
        stmt = text(
            f"""
            select cpa.*
            from contact_pipeline_association as cpa
            {join_statement_account}
            where cpa.organization_id = :organization_id
            and cpa.contact_id = any(:contact_ids)
            {where_filter_archived}
            {where_filter_account}
            {where_filter_pipeline}
            """  # noqa: S608
        ).bindparams(
            organization_id=organization_id,
            contact_ids=list(contact_ids),
        )

        if account_id:
            stmt = stmt.bindparams(account_id=account_id)
        if pipeline_id:
            stmt = stmt.bindparams(pipeline_id=pipeline_id)

        rows = await self.engine.all(stmt)
        return await ContactPipelineAssociation.bulk_from_rows(rows=rows)

    async def list_pipelines_by_contact_id(
        self,
        *,
        organization_id: UUID,
        contact_id: UUID,
        account_id: UUID | None = None,
        exclude_archived: bool = True,
    ) -> list[Pipeline]:
        where_account = "and p.account_id = :account_id" if account_id else ""
        where_archived = "and p.archived_at is null" if exclude_archived else ""
        stmt = text(f"""
        select p.* from pipeline p
            join contact_pipeline_association cpa
                on p.id = cpa.pipeline_id and cpa.organization_id = p.organization_id
            join contact c
                on c.id = cpa.contact_id and c.organization_id = cpa.organization_id
            where p.organization_id = :organization_id
            and cpa.archived_at is null
            and c.id = :contact_id
            {where_account}
            {where_archived}
        """).bindparams(  # noqa: S608
            organization_id=organization_id,
            contact_id=contact_id,
        )
        if account_id:
            stmt = stmt.bindparams(account_id=account_id)
        rows = await self.engine.all(stmt)
        return await Pipeline.bulk_from_rows(rows=rows)

    async def map_contact_associations_for_org_pipelines_sql_grouped(
        self,
        *,
        organization_id: UUID,
        exclude_archived_pipelines: bool = True,
        active_association_only: bool = True,
        db_pipeline_ids: set[UUID] | None = None,
    ) -> Mapping[UUID, Sequence[ContactPipelineAssociation]]:
        """
        Fetches contact associations for all relevant pipelines in an organization
        and groups them by pipeline_id in Python.

        Args:
            organization_id: The organization ID.
            exclude_archived_pipelines: Whether to exclude archived pipelines.
            active_association_only: Whether to only include active contact associations.

        Returns:
            A mapping of pipeline_id to a sequence of ContactPipelineAssociation objects.
            Pipeline IDs without matching associations will not be keys in the map.
        """

        # Constructing WHERE clause parts for pipeline table
        pipeline_conditions = ["p.organization_id = :organization_id"]
        if exclude_archived_pipelines:
            pipeline_conditions.append("p.archived_at IS NULL")
        pipeline_where_clause = " AND ".join(pipeline_conditions)

        if db_pipeline_ids:
            pipeline_where_clause += " AND p.id = any(:pipeline_ids)"
        # Constructing ON clause parts for the join with cpa table
        # p.organization_id = cpa.organization_id is crucial for correctness and performance.
        join_conditions = [
            "p.id = cpa.pipeline_id",
            "p.organization_id = cpa.organization_id",
        ]
        if active_association_only:
            join_conditions.append("cpa.archived_at IS NULL")
        join_on_clause = " AND ".join(join_conditions)

        # The query selects all columns from cpa for rows that have a match.
        # The LEFT JOIN with pipeline (p) table is to filter based on pipeline criteria.
        # We only care about cpa rows that successfully join to a relevant pipeline.
        # If a pipeline meets criteria but has no (active) associations, it won't produce cpa.* rows.
        sql_query = f"""
            SELECT cpa.*
            FROM pipeline AS p
            INNER JOIN contact_pipeline_association AS cpa ON {join_on_clause}
            WHERE {pipeline_where_clause}
            ORDER BY cpa.pipeline_id, cpa.created_at -- Sorting for consistent list order if needed
        """  # noqa: S608
        # Using INNER JOIN because we SELECT cpa.*. If a pipeline has no matching CPA, that pipeline
        # won't contribute to the result of cpa.*, so a LEFT JOIN showing NULLs for cpa.* is not useful
        # for bulk_from_rows expecting CPA data.

        if db_pipeline_ids:
            stmt = text(sql_query).bindparams(
                organization_id=organization_id, pipeline_ids=db_pipeline_ids
            )
        else:
            stmt = text(sql_query).bindparams(organization_id=organization_id)

        # The parameters active_association_only and exclude_archived_pipelines are implicitly handled
        # by how join_on_clause and pipeline_where_clause are constructed, so no direct bind for them.
        association_rows = await self.engine.all(stmt)
        all_associations = await ContactPipelineAssociation.bulk_from_rows(
            rows=association_rows
        )
        associations_map: defaultdict[UUID, list[ContactPipelineAssociation]] = (
            defaultdict(list)
        )
        for assoc in all_associations:
            associations_map[assoc.pipeline_id].append(assoc)

        return frozendict(associations_map)

    async def list_pipeline_tracking_records(
        self,
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        field_name: Literal["stage_id", "amount"],
        is_current: UnsetAware[bool] = UNSET,
    ) -> list[PipelineTracking]:
        where_filter_is_current = (
            "and is_current = :is_current" if specified(is_current) else ""
        )
        stmt = text(f"""
            select * from pipeline_tracking
            where organization_id = :organization_id
              and pipeline_id = :pipeline_id
              and field_name = :field_name
              {where_filter_is_current}
            order by created_at desc
        """).bindparams(  # noqa: S608
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            field_name=field_name,
        )
        if specified(is_current):
            stmt = stmt.bindparams(is_current=is_current)
        rows = await self.engine.all(stmt)
        return await PipelineTracking.bulk_from_rows(rows=rows)

    async def _validate_and_conditional_update_account_for_pipeline_creation_or_update(
        self,
        *,
        organization_id: UUID,
        account_id: UUID,
        update_account_status_from: AccountStatus,
        new_account_status: AccountStatus | None,
        user_id: UUID,
    ) -> Account:
        _account_update = AccountUpdate(
            status=new_account_status if new_account_status else UNSET,
            updated_by_user_id=user_id,
        )

        _account_update_condition = AccountUpdateCondition(
            # only check state if we are updating to a new state
            status=update_account_status_from if new_account_status else UNSET,
            archived_at=None,
        )

        _update_account_result = (
            await self.conditionally_update_by_tenanted_primary_key_or_get(
                Account,
                organization_id=organization_id,
                exclude_deleted_or_archived=False,
                exclude_locked_by_integrity_jobs=False,
                primary_key_to_value={"id": account_id},
                column_to_update=_account_update,
                column_condition=_account_update_condition,
            )
        )
        if not _update_account_result.is_updated:
            # find the current account to see if this is caused by a race condition on state change or the account is already archived
            _current_account = await self.find_by_tenanted_primary_key_or_fail(
                table_model=Account,
                exclude_deleted_or_archived=False,
                exclude_locked_by_integrity_jobs=False,
                organization_id=organization_id,
                id=account_id,
            )
            if _current_account.archived_at:
                raise ResourceNotFoundError(f"Account {account_id} is archived")
            raise ConcurrentModificationError(
                f"Account {account_id} is in state {_current_account.status}, "
                f"which is different from the expected state {update_account_status_from} before pipeline creation, "
                "please retry the user request!"
            )
        return _update_account_result.record

    async def _touch_and_validate_contact_account_associations(
        self, *, organization_id: UUID, account_id: UUID, contact_ids: set[UUID]
    ) -> None:
        if not contact_ids:
            return
        stmt = (
            text("""
            update contact_account_association set updated_at = now()
                where organization_id = :organization_id
                and account_id = :account_id
                and contact_id in :contact_ids
                and archived_at is null
                returning *
            """)
            .bindparams(
                organization_id=organization_id,
                account_id=account_id,
            )
            .bindparams(
                bindparam("contact_ids", list(contact_ids), expanding=True),
            )
        )
        updated = [
            ContactAccountAssociation.from_row(row)
            for row in await self.engine.all(stmt)
        ]
        association_by_contact_id = {
            association.contact_id: association for association in updated
        }
        if missing_contact_ids := contact_ids - set(association_by_contact_id.keys()):
            logger.error(
                "contacts are not associated with account",
                missing_contact_ids=missing_contact_ids,
                account_id=account_id,
                organization_id=organization_id,
            )
            raise ResourceNotFoundError(
                f"Contacts {missing_contact_ids} are not "
                f"associated with account {account_id} anymore"
            )

    async def _touch_and_validate_contacts(
        self, organization_id: UUID, contact_ids: set[UUID]
    ) -> list[Contact]:
        if not contact_ids:
            return []
        stmt = (
            text("""
            update contact set updated_at = now()
                where organization_id = :organization_id
                and id in :contact_ids
                and archived_at is null
                returning *
            """)
            .bindparams(
                organization_id=organization_id,
            )
            .bindparams(
                bindparam("contact_ids", list(contact_ids), expanding=True),
            )
        )
        rows = await self.engine.all(stmt)
        updated = await Contact.bulk_from_rows(rows=rows)
        contact_by_id = {contact.id: contact for contact in updated}
        if missing_contact_ids := contact_ids - set(contact_by_id.keys()):
            logger.error(
                "contacts are not found or already archived",
                missing_contact_ids=missing_contact_ids,
                organization_id=organization_id,
            )
            raise ResourceNotFoundError(
                f"Contacts {missing_contact_ids} are not found or archived"
            )
        return updated

    @staticmethod
    def _get_remove_primary_contact_association_stmt(
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        updated_by_user_id: UUID,
    ) -> TextClause:
        return text(
            """
            update contact_pipeline_association
            set is_primary          = false,
                updated_at          = now(),
                updated_by_user_id  = :updated_by_user_id
            where organization_id = :organization_id
              and pipeline_id = :pipeline_id
              and is_primary
              and archived_at is null
            returning *
            """
        ).bindparams(
            organization_id=organization_id,
            updated_by_user_id=updated_by_user_id,
            pipeline_id=pipeline_id,
        )

    @staticmethod
    def _get_archive_contact_association_stmt(
        *,
        pipeline_id: UUID,
        organization_id: UUID,
        contact_ids_to_archive: set[UUID],
        archive_by_user_id: UUID,
    ) -> TextClause:
        return text(
            """
            update contact_pipeline_association
            set archived_at         = now(),
                updated_at          = now(),
                updated_by_user_id  = :archived_by_user_id,
                archived_by_user_id = :archived_by_user_id
            where organization_id = :organization_id
              and pipeline_id = :pipeline_id
              and contact_id = any(:contact_ids_to_archive)
              and archived_at is null
              returning *
            """
        ).bindparams(
            organization_id=organization_id,
            pipeline_id=pipeline_id,
            archived_by_user_id=archive_by_user_id,
            contact_ids_to_archive=list(contact_ids_to_archive),
        )

    @staticmethod
    def _get_upsert_contact_association_stmt(
        *,
        contact_pipeline_association: ContactPipelineAssociation,
    ) -> TextClause:
        if (
            contact_pipeline_association.archived_at
            or contact_pipeline_association.archived_by_user_id
        ):
            raise InvalidArgumentError(
                "Upserting contact pipeline association must not be archived"
            )
        return text(
            """
            insert into contact_pipeline_association (  id,
                                                        organization_id,
                                                        pipeline_id,
                                                        contact_id,
                                                        role_types,
                                                        note,
                                                        is_primary,
                                                        created_at,
                                                        created_by_user_id,
                                                        updated_at,
                                                        updated_by_user_id,
                                                        archived_at,
                                                        archived_by_user_id,
                                                        creation_ai_rec_id,
                                                        creation_citation_ids)
            values (:new_association_id,
                    :organization_id,
                    :pipeline_id,
                    :contact_id,
                    :role_types,
                    :note,
                    :is_primary,
                    now(),
                    :created_by_user_id,
                    now(),
                    :updated_by_user_id,
                    :archived_at,
                    :archived_by_user_id,
                    :creation_ai_rec_id,
                    :creation_citation_ids
                   )
            on conflict (organization_id, pipeline_id, contact_id) do update
                set role_types          = excluded.role_types,
                    note                = excluded.note,
                    is_primary          = excluded.is_primary,
                    updated_at          = now(),
                    updated_by_user_id  = excluded.updated_by_user_id,
                    archived_at         = excluded.archived_at,
                    archived_by_user_id = excluded.archived_by_user_id
            returning *
            """
        ).bindparams(
            new_association_id=contact_pipeline_association.id,
            organization_id=contact_pipeline_association.organization_id,
            pipeline_id=contact_pipeline_association.pipeline_id,
            contact_id=contact_pipeline_association.contact_id,
            role_types=contact_pipeline_association.role_types,
            note=contact_pipeline_association.note,
            is_primary=contact_pipeline_association.is_primary,
            created_by_user_id=contact_pipeline_association.created_by_user_id,
            updated_by_user_id=contact_pipeline_association.updated_by_user_id,
            archived_at=contact_pipeline_association.archived_at,
            archived_by_user_id=contact_pipeline_association.archived_by_user_id,
            creation_ai_rec_id=contact_pipeline_association.creation_ai_rec_id,
            creation_citation_ids=contact_pipeline_association.creation_citation_ids,
        )

    @staticmethod
    def _validate_adding_contact_pipeline_associations(
        *,
        organization_id: UUID,
        pipeline_id: UUID,
        contact_pipeline_associations: list[ContactPipelineAssociation],
        archive_unspecified_existing_associations: bool,
    ) -> None:
        # validate the pipeline_id matches the association's pipeline_id
        if any(
            association.pipeline_id != pipeline_id
            for association in contact_pipeline_associations
        ):
            raise InvalidArgumentError(
                "All contact pipeline associations must have the same pipeline_id"
            )

        # validate organization_id matches the association's organization_id
        if any(
            association.organization_id != organization_id
            for association in contact_pipeline_associations
        ):
            raise InvalidArgumentError(
                "All contact pipeline associations must have the same organization_id"
            )

        # validate none of the associations are archived
        if any(
            association.archived_at for association in contact_pipeline_associations
        ):
            raise InvalidArgumentError(
                "None of the contact pipeline associations must be archived"
            )

        # validate there is at most one association with is_primary = True
        primary_associations_count = sum(
            1 for association in contact_pipeline_associations if association.is_primary
        )
        if contact_pipeline_associations and primary_associations_count > 1:
            raise InvalidArgumentError(
                "There must be at most one contact association with is_primary set to True"
            )

        # validate there is exactly one primary contact association if archive_unspecified_existing_associations is True
        if archive_unspecified_existing_associations:
            primary_contact_associations = [
                association
                for association in contact_pipeline_associations
                if association.is_primary
            ]
            if len(primary_contact_associations) > 1:
                raise InvalidArgumentError(
                    "There must be exactly one primary contact association"
                )
