import uuid
from collections import defaultdict
from typing import Any
from uuid import UUID

from sqlalchemy import text

from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.email_account_warm_up import (
    EmailAccountWarmUpCampaign,
    MailboxWarmUpService,
    MailboxWarmUpSpeed,
    MailboxWarmUpStatus,
)
from salestech_be.ree_logging import get_logger
from salestech_be.util.time import zoned_utc_now

logger = get_logger()


class EmailAccountWarmUpCampaignRepository(GenericRepository):
    async def create_campaign(
        self,
        organization_id: UUID,
        user_id: UUID,
        email_per_day: int,
        response_rate: int,
        email_account_id: UUID,
        external_id: str,
        domain_id: UUID,
        with_rampup: bool = False,
        rampup_speed: MailboxWarmUpSpeed = MailboxWarmUpSpeed.FAST,
        status: MailboxWarmUpStatus = MailboxWarmUpStatus.IN_PROGRESS,
    ) -> EmailAccountWarmUpCampaign:
        logger.bind(
            organization_id=organization_id,
            user_id=user_id,
            email_per_day=email_per_day,
            response_rate=response_rate,
            email_account_id=email_account_id,
            external_id=external_id,
            with_rampup=with_rampup,
            domain_id=domain_id,
            rampup_speed=rampup_speed,
            status=status,
        ).info("Creating email account warm up campaign")

        return await self.insert(
            EmailAccountWarmUpCampaign(
                id=uuid.uuid4(),
                domain_id=domain_id,
                email_per_day=email_per_day,
                response_rate=response_rate,
                email_account_id=email_account_id,
                requested_by_user_id=user_id,
                requested_at=zoned_utc_now(),
                warm_up_service=MailboxWarmUpService.MAILIVERY,
                external_id=external_id,
                status=status,
                with_rampup=with_rampup,
                rampup_speed=rampup_speed,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
                last_started_at=zoned_utc_now(),
            )
        )

    async def delete_campaign(
        self,
        campaign_id: UUID,
        user_id: UUID,
    ) -> None:
        await self.update_by_primary_key(
            table_model=EmailAccountWarmUpCampaign,
            primary_key_to_value={"id": campaign_id},
            column_to_update={
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            },
        )

    async def update_campaign(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self, user_id: UUID, campaign_id: UUID, column_to_update: dict[str, Any]
    ) -> EmailAccountWarmUpCampaign | None:
        column_to_update["updated_at"] = zoned_utc_now()
        column_to_update["updated_by_user_id"] = user_id
        return await self.update_by_primary_key(
            table_model=EmailAccountWarmUpCampaign,
            primary_key_to_value={
                "id": campaign_id,
            },
            column_to_update=column_to_update,
        )

    async def find_campaign(
        self,
        campaign_id: UUID,
    ) -> EmailAccountWarmUpCampaign | None:
        return await self.find_by_primary_key(
            table_model=EmailAccountWarmUpCampaign, id=campaign_id
        )

    async def find_campaign_by_email_account_id(
        self,
        email_account_id: UUID,
    ) -> list[EmailAccountWarmUpCampaign]:
        return await self._find_by_column_values(
            table_model=EmailAccountWarmUpCampaign,
            email_account_id=email_account_id,
            warm_up_service=MailboxWarmUpService.MAILIVERY,
        )

    async def find_campaigns_by_email_account_ids(
        self,
        email_account_ids: list[UUID],
    ) -> dict[UUID, list[EmailAccountWarmUpCampaign]]:
        campaigns = await self._find_by_column_values(
            table_model=EmailAccountWarmUpCampaign,
            email_account_id=email_account_ids,
            warm_up_service=MailboxWarmUpService.MAILIVERY,
        )
        campaigns_by_email_account_id = defaultdict(list)
        for campaign in campaigns:
            campaigns_by_email_account_id[campaign.email_account_id].append(campaign)
        return campaigns_by_email_account_id

    async def find_campaign_by_email_account_id_organization_id_and_warm_up_service(
        self,
        email_account_id: UUID,
        organization_id: UUID,
        warm_up_service: MailboxWarmUpService,
    ) -> EmailAccountWarmUpCampaign | None:
        result = await self._find_by_column_values(
            table_model=EmailAccountWarmUpCampaign,
            email_account_id=email_account_id,
            organization_id=organization_id,
            warm_up_service=warm_up_service,
            unique=True,
        )
        return result[0] if result else None

    async def list_all_campaigns_by_organization_id(
        self,
        organization_id: UUID,
    ) -> list[EmailAccountWarmUpCampaign]:
        stmt = text(
            """
            select * from email_account_warm_up_campaign
            where warm_up_service = :warm_up_service
            and external_id is not null
            and organization_id = :organization_id
            and deleted_at is null
            """
        ).bindparams(
            warm_up_service=MailboxWarmUpService.MAILIVERY.value,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await EmailAccountWarmUpCampaign.bulk_from_rows(rows=rows)

    async def list_all_campaigns(
        self,
    ) -> list[EmailAccountWarmUpCampaign]:
        stmt = text(
            """
            select * from email_account_warm_up_campaign
            where warm_up_service = :warm_up_service
            and external_id is not null
            and deleted_at is null
            """
        ).bindparams(
            warm_up_service=MailboxWarmUpService.MAILIVERY.value,
        )
        rows = await self.engine.all(stmt)
        return await EmailAccountWarmUpCampaign.bulk_from_rows(rows=rows)

    async def list_campaigns_by_email_account_ids(
        self,
        email_account_ids: set[UUID],
        organization_id: UUID,
    ) -> list[EmailAccountWarmUpCampaign]:
        stmt = text(
            """
            select * from email_account_warm_up_campaign
            where email_account_id = ANY(:email_account_ids)
            and organization_id = :organization_id
            and deleted_at is null
            """
        ).bindparams(
            email_account_ids=email_account_ids,
            organization_id=organization_id,
        )
        rows = await self.engine.all(stmt)
        return await EmailAccountWarmUpCampaign.bulk_from_rows(rows=rows)

    async def update_campaign_status_by_email_account_ids(
        self,
        email_account_ids: list[UUID],
        organization_id: UUID,
        status: MailboxWarmUpStatus,
    ) -> list[EmailAccountWarmUpCampaign]:
        stmt = text(
            """
            update email_account_warm_up_campaign
            set status = :status
            where email_account_id = ANY(:email_account_ids)
            and organization_id = :organization_id
            and deleted_at is null
            RETURNING *
            """
        ).bindparams(
            email_account_ids=email_account_ids,
            organization_id=organization_id,
            status=status,
        )
        rows = await self.engine.all(stmt)
        return await EmailAccountWarmUpCampaign.bulk_from_rows(rows=rows)
