from datetime import datetime
from typing import Annotated, Any, Final, Literal, NamedTuple, TypeVar
from uuid import UUID

from fastapi import Depends
from sqlalchemy import BindParameter, TextClause, bindparam
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import text

from salestech_be.common.exception import ResourceNotFoundError
from salestech_be.common.lifespan import get_db_engine
from salestech_be.db.dao.array import ArrayAppend, ColumnIsArray, ValueIsArray
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.core.base import (
    ConditionalUpdateResult,
    TableBoundedModel,
    TableModel,
)
from salestech_be.db.models.core.utils import SqlComparator, SqlSelection, SqlSort
from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)

TableModelT = TypeVar("TableModelT", bound=TableModel)

ColumnConditionValueType = str | int | float | bool | None | datetime

_DEFAULT_LIST_BY_SELECTION_SPEC_LIMIT: Final[int] = 20

# PostgreSQL has a limit of 32767 placeholders per query
POSTGRES_PARAM_LIMIT: Final[int] = 32767


def calculate_max_batch_size(
    model_columns_count: int, model_json_columns_count: int
) -> int:
    """
    Calculate the maximum batch size based on PostgreSQL's parameter limits.

    Args:
        model_columns_count: Number of columns in the model
        model_json_columns_count: Number of JSON columns in the model

    Returns:
        Maximum number of instances that can be inserted in a single query
    """
    # Each instance requires (columns + json_columns) parameters
    param_count_per_instance = model_columns_count + model_json_columns_count
    # Prevent division by zero and ensure we have at least one param per instance
    return POSTGRES_PARAM_LIMIT // max(param_count_per_instance, 1)


class SelectionSpec(NamedTuple):
    must: tuple[SqlSelection, ...] = ()
    must_not: tuple[SqlSelection, ...] = ()
    any: tuple[SqlSelection, ...] = ()
    sorting: tuple[SqlSort, ...] = ()
    exclude_invisible: bool = True
    exclude_locked_by_integrity_jobs: bool = True
    limit: int | None = _DEFAULT_LIST_BY_SELECTION_SPEC_LIMIT
    offset: int | None = None


class GenericRepository:
    """Generic repository provide basic CRUD db queries.
    User can subclass this repo to extend with custom text SQLs for various use cases.

    Examples:

        1. simply invoke pre-built interfaces for the most basic CRUD operations:
            await repository.insert(SomeDbModel(...))
            await repository.update_instance(SomeDbModel(...))

        2. wrap transactions around multiple operations:
           - NOTE: this need to be done within your repo subclass, NEVER do it outside
           your repo class.

           async def my_custom_repo_function(...):
               async with self.engine.begin():
                    await self.insert(...)
                    await self.engine.execute(...)
                    await self.engine.one(...)
           # auto committed after existing the txn context manager

           async def my_custom_repo_function(...):
               async with self.engine.begin():
                    await self.insert(...)
                    await self.engine.at_most_one(...)
                    raise Exception(....)
               # any exception occurred within the txn context manager will cause
               # transaction's rollback automatically.
    """

    def __init__(self, *, engine: Annotated[DatabaseEngine, Depends(get_db_engine)]):
        self.engine = engine

    @staticmethod
    def _get_table_model_upsert_pk_stmt(
        instance: TableModel,
        *,
        exclude_columns_from_update: list[str] | None = None,
    ) -> TextClause:
        instance.validate_in_columns(exclude_columns_from_update or [])

        all_columns: tuple[str, ...] = instance.insert_sql_column_list()

        stmt_text = f"""insert into {instance.fq_table_name}
        ({", ".join([f'"{c}"' for c in all_columns])})
        values
        ({", ".join(instance.insert_sql_column_param_list())})
        """  # noqa: S608
        param_kwargs: dict[str, Any] = instance.insert_bind_params()  # type: ignore[explicit-any] # TODO: fix-any-annotation

        primary_keys = instance.primary_key_column_list()
        excluded_columns = list(primary_keys)
        if exclude_columns_from_update:
            excluded_columns += exclude_columns_from_update
        update_columns = [c for c in all_columns if c not in excluded_columns]
        update_values = tuple(f":{col}" for col in update_columns)

        conflict_targets = f"({', '.join(primary_keys)})"
        updates = ", ".join(
            [
                f"{t[0]} = {t[1]}"
                for t in zip(update_columns, update_values, strict=True)
            ]
        )
        on_conflict_stmt = f"{stmt_text} on conflict {conflict_targets}"
        do_stmt = (
            f"{on_conflict_stmt} do update set {updates}"
            if updates
            else f"{on_conflict_stmt} do nothing"
        )
        stmt_text_with_return = f"{do_stmt} returning *"

        json_params = [bindparam(f, type_=JSONB) for f in instance.get_json_columns()]

        return text(stmt_text_with_return).bindparams(
            *json_params,
            **param_kwargs,
        )

    @staticmethod
    def _get_table_model_upsert_unique_columns_stmt(
        instance: TableModel,
        *,
        on_conflict_target_columns: list[str],
        columns_to_update: list[str] | None = None,
        exclude_columns_from_update: list[str] | None = None,
        exclude_deleted_or_archived: bool | None = False,
        exclude_locked_by_integrity_jobs: bool = False,
    ) -> TextClause:
        if columns_to_update and exclude_columns_from_update:
            raise ValueError(
                "columns_to_update or exclude_columns_from_update should provide at most 1."
            )

        instance.validate_in_columns(exclude_columns_from_update or [])
        instance.validate_in_columns(on_conflict_target_columns)

        all_columns: tuple[str, ...] = instance.insert_sql_column_list()

        stmt_text = f"""insert into {instance.fq_table_name}
        ({", ".join([f'"{c}"' for c in all_columns])})
        values
        ({", ".join(instance.insert_sql_column_param_list())})
        """  # noqa: S608
        param_kwargs: dict[str, Any] = instance.insert_bind_params()  # type: ignore[explicit-any] # TODO: fix-any-annotation

        primary_keys = instance.primary_key_column_list()
        excluded_columns = list(primary_keys) + on_conflict_target_columns

        if exclude_columns_from_update:
            excluded_columns += exclude_columns_from_update
        update_columns = [c for c in all_columns if c not in excluded_columns]

        if columns_to_update:
            update_columns = [c for c in update_columns if c in columns_to_update]
        update_values = tuple(f":{col}" for col in update_columns)

        conflict_targets = f"({', '.join(on_conflict_target_columns)})"
        updates = ", ".join(
            [
                f"{t[0]} = {t[1]}"
                for t in zip(update_columns, update_values, strict=True)
            ]
        )
        on_conflict_stmt = f"{stmt_text} on conflict {conflict_targets} where {'and '.join([f'{column} is not null ' for column in on_conflict_target_columns])}"
        if exclude_deleted_or_archived and "deleted_at" in instance.column_fields:
            on_conflict_stmt = f"{on_conflict_stmt} and deleted_at is null"

        if exclude_deleted_or_archived and "archived_at" in instance.column_fields:
            on_conflict_stmt = f"{on_conflict_stmt} and archived_at is null"

        if (
            exclude_locked_by_integrity_jobs
            and "integrity_job_started_at" in instance.column_fields
        ):
            on_conflict_stmt = (
                f"{on_conflict_stmt} and integrity_job_started_at is null"
            )

        do_stmt = (
            f"{on_conflict_stmt} do update set {updates}"
            if updates
            else f"{on_conflict_stmt} do update set {', '.join([f'{col} = EXCLUDED.{col}' for col in on_conflict_target_columns])}"
        )
        stmt_text_with_return = f"{do_stmt} returning *"

        json_params = [bindparam(f, type_=JSONB) for f in instance.get_json_columns()]

        return text(stmt_text_with_return).bindparams(
            *json_params,
            **param_kwargs,
        )

    @staticmethod
    def _get_table_model_standard_insert_stmt(
        instance: TableModel,
        *,
        on_conflict_do_nothing_target_columns: list[str] | None = None,
        on_conflict_do_nothing_conditional_columns: (
            dict[
                str,
                ColumnConditionValueType,
            ]
            | None
        ) = None,
    ) -> TextClause:
        instance.validate_in_columns(on_conflict_do_nothing_target_columns or [])
        instance.validate_in_columns(on_conflict_do_nothing_conditional_columns or {})

        columns: tuple[str, ...] = instance.insert_sql_column_list()

        stmt_text = f"""insert into {instance.fq_table_name}
        ({", ".join([f'"{c}"' for c in columns])})
        values
        ({", ".join(instance.insert_sql_column_param_list())})
        """  # noqa: S608
        param_kwargs: dict[str, Any] = instance.insert_bind_params()  # type: ignore[explicit-any] # TODO: fix-any-annotation

        if on_conflict_do_nothing_target_columns:
            conflict_targets = f"({', '.join(on_conflict_do_nothing_target_columns)})"
            stmt_text = f"{stmt_text} on conflict {conflict_targets}"
            if on_conflict_do_nothing_conditional_columns:
                conflict_wheres = [
                    f"{k} is null" if v is None else f"{k} = :cf_{k}"
                    for k, v in on_conflict_do_nothing_conditional_columns.items()
                ]
                conflict_wheres_clause = f"where {' and '.join(conflict_wheres)}"
                conflict_where_params = {
                    f"cf_{k}": v
                    for k, v in on_conflict_do_nothing_conditional_columns.items()
                    if v is not None
                }
                param_kwargs.update(conflict_where_params)
                stmt_text = f"{stmt_text} {conflict_wheres_clause} do nothing"
            else:
                stmt_text = f"{stmt_text} do nothing"

        stmt_text_with_return = f"{stmt_text} returning *"

        json_params = [bindparam(f, type_=JSONB) for f in instance.get_json_columns()]

        return text(stmt_text_with_return).bindparams(
            *json_params,
            **param_kwargs,
        )

    async def insert(
        self,
        instance: TableModelT,
    ) -> TableModelT:
        """Insert a non-existed instance into table.

        :param instance: instance to be inserted.
        :type instance: TableModelT
        :return: Inserted instance.
        :rtype: TableModelT
        """
        stmt = GenericRepository._get_table_model_standard_insert_stmt(instance)
        row = await self.engine.one(stmt)
        return instance.from_row(row)

    async def bulk_insert(
        self,
        table_model: type[TableModelT],
        instances: list[TableModelT],
    ) -> list[TableModelT]:
        """Insert multiple instances into table in a single query.

        :param instances: list of instances to be inserted.
        :type instances: list[TableModelT]
        :return: List of inserted instances.
        :rtype: list[TableModelT]

        Processes batches of instances SEQUENTIALLY to maintain transaction integrity, NOT in parallel.
        """
        if not instances:
            return []

        # Get column info for batch size calculation
        columns = instances[0].insert_sql_column_list()
        model_json_column_names = table_model.get_json_columns()

        # Calculate optimal batch size based on PostgreSQL limits
        max_batch_size = calculate_max_batch_size(
            model_columns_count=len(columns),
            model_json_columns_count=len(model_json_column_names),
        )

        # Create batches of instances to stay within PostgreSQL parameter limits
        batches = [
            instances[i : i + max_batch_size]
            for i in range(0, len(instances), max_batch_size)
        ]

        # Process all batches (sequentially to maintain transaction integrity)
        if len(batches) == 1:
            return await self._bulk_insert_batch(table_model, batches[0])
        else:
            combined_results = []
            async with self.engine.begin():
                for batch in batches:
                    batch_result = await self._bulk_insert_batch(table_model, batch)
                    combined_results.extend(batch_result)

            return combined_results

    async def _bulk_insert_batch(
        self,
        table_model: type[TableModelT],
        instances: list[TableModelT],
    ) -> list[TableModelT]:
        """Insert a batch of instances into the table in a single query.
        This is an internal helper method used by bulk_insert.

        :param table_model: The model class for the table
        :param instances: A batch of instances to insert
        :return: List of inserted instances
        """
        if not instances:
            return []

        # Get column names from first instance
        columns: tuple[str, ...] = instances[0].insert_sql_column_list()

        # Build VALUES part of the query for each instance
        values_placeholders = []
        param_kwargs: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        json_params: list[BindParameter[Any]] = []  # type: ignore[explicit-any] # TODO: fix-any-annotation
        model_json_column_names = table_model.get_json_columns()

        for idx, instance in enumerate(instances):
            instance_values = []

            # Add parameters for this instance
            for col_name in columns:
                param_key = f"{col_name}_{idx}"
                param_value = getattr(instance, col_name)
                param_kwargs[param_key] = param_value
                instance_values.append(f":{param_key}")

                # Add JSON parameter if needed
                if col_name in model_json_column_names:
                    json_params.append(bindparam(param_key, type_=JSONB))

            values_placeholders.append(f"({', '.join(instance_values)})")

        # Build the complete query
        stmt_text = f"""
            INSERT INTO {table_model.fq_table_name}
            ({", ".join([f'"{c}"' for c in columns])})
            VALUES
            {", ".join(values_placeholders)}
            RETURNING *
        """  # noqa: S608

        stmt = text(stmt_text).bindparams(*json_params, **param_kwargs)
        rows = await self.engine.all(stmt)
        return await table_model.bulk_from_rows(rows=rows)

    async def upsert_pk(
        self,
        instance: TableModelT,
        *,
        exclude_columns_from_update: list[str] | None = None,
    ) -> TableModelT | None:
        stmt = GenericRepository._get_table_model_upsert_pk_stmt(
            instance, exclude_columns_from_update=exclude_columns_from_update
        )
        row = await self.engine.at_most_one(stmt)
        return instance.from_row(row) if row else None

    async def upsert_unique_target_columns(
        self,
        instance: TableModelT,
        *,
        on_conflict_target_columns: list[str],
        columns_to_update: list[str] | None = None,
        exclude_columns_from_update: list[str] | None = None,
        exclude_deleted_or_archived: bool | None = False,
        exclude_locked_by_integrity_jobs: bool = False,
    ) -> TableModelT:
        stmt = GenericRepository._get_table_model_upsert_unique_columns_stmt(
            instance,
            on_conflict_target_columns=on_conflict_target_columns,
            columns_to_update=columns_to_update,
            exclude_columns_from_update=exclude_columns_from_update,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )
        row = await self.engine.one(stmt)
        return instance.from_row(row)

    async def insert_or_none(
        self,
        instance: TableModelT,
        *,
        on_conflict_do_nothing_target_columns: list[str],
        on_conflict_do_nothing_conditional_columns: (
            dict[
                str,
                ColumnConditionValueType,
            ]
            | None
        ) = None,
    ) -> TableModelT | None:
        """Insert a non-existed instance into table.

        :param instance: instance to be inserted.
        :type instance: TableModelT
        :param on_conflict_do_nothing_target_columns: specify columns for
            "on conflict do nothing"
        :param on_conflict_do_nothing_conditional_columns: specify column conditions for
            "on conflict do nothing where (...)"
        :return: Inserted instance.
        :rtype: TableModelT
        """
        stmt = GenericRepository._get_table_model_standard_insert_stmt(
            instance,
            on_conflict_do_nothing_target_columns=on_conflict_do_nothing_target_columns,
            on_conflict_do_nothing_conditional_columns=on_conflict_do_nothing_conditional_columns,
        )
        row = await self.engine.at_most_one(stmt)
        return instance.from_row(row) if row else None

    async def find_by_primary_key(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **primary_key_to_value: Any,
    ) -> TableModelT | None:
        """Find a pre-existed row by its table's primary key(s).

        :param table_model: model / table to query from and serialize to
        :type table_model: Type[TableModelT]
        :param exclude_deleted_or_archived: whether to exclude deleted rows
        :param primary_key_to_value: primary key value pairs to query the desired row
        :return: retrieved instance if found, otherwise None.
        """
        for pk in table_model.ordered_primary_keys:
            if pk not in primary_key_to_value:
                raise ValueError(
                    f"primary key ({pk}) of table ({table_model.table_name}) "
                    f"need to be specified!",
                )

        pk_columns = table_model.primary_key_column_list()
        pk_where = " and ".join([f"{pk_col} = :{pk_col}" for pk_col in pk_columns])
        where_filter_deleted = (
            "and deleted_at is null"
            if (
                exclude_deleted_or_archived
                and "deleted_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_archived = (
            "and archived_at is null"
            if (
                exclude_deleted_or_archived
                and "archived_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_integrity_job_locked = (
            "and integrity_job_started_at is null"
            if (
                exclude_locked_by_integrity_jobs
                and "integrity_job_started_at" in table_model.column_fields
            )
            else ""
        )
        stmt = text(
            f"""
            select * from {table_model.fq_table_name}
            where {pk_where} {where_filter_deleted} {where_filter_archived} {where_filter_integrity_job_locked}
            """,  # noqa: S608
        ).bindparams(**primary_key_to_value)

        row = await self.engine.at_most_one(stmt)
        return table_model.from_row(row) if row else None

    async def find_by_primary_key_or_fail(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **primary_key_to_value: Any,
    ) -> TableModelT:
        result = await self.find_by_primary_key(
            table_model=table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            **primary_key_to_value,
        )
        if not result:
            raise ResourceNotFoundError(
                f"Record not found for table {table_model.table_name} "
                f"with primary key {primary_key_to_value}",
            )
        return result

    async def find_by_tenanted_primary_key(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        organization_id: UUID,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **primary_key_to_value: Any,
    ) -> TableModelT | None:
        for pk in table_model.ordered_primary_keys:
            if pk not in primary_key_to_value:
                raise ValueError(
                    f"primary key ({pk}) of table ({table_model.table_name}) "
                    f"need to be specified!",
                )

        if "organization_id" not in table_model.column_fields:
            raise ValueError(
                f"organization_id is not a column in table {table_model.table_name}",
            )

        pk_columns = table_model.primary_key_column_list()
        pk_where = " and ".join([f"{pk_col} = :{pk_col}" for pk_col in pk_columns])
        org_id_filter = "and organization_id = :organization_id"
        where_filter_deleted = (
            "and deleted_at is null"
            if (
                exclude_deleted_or_archived
                and "deleted_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_archived = (
            "and archived_at is null"
            if (
                exclude_deleted_or_archived
                and "archived_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_integrity_job_locked = (
            "and integrity_job_started_at is null"
            if (
                exclude_locked_by_integrity_jobs
                and "integrity_job_started_at" in table_model.column_fields
            )
            else ""
        )
        stmt = (
            text(
                f"""
            select * from {table_model.fq_table_name}
            where {pk_where} {org_id_filter} {where_filter_deleted} {where_filter_archived} {where_filter_integrity_job_locked}
            """,  # noqa: S608
            )
            .bindparams(**primary_key_to_value)
            .bindparams(organization_id=organization_id)
        )

        row = await self.engine.at_most_one(stmt)
        return table_model.from_row(row) if row else None

    async def find_by_tenanted_primary_key_or_fail(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        organization_id: UUID,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **primary_key_to_value: Any,
    ) -> TableModelT:
        result = await self.find_by_tenanted_primary_key(
            table_model=table_model,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            **primary_key_to_value,
        )
        if not result:
            raise ResourceNotFoundError(
                f"Record not found for table {table_model.table_name} "
                f"with primary key {primary_key_to_value}",
            )
        return result

    async def list_by_selection_spec(
        self,
        table_model: type[TableModelT],
        *,
        organization_id: UUID,
        selection_spec: SelectionSpec,
    ) -> list[TableModelT]:
        additional_selection_clause = SqlSelection(
            column_name="organization_id",
            sql_comparator=SqlComparator.EQ,
            comparable_value=organization_id,
        )
        must_selections = (
            additional_selection_clause,
            *selection_spec.must,
        )
        final_selection_spec = selection_spec._replace(must=must_selections)
        stmt, stmt_text = GenericRepository._get_list_by_selection_spec_statement(
            table_model=table_model,
            selection_spec=final_selection_spec,
        )
        logger.info("executing list_by_selection_spec", stmt_text=stmt_text)
        rows = await self.engine.all(stmt)
        return await table_model.bulk_from_rows(rows=rows)

    @staticmethod
    def _get_list_by_selection_spec_statement(
        table_model: type[TableModelT],
        *,
        selection_spec: SelectionSpec,
    ) -> tuple[TextClause, str]:
        must_selections: tuple[SqlSelection, ...] = selection_spec.must
        any_selections: tuple[SqlSelection, ...] = selection_spec.any
        must_not_selections: tuple[SqlSelection, ...] = selection_spec.must_not
        sortings = selection_spec.sorting
        limit = selection_spec.limit
        exclude_invisible = selection_spec.exclude_invisible
        exclude_locked_by_integrity_jobs = (
            selection_spec.exclude_locked_by_integrity_jobs
        )
        offset = selection_spec.offset

        # validate involved columns
        involved_columns: list[str] = (
            [s.column_name for s in must_selections if s.column_name]
            + [s.column_name for s in any_selections if s.column_name]
            + [s.column_name for s in must_not_selections if s.column_name]
            + [s.column_name for s in sortings if s.column_name]
        )
        table_model.validate_in_columns(involved_columns)

        params: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation

        # build selectionwhere clause and populate params if any
        _where_clause_items__must: list[str] = []
        _where_clause_items__any: list[str] = []
        _where_clause_items__must_not: list[str] = []
        for _idx, _selection in enumerate(must_selections):
            _param_prefix = f"p_{_idx}"
            _where_clause_items__must.append(
                _selection.as_where_clause(tb_prefix=None, param_prefix=_param_prefix)
            )
            params.update(_selection.as_params(param_prefix=_param_prefix))

        for _idx, _selection in enumerate(any_selections):
            _param_prefix = f"a_{_idx}"
            _where_clause_items__any.append(
                _selection.as_where_clause(tb_prefix=None, param_prefix=_param_prefix)
            )
            params.update(_selection.as_params(param_prefix=_param_prefix))

        for _idx, _selection in enumerate(must_not_selections):
            _param_prefix = f"n_{_idx}"
            _where_clause_items__must_not.append(
                _selection.as_where_clause(tb_prefix=None, param_prefix=_param_prefix)
            )
            params.update(_selection.as_params(param_prefix=_param_prefix))

        # build visibility where clause
        visibility_where_clause = (
            GenericRepository._get_exclude_invisible_columns_str_clause(
                table_model=table_model,
                exclude_deleted_or_archived=exclude_invisible,
                exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            )
        )
        _where_clause_items__must.append(visibility_where_clause)

        # combine where clause items into a single where clause

        _total_must_clause = (
            " and ".join(_where_clause_items__must)
            if _where_clause_items__must
            else None
        )
        _total_any_clause = (
            " or ".join(_where_clause_items__any) if _where_clause_items__any else None
        )
        _total_must_not_clause = (
            " or ".join(_where_clause_items__must_not)
            if _where_clause_items__must_not
            else None
        )

        total_where_clause = f"({_total_must_clause or 'TRUE'}) and ({_total_any_clause or 'TRUE'}) and (not ({_total_must_not_clause or 'FALSE'}))"

        # build sort clause
        _sort_clause_items: list[str] = []
        _sort_params: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        for idx, sort in enumerate(sortings):
            sort_clause, sort_params = sort.as_sort_clause_and_param(
                tb_prefix=None, param_prefix=f"s_{idx}"
            )
            _sort_clause_items.append(sort_clause)
            _sort_params.update(sort_params)

        if params.keys() & _sort_params.keys():
            logger.error(
                "sort params and other params have overlapping keys",
                sort_params=_sort_params,
                accumulated_params=params,
            )
            raise ValueError(
                "sort params and other params have overlapping keys: "
                f"{params.keys() & _sort_params.keys()}"
            )

        params.update(_sort_params)

        sort_clause = ", ".join(_sort_clause_items)

        # build limit clause
        limit_clause = f"limit {limit}" if limit else ""
        offset_clause = f"offset {offset}" if offset else ""

        stmt_str = (
            f"""select * from {table_model.fq_table_name} where {total_where_clause} order by {sort_clause} {limit_clause} {offset_clause}""".strip()  # noqa: S608
            if sort_clause
            else f"""select * from {table_model.fq_table_name} where {total_where_clause} {limit_clause} {offset_clause}""".strip()  # noqa: S608
        )

        return text(stmt_str).bindparams(**params), stmt_str

    @staticmethod
    def _get_find_by_column_values_statement(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **column_to_query: Any,
    ) -> TextClause:
        table_model.validate_in_columns(column_to_query)

        column_where_clauses = []
        column_to_query_not_none = {}

        for column_name, column_value in column_to_query.items():
            if column_value is None:
                # Handle simple None case
                column_where_clauses.append(f"{column_name} is null")
            elif isinstance(column_value, list):
                # Handle list that may contain None values
                non_null_values = [v for v in column_value if v is not None]
                has_null = None in column_value

                if non_null_values and has_null:
                    # Both non-null values and null - use OR condition
                    column_where_clauses.append(
                        f"({column_name} = any(:{column_name}) or {column_name} is null)"
                    )
                    column_to_query_not_none[column_name] = non_null_values
                elif non_null_values:
                    # Only non-null values
                    column_where_clauses.append(f"{column_name} = any(:{column_name})")
                    column_to_query_not_none[column_name] = non_null_values
                elif has_null:
                    # Only null values
                    column_where_clauses.append(f"{column_name} is null")
                else:
                    # Empty list - this should match nothing, but we'll handle it gracefully
                    column_where_clauses.append("false")
            elif isinstance(column_value, ColumnIsArray):
                # Handle ColumnIsArray that may contain None values
                array_values = (
                    column_value.value if hasattr(column_value, "value") else []
                )
                if isinstance(array_values, list):
                    non_null_values = [v for v in array_values if v is not None]
                    has_null = None in array_values

                    if non_null_values and has_null:
                        # Both non-null values and null - use OR condition
                        column_where_clauses.append(
                            f"({column_name} = any(:{column_name}) or {column_name} is null)"
                        )
                        column_to_query_not_none[column_name] = non_null_values
                    elif non_null_values:
                        # Only non-null values
                        column_where_clauses.append(
                            f"{column_name} = any(:{column_name})"
                        )
                        column_to_query_not_none[column_name] = non_null_values
                    elif has_null:
                        # Only null values
                        column_where_clauses.append(f"{column_name} is null")
                    else:
                        # Empty array - this should match nothing
                        column_where_clauses.append("false")
                else:
                    # Non-list value in ColumnIsArray
                    column_where_clauses.append(f"{column_name} = any(:{column_name})")
                    column_to_query_not_none[column_name] = array_values
            else:
                # Handle regular non-null values
                column_where_clauses.append(f"{column_name} = :{column_name}")
                column_to_query_not_none[column_name] = column_value

        column_where = " and ".join(column_where_clauses)

        where_filter_deleted = (
            "and deleted_at is null"
            if (
                exclude_deleted_or_archived
                and "deleted_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_archived = (
            "and archived_at is null"
            if (
                exclude_deleted_or_archived
                and "archived_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_integrity_job_locked = (
            "and integrity_job_started_at is null"
            if (
                exclude_locked_by_integrity_jobs
                and "integrity_job_started_at" in table_model.column_fields
            )
            else ""
        )
        return text(
            f"""select * from {table_model.fq_table_name} where {column_where}
                {where_filter_deleted} {where_filter_archived} {where_filter_integrity_job_locked}""",  # noqa: S608
        ).bindparams(**column_to_query_not_none)

    @staticmethod
    def _get_find_by_column_values_statement_paginated(  # noqa: C901
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
        limit: int | None = None,
        offset: int = 0,
        sort_by: str = "id",
        sort_order: Literal["asc", "desc"] = "asc",
        **column_to_query: object,
    ) -> TextClause:
        table_model.validate_in_columns(list(column_to_query.keys()))
        if sort_by not in table_model.column_fields:
            raise ValueError(
                f"Sort key '{sort_by}' not found in table {table_model.table_name}"
            )

        column_where_parts = []
        for column_name, column_value in column_to_query.items():
            quoted_column_name = f'"{column_name}"'
            if column_value is None:
                column_where_parts.append(f"{quoted_column_name} is null")
            elif isinstance(column_value, list):
                column_where_parts.append(f"{quoted_column_name} = any(:{column_name})")
            elif isinstance(column_value, ColumnIsArray):
                column_where_parts.append(f":{column_name} = any({quoted_column_name})")
            else:
                column_where_parts.append(f"{quoted_column_name} = :{column_name}")
        column_where = " AND ".join(column_where_parts)

        column_to_query_not_none = {
            k: (v.value if isinstance(v, (ValueIsArray, ColumnIsArray)) else v)
            for k, v in column_to_query.items()
            if v is not None
        }

        visibility_filters = []
        if exclude_deleted_or_archived:
            if "deleted_at" in table_model.column_fields:
                visibility_filters.append('"deleted_at" is null')
            if "archived_at" in table_model.column_fields:
                visibility_filters.append('"archived_at" is null')
        if (
            exclude_locked_by_integrity_jobs
            and "integrity_job_started_at" in table_model.column_fields
        ):
            visibility_filters.append('"integrity_job_started_at" is null')

        visibility_where = " AND ".join(visibility_filters)

        where_clause_parts = [part for part in [column_where, visibility_where] if part]
        final_where_clause = (
            " AND ".join(where_clause_parts) if where_clause_parts else "TRUE"
        )

        order_by_clause = f'ORDER BY "{sort_by}" {sort_order.upper()}'

        limit_clause = "LIMIT :limit_val" if limit is not None else ""
        offset_clause = (
            "OFFSET :offset_val" if offset is not None and offset >= 0 else ""
        )

        final_bind_params = {**column_to_query_not_none}
        if limit is not None:
            final_bind_params["limit_val"] = limit
        if offset is not None and offset >= 0:
            final_bind_params["offset_val"] = offset

        sql_string = (
            f"SELECT * FROM {table_model.fq_table_name} WHERE {final_where_clause} "  # noqa: S608
            f"{order_by_clause} {limit_clause} {offset_clause}"
        ).strip()

        return text(sql_string).bindparams(**final_bind_params)

    async def _find_by_column_values_paginated(
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
        limit: int | None = None,
        offset: int = 0,
        sort_by: str = "id",
        sort_order: Literal["asc", "desc"] = "asc",
        **column_to_query: object,
    ) -> list[TableModelT]:
        stmt = GenericRepository._get_find_by_column_values_statement_paginated(
            table_model=table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            limit=limit,
            offset=offset,
            sort_by=sort_by,
            sort_order=sort_order,
            **column_to_query,
        )
        rows = await self.engine.all(stmt)
        return await table_model.bulk_from_rows(rows=rows)

    async def _find_by_column_values(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        unique: bool = False,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **column_to_query: Any,
    ) -> list[TableModelT]:
        stmt = self._get_find_by_column_values_statement(
            table_model=table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            **column_to_query,
        )

        if unique:
            row = await self.engine.at_most_one(stmt)
            rows = [row] if row else []
        else:
            rows = list(await self.engine.all(stmt))

        return await table_model.bulk_from_rows(rows=rows)

    async def _find_unique_by_column_values(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **column_to_query: Any,
    ) -> TableModelT | None:
        logger.debug(table_model.column_fields)

        results = await self._find_by_column_values(
            table_model=table_model,
            unique=True,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            **column_to_query,
        )
        return results[0] if results else None

    async def _find_unique_by_column_values_or_fail(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **column_to_query: Any,
    ) -> TableModelT:
        result = await self._find_unique_by_column_values(
            table_model=table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            **column_to_query,
        )
        if not result:
            raise ResourceNotFoundError(
                f"Record not found for table {table_model.table_name} "
                f"with column values {column_to_query}",
            )
        return result

    async def _count_by_column_values(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **column_to_query: Any,
    ) -> int:
        rows = await self._find_by_column_values(
            table_model=table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            **column_to_query,
        )
        return len(rows)

    async def _find_all(
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> list[TableModelT]:
        visibility_filter = self._get_exclude_invisible_columns_str_clause(
            table_model=table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )
        stmt = text(
            f"""select * from {table_model.fq_table_name} WHERE {visibility_filter}""",  # noqa: S608
        )
        rows = await self.engine.all(stmt)
        return await table_model.bulk_from_rows(rows=rows)

    @staticmethod
    def _get_exclude_invisible_columns_str_clause(
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> str:
        where_filter_deleted = (
            "deleted_at is null"
            if (
                exclude_deleted_or_archived
                and "deleted_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_archived = (
            "archived_at is null"
            if (
                exclude_deleted_or_archived
                and "archived_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_integrity_job_locked = (
            "integrity_job_started_at is null"
            if (
                exclude_locked_by_integrity_jobs
                and "integrity_job_started_at" in table_model.column_fields
            )
            else ""
        )

        where_items = [
            w
            for w in [
                where_filter_deleted.strip(),
                where_filter_archived.strip(),
                where_filter_integrity_job_locked.strip(),
            ]
            if w
        ]
        return " and ".join(where_items) if where_items else "TRUE"

    @staticmethod
    async def _update_by_primary_key_stmt(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        table_model: type[TableModelT],
        *,
        organization_id: UUID | None = None,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        primary_key_to_value: dict[str, Any],
        column_to_update: dict[str, Any] | TableBoundedModel[TableModelT],
        column_condition: TableBoundedModel[TableModelT] | None = None,
    ) -> TextClause:
        if isinstance(column_to_update, TableBoundedModel):
            if not issubclass(table_model, column_to_update.table_model()):
                raise ValueError(
                    f"UpdateModel ({column_to_update}) is not for table ({table_model.table_name}).",
                )
            else:
                column_to_update = column_to_update.flatten_specified_values()

        table_model.validate_contains_all_primary_keys(primary_key_to_value)
        table_model.validate_in_columns(column_to_update)

        pk_columns = table_model.primary_key_column_list()
        pk_where = " and ".join([f"{pk_col} = :{pk_col}" for pk_col in pk_columns])

        column_update_param_prefix = "u_"
        columns_to_update_set = ", ".join(
            [
                f"{k} = array_append({k}, :{column_update_param_prefix}{k})"
                if isinstance(column_to_update[k], ArrayAppend)
                else f"{k} = :{column_update_param_prefix}{k}"
                for k in column_to_update
            ],
        )

        where_organization_filter = (
            "and organization_id=:organization_id" if organization_id else ""
        )
        columns_update_param: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        json_params: list[BindParameter[Any]] = []  # type: ignore[explicit-any] # TODO: fix-any-annotation
        model_json_column_names = table_model.get_json_columns()
        for col_name, update_value in column_to_update.items():
            col_name_alias = f"{column_update_param_prefix}{col_name}"
            columns_update_param[col_name_alias] = (
                update_value.value
                if isinstance(update_value, ArrayAppend)
                else update_value
            )
            if col_name in model_json_column_names:
                json_params.append(bindparam(col_name_alias, type_=JSONB))
        where_filter_deleted = (
            "and deleted_at is null"
            if (
                exclude_deleted_or_archived
                and "deleted_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_archived = (
            "and archived_at is null"
            if (
                exclude_deleted_or_archived
                and "archived_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_integrity_job_locked = (
            "and integrity_job_started_at is null"
            if (
                exclude_locked_by_integrity_jobs
                and "integrity_job_started_at" in table_model.column_fields
            )
            else ""
        )
        column_condition_param: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        if column_condition:
            conditions = column_condition.flatten_specified_values()
            column_condition_param = {
                f"cc_{k}": v for k, v in conditions.items() if v is not None
            }
            column_condition_where = " and ".join(
                [
                    f"{k} = :cc_{k}" if v is not None else f"{k} is null"
                    for k, v in conditions.items()
                ],
            )
            column_condition_where = f" and {column_condition_where}"
        else:
            column_condition_where = ""

        return_stm = text(
            f"""
            update {table_model.fq_table_name}
            set {columns_to_update_set}
            where {pk_where} {where_organization_filter} {column_condition_where} {where_filter_deleted} {where_filter_archived} {where_filter_integrity_job_locked}
            returning *
            """,  # noqa: S608
        ).bindparams(
            *json_params,
            **primary_key_to_value,
            **columns_update_param,
            **column_condition_param,
        )

        return (
            return_stm
            if not organization_id
            else return_stm.bindparams(organization_id=organization_id)
        )

    @staticmethod
    async def _update_by_column_values_stmt(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        column_value_to_query: dict[str, Any],
        column_to_update: dict[str, Any] | TableBoundedModel[TableModelT],
    ) -> TextClause:
        if isinstance(column_to_update, TableBoundedModel):
            if not issubclass(table_model, column_to_update.table_model()):
                raise ValueError(
                    f"UpdateModel ({column_to_update}) is not for table ({table_model.table_name}).",
                )
            else:
                column_to_update = column_to_update.flatten_specified_values()

        table_model.validate_in_columns(column_value_to_query)
        table_model.validate_in_columns(column_to_update)

        where_filter_deleted = (
            " and deleted_at is null"
            if (
                exclude_deleted_or_archived
                and "deleted_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_integrity_job_locked = (
            " and integrity_job_started_at is null"
            if (
                exclude_locked_by_integrity_jobs
                and "integrity_job_started_at" in table_model.column_fields
            )
            else ""
        )

        column_where = " and ".join(
            [
                (
                    f"{column_name} = :{column_name}"
                    if column_value is not None
                    else f"{column_name} is null"
                )
                for column_name, column_value in column_value_to_query.items()
            ],
        )

        column_update_param_prefix = "u_"
        columns_to_update_set = ", ".join(
            [
                f"{k} = array_append({k}, :{column_update_param_prefix}{k})"
                if isinstance(column_to_update[k], ArrayAppend)
                else f"{k} = :{column_update_param_prefix}{k}"
                for k in column_to_update
            ],
        )

        columns_update_param: dict[str, Any] = {}  # type: ignore[explicit-any] # TODO: fix-any-annotation
        json_params: list[BindParameter[Any]] = []  # type: ignore[explicit-any] # TODO: fix-any-annotation
        model_json_column_names = table_model.get_json_columns()
        for col_name, update_value in column_to_update.items():
            col_name_alias = f"{column_update_param_prefix}{col_name}"
            columns_update_param[col_name_alias] = (
                update_value.value
                if isinstance(update_value, ArrayAppend)
                else update_value
            )
            if col_name in model_json_column_names:
                json_params.append(bindparam(col_name_alias, type_=JSONB))

        column_to_query_not_none = {
            k: v for k, v in column_value_to_query.items() if v is not None
        }
        return text(
            f"""
                    update {table_model.fq_table_name}
                    set {columns_to_update_set}
                    where {column_where}{where_filter_deleted}{where_filter_integrity_job_locked}
                    returning *
                    """,  # noqa: S608
        ).bindparams(*json_params, **column_to_query_not_none, **columns_update_param)

    async def update_by_primary_key(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        primary_key_to_value: dict[str, Any],
        column_to_update: dict[str, Any] | TableBoundedModel[TableModelT],
        column_condition: TableBoundedModel[TableModelT] | None = None,
    ) -> TableModelT | None:
        """Update a pre-existed row.
        By its table's primary key(s) and provided update data.
        """
        stmt: TextClause = await GenericRepository._update_by_primary_key_stmt(
            table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            primary_key_to_value=primary_key_to_value,
            column_to_update=column_to_update,
            column_condition=column_condition,
        )

        row = await self.engine.at_most_one(stmt)
        return table_model.from_row(row) if row else None

    async def update_by_tenanted_primary_key(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        organization_id: UUID | None = None,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        primary_key_to_value: dict[str, Any],
        column_to_update: dict[str, Any] | TableBoundedModel[TableModelT],
        column_condition: TableBoundedModel[TableModelT] | None = None,
    ) -> TableModelT | None:
        """Update a pre-existed row.
        By its table's primary key(s) and organization_id, along with provided update data.
        """
        stmt: TextClause = await GenericRepository._update_by_primary_key_stmt(
            table_model,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            primary_key_to_value=primary_key_to_value,
            column_to_update=column_to_update,
            column_condition=column_condition,
        )

        row = await self.engine.at_most_one(stmt)
        return table_model.from_row(row) if row else None

    async def conditionally_update_by_tenanted_primary_key_or_get(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        organization_id: UUID,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        primary_key_to_value: dict[str, Any],
        column_to_update: TableBoundedModel[TableModelT],
        column_condition: TableBoundedModel[TableModelT] | None,
    ) -> ConditionalUpdateResult[TableModelT]:
        updated_or_none = await self.update_by_tenanted_primary_key(
            table_model=table_model,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            primary_key_to_value=primary_key_to_value,
            column_to_update=column_to_update,
            column_condition=column_condition,
        )
        if updated_or_none:
            return ConditionalUpdateResult(record=updated_or_none, is_updated=True)

        existing = await self.find_by_tenanted_primary_key(
            table_model=table_model,
            organization_id=organization_id,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            **primary_key_to_value,
        )

        if not existing:
            raise ResourceNotFoundError(
                f"Record not found for table {table_model.table_name} "
                f"with primary key {primary_key_to_value}",
            )
        return ConditionalUpdateResult(record=existing, is_updated=False)

    async def update_instance(
        self,
        instance: TableModelT,
        exclude_deleted_or_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
    ) -> TableModelT | None:
        """Update a pre-existed row by its table's primary key(s).

        Save the entire provided instance as-is to db no matter db side columns
        are changed or not. This is more convenient than update by columns, but
        potential a waste of DB server side IO resource.

        Args:
            instance ():

        Returns: updated instance if found, otherwise None

        """
        return await self.update_by_primary_key(
            table_model=type(instance),
            primary_key_to_value=instance.primary_key_to_value(),
            column_to_update=instance.model_dump(
                exclude=set(instance.ordered_primary_keys),
            ),
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
        )

    async def _update_unique_by_column_values(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
        column_value_to_query: dict[str, Any],
        column_to_update: dict[str, Any] | TableBoundedModel[TableModelT],
    ) -> TableModelT | None:
        stmt = await GenericRepository._update_by_column_values_stmt(
            table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            column_value_to_query=column_value_to_query,
            column_to_update=column_to_update,
        )

        row = await self.engine.at_most_one(stmt)
        return table_model.from_row(row) if row else None

    async def _update_by_column_values(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool = True,
        exclude_locked_by_integrity_jobs: bool = True,
        column_value_to_query: dict[str, Any],
        column_to_update: dict[str, Any] | TableBoundedModel[TableModelT],
        unique: bool = False,
    ) -> list[TableModelT]:
        stmt = await GenericRepository._update_by_column_values_stmt(
            table_model,
            exclude_deleted_or_archived=exclude_deleted_or_archived,
            exclude_locked_by_integrity_jobs=exclude_locked_by_integrity_jobs,
            column_value_to_query=column_value_to_query,
            column_to_update=column_to_update,
        )

        if unique:
            row = await self.engine.one(stmt)
            unique_rows = [row] if row else []
            return await table_model.bulk_from_rows(rows=unique_rows)
        else:
            rows = await self.engine.all(stmt)
            return await table_model.bulk_from_rows(rows=rows)

    async def count_by_column_values(  # type: ignore[explicit-any] # TODO: fix-any-annotation
        self,
        table_model: type[TableModelT],
        *,
        exclude_deleted_or_archived: bool | None = True,
        exclude_locked_by_integrity_jobs: bool = True,
        **column_to_query: Any,
    ) -> int:
        """Count records matching the given column values.
        Args:
            table_model: The model class to count records for
            exclude_deleted_or_archived: Whether to exclude deleted/archived records
            exclude_locked_by_integrity_jobs: Whether to exclude records locked by integrity jobs
            **column_to_query: Column name to value mappings to filter by
        Returns:
            The count of matching records
        """
        table_model.validate_in_columns(column_to_query)
        column_where = " and ".join(
            [
                (
                    (
                        f"{column_name} = any(:{column_name})"
                        if isinstance(column_value, list)
                        else f":{column_name} = any({column_name})"
                        if isinstance(column_value, ColumnIsArray)
                        else f"{column_name} = :{column_name}"
                    )
                    if column_value is not None
                    else f"{column_name} is null"
                )
                for column_name, column_value in column_to_query.items()
            ],
        )

        column_to_query_not_none = {
            k: (v.value if isinstance(v, ValueIsArray | ColumnIsArray) else v)
            for k, v in column_to_query.items()
            if v is not None
        }

        where_filter_deleted = (
            "and deleted_at is null"
            if (
                exclude_deleted_or_archived
                and "deleted_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_archived = (
            "and archived_at is null"
            if (
                exclude_deleted_or_archived
                and "archived_at" in table_model.column_fields
            )
            else ""
        )
        where_filter_integrity_job_locked = (
            "and integrity_job_started_at is null"
            if (
                exclude_locked_by_integrity_jobs
                and "integrity_job_started_at" in table_model.column_fields
            )
            else ""
        )
        stmt = text(
            f"""select count(*) from {table_model.fq_table_name} where {column_where}
                {where_filter_deleted} {where_filter_archived} {where_filter_integrity_job_locked}""",  # noqa: S608
        ).bindparams(**column_to_query_not_none)

        result = await self.engine.one(stmt)
        return int(result[0])
