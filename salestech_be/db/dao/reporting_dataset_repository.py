from typing import TypeVar
from uuid import UUID, uuid4

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.core.reporting.type.query_config import (
    ExpressionColumnConfig,
    FieldColumnConfig,
    QueryConfig,
)
from salestech_be.db.dao.generic_repository import GenericRepository
from salestech_be.db.models.core.base import TableModel
from salestech_be.db.models.reporting import (
    ReportingDataset,
    ReportingDatasetField,
    ReportingDatasetFieldDataType,
    ReportingDatasetSource,
    ReportingDatasetType,
)
from salestech_be.util.time import zoned_utc_now

T = TypeVar("T", bound=TableModel)


class ReportingDatasetRepository(GenericRepository):

    def map_dataset_fields_by_ids(dataset_field_ids: list[UUID]) -> map[UUID, ReportingDatasetField]:
        pass

    def _extract_dataset_field_ids_from_query_config(
        self, query_config: QueryConfig
    ) -> list[UUID]:
        pass

    def _extract_dataset_fields_from_query_config(
        self, query_config: QueryConfig
    ) -> map[UUID, ReportingDatasetField]:
        dataset_field_ids = self._extract_dataset_field_ids_from_query_config(query_config)
        return self.map_dataset_fields_by_ids(dataset_field_ids)

    def _extract_columns_from_query_config(
        self, query_config: QueryConfig
    ) -> list[tuple[str, str | None, ReportingDatasetFieldDataType]]:
        """
        Extract column information from QueryConfig.

        Returns:
            List of tuples containing (name, description, data_type)
        """
        columns = []

        map_dataset_fields = self._extract_dataset_fields_from_query_config(query_config)
        for column_config in query_config.columns:
            if isinstance(column_config, FieldColumnConfig):
                # For field columns, use the field name and alias
                dataset_field: ReportingDatasetField = map_dataset_fields[column_config.dataset_field.field.field_id]
                name = column_config.alias or dataset_field.name
                description = name
                data_type = dataset_field.data_type
            elif isinstance(column_config, ExpressionColumnConfig):
                # For expression columns, use the alias as name
                name = column_config.alias or column_config.to_sql()
                description = name
                # TODO resolve data_type based on expression
                data_type = ReportingDatasetFieldDataType.STRING
            else:
                raise InvalidArgumentError(f"Unsupported column config type: {column_config}")

            columns.append((name, description, data_type))
        return columns

    async def create_dataset(
        self,
        *,
        name: str,
        description: str | None,
        query_config: QueryConfig,
        dataset_source: ReportingDatasetSource,
        user_id: UUID,
        organization_id: UUID,
    ) -> tuple[ReportingDataset, list[ReportingDatasetField]]:
        """Create a new dataset with fields extracted from query_config."""
        async with self.engine.begin():
            # Create the dataset
            dataset = ReportingDataset(
                id=uuid4(),
                name=name,
                description=description,
                source=dataset_source,
                type=ReportingDatasetType.QUERY,
                query_config=query_config,
                organization_id=organization_id,
                created_at=zoned_utc_now(),
                created_by_user_id=user_id,
            )
            created_dataset = await self.insert(dataset)

            # Extract columns from query_config and create dataset fields
            column_info = self._extract_columns_from_query_config(query_config)
            created_fields = []

            for name, description, data_type in column_info:
                field = ReportingDatasetField(
                    id=uuid4(),
                    dataset_id=created_dataset.id,
                    name=name,
                    description=description,
                    data_type=data_type,
                    organization_id=organization_id,
                    created_at=zoned_utc_now(),
                    created_by_user_id=user_id,
                )
                created_field = await self.insert(field)
                created_fields.append(created_field)

            return created_dataset, created_fields

    async def update_dataset_with_fields(
        self,
        *,
        dataset_id: UUID,
        name: str | None = None,
        description: str | None = None,
        query_config: QueryConfig | None = None,
        user_id: UUID,
        organization_id: UUID,
    ) -> tuple[ReportingDataset, list[ReportingDatasetField]]:
        """Update a dataset and regenerate fields if query_config is updated."""
        async with self.engine.begin():
            # Build update data
            update_data = {}
            if name is not None:
                update_data["name"] = name
            if description is not None:
                update_data["description"] = description
            if query_config is not None:
                update_data["query_config"] = query_config

            update_data["updated_at"] = zoned_utc_now()
            update_data["updated_by_user_id"] = user_id

            # Update dataset
            updated_dataset = await self.update_by_tenanted_primary_key(
                ReportingDataset,
                primary_key_to_value={"id": dataset_id},
                column_to_update=update_data,
                organization_id=organization_id,
            )

            # If query_config was updated, regenerate fields
            if query_config is not None:
                # Delete existing fields
                await self._update_by_column_values(
                    ReportingDatasetField,
                    column_value_to_query={
                        "dataset_id": dataset_id,
                        "organization_id": organization_id,
                    },
                    column_to_update={
                        "deleted_at": zoned_utc_now(),
                        "deleted_by_user_id": user_id,
                    },
                )

                # Extract columns from query_config and create new dataset fields
                column_info = self._extract_columns_from_query_config(query_config)
                created_fields = []

                for name_field, description, data_type in column_info:
                    field = ReportingDatasetField(
                        id=uuid4(),
                        dataset_id=dataset_id,
                        name=name_field,
                        description=description,
                        data_type=data_type,
                        organization_id=organization_id,
                        created_at=zoned_utc_now(),
                        created_by_user_id=user_id,
                    )
                    created_field = await self.insert(field)
                    created_fields.append(created_field)
            else:
                # Get existing fields if query_config wasn't updated
                created_fields = await self._find_by_column_values(
                    ReportingDatasetField,
                    dataset_id=dataset_id,
                    organization_id=organization_id,
                )

            return updated_dataset, created_fields

    async def patch_dataset(
        self,
        *,
        dataset_id: UUID,
        name: str | None = None,
        description: str | None = None,
        query_config: QueryConfig | None = None,
        user_id: UUID,
        organization_id: UUID,
    ) -> tuple[ReportingDataset, list[ReportingDatasetField]]:
        """Patch a dataset following the standard patch pattern."""
        async with self.engine.begin():
            # Get existing dataset
            db_dataset = await self.find_by_tenanted_primary_key_or_fail(
                ReportingDataset,
                id=dataset_id,
                organization_id=organization_id,
            )

            # Only allow updating QUERY type datasets
            if db_dataset.type != ReportingDatasetType.QUERY:
                raise InvalidArgumentError("Only QUERY type datasets can be updated")

            # Update dataset and fields using existing method
            updated_db_dataset, current_fields = await self.update_dataset_with_fields(
                dataset_id=dataset_id,
                name=name,
                description=description,
                query_config=query_config,
                user_id=user_id,
                organization_id=organization_id,
            )

            return updated_db_dataset, current_fields

    async def list_datasets(
        self,
        *,
        organization_id: UUID,
    ) -> list[ReportingDataset]:
        return await self._find_by_column_values(
            ReportingDataset,
            organization_id=[organization_id, None],
        )

    async def list_dataset_fields(
        self,
        *,
        dataset_id: UUID,
        organization_id: UUID,
    ) -> list[ReportingDatasetField]:
        return await self._find_by_column_values(
            ReportingDatasetField,
            dataset_id=dataset_id,
            organization_id=[organization_id, None],
        )

    async def get_dataset(
        self,
        *,
        dataset_id: UUID,
        organization_id: UUID,
    ) -> ReportingDataset:
        return await self._find_unique_by_column_values_or_fail(
            ReportingDataset,
            id=dataset_id,
            organization_id=[organization_id, None],
        )

    async def delete_dataset(
        self,
        *,
        dataset_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> None:
        async with self.engine.begin():
            # Create update data with soft delete fields
            update_data = {
                "deleted_at": zoned_utc_now(),
                "deleted_by_user_id": user_id,
            }

            # Delete dataset field
            await self._update_by_column_values(
                ReportingDatasetField,
                column_value_to_query={
                    "dataset_id": dataset_id,
                    "organization_id": organization_id,
                },
                column_to_update=update_data,
            )

            # Delete dataset
            await self.update_by_tenanted_primary_key(
                ReportingDataset,
                primary_key_to_value={"id": dataset_id},
                column_to_update=update_data,
                organization_id=organization_id,
            )
