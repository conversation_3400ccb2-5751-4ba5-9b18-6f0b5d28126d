"""Voice API v2 schemas."""

from datetime import datetime
from enum import Str<PERSON>num
from typing import Any, TypeVar
from uuid import UUID

from pydantic import BaseModel, Field

from salestech_be.common.type.patch_request import (
    UNSET,
    BasePatchRequest,
    UnsetAware,
)
from salestech_be.core.voice.v2.types import (
    AvailablePhoneNumber,
)
from salestech_be.db.models.voice_v2 import (
    CallDirection,
    CallDisposition,
    CallStatus,
    CallType,
    VoiceProvider,
    VoiceUsageCategory,
)
from salestech_be.util.pydantic_types.str import PhoneNumber
from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime

T = TypeVar("T")


class AvailablePhoneNumberSearchRequest(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Available phone number search request."""

    area_code: str | None = None
    country_code: str = Field(default="US")  # Default to US
    capabilities: list[str] = Field(default_factory=lambda: ["voice"])
    limit: int = Field(default=10, ge=1, le=30)
    metadata: dict[str, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation
    provider: str = Field(default="twilio")
    page: int = Field(default=0)
    latlong: str | None = Field(
        default=None,
        pattern=r"^-?\d+(\.\d+)?,-?\d+(\.\d+)?$",
        description="Latitude and longitude in format 'lat,long' (e.g., '37.786952,-122.399523')",
    )
    distance: int | None = Field(
        default=None,
        ge=25,
        le=500,
        description="Search radius in miles (min 25, max 500)",
    )
    near_number: str | None = Field(
        default=None,
        description="Search for numbers near a specific number",
    )
    area_code_suggestion: bool | None = Field(
        default=True,
        description="Whether to suggest area codes based on the search criteria",
    )


class AvailablePhoneNumberResponse(BaseModel):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Available phone number response."""

    number: str
    country_code: str
    area_code: str | None = None
    region: str | None = None
    capabilities: list[str]
    metadata: dict[str, Any] | None = None  # type: ignore[explicit-any] # TODO: fix-any-annotation

    @classmethod
    def from_model(cls, model: AvailablePhoneNumber) -> "AvailablePhoneNumberResponse":
        """Create response from model."""
        return cls(
            number=model.number,
            country_code=model.country_code,
            area_code=model.area_code,
            region=model.region,
            capabilities=model.capabilities,
            metadata=model.metadata,
        )


class VoiceTokenRequest(BaseModel):
    """Voice token request."""

    type: CallType = Field(default=CallType.WEBRTC)
    provider: VoiceProvider = Field(default=VoiceProvider.TWILIO)


class VoiceUsageRequest(BaseModel):
    """Voice usage request."""

    provider: VoiceProvider = Field(default=VoiceProvider.TWILIO)
    start_date: (
        datetime  # need year, month, day, and FE should pass in time zone information
    )
    end_date: (
        datetime  # need year, month, day, and FE should pass in time zone information
    )
    category: list[VoiceUsageCategory] = Field(default=[VoiceUsageCategory.TOTAL_PRICE])


class VoiceTokenResponse(BaseModel):
    """Response model for voice token endpoint."""

    token: str
    type: str


class UpdateCallRecordingStatus(StrEnum):
    """Update call recording status."""

    PAUSED = "paused"
    IN_PROGRESS = "in-progress"


class UpdateCallRecordingRequest(BaseModel):
    """Update call recording request."""

    external_call_id: str
    status: UpdateCallRecordingStatus


class InitiateCallRequest(BaseModel):
    """Initiate call request."""

    contact_id: UUID | None = None
    pipeline_id: UUID | None = None
    account_id: UUID | None = None
    to_number: PhoneNumber
    provider: VoiceProvider = Field(default=VoiceProvider.TWILIO)
    type: CallType | None = Field(default=CallType.BRIDGE)
    caller_extension: str | None = None
    recipient_extension: str | None = None
    task_id: UUID | None = None


class InitiateCallResponse(BaseModel):
    """Initiate call response."""

    call_id: UUID
    meeting_id: UUID
    external_call_id: str


class UpdateCallRequest(BasePatchRequest):  # type: ignore[explicit-any] # TODO: fix-any-annotation
    """Update call request."""

    caller_id: UnsetAware[UUID | None] = UNSET
    recipient_id: UnsetAware[UUID | None] = UNSET
    contact_id: UnsetAware[UUID | None] = UNSET
    organization_phone_number_id: UnsetAware[UUID] = UNSET
    voice_provider_account_id: UnsetAware[UUID] = UNSET
    call_type: UnsetAware[str] = UNSET
    pipeline_id: UnsetAware[UUID | None] = UNSET
    account_id: UnsetAware[UUID | None] = UNSET
    pipeline_select_list_value_id: UnsetAware[UUID | None] = UNSET
    parent_call_id: UnsetAware[UUID | None] = UNSET
    caller_number: UnsetAware[PhoneNumber] = UNSET
    caller_country_code: UnsetAware[str | None] = UNSET
    caller_area_code: UnsetAware[str | None] = UNSET
    caller_extension: UnsetAware[str | None] = UNSET
    status: UnsetAware[CallStatus] = UNSET
    direction: UnsetAware[CallDirection] = UNSET
    started_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    ended_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    duration: UnsetAware[int | None] = UNSET
    recording_url: UnsetAware[str | None] = UNSET
    disposition: UnsetAware[CallDisposition | None] = UNSET
    status_changed_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    disposition_set_at: UnsetAware[ZoneRequiredDateTime | None] = UNSET
    transcript_id: UnsetAware[UUID | None] = UNSET
    external_id: UnsetAware[str | None] = UNSET
    metadata: UnsetAware[dict[str, Any] | None] = UNSET  # type: ignore[explicit-any] # TODO: fix-any-annotation
    created_by_user_id: UnsetAware[UUID | None] = UNSET
    deleted_by_user_id: UnsetAware[UUID | None] = UNSET
    recipient_number: UnsetAware[PhoneNumber | None] = UNSET
    recipient_country_code: UnsetAware[str | None] = UNSET
    recipient_extension: UnsetAware[str | None] = UNSET
    recipient_area_code: UnsetAware[str | None] = UNSET


class VerifyPhoneNumberRequest(BaseModel):
    """Request model for verifying a phone number."""

    phone_number: str = Field(..., description="The phone number to verify")
    provider: str = Field(
        default="twilio", description="The voice provider to use for verification"
    )


class VerifyPhoneNumberResponse(BaseModel):
    """Response model for the phone number verification result."""

    validation_code: str = Field(
        ..., description="The validation code for the phone number"
    )
    phone_number: str = Field(..., description="The phone number that was verified")
