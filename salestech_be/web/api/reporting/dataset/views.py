from typing import Annotated
from uuid import UUID

from fastapi import Depends
from starlette import status

from salestech_be.common.results import <PERSON>urs<PERSON>
from salestech_be.db.models.reporting import ReportingDatasetSource
from salestech_be.ree_logging import get_logger
from salestech_be.services.auth.perm_predicates import require_read_placeholder_access
from salestech_be.web.api.common.container import (
    ListEntityRequest,
    PaginatedListResponse,
)
from salestech_be.web.api.filter.logical_filter import (
    paginate_entities,
    sort_entities,
    standard_filter_entities,
)
from salestech_be.web.api.reporting.dataset.schema import (
    CreateReportingDatasetRequest,
    PatchReportingDatasetRequest,
    ReportingDatasetDTO,
)
from salestech_be.web.api.reporting.dataset.service import ReportingDatasetService
from salestech_be.web.api_router_ext import ReeAPIRouter
from salestech_be.web.lifespan_service import reporting_dataset_service_from_lifespan
from salestech_be.web.middleware.types import (
    AnnotatedReevoOrganizationId,
    AnnotatedReevoUserId,
)

router = ReeAPIRouter()
logger = get_logger("reporting.dataset")

ReportingDatasetServiceDI = Annotated[
    ReportingDatasetService, Depends(reporting_dataset_service_from_lifespan)
]


@router.post(
    "",
    response_model=ReportingDatasetDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def create_dataset(
    request: CreateReportingDatasetRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dataset_service: ReportingDatasetServiceDI,
) -> ReportingDatasetDTO:
    return await reporting_dataset_service.create_dataset(
        request=request,
        dataset_source=ReportingDatasetSource.CUSTOM,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.get(
    "/{dataset_id}",
    response_model=ReportingDatasetDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def get_dataset(
    dataset_id: UUID,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dataset_service: ReportingDatasetServiceDI,
) -> ReportingDatasetDTO:
    return await reporting_dataset_service.get_dataset(
        dataset_id=dataset_id,
        organization_id=organization_id,
    )


@router.patch(
    "/{dataset_id}",
    response_model=ReportingDatasetDTO,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def patch_dataset(
    dataset_id: UUID,
    request: PatchReportingDatasetRequest,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dataset_service: ReportingDatasetServiceDI,
) -> ReportingDatasetDTO:
    return await reporting_dataset_service.patch_dataset(
        dataset_id=dataset_id,
        request=request,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.delete(
    "/{dataset_id}",
    response_model=None,
    status_code=status.HTTP_204_NO_CONTENT,
    dependencies=[Depends(require_read_placeholder_access)],
)
async def delete_dataset(
    dataset_id: UUID,
    user_id: AnnotatedReevoUserId,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dataset_service: ReportingDatasetServiceDI,
) -> None:
    await reporting_dataset_service.delete_dataset(
        dataset_id=dataset_id,
        user_id=user_id,
        organization_id=organization_id,
    )


@router.post(
    "/_list",
    response_model=PaginatedListResponse[ReportingDatasetDTO],
    dependencies=[Depends(require_read_placeholder_access)],
)
async def list_datasets(
    list_request: ListEntityRequest,
    organization_id: AnnotatedReevoOrganizationId,
    reporting_dataset_service: ReportingDatasetServiceDI,
) -> PaginatedListResponse[ReportingDatasetDTO]:
    db_dataset_dtos = await reporting_dataset_service.list_datasets(
        organization_id=organization_id,
    )

    filtered_dataset_dtos = standard_filter_entities(
        db_dataset_dtos,
        list_request.filters,
    )

    if not filtered_dataset_dtos:
        return PaginatedListResponse(list_data=[], cursor=Cursor())

    sorted_dataset_dtos = sort_entities(
        filtered_dataset_dtos,
        list_request.sorters,
    )

    paginated_dataset_dtos, response_cursor = paginate_entities(
        sorted_dataset_dtos,
        list_request.cursor,
    )

    return PaginatedListResponse[ReportingDatasetDTO](
        list_data=paginated_dataset_dtos,
        cursor=response_cursor,
    )
