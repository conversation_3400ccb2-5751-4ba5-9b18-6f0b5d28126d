diff --git a/salestech_be/db/dao/generic_repository.py b/salestech_be/db/dao/generic_repository.py
index 31421702d..324a77a59 100644
--- a/salestech_be/db/dao/generic_repository.py
+++ b/salestech_be/db/dao/generic_repository.py
@@ -762,28 +762,72 @@ class GenericRepository:
         **column_to_query: Any,
     ) -> TextClause:
         table_model.validate_in_columns(column_to_query)
-        column_where = " and ".join(
-            [
-                (
-                    (
-                        f"{column_name} = any(:{column_name})"
-                        if isinstance(column_value, list)
-                        else f":{column_name} = any({column_name})"
-                        if isinstance(column_value, ColumnIsArray)
-                        else f"{column_name} = :{column_name}"
+
+        column_where_clauses = []
+        column_to_query_not_none = {}
+
+        for column_name, column_value in column_to_query.items():
+            if column_value is None:
+                # Handle simple None case
+                column_where_clauses.append(f"{column_name} is null")
+            elif isinstance(column_value, list):
+                # Handle list that may contain None values
+                non_null_values = [v for v in column_value if v is not None]
+                has_null = None in column_value
+
+                if non_null_values and has_null:
+                    # Both non-null values and null - use OR condition
+                    column_where_clauses.append(
+                        f"({column_name} = any(:{column_name}) or {column_name} is null)"
                     )
-                    if column_value is not None
-                    else f"{column_name} is null"
+                    column_to_query_not_none[column_name] = non_null_values
+                elif non_null_values:
+                    # Only non-null values
+                    column_where_clauses.append(f"{column_name} = any(:{column_name})")
+                    column_to_query_not_none[column_name] = non_null_values
+                elif has_null:
+                    # Only null values
+                    column_where_clauses.append(f"{column_name} is null")
+                else:
+                    # Empty list - this should match nothing, but we'll handle it gracefully
+                    column_where_clauses.append("false")
+            elif isinstance(column_value, ColumnIsArray):
+                # Handle ColumnIsArray that may contain None values
+                array_values = (
+                    column_value.value if hasattr(column_value, "value") else []
                 )
-                for column_name, column_value in column_to_query.items()
-            ],
-        )
+                if isinstance(array_values, list):
+                    non_null_values = [v for v in array_values if v is not None]
+                    has_null = None in array_values
+
+                    if non_null_values and has_null:
+                        # Both non-null values and null - use OR condition
+                        column_where_clauses.append(
+                            f"({column_name} = any(:{column_name}) or {column_name} is null)"
+                        )
+                        column_to_query_not_none[column_name] = non_null_values
+                    elif non_null_values:
+                        # Only non-null values
+                        column_where_clauses.append(
+                            f"{column_name} = any(:{column_name})"
+                        )
+                        column_to_query_not_none[column_name] = non_null_values
+                    elif has_null:
+                        # Only null values
+                        column_where_clauses.append(f"{column_name} is null")
+                    else:
+                        # Empty array - this should match nothing
+                        column_where_clauses.append("false")
+                else:
+                    # Non-list value in ColumnIsArray
+                    column_where_clauses.append(f"{column_name} = any(:{column_name})")
+                    column_to_query_not_none[column_name] = array_values
+            else:
+                # Handle regular non-null values
+                column_where_clauses.append(f"{column_name} = :{column_name}")
+                column_to_query_not_none[column_name] = column_value
 
-        column_to_query_not_none = {
-            k: (v.value if isinstance(v, ValueIsArray | ColumnIsArray) else v)
-            for k, v in column_to_query.items()
-            if v is not None
-        }
+        column_where = " and ".join(column_where_clauses)
 
         where_filter_deleted = (
             "and deleted_at is null"
